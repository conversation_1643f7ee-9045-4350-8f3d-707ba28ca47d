@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,300;0,400;0,500;0,700;0,900;1,300;1,500&display=swap');

body {
    background-color: #fff !important;
}

a {
    cursor: pointer;
}

.incentiveStructureSection,
.bookingsIncentiveSection {
    border-radius: 24px;
    background: #FFFFE3;
    padding: 20px;
    margin: 0px;


    h2 {
        color: #6B8F07;
        text-align: center;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
    }

    .sectionDescription {
        color: #253858;
        text-align: center;
        font-family: Roboto;
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 28px;
        margin-bottom: 20px;
    }

    .incentiveTable {
        width: 100%;
        border-radius: 8px;
        overflow: hidden;


        .tableHeader {
            display: flex;
            border-radius: 8px;
            background: #89AD25;
            color: white;
            font-weight: 600;
            padding: 12px 15px;
            margin-bottom: 2px;

            .productType {
                flex: 1;
                text-align: left;
            }

            .incentiveRate {
                flex: 1;
                text-align: right;
            }
        }

        .tableRow {
            display: flex;
            padding: 12px 25px;
            border-radius: 8px;
            background: rgba(137, 173, 37, 0.20);
            color: #607A16;
            font-family: Roboto;
            font-size: 13px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            border: 2px solid #ffffe3;
            justify-content: space-between;

            .incentiveRate {
                font-weight: 800;
            }


        }
    }

    .bookingRules {
        margin-top: 15px;
        margin-left: 8px;

        p {
            color: #253858;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 25px;

            strong {
                font-weight: 700;

            }
        }
    }
}

.bookingsIncentiveSection {
    margin-bottom: 30px;
}

.fosAllowanceSection
 {
    background-color: #E5E9FF;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;

    h3 {
        color: #253858;
        margin-bottom: 15px;
    }

    .FeedbackHeading {
        color: #4E5A9E;
        font-family: Roboto;
        font-size: 16px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        margin-bottom: 10px;
    }

    .allowanceTable {
        border-radius: 8px;
        overflow: hidden;
        background: rgba(78, 90, 158, 0.05);

        .tableHeader {
            display: flex;
            background-color: #6B72B7;
            color: white;
            padding: 12px 15px;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;

            .slabColumn {
                flex: 1;
                text-align: left;
                font-weight: 600;
            }

            .payoutColumn {
                flex: 1;
                text-align: right;
                font-weight: 600;
            }
        }

        .tableRow {
            display: flex;
            padding: 12px 15px;
            color: #4E5A9E;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
         

            .slabColumn {
                flex: 1.5;
                text-align: left;
               
            }

            .payoutColumn {
                flex: 1;
                text-align: right;
               
            }
        }
        .noteText {
            margin-top: 8px;
            color: #4E5A9E;
            font-family: Roboto;
            font-size: 11px;
            font-style: normal;
            font-weight: 500;
            padding-left: 15px;
            line-height: normal;
            text-align: left;
            padding-bottom: 12px;
        }
    }

    .navigationContainer {
        position: relative;
        width: 100%;
    }

    .navArrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        background: #5D71B7;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 18px;
        z-index: 10;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .navArrowLeft {
        left: -50px;
    }

    .navArrowRight {
        right: -50px;
    }

}

// .incentiveCriteriaSection {
//     border: 1px solid #e0e0e0;
//     background-color: #FFFFFF;

//     h2 {
//         color: #253858;
//         font-size: 20px;
//         font-weight: 700;
//         margin-bottom: 20px;
//         border-bottom: 1px solid #e0e0e0;
//         padding-bottom: 10px;
//     }

//     .criteriaContent {
//         display: flex;
//         justify-content: space-between;
//         flex-wrap: wrap;

//         .criteriaColumn {
//             flex: 1;
//             min-width: 250px;
//             margin-bottom: 15px;
//             padding-right: 15px;
//         }
//     }

//     .highlight {
//         color: #FF5252;
//         font-weight: 600;
//     }

//     p {
//         color: #253858;
//         font-size: 16px;
//         line-height: 1.6;
//         margin-bottom: 15px;
//     }
// }

.mt-2 {
    margin-top: 2rem;
}

.MuiPopover-root {
    z-index: 9999999 !important;
}

.MonthName {
    font-size: 13px !important;
    font-family: 'Roboto' !important;
    font-weight: 600 !important;

}

.ViewIncetiveCriteriaBtn {
    position: fixed;
    bottom: 15px;
    z-index: 99999;
    right: 15px;
    float: right;

    button {
        background: #5D71B7;
        font-family: Roboto;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        line-height: 19px;
        padding: 10px;
        letter-spacing: 0em;
        height: 42px;
        text-align: center;

        img {
            width: 32px;
            margin-right: 5px;
            border-radius: 50%;
        }

        &:hover {
            background: #5D71B7;
        }
    }
}

.BookingIncentivePopop {
    z-index: 999999 !important;

    .MuiButtonBase-root {
        height: auto;
    }

    .MuiPaper-elevation {
        width: 457px;
        margin: 15px;
        border-radius: 20px;
    }

    .MuiBackdrop-root {
        background-color: #000000d1 !important;
    }

    .MuiDialogContent-root {
        padding: 20px 0px;
    }

    h4 {
        color: #000;
        font-family: Roboto;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding: 0px 15px;
    }


    .incentiveCalculation {
        display: flex;
        height: 40px;
        width: 100%;
        padding: 0px 15px;
        align-items: center;
    }

    .Caption {
        li {
            color: #253858;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            opacity: 0.6;
            margin-top: 1rem;

            &:first-child {
                width: 230px;
            }
        }
    }

    .white {
        background: #fff;
        padding: 0px;

        li {
            color: #253858;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;

            &:first-child {
                width: 230px;
            }

        }
    }

    .gray {
        background: #F5F5F5;
        padding: 0px;

        li {
            color: #253858;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;

            &:first-child {
                width: 230px;
            }

        }
    }

    .bookingIncentive {
        background: linear-gradient(90deg, #85FFC5 -1.53%, rgba(201, 255, 229, 0.00) 85.56%);
        color: #000;
        font-family: Roboto;
        font-size: 18px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        padding: 9px 17px;
        margin: 1.2rem 0rem 0rem;
    }
}

.FOSVisitAllowance {
    z-index: 999999 !important;

    .MuiPaper-elevation {
        width: 627px;
        margin: 15px;
        border-radius: 20px;
    }

    .MuiBackdrop-root {
        background-color: #000000d1 !important;
    }

    .MuiDialogContent-root {
        padding: 20px 0px;
        text-align: center;
    }

    h4 {
        color: #000;
        font-family: Roboto;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding: 0px 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .downloadbutton {
        border-radius: 8px;
        background: rgba(0, 101, 255, 0.05);
        border: none;
        outline: none;
        color: #000;
        font-family: Roboto;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        cursor: pointer;
        line-height: normal;
        padding: 3px 12px;
        margin-top: 20px;

        img {
            margin-right: 3px;
        }
    }

    th {
        color: #253858;
        font-family: Roboto;
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        opacity: 0.6;
        border: none;
    }

    td {
        color: #253858;
        font-family: Roboto;
        font-size: 15px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        border: none;
        padding: 0px 15px;

        p {
            color: #253858;
            font-family: Roboto;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
        }
    }
}

.NetArrearClawbackPopup {
    z-index: 999999 !important;

    .MuiButtonBase-root {
        height: auto;
    }

    .MuiPaper-elevation {
        width: 1090px;
        margin: 15px;
        margin-left: 5% !important;
        border-radius: 20px;
        max-width: 1200px;
    }

    .MuiBackdrop-root {
        background-color: #000000d1 !important;
    }

    .MuiDialogContent-root {
        padding: 20px 0px;
        text-align: center;
    }

    h4 {
        color: #000;
        font-family: Roboto;
        font-size: 18px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding: 0px 15px;
        text-align: center;
        margin-bottom: 2rem;
    }

    .BookingBreakdown {
        padding: 0px 15px;

        .Heading {
            display: flex;
            justify-content: space-between;

            h3 {
                color: #000;
                font-family: Roboto;
                font-size: 24px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
            }

            button {
                border-radius: 8px;
                background: rgba(0, 101, 255, 0.05);
                border: none;
                outline: none;
                color: #000;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                cursor: pointer;
                line-height: normal;
                padding: 3px 12px;

                img {
                    margin-right: 3px;
                }
            }
        }

        h5 {
            color: #000;
            font-family: Roboto;
            font-size: 18px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;
            text-align: left;
        }

        table {
            margin-top: 16px;

            thead {
                tr {
                    border-radius: 8px 8px 0px 0px;
                    background: #F6F6F6;

                    th {
                        color: #253858;
                        font-family: Roboto;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 700;
                        line-height: normal;
                        border-bottom: 1px solid #fff;
                        position: relative;
                    }

                    .SortingIcon {
                        position: absolute;
                        top: 8px;
                        display: inline-flex;
                        flex-direction: column;

                        img {
                            margin: 1px 0px;
                        }
                    }
                }

            }

            th {
                &:first-child {
                    border-radius: 8px 0px 0px 0px;
                }

                &:last-child {
                    border-radius: 0px 8px 0px 0px;
                }
            }

            .MuiTableCell-root {
                padding: 10px 8px;
                text-align: center;
                color: #253858;

                font-family: Roboto;
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                border-bottom: none;

            }

            .statusYes {
                background: rgba(34, 187, 51, 0.10);

            }

            .lightColor {
                color: #8090AC;
            }

            .button.MuiButton-root.MuiButton-containedPrimary {
                color: #FFF;
                font-family: Roboto;
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
            }

            .statusNo {
                background: rgba(187, 33, 36, 0.05);
            }

            .Blue {
                background: #0065FF1A;
            }
        }
    }
}

.missellDeduction {
    table {
        thead {
            tr {
                background: #BB212426 !important;
            }
        }
    }
}

.ViewIncetiveCriteriaPopup {
    width: 925px;
    background: #F5F5F5;
    padding: 15px;
    position: relative;

    .closebtn {
        position: absolute;
        right: 10px;
        cursor: pointer;
    }

    h4 {
        color: #253858;
        font-family: Roboto;
        font-size: 24px;
        font-style: normal;
        font-weight: 800;
        line-height: normal;
        width: 100%;

    }


    .MainContent {
        border-radius: 24px;
        background: #FFF;
        padding: 15px;
        margin-top: 15px;

        .Heading {
            color: #253858;
            text-align: left;
            font-family: Roboto;
            font-size: 18px;
            font-style: normal;
            font-weight: 800;
            line-height: normal;
            margin-bottom: 8px;
        }

        .diaGram {


            li {
                background-repeat: no-repeat;
                height: 75px;
                position: relative;

                .slab1 {
                    display: inline-block;
                    font-family: Roboto;
                    font-size: 16px;
                    font-weight: 700;
                    line-height: 19px;
                    color: #4F0074;
                    position: relative;
                    letter-spacing: 0em;
                    left: 35px;
                    text-align: left;
                    top: 20px;

                    p {
                        font-weight: 400;
                    }

                }

                .percentage {
                    position: absolute;
                    right: 126px;
                    font-family: Roboto;
                    color: #fff;
                    font-size: 14px;
                    font-weight: 900;
                    line-height: 16px;
                    letter-spacing: 0em;
                    text-align: left;
                    bottom: 17px;
                }

                &:first-child {
                    background-image: url(/images/TermDashboard/Layer.png);
                }

                &:nth-child(2) {
                    background-image: url(/images/TermDashboard/Layer2.png);
                }

                &:nth-child(3) {
                    background-image: url(/images/TermDashboard/Layer3.png);
                }

                &:nth-child(4) {
                    background-image: url(/images/TermDashboard/Layer4.png);
                }
            }
        }

        .howItWork {
            h2 {
                font-family: Roboto;
                font-size: 24px;
                font-weight: 800;
                line-height: 28px;
                letter-spacing: 0em;
                text-align: left;
                color: #253858;
                margin-bottom: 18px;
                margin-top: 8px;
            }

            p {
                font-family: Roboto;
                font-size: 16px;
                font-weight: 400;
                line-height: 19px;
                letter-spacing: 0em;
                text-align: left;
                color: #253858;
                width: 100%;

                b {
                    color: #A95ACE;
                }
            }


        }

        .planTypes {
            margin-top: 20px;

            .planType {
                display: flex;
                justify-content: space-between;
                align-items: center;
                border-radius: 12px 0px 0px 12px;
                background: linear-gradient(90deg, rgba(79, 0, 116, 0.2) 0%, rgba(79, 0, 116, 0) 100%);
                padding: 3px 20px;
                margin-bottom: 10px;
                height: 52px;

                .planName {
                    color: #6D1E92;
                    text-align: center;
                    font-family: Roboto;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

                .planValue {
                    border-radius: 9px;
                    background: #4F0074;
                    color: white;
                    font-weight: 600;
                    padding: 4px;
                    width: 76px;
                    text-align: center;
                    font-family: Roboto;
                    font-size: 18px;
                    font-style: normal;
                    line-height: normal;
                }


            }
        }

        .title {
            background-color: #fff;
            color: #253858;
            font-family: Roboto;
            width: 100%;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin: 15px 0px 0px;
            left: 12px;

            hr {
                margin: 0px;
                left: 4px;
                position: relative;
                top: -4px;
                background: rgba(0, 0, 0, 0.12);
                width: 75%;
                border: none;
                height: 1px;
            }
        }

        .ApeCriteria {
            color: #253858;
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;

            strong {
                color: #ED4F44;
            }

            .textgreen {
                color: #89AD25;
                font-weight: 500;
                margin-top: 8px;
            }
        }


    }

    .GreenBg {
        border-radius: 24px;
        background: #FFFFE3;
        padding: 15px 50px;
        margin: 15px 0px;

        h2 {
            color: #6B8F07;
            text-align: center;
            font-family: Roboto;
            font-size: 24px;
            font-style: normal;
            font-weight: 800;
            line-height: normal;
        }

        p {
            color: #253858;
            text-align: center;
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            margin: 14px;
            font-weight: 400;
            line-height: normal;
        }

        ul {
            li {
                border-radius: 8px;
                background: #6B8F07;
                color: #CFF36B;
                padding: 12px 35px;
                text-align: right;
                font-family: Roboto;
                font-size: 12px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
                margin-top: 8px;
                display: flex;
                justify-content: space-between;

                &:nth-child(2) {
                    background-color: #89AD25;
                    color: #fff;
                }

                &:nth-child(3) {
                    background-color: #B1D54D;
                    color: #fff;
                }

                :nth-child(4) {
                    background-color: #CFF36B;
                    color: #6B8F07;
                }

                span {
                    width: 30%;
                }
            }
        }

        .comment {
            font-family: Roboto;
            font-size: 12px;
            font-weight: 400;
            line-height: 14px;
            letter-spacing: 0em;
            text-align: center;
            color: #25385899;
            margin-left: 0px;
            margin-right: 0px;
            margin-top: 3.3rem;
        }
    }

    .RedBg {
        border-radius: 32px;
        background: #FFF0E5;
        padding: 15px 20px;
        margin: 15px 0px;
        position: relative;


        p {
            font-family: Roboto;
            font-size: 14px;
            font-weight: 600;
            line-height: 25px;
            letter-spacing: 0em;
            text-align: left;
            color: #ED4F44CC;
            margin: 5px 0px;
        }

        img {
            position: absolute;
            top: -1px;
            right: 0px;
            height: 57px;
        }

        h3 {
            color: rgba(237, 79, 68, 0.80);
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 10px;

        }

        .padding10 {
            li {
                padding: 11.5px 16px !important;
            }
        }

        .DarkRedBg {
            background: #ED4F441A;
            border-radius: 8px;

            ul {
                li {
                    font-family: Roboto;
                    font-size: 12px;
                    font-weight: 500;
                    line-height: 14px;
                    letter-spacing: 0em;
                    text-align: left;
                    color: #ED4F44;
                    display: flex;
                    justify-content: space-between;

                    &:first-child {
                        background: #ED4F44;
                        color: #fff;
                        border-radius: 8px 8px 0px 0px;
                    }
                }
            }
        }
    }

    .BlueBg {
        border-radius: 32px;
        background: #0D0041;
        padding: 15px 20px;
        margin: 15px 0px;
        position: relative;

        p {
            font-family: Roboto;
            font-size: 14px;
            font-weight: 600;
            line-height: 25px;
            letter-spacing: 0em;
            text-align: left;
            color: #FFFFFF99;
            margin: 5px 0px;
        }

        h3 {
            font-family: Roboto;
            font-size: 20px;
            font-weight: 800;
            line-height: 28px;
            letter-spacing: 0em;
            text-align: left;
            color: #fff;

        }

        .padding10 {
            li {
                padding: 11.5px 16px !important;
            }
        }

        .DarkblueBg {
            background: #FFFFFF1A;
            border-radius: 8px;

            ul {
                li {
                    font-family: Roboto;
                    font-size: 12px;
                    font-weight: 500;
                    line-height: 14px;
                    color: #fff;
                    letter-spacing: 0em;
                    text-align: left;
                    padding: 15px 16px;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    &:first-child {
                        background: #fff;
                        color: #253858;
                        border-radius: 8px 8px 0px 0px;
                    }

                    span {
                        width: 28%;
                        text-align: right;
                    }
                }
            }
        }

        .note {
            font-family: Roboto;
            font-size: 12px;
            font-weight: 400;
            line-height: 14px;
            letter-spacing: 0em;
            text-align: center;
            margin-top: 20px;
        }

        img {
            position: relative;
            top: 15px;
        }
    }
}

.Dashboard2024 {
    padding: 0px;
    background-color: #fff;

    header {
        background: linear-gradient(90deg, #E6FAFE 0.02%, #EDDDFB 100%) !important;
        box-shadow: none;
        height: 321px;
        padding: 18px 15px 15px 15px;
        position: relative;

        .Banner {
            position: relative;

            img {
                position: relative;
                right: -85px;
                bottom: 29px;
            }

            .Rank {
                display: inline-block;
                position: absolute;
                left: 135px;
                top: 24px;
                color: #B95D0D;

                p {
                    font-family: Roboto;
                    font-size: 15px;
                    font-weight: 500;
                    line-height: 18px;
                    letter-spacing: 0em;
                    text-align: center;

                }

                h2 {
                    font-family: roboto;
                    font-size: 28px;
                    font-weight: 700;
                    line-height: 35px;
                    letter-spacing: 0em;
                    text-align: center;

                }
            }
        }

        .AgentMsg {
            margin-top: 2rem;

            h3 {
                color: #253858;
                font-family: roboto;
                font-size: 20px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;
            }

            p {
                color: #253858;
                font-family: Roboto;
                font-size: 16px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
            }

            img {
                width: 25px;
                float: left;
                margin-right: 10px;
            }
        }

        .month-view {
            border-radius: 12px;
            border: 1px solid #B3D1FF;
            background: #FAFAFA;
            box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.25);
            display: flex;
            padding: 14px 5px 14px 5px;
            color: #253858;
            font-family: Roboto;
            font-size: 14px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            width: 270px;
            justify-content: space-evenly;
            text-align: center;
            margin: 2rem auto;
            align-items: center;

            .BookingMonth {
                .MuiSelect-select {
                    border: transparent;
                    opacity: 1;
                    font: normal normal 600 12px/16px Roboto;
                    color: #0065FF;
                    text-align: center;
                    padding: 0px 38px 0px 15px;
                }

                .MuiOutlinedInput-notchedOutline {
                    border: none !important;
                }

                svg {
                    color: #0065FF;
                }
            }
        }
    }

    .midLayout {
        position: relative;
        top: -148px;
        z-index: 99999;

        .fa-info-circle {
            color: #0065FF;
            margin-left: 5px;
        }

        .box1 {
            height: auto;
            padding: 10px 0px 16px 0px;
            border-radius: 12px;
            background: #FFF;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.16);
            width: 100%;
            position: relative;
            z-index: 99;


            h4 {
                color: #253858;
                text-align: center;
                font-family: Roboto;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 20px;
            }

            .gray {
                background: #F5F5F5;
                padding: 0px 15px 0px;

                .DropdownToogle {
                    background: #F5F5F5;
                    padding-bottom: 10px;
                }
            }

            .white {
                background-color: #fff;
                padding: 0px 15px;

                .DropdownToogle {
                    background: #fff;
                    padding-bottom: 10px;
                }
            }

            .DropdownToogle {
                display: flex;
                justify-content: space-between;

                li {
                    color: #8090AC;
                    font-family: Roboto;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    line-height: normal;
                    padding-left: 5px;

                    &:first-child {
                        width: 160px;
                    }

                }


            }

            .incentiveCalculation {
                display: flex;
                justify-content: space-between;
                height: 40px;

                li {
                    color: #253858;
                    font-family: Roboto;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    display: flex;
                    align-items: center;
                    line-height: normal;

                    svg {
                        color: #0065FF;
                    }

                    &:first-child {
                        width: 160px;
                    }
                }
            }

            .Caption {
                li {
                    color: #253858;
                    font-family: Roboto;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    opacity: 0.6;

                    &:first-child {
                        width: 121px;
                    }
                }
            }

            .title {
                background-color: #fff;
                color: #D9686F;
                font-family: Roboto;
                width: 100%;
                font-size: 16px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                margin: 15px 0px;

                hr {
                    margin: 0px;
                    left: 4px;
                    position: relative;
                    top: -4px;
                    width: 95%;
                }
            }

            .borderLine {
                width: 100%;
                margin: 10px 0px;
                border: 1px dashed #ddd;
            }

            .twoGrid {
                .DropdownToogle {
                    li {
                        &:first-child {
                            width: auto;
                        }
                    }
                }

                li {
                    &:first-child {
                        width: auto;
                    }
                }
            }
        }

        .box2 {
            border-radius: 12px;
            border: 1px solid #A3BBC2;
            background: #E7FAFF;
            height: auto;
            left: -20px;
            padding: 15px 0px 15px 0px;
            width: 399px;
            z-index: 0;
            top: 30px;
            position: relative;

            button {
                cursor: pointer;
            }

            .incentive-box {
                padding: 5px 0px;
                display: block;

                .Heading {
                    color: #253858;
                    font-family: Roboto;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    opacity: 0.6;


                }

                li {
                    display: inline-block;
                    width: 100%;
                    position: static !important;
                    margin: 2px 0;
                    padding: 0px 15px 0px 20px;
                    letter-spacing: 0px;
                    color: #253858;
                    font-family: Roboto;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 500;
                    text-align: right;
                    line-height: 35px;
                    height: 35px;
                    background-color: #FFF;
                    margin: 10px 0px;

                    span {
                        color: #253858;
                        float: left;
                        display: flex;
                        align-items: center;

                        svg {
                            cursor: pointer;
                            color: #0065FF;
                        }
                    }

                }

                .DropdownToogle {
                    background-color: #fff;
                    position: relative;
                    top: -10px;

                    ul {
                        li {
                            display: inline-block;
                            width: 100%;
                            margin: 2px 0;
                            padding: 0px 15px 0px 20px;
                            letter-spacing: 0px;
                            font-size: 12px;
                            font-family: Roboto;
                            font-style: normal;
                            font-weight: 500;
                            text-align: right;
                            line-height: 30px;
                            height: 30px;
                            background-color: #FFF;

                            span {
                                color: #8090AC;
                                float: left;
                                display: flex;
                                align-items: center;

                                svg {
                                    cursor: pointer;
                                }
                            }

                        }

                    }
                }

                .ml-2 {
                    margin-left: 2rem;
                }

                .active {
                    border-radius: 12px;
                    background: linear-gradient(90deg, #E6FAFE 0.02%, #EDDDFB 100%);
                    ;
                }

                .positive {
                    color: #1E8B57;
                }

                .Negative {
                    color: #B93131;
                }


            }

            h1 {
                color: #1E8B57;
                text-align: center;
                font-family: Roboto;
                display: flex;
                cursor: pointer;
                justify-content: center;
                margin-top: 17px;
                font-style: normal;
                font-weight: 700;
                align-items: center;
                line-height: normal;

                img {
                    margin-right: 10px;
                }
            }

            p {
                color: #253858;
                text-align: center;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                margin-left: 5px;
                line-height: normal;
            }

            h4 {
                color: #253858;
                text-align: center;
                font-family: Roboto;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 20px;
            }

            h5 {
                color: #253858;
                font-family: Roboto;
                font-size: 16px;
                font-style: normal;
                font-weight: 700;
                text-align: center;
                margin-top: 30px;
                line-height: normal;
            }
        }

        .box3 {
            border-radius: 12px;
            background: #FFF;
            box-shadow: 0px 0px 20px 0px rgba(0, 0, 0, 0.16);
            height: auto;
            width: 100%;
            padding: 15px;

            h4 {
                color: #253858;
                text-align: center;
                font-family: Roboto;
                font-size: 18px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                margin-bottom: 20px;
            }

            ul {
                display: flex;
                list-style-type: none;
                justify-content: space-around;
                align-items: flex-end;

                li {
                    text-align: center;

                    img {
                        width: 106px;
                    }

                    &:first-child {
                        position: relative;
                        top: 25px;

                        img {
                            width: 72px;
                        }
                    }

                    &:last-child {
                        position: relative;
                        top: 25px;

                        img {
                            width: 72px;
                        }
                    }

                    p {
                        color: #253858;
                        text-align: center;
                        font-family: Poppins;
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 20px;
                        opacity: 0.6;
                    }

                    h3 {
                        color: #253858;
                        text-align: center;
                        font-family: Poppins;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: 23px;
                    }
                }
            }

            // Incentive

            .incentive-box {
                padding: 5px 0px;
                display: block;

                .Heading {
                    color: #253858;
                    font-family: Roboto;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    opacity: 0.6;

                }

                li {
                    display: inline-block;
                    width: 100%;
                    position: static !important;
                    margin: 2px 0;
                    padding: 10px;
                    letter-spacing: 0px;
                    color: #253858;
                    font-family: Roboto;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    text-align: right;
                    line-height: normal;

                    span {

                        float: left;
                    }

                }

                .ml-2 {
                    margin-left: 2rem;
                }

                .active {
                    border-radius: 12px;
                    background: linear-gradient(90deg, #E6FAFE 0.02%, #EDDDFB 100%);
                    ;
                }
            }
        }
    }

    hr {
        background: rgba(0, 0, 0, 0.12);
        height: 1px;
        border: none;
        margin-top: 40px;
    }

    .BookingBreakdown {
        border-radius: 12px;
        background: #FFF;
        box-shadow: 12px 12px 12px 12px rgba(217, 45, 214, 0.16);
        // box-shadow: 0px 3px 12px 0px rgba(135, 135, 135, 0.16);
        margin-top: 2.8rem;
        padding: 15px;

        .Heading {
            display: flex;
            justify-content: space-between;

            h3 {
                color: #000;
                font-family: Roboto;
                font-size: 24px;
                font-style: normal;
                font-weight: 700;
                line-height: normal;
            }

            button {
                border-radius: 8px;
                background: rgba(0, 101, 255, 0.05);
                border: none;
                outline: none;
                color: #000;
                font-family: Roboto;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                cursor: pointer;
                line-height: normal;
                padding: 3px 12px;

                img {
                    margin-right: 3px;
                }
            }
        }

        table {
            margin-top: 16px;

            thead {
                tr {
                    border-radius: 8px 8px 0px 0px;
                    background: #F6F6F6;

                    th {
                        font-weight: 700 !important;
                        border-bottom: 1px solid #fff;
                        position: relative;
                    }
                }

                .SortingIcon {
                    position: absolute;
                    top: 8px;
                    display: inline-flex;
                    flex-direction: column;

                    img {
                        margin: 1px 0px;
                    }
                }
            }

            .MuiTableCell-root {
                padding: 10px 8px;
                text-align: center;
                color: #253858;
                font-family: Roboto;
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                border-bottom: 1px solid #fff;

                &:last-child {
                    width: 160px;
                }
            }

            .statusYes {
                background: rgba(34, 187, 51, 0.10);
            }

            .statusNo {
                background: rgba(187, 33, 36, 0.05);
            }


        }
    }

}

@media screen and (min-width: 320px) and (max-width: 900px) {
    .box2 {
        position: static !important;
        width: 100% !important;
    }

    .Dashboard2024 .midLayout {
        top: -39px !important;
    }

    header {
        .banner {
            right: 10px;
            bottom: 0px;
            width: 117px;
        }

        .month-view {
            margin: 2rem 0px;
            float: left;
            font-size: 13px;
        }
    }

    .ViewIncetiveCriteriaPopup {
        width: 100% !important;

        .GreenBg {
            h2 {
                font-size: 18px;
            }

            ul {
                li {
                    text-align: left !important;
                }
            }
        }
    }

    .BookingBreakdown {
        .Heading {
            h3 {
                font-size: 18px !important;
                position: sticky;
                z-index: 99999;
            }

            button {
                font-size: 13px;

                img {
                    width: 24px;
                }
            }
        }
    }
}

.TablePagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 18px;

    button {
        min-width: 19px;
        font-size: 11px;
        margin: 0px 10px;
        color: #252538;
        background-color: #f6f6f6;
        height: 22px;

    }

    .activeBtn {
        background-color: #0065ff;
        padding: 0px;
        color: #fff;
        border-radius: 80%;
        width: 20px;
        height: 20px;
        justify-content: center;
    }
}

.loader {
    margin: auto;
    width: 40px;
    aspect-ratio: 1;
    --c: linear-gradient(#000 0 0);
    --r1: radial-gradient(farthest-side at bottom, #000 93%, #0000);
    --r2: radial-gradient(farthest-side at top, #000 93%, #0000);
    background:
        var(--c), var(--r1), var(--r2),
        var(--c), var(--r1), var(--r2),
        var(--c), var(--r1), var(--r2);
    background-repeat: no-repeat;
    animation: l2 1s infinite alternate;
}

@keyframes l2 {

    0%,
    25% {
        background-size: 8px 0, 8px 4px, 8px 4px, 8px 0, 8px 4px, 8px 4px, 8px 0, 8px 4px, 8px 4px;
        background-position: 0 50%, 0 calc(50% - 2px), 0 calc(50% + 2px), 50% 50%, 50% calc(50% - 2px), 50% calc(50% + 2px), 100% 50%, 100% calc(50% - 2px), 100% calc(50% + 2px);
    }

    50% {
        background-size: 8px 100%, 8px 4px, 8px 4px, 8px 0, 8px 4px, 8px 4px, 8px 0, 8px 4px, 8px 4px;
        background-position: 0 50%, 0 calc(0% - 2px), 0 calc(100% + 2px), 50% 50%, 50% calc(50% - 2px), 50% calc(50% + 2px), 100% 50%, 100% calc(50% - 2px), 100% calc(50% + 2px);
    }

    75% {
        background-size: 8px 100%, 8px 4px, 8px 4px, 8px 100%, 8px 4px, 8px 4px, 8px 0, 8px 4px, 8px 4px;
        background-position: 0 50%, 0 calc(0% - 2px), 0 calc(100% + 2px), 50% 50%, 50% calc(0% - 2px), 50% calc(100% + 2px), 100% 50%, 100% calc(50% - 2px), 100% calc(50% + 2px);
    }

    95%,
    100% {
        background-size: 8px 100%, 8px 4px, 8px 4px, 8px 100%, 8px 4px, 8px 4px, 8px 100%, 8px 4px, 8px 4px;
        background-position: 0 50%, 0 calc(0% - 2px), 0 calc(100% + 2px), 50% 50%, 50% calc(0% - 2px), 50% calc(100% + 2px), 100% 50%, 100% calc(0% - 2px), 100% calc(100% + 2px);
    }
}