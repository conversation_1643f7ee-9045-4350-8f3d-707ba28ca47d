/* eslint-disable react/no-multi-comp */
/* eslint-disable react/display-name */
import React, { forwardRef, useState } from "react";
import { NavLink as RouterLink } from "react-router-dom";
import clsx from "clsx";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/styles";
import { List, ListItem, Button, colors } from "@material-ui/core";
import ExpandLess from "@material-ui/icons/ExpandLess";
import ExpandMore from "@material-ui/icons/ExpandMore";
import StarBorder from "@material-ui/icons/StarBorder";
import ListItemIcon from "@material-ui/core/ListItemIcon";
import ListItemText from "@material-ui/core/ListItemText";
import Collapse from "@material-ui/core/Collapse";
import InboxIcon from "@material-ui/icons/MoveToInbox";
import DraftsIcon from "@material-ui/icons/Drafts";
import SendIcon from "@material-ui/icons/Send";
import { AuxParser } from "./../../../../../../hoc";
import { red } from "@material-ui/core/colors";
const useStyles = makeStyles((theme) => ({
  root: {},
  item: {
    display: "flex",
    paddingTop: 0,
    paddingBottom: 0,
  },
  button: {
    color: "#253858",
    padding: "10px 15px",
    justifyContent: "flex-start",
    textTransform: "none",
    letterSpacing: 0,
    width: "100%",
    fontWeight: theme.typography.fontWeightMedium,
    fontSize: 16,
    marginBottom: 10,
    maxWidth: 180,
    borderRadius: "0px 12px 12px 0px",
    "&:hover": {
      color: "#253858",
    },
  },
  icon: {
    color: theme.palette.primary.main,
    width: 24,
    height: 24,
    display: "flex",
    alignItems: "center",
    marginRight: theme.spacing(2),
  },
  active: {
    color: theme.palette.white,
    fontWeight: theme.typography.fontWeightMedium,
    background: "#0052cc",
    borderRadius: "0px 12px 12px 0px",
    "& $icon": {
      color: theme.palette.white,
    },
    "&:hover": {
      background: "#0052cc",color: "#fff"
    },
  },
  nestedList: {
    background: "#fff",
    padding: theme.spacing(1, 0),
  },
  nestedItem: {
    paddingLeft: 0,
    paddingTop: 0,
    paddingBottom: 0,
    "& $active": {
      color: "#fff !important",
      "& $icon": {
        color: theme.palette.white,
      },
    },
    "& $button": {
      fontSize: 13,
      //paddingLeft: theme.spacing(4),
      color: theme.palette.primary.main,
      "& $icon": {
        color: theme.palette.primary.main,
      },
    },
  },
  logo: {
    maxWidth: "122px",
  },
}));

const CustomRouterLink = forwardRef((props, ref) => (
  <div ref={ref} style={{ flexGrow: 1 }}>
    <RouterLink {...props} />
  </div>
));

const SidebarNav = (props) => {
  const { pages, className, ...rest } = props;

  const classes = useStyles();
  const [open, setOpen] = React.useState(false);

  const handleClick = () => {
    setOpen(!open);
  };
  return (
    <List {...rest} className={clsx(classes.root, className)}>
      {pages
        .filter((itemPage) => itemPage.show)
        .map((page, i) => (
          <AuxParser key={i}>
            <ListItem
              className={classes.item}
              disableGutters
              onClick={page.isSubPage ? handleClick : null}
            >
              {page.isSubPage ? (
                <Button className={classes.button}>
                  <div className={classes.icon}>{page.icon}</div>
                  {page.title}
                  {page.isSubPage ? (
                    open ? (
                      <ExpandLess />
                    ) : (
                      <ExpandMore />
                    )
                  ) : null}
                </Button>
              ) : (
                <Button
                  activeClassName={classes.active}
                  className={classes.button}
                  component={CustomRouterLink}
                  to={page.href}
                >
                  <div className={classes.icon}>{page.icon}</div>
                  {page.title}
                </Button>
              )}
            </ListItem>
            {page.isSubPage ? (
              <Collapse
                key={`${i}-collapse`}
                in={open}
                timeout="auto"
                unmountOnExit
              >
                <List
                  component="div"
                  disablePadding
                  className={classes.nestedList}
                >
                  {page.subpages &&
                    page.subpages.map((subpage, iSubPage) => (
                      <ListItem
                        className={classes.nestedItem}
                        disableGutters
                        key={`${i}-${iSubPage}-subpage`}
                      >
                        <Button
                          activeClassName={classes.active}
                          className={classes.button}
                          component={CustomRouterLink}
                          to={subpage.href}
                        >
                          <div className={classes.icon}>{subpage.icon}</div>
                          {subpage.title}
                        </Button>
                      </ListItem>
                    ))}
                </List>
              </Collapse>
            ) : null}
          </AuxParser>
        ))}
    </List>
  );
};

SidebarNav.propTypes = {
  className: PropTypes.string,
  pages: PropTypes.array.isRequired,
};

export default SidebarNav;
