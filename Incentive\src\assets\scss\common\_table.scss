@import '../variables/variables';

.table-wrapper{
    //border-radius: 5px 5px 0 0;
    box-shadow: 0px 0px 3px #00000029;
    border-radius: 4px;
    display: block;
    @media screen and (max-width: 960px){
        display: none;
    }
}
.table{
    background-color: $white;
    .table-content-heading{
        background-color: transparent;
        // border-bottom: 2px solid $grey-lighter;
        .table-heading{
            text-align: left;
            padding: 16px 40px;
            // text-transform: uppercase;
            color: #000;
            font-weight: normal;
            border-bottom:none;
            text-align: center;
            font-size: 12px;

        }
    }
    .table-content-body{
        background: $white;
        & tr:nth-of-type(odd){
            background-color: #FAFAFA;
        }
        .table-cell{
            text-align: left;
            padding: 9px 40px;
            font-weight: 400;
            border-bottom:none;
            text-align: center;
            font-size: 12px;
            color: $grey-dark;
            a{
                h6{
                    color: $secondary-color;
                    // font-weight:  bold;
                }
            }
            p{
                font-size: 12px;
                color: $grey-dark;
                svg{
                    width: 13px;
                    height: 13px;
                    margin-right: 5px;
                }
            }
        }
    }
}

