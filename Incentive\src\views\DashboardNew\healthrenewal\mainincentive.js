import React, { useEffect, useState, Fragment } from "react";
import * as services from "../.././../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
import moment, { min } from "moment";
import {
  TableContainer,
  Table,
  TableRow,
  TableCell,
  TableBody,
  Grid,

} from "@material-ui/core";

import {
  Box,
  Tabs, Tab
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));

//   const WrapperTooltip = withStyles(theme => ({
//     tooltip: {
//       backgroundColor: "#fff",
//       padding: "10px",
//       boxShadow: " 0px 6px 16px #3469CB29",
//       fontSize: 11,
//       borderRadius: "5px",
//       maxWidth: "300px",
//       "& ul": {
//         "& li": {
//           listStyle: "none",
//           color: "#808080",
//         },
//       },
//     },
//     arrow: {
//       color: "#fff",
//     }
//   }))(Tooltip);

const MainIncentive = (props) => {
  const classes = useStyles();
  const { userId, date, mainIncentive } = props;
  let mIncentive = JSON.parse(mainIncentive)


  


    
  
const func = (mIncentive) =>{
  let res = []

  

  for(let index  = 0; index < Object.keys(mIncentive).length; index++){
    res.push(mIncentive[0].TargetReq)

  }

  // mIncentive.map((item, index) =>{

  //   res.push(mIncentive[index].TargetReq)

  // })

  return res
}

console.log(mIncentive[0].SameMonthIncentive)

  
  

  return ( mIncentive && mIncentive.length != 0 &&


    <div className={classes.root}>
  
        <div className="HealthRenewalCommanView">
          <h6>
            Main Incentive
          </h6>
          <table>
            <thead>
              <tr>
                <th>Category</th>
                <th>Main Bookings</th>
                <th>Add On Bookings</th>

              </tr>
            </thead>
            <tbody>
              
              <tr>
                <td>Leads Assigned</td>
                <td>#{mIncentive[0].LeadsAssigned}</td>
                <td>#{mIncentive[1].LeadsAssigned}</td>
                
                </tr>
               
              <tr>
                <td>Qualifier %age</td>
                <td>{mIncentive[0].QualifierPercentage}%</td>
                <td>{mIncentive[1].QualifierPercentage}%</td>
              </tr>
              <tr>
                <td>Target Bookings Required</td>
                <td>#{mIncentive[0].TargetReq}</td>
                <td>#{mIncentive[1].TargetReq}</td>
              </tr>
              <tr>
                <td>Bookings Achieved</td>
                <td>#{mIncentive[0].BookingAchieved}</td>
                <td>#{mIncentive[1].BookingAchieved}</td>
              </tr>
              <tr className="lightgreen">
                <td>Issued Bookings Achieved</td>
                <td>#{mIncentive[0].IssuedBooking}</td>
                <td>#{mIncentive[1].IssuedBooking}</td>
              </tr>
              
              <tr className="lightgreen">
                <td>Target Achieved</td>
                <td className="green">{mIncentive[0].TargetAchieved}%</td>
                <td>{mIncentive[1].TargetAchieved}%</td>
              </tr>
              <tr>
                <td>Incentive</td>
                <td className="green"><i class="fa fa-rupee"></i> {mIncentive[0].Incentive.toLocaleString('en-IN')}</td>
                <td className="green"><i class="fa fa-rupee"></i> {mIncentive[1].Incentive.toLocaleString('en-IN')}</td>
              </tr>
              <tr>
                <td>Same month incentive</td>
                <td>{mIncentive[0].SameMonthIncentive && <i class="fa fa-rupee"></i>}{mIncentive[0].SameMonthIncentive == null? "": mIncentive[0].SameMonthIncentive.toLocaleString('en-IN')}</td>
                <td>{mIncentive[1].SameMonthIncentive && <i class="fa fa-rupee"></i>}{mIncentive[1].SameMonthIncentive == null? "": mIncentive[1].SameMonthIncentive.toLocaleString('en-IN')}</td>
              </tr> 

            </tbody>

          </table>




        </div>
      </div>
   

  );

}

export default MainIncentive