import React, { useEffect, useState } from "react";

const CurrentRank = (props) => {
  const { agentDetail } = props;

  return (
    <>
    <div className="currentRank">
      <ul>
        <li>
          <span className='label'>Rank</span><span className='value'>#{agentDetail.AgentRank || "-"} </span>
        </li>
        {(agentDetail.ProductId != '115')?
        <li>
          <span className='label'>Slab</span><span className='value'>{agentDetail.Currentlevel || "-"}</span>
        </li>
        : ''}
      </ul>
      <div className='background-caricrature'></div>
    </div>
    </>
  );
};

export default CurrentRank;
