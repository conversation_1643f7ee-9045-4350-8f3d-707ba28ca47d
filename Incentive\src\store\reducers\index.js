import { combineReducers } from 'redux';
import authReducer from './Auth';
import commonReducer from './Common';
import * as actionTypes from './../ActionsTypes';

const appReducer = combineReducers({
  auth: authReducer,
  common: commonReducer
});

// export default rootReducer;
export default (state, action) => {
  if (action.type === actionTypes.AUTH_LOGOUT) {
    state = undefined;
  }
  return appReducer(state, action);
};