import React, { useEffect, useState, Fragment } from "react";
import moment from "moment";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/Criteria";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button
} from "@material-ui/core";
import Dialog from '@material-ui/core/Dialog';

import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));




const RewardCalculator = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [agentDetail, setAgentDetail] = useState({});
  const [pointSlabMaster, setPointSlabMaster] = useState([]);
  const [rewardBreakUp, setRewardBreakUp] = useState([]);
  const [rewardType, setRewardType] = useState('cash');
  const [minAPE, setMinAPE] = useState(0);
  const [minBKGS, setMinBKGS] = useState(0);
  const [pointSlabId, setPointSlabId] = useState(0);
  const [rewardId, setRewardId] = useState(0);
  const [open, setOpen] = React.useState(false);

  //const [highlights, setHighlights] = useState([]);
  //const [productId, setProductId] = useState([]);
  //const [showCriteria, setShowCriteria] = React.useState(false);

  // const handleClickOpen = (value) => {
  //   setShowCriteria(value);
  // };

  const getAgentDetail = () => {
    setAgentDetail({});
    //setHighlights([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Jag/GetAgentDetails/${userId}/${date}`)
      .then(response => {
        if (response && response !== "[]") {
          setAgentDetail(response);

          setMinAPE(response.MinAPE * 100000);
          setMinBKGS(parseInt(response.MinBKGS));
          setPointSlabId(response.pointSlabId);
          setRewardId(response.rewardId);
          setRewardType(response.rewardType ? response.rewardType : 'cash')
          //getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }
  const getJAGPointSlabMaster = () => {
    setPointSlabMaster([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Jag/GetJAGPointSlabMaster/${userId}`)
      .then(response => {

        if (response && response !== "[]" && response.Status === true) {
          let slab = JSON.parse(response.Response)
          slab = JSON.parse(slab[0])
          setPointSlabMaster(slab);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const getRewardCalculatorBreakUp = () => {
    setRewardBreakUp([]);
    if (!rewardId) {
      return;
    }

    services
      .API_GET(`Jag/RewardCalculatorBreakUp/${rewardId}`)
      .then(response => {

        if (response && response !== "[]" && response.Status === true) {
          let list = JSON.parse(response.Response)
          list = JSON.parse(list[0])
          setRewardBreakUp(list);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const calculateReward = () => {
    setMinAPE(0);
    setMinBKGS(0);

    if (!userId) {
      return;
    }

    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${userId}/0/${response.ProductId}/${date}?PageName=JAG&EventName=RewardCalculator`;

    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });


    services
      .API_GET(`Jag/RewardCalculator/${userId}/${pointSlabId}/${rewardType}`)
      .then(response => {

        if (response && response !== "[]" && response.Status === true) {

          let list = JSON.parse(response.Response)
          let res = JSON.parse(list[0])
          //let res = JSON.parse(response.Response)
          //[{"MINAPE":172.85,"MINBKGS":2066.62}]
          setMinAPE(res[0].MINAPE * 100000);
          setMinBKGS(parseInt(res[0].MINBKGS));
          setRewardId(res[0].RewardId);


        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const showTotalPrevAPE = (agentDetail) => {
    if (agentDetail == null) {
      return '0';
    }
    else {
      let TotalAPE_PrevYear = (agentDetail.IssuedAPE_CurrYear ? agentDetail.IssuedAPE_CurrYear : 0)
      return (TotalAPE_PrevYear / 100000).toFixed(2) + " Lakhs"
    }
  }
  const showMinAPE = () => {

    if (minAPE > 0)
      return (minAPE / 100000).toFixed(2) + " Lakhs"
    else
      return "-"
  }

  const showDiffAPE = (agentDetail) => {

    if (minAPE > 0) {
      let totalape = minAPE - (agentDetail.IssuedAPE_CurrYear ? agentDetail.IssuedAPE_CurrYear : 0)
      return (totalape / 100000).toFixed(2) + " Lakhs"
    }
    else
      return "-"
  }
  const showDiffBKGS = (agentDetail) => {
    if (minBKGS > 0) {
      let total = minBKGS - (agentDetail.IssuedBKG_CurrYear ? agentDetail.IssuedBKG_CurrYear : 0)
      return total;
    }
    else
      return "-"
  }

  const handlePointSlabValue = (e) => {
    setPointSlabId(parseInt(e.target.value));
  }

  const openbreakup = (e) => {
    if (!rewardId) {
      return;
    }
    setOpen(true);
    getRewardCalculatorBreakUp();

  }

  const handleClose = (e) => {
    setOpen(false);
  }

  useEffect(() => {
    getAgentDetail();
    getJAGPointSlabMaster();
  }, [props]
  );

  if (!agentDetail) {
    return null;
  }

  // if (agentDetail && agentDetail.BU && agentDetail.BU.indexOf('Invest') > -1) {
  //   return null;
  // }
  var startDate = moment(new Date());
  var endDate = moment(new Date('03/31/2021'));

  var diffMonths = endDate.diff(startDate, 'months');



  return (
    <>
      <div className="pading-zero" className={classes.root}>

        <div className="jag-highlights">
          <h6>
            Rewards Calculator  <i className="fa fa-chevron-right" aria-hidden="true"></i>
            <span>Calculate rewards</span>
          </h6>
          <div className="calculator-box">
            <Grid container spacing={12}>

              <Grid item sm={12} md={4} xs={12}>
                <div className="box">
                  <p className="formatselector-buttons">
                    <span className={rewardType === 'cash' ? "active" : ""} onClick={() => setRewardType('cash')}>Cash Rewards</span>
                    <span className={rewardType === 'lottery' ? "active" : ""} onClick={() => setRewardType('lottery')}>Lottery Tickets</span>
                  </p>
                  <div className="selectoptions">
                    <select onChange={handlePointSlabValue} value={pointSlabId}>
                      <option value={0}>Select Value</option>
                      {

                        pointSlabMaster ? pointSlabMaster.map((item) =>
                          <option value={item.ID}>{rewardType === 'cash' ? item.CashReward : item.LotteryTickets}</option>)
                          : null}
                    </select>
                    <button onClick={() => calculateReward()}>Calculate</button>
                  </div>
                </div>
              </Grid>
              <Grid item sm={12} md={8} xs={12}>
                <Grid container spacing={12}>
                  <Grid item sm={12} md={6} xs={12}>

                    <div className="box">
                      <Grid container spacing={12}>
                        <h4>Current Standings FY 20-21</h4>
                        {["Motor", "Health Renewal", "Motor Renewal"].indexOf(agentDetail.BU) > -1 ? null : <Grid item sm={6} md={6} xs={6}>
                          <span>Issued APE</span>
                          <h3><i className="fa fa-inr"></i> {showTotalPrevAPE(agentDetail)}</h3>
                        </Grid>}
                        {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null :
                          <Grid item sm={6} md={6} xs={6}>
                            <span>Issued Bookings</span>
                            <h3>{agentDetail.IssuedBKG_CurrYear}</h3>
                          </Grid>}
                      </Grid>

                    </div>
                  </Grid>
                  {/* <Grid item sm={12} md={4} xs={12}>

                    <div className="box">
                      <Grid container spacing={12}>
                        <h4>REQ. Target</h4>
                        <Grid item sm={6} md={6} xs={6}>
                          <span>Minimum APE</span>
                          <h3><i className="fa fa-inr"></i> {showMinAPE()}</h3>
                        </Grid>
                        <Grid item sm={6} md={6} xs={6}>
                          <span>Minimum Bookings</span>
                          <h3>{minBKGS > 0 ? minBKGS : '-'}</h3>
                        </Grid>
                      </Grid>
                    </div>
                  </Grid> */}
                  <Grid item sm={12} md={6} xs={12}>
                    <div className="box noborder">
                      <Grid container spacing={12}>
                        <h4>To do in next {diffMonths + 1} Months</h4>
                        {["Motor", "Health Renewal", "Motor Renewal"].indexOf(agentDetail.BU) > -1 ? null : <Grid item sm={6} md={6} xs={6}>
                          <span>Issued APE (min.)</span>
                          <h3><i className="fa fa-inr"></i> {showDiffAPE(agentDetail)}</h3>
                        </Grid>}

                        <Grid item sm={6} md={6} xs={6}>
                          {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null : <span>Issued Bookings (min.)</span>}
                          <h3>

                            {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null : showDiffBKGS(agentDetail)}

                            <i className="fa fa-plus-circle rewardbreakup" onClick={() => openbreakup()}></i></h3>
                          <Dialog onClose={handleClose} aria-labelledby="customized-dialog-title" open={open}
                            fullWidth={'xs'}
                            maxWidth={'xs'}
                          >
                            <DialogTitle id="customized-dialog-title" onClose={handleClose}>
                              <b><i className="breakupheader">Monthly break up of required sourcing</i></b>
                            </DialogTitle>
                            <DialogContent dividers>
                              <TableContainer >
                                <Table className={classes.table} size="small">
                                  <TableHead>
                                    <TableRow>
                                      <TableCell>MONTH</TableCell>
                                      {["Motor", "Health Renewal", "Motor Renewal"].indexOf(agentDetail.BU) > -1 ? null : <TableCell align="right">APE</TableCell>}
                                      {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null : <TableCell align="right">BOOKING</TableCell>}
                                    </TableRow>
                                  </TableHead>
                                  <TableBody>


                                    {rewardBreakUp.map((row) => (
                                      <TableRow key={row.ID}>
                                        <TableCell >{moment(row.FORMONTH).format("MMM")}</TableCell>
                                        {["Motor", "Health Renewal", "Motor Renewal"].indexOf(agentDetail.BU) > -1 ? null : <TableCell align="right"><i className="fa fa-inr"></i> {row.APE} Lakhs</TableCell>}

                                        {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null : <TableCell align="right">{row.BKGS}</TableCell>}
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </TableContainer>





                            </DialogContent>
                            <DialogActions>
                              <Button autoFocus onClick={handleClose} color="primary"> Close </Button>
                            </DialogActions>
                          </Dialog>
                        </Grid>
                      </Grid>
                    </div>
                  </Grid>
                </Grid>
              </Grid>



            </Grid>
          </div>
        </div>
      </div>

    </>
  );
};

export default RewardCalculator;
