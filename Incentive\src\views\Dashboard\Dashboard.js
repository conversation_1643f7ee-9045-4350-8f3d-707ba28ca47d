import React, { useEffect, useState, Fragment } from "react";
import moment from "moment";
import { makeStyles } from "@material-ui/core/styles";
import LoaderComponent from "../../components/Loader";
import {
  Card,
  CardContent,
  Grid,
} from "@material-ui/core";

import Ranking from "./Ranking";
import LeaderBoard from "./LeaderBoard";
import IncentiveCalculations from "./IncentiveCalculations";
import IncentiveCalculator from "./IncentiveCalculator";
import BookingTable from "./bookingTable";
import PayoutTable from "./PayoutTable";
import { useParams } from "react-router";
import STORAGE from './../../store/storage'
import * as services from "./../../services";
import Slider from "react-slick";
import CriteriaTerm from "./Critaria/CriteriaTerm";
import LotteryBanner from "../jag2021/LotteryBanner";
import RaiseTicket from "./RaiseTicket";
//import * as actions from './store/actions';

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
}));

const getLastMonths = (months) => {
  let monthNames = moment.monthsShort()
  let today = new Date();
  let d; let mth;
  let monthsList = [];
  for (let i = months; i >= 3; i -= 1) {
    d = new Date(today.getFullYear(), today.getMonth() - i, 1);
    mth = d.getMonth() + 1
    mth = mth < 10 ? "0" + mth : mth;
    monthsList.push({
      value: "01-" + mth + "-" + d.getFullYear(),
      name: monthNames[d.getMonth()] + " " + d.getFullYear()
    });
  }
  return monthsList;
};

export default function Dashboard(props) {

  const urlParams = useParams();
  const classes = useStyles();
  //const [isLoading, setIsLoading] = useState(false);
  const [date, setDate] = useState(moment().subtract(3, 'months').startOf('month').format("DD-MM-YYYY"));
  const [months] = useState(getLastMonths(3));
  const [userId, setUserId] = useState(null);
  const [productId, setProductId] = useState(null);
  const [SuperGroupID, setSuperGroupID] = useState(null);
  const [user, setUser] = useState(null);
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [monthList, setMonthList] = useState([moment().format('MM'),
  moment().subtract(1, 'months').format('MM'),
  moment().subtract(2, 'months').format('MM')]);
  const [displayMonth, setDisplayMonth] = useState(moment().subtract(3, 'months').format('MM'));


  //const [userDetail, setUserDetail] = useState({});
  useEffect(() => {
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
    //const tokenPath = window.location.pathname.split('/');
    //if (tokenPath.length >= 3) {
    //Set Token Using URL parameter
    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);

    services
      .API_GET(`Incentive/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {
          if (response.status && response.status == 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            //setUserDetail(response);
            //props.login(response);
            //console.log("Dashboard================", userDetail);
            setUserId(response.AgentId);
            //debugger
            setSuperGroupID(response.SuperGroupID);
            setProductId(response.ProductId);
            setUser(response);
            console.log(response)
            localStorage.setItem('user', JSON.stringify(response));



            //getCriteria(response)

            let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=Pageload`;
            if (Source) {
              url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=` + Source
            }

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });


    //}
  }, []);

  const getCriteria = (user) => {
    setCriteriaList([]);
    setCriteriaListHtml([]);
    setCriteriaSlabs([]);
    services
      .API_GET(`Incentive/GetIncentiveCriteria/${user.SuperGroupID}/${user.ProductId}/${date}`)
      .then(response => {
        //debugger
        console.log(response && response != "[]");
        if (response && response != "[]") {
          setCriteriaList(response.Insights || null);
          setCriteriaListHtml(response.InsightsHtml || null);
          setCriteriaSlabs(response.Slabs ? _.orderBy(response.Slabs, ['IncentiveLevel'], ['desc']) : null);
        }
        //setIsLoading(false);
      })
      .catch((err) => {
        //setIsLoading(false);
        console.log("Error", err);
      });
  }

  const handleChange = (event, newValue) => {
    setDate(event.target.value || moment().startOf('month').format("DD-MM-YYYY"));

    const dt = event.target.value;
    let urlToken = urlParams.Token;
    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=MonthChange`;

    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });
  };

  const settings = {
    dots: true,
    infinite: true,
    arrows: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1
  };

  const loadCriteria = (productId) => {
    if (productId == 7) {
      return <CriteriaTerm productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaTerm>
    }
    return <CriteriaTerm productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaTerm>
  }

  return (
    <>
      <LotteryBanner agentDetail={user} />
      <div className={classes.root} className="wrapper">
        {/*isLoading ? <LoaderComponent open={true} /> : null*/}
        <Grid container spacing={0}>
          <Grid item sm={12} md={12} xs={12} className="paddingTopBottom">
            <span className="month-view">
              <i className="fa fa-calendar" aria-hidden="true"></i>
              <select onChange={handleChange} id="month" defaultValue={date} >
                <option disabled="disabled" value="">Select Month</option>
                {months.map((item, index) =>
                  <option
                    key={index}
                    value={item.value}
                  >
                    {item.name}
                  </option>

                )}
              </select>
            </span>
          </Grid>
          {((!(productId == 7 || productId == null)) && monthList.indexOf(moment(date, 'DD-MM-YYYY').format('MM')) > -1) && <div className='maintenance'>
            <img style={{ width: "150%" }} src="/images/maintenance.PNG" />

          </div>}
          {(productId == 7 || moment(date, 'DD-MM-YYYY').format('MM') == displayMonth) &&
            <Grid container item xs={12} md={7} spacing={3}>
              <Grid item sm={12} md={12} xs={12}>
                <Ranking userId={userId} date={date} />
              </Grid>
              <Grid item sm={12} md={6} xs={12}>
                <LeaderBoard userId={userId} date={date} />
              </Grid>
              <Grid item sm={12} md={6} xs={12}>
                <IncentiveCalculations userId={userId} date={date} />
              </Grid>
            </Grid>}



          {/* Incentive Calculator */}
          {(productId == 7 || moment(date, 'DD-MM-YYYY').format('MM') == displayMonth) &&
            <Grid item md={4} xs={12}>
              <IncentiveCalculator userId={userId} date={date} user={user} />
            </Grid>}

          {/* Payout */}
          {(productId == 7 || moment(date, 'DD-MM-YYYY').format('MM') == displayMonth) &&
            <>
              <Grid item md={7} xs={12}>

                {loadCriteria(productId)}
              </Grid>
              <Grid item md={4} xs={12}>
                <PayoutTable userId={userId} date={date} />
              </Grid>
              <Grid item md={12} xs={12}>
                <BookingTable userId={userId} date={date} />
              </Grid></>
          }
        </Grid>
      </div>
      <RaiseTicket/>

    </>
  );
}
