.projected-growth:before, .projected-growth:after{position: absolute;content: '';top: -30px;
left: -12px;margin: auto;background: #11243E;width: 24px;height: 24px;border-radius: 50%;
    @media (max-width:1024px) and (min-width:320px){
    top: 25px!important;left: -28px
    }  
}
.projected-growth{max-width: 310px;width: 100%;color: #38374B;position: relative;
padding: 15px 50px 0px;border-left: 2px dashed #dbdbdb99;
    @media (max-width:1024px) and (min-width:320px){
    padding: 30px 0px 0px;max-width: 100%;border: 0px;
    ul{display: flex;justify-content: space-around;flex-wrap: wrap;width: 100%;
        @media (max-width:539px) and (min-width:320px){
        justify-content: space-between;
        }    
    }
    ul li:last-child{text-align: center;width: 100%;}
    }  
}
.projected-growth ul li label{font-weight: 400;font-size: 12px;line-height: 24px;color: #38374B;
display: block;}
.projected-growth ul li h4{font-weight: 700;font-size: 20px;line-height: 23px;background: linear-gradient(90.11deg, #7095FA 2.36%, #AC9FFF 99.06%);
-webkit-background-clip: text;-webkit-text-fill-color: transparent;background-clip: text;
text-fill-color: transparent;margin: 8px 0px 35px;white-space: nowrap;
    @media (max-width:1024px) and (min-width:320px){
    margin-bottom: 18px;
    }
}
.projected-growth:after{top: auto;bottom: -30px;
    @media (max-width:1024px) and (min-width:320px){
    right: -28px;left: auto;bottom: auto;
    }  
}