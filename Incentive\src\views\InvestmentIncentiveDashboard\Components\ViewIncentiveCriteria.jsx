import { Grid } from '@mui/material';
import React, { useContext, useEffect, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import { DashboardContext } from '../Context/Context';
import { GetCommonData } from '../../../common/CommonAction';
import { convertToIndianNumbering, getUserDetails, roundOffToTwo, rupeeConverter } from "../../Dashboard2024/Utility/Utility";

const ViewIncentiveCriteria = () => {

    const popupHandler = useContext(DashboardContext);
    const [incentiveCriteria, setIncentiveCriteria] = useState(popupHandler.dashboardData['IncentiveCriteria']);

    let convertToIndianNumber = convertToIndianNumbering();

    let rupee = rupeeConverter();

    useEffect(() => {
        if (!incentiveCriteria) {

            let body = {
                eCode: popupHandler.agentDetails && popupHandler.agentDetails.EmpId,
                monthYear: popupHandler.monthChosen,
                userid: getUserDetails('UserId'),
                bu: "investment"
            }
            // let body = {
            //     eCode: "PW00000" || popupHandler.agentDetails.EmpId,
            //     monthYear: "FEB2025" || popupHandler.monthChosen,
            //     userid: 91072,
            //     bu: "investment"
            // }
            let data = { EndPoint: "IncentiveCriteriaInvestment", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "investment", body: JSON.stringify(body) };
            GetCommonData(
                "POST", data
                , (errorStatus, data) => {
                    if (!errorStatus) {

                        popupHandler.setDashboardData(data, 'IncentiveCriteria');
                        setIncentiveCriteria(data);
                    }
                    else {
                        popupHandler.setDashboardData(null, 'IncentiveCriteria');
                        setIncentiveCriteria(null)
                    }
                })
        }

    }, [])

    const handleClick = () => {
        popupHandler.closeFunction();
    }

    return (

        <div className="ViewIncetiveCriteriaPopup">
            <CloseIcon onClick={handleClick} className="closebtn" />
            <h4>Incentive Criteria </h4>

            <div className="MainContent">
                <Grid container spacing={3}>
                    <Grid item md={12} xs={12}>
                        <div className="howItWork">
                            <h2>How it works?</h2>
                            <p>Your Journey will be divided into levels based on <b>Outbound (OB) Teams</b></p>
                        </div>
                    </Grid>
                    {/* Navigation wrapper with arrows */}
                    <Grid item md={12} xs={12}>
                        <div className="slideshow-container">
                            {/* Previous Arrow */}
                            <div className="prev">
                                &#8249; Previous
                            </div>

                            {/* Content Section */}
                            <div className="mySlides show">
                                <Grid container spacing={3}>
                                    <Grid item md={7} xs={12}>
                                        <h4 className="Heading">For CG Plans</h4>
                                        <ul className="diaGram">
                                            <li>
                                                <div className="slab1">Slab 1
                                                    <p>&gt; ₹18,00,000</p>
                                                </div>
                                                <div className="percentage">10.53%</div>
                                            </li>
                                            <li>
                                                <div className="slab1">Slab 2
                                                    <p>&gt; ₹12,00,000</p>
                                                </div>
                                                <div className="percentage">9.08%</div>
                                            </li>
                                            <li>
                                                <div className="slab1">Slab 3
                                                    <p>&gt; ₹9,00,000</p>
                                                </div>
                                                <div className="percentage">8.25%</div>
                                            </li>
                                            <li>
                                                <div className="slab1">Slab 4
                                                    <p>&lt;= ₹9,00,000</p>
                                                </div>
                                                <div className="percentage">7.50%</div>
                                            </li>
                                        </ul>
                                    </Grid>
                                    <Grid item md={5} xs={12}>
                                        <div className="planTypes">
                                            <div className="planType">
                                                <span className="planName">ULIP Plans</span>
                                                <span className="planValue">2.50%</span>
                                            </div>
                                            <div className="planType">
                                                <span className="planName">LIC ULIP</span>
                                                <span className="planValue">4%</span>
                                            </div>
                                            <div className="planType">
                                                <span className="planName">HSA ULIP</span>
                                                <span className="planValue">4.50%</span>
                                            </div>
                                            <div className="planType">
                                                <span className="planName">Traditional Plans</span>
                                                <span className="planValue">11.50%</span>
                                            </div>
                                            <div className="planType">
                                                <span className="planName">Regular Pay Pension</span>
                                                <span className="planValue">14%</span>
                                            </div>
                                        </div>
                                    </Grid>
                                </Grid>
                            </div>

                            {/* Next Arrow */}
                            <div className="next">
                                Next &#8250;
                            </div>
                        </div>
                    </Grid>
                    <Grid item md={12} xs={12}>
                        <div className="incentiveStructureSection">
                            <h2>Incentive Structure for Single Pay Products (All Teams)</h2>
                            <p className="sectionDescription">The following incentive rates apply to single-premium products, regardless of the team:</p>

                            <div className="incentiveTable">
                                <div className="tableHeader">
                                    <div className="productType">Product Type</div>
                                    <div className="incentiveRate">Incentive Rate (% of Weighted APE)</div>
                                </div>
                                <div className="tableRow">
                                    <div className="productType">Single Pay ULIP</div>
                                    <div className="incentiveRate">0.5%</div>
                                </div>
                                <div className="tableRow">
                                    <div className="productType">Single Pay Traditional Plans</div>
                                    <div className="incentiveRate">2.0%</div>
                                </div>
                                <div className="tableRow">
                                    <div className="productType">Single Pay CG Plans</div>
                                    <div className="incentiveRate">1.5%</div>
                                </div>
                                <div className="tableRow">
                                    <div className="productType">Single Annuity Plans/Pay ULIP</div>
                                    <div className="incentiveRate">1.0%</div>
                                </div>
                            </div>
                        </div>
                    </Grid>
                    <Grid item md={12} xs={12}>
                        <div className="bookingsIncentiveSection">
                            <h2>Bookings Incentive Criteria</h2>
                            <p className="sectionDescription">Incentive Per Booking</p>

                            <div className="incentiveTable">
                                <div className="tableRow">
                                    <div className="productType">ULIP</div>
                                    <div className="incentiveRate">₹50 per booking</div>
                                </div>
                                <div className="tableRow">
                                    <div className="productType">CG & Traditional Plans</div>
                                    <div className="incentiveRate">₹200 per booking</div>
                                </div>
                            </div>

                            <div className="bookingRules">
                                <p><strong>Minimum Bookings Qualifier:</strong> 5 bookings</p>
                                <p><strong>Minimum ATS:</strong> Only bookings with an Ticket size greater than ₹30,000 will be considered for the Bookings Incentive.</p>
                                <p><strong>Payment Rule:</strong> The Bookings Incentive is paid only if it exceeds the incentive earned through the Cost Justification method.</p>
                            </div>
                        </div>
                    </Grid>
                    <h5 class="title">Weightage APE Criteria <hr /></h5>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            All Monthly Payment <strong> WITHOUT SI & eMandate </strong> or done by <strong> RM  </strong>will have <strong>50% weightage </strong>on APE
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> NRI: Auto-Pay </strong> attachment <strong> &#8804; 65% </strong> for monthly payment cases = <strong> 75% weightage </strong>on APE
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> T*ta SSR for </strong> APE Self Employed & Retainers Self Employed will have a  <strong>weightage of 125% </strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> HD*C  </strong> sold by <strong> HD*C Dedicated </strong> team will have a <strong> weightage of 125% (Exception Sampoor Nivesh: 100%) </strong> and weightage of  <strong> 50% </strong> on <strong> NON HD*C </strong> Bookings <strong> (Exception on Housewife Plan where it is 100%)</strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> HNI Process: Sum Assured &#8805; 5cr, weightage of 125%, </strong> If all issued policies &gt; 30. <strong> Excluding All HIGH Sum Assured ULIPs </strong> (List in the table below)
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> iP*u  </strong> sold by <strong> iP*u Dedicated </strong> team will have a <b> weightage of 125% </b> and weightage of <strong> 50% </strong> on <strong> NON iP*u </strong>Bookings
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> Single Premium / Single Pay / One Shot, </strong> will have <strong> 10% weightage </strong> of the Premium.
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> Working Women  </strong> as <strong> RIDER </strong> bookings will have <strong> 125% </strong>weightage
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            All <strong> GST waiver  </strong> bookings <strong> will be considered </strong> with GST in the APE <strong> for incentive calculation.</strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> All NRI </strong> Sum Assured  &#8805; 5cr will have a <strong> 125% </strong> weightage.
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> Baj*j Diabetic bkgs </strong> will have a <strong> weightage of 125% </strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            All <strong> HIGH Sum Assured ULIP </strong> (List in the table below) sold by NRI process will have a <strong> 50% weightage.</strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            All bkgs <strong> cancelled </strong> during <strong> FreeLook </strong> & <strong> Monthly Premium NOT PAID </strong> will result in <strong> CLAWBACK </strong> of the incentive
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> House Wife bookings </strong> will have <strong> 125% </strong> weightage. <strong> (Exception: HD*C & iP*u Dedicated Process where they will get 100%)</strong>
                        </p>
                    </Grid>
                    <Grid item md={4} xs={12}>
                        <p className="ApeCriteria">
                            <strong> ZERO </strong> credit for bookings with overall Talk Time less than 5 minutes.
                        </p>
                    </Grid>
                    <Grid item md={12} xs={12}>
                        <p className="ApeCriteria">
                            <strong> Combined weightage cannot exceed 150%.</strong><br /><br />
                            E.g. If Working Women as Rider on 10yrs Limited Pay will NOT be 125% X 125% = 162.5%
                            <p className="textgreen"> Combined Multiplier will be capped at 150%</p>
                        </p>
                    </Grid>
                 
                        <h5 className="title">10 Ka Dum Incentive Criteria <hr /></h5>
                     
                            <Grid item md={4} xs={12}>
                                <p className="ApeCriteria">
                                    Advisors can earn a <strong>Take Home Incentive</strong> of <strong>₹10 Lacs</strong> if they achieve <strong>₹50 Lacs Issued Premium</strong> (excluding ULIP and Single Pay Annuity plans).
                                </p>
                            </Grid>

                            <Grid item md={4} xs={12}>
                                <p className="ApeCriteria">
                                    For <strong>Single Premium Bookings</strong>, the APE of these plans will be <strong>considered at 10%</strong> for the <strong>"10 Ka Dum"</strong> calculation.
                                </p>
                            </Grid>

                            <Grid item md={4} xs={12}>
                                <p className="ApeCriteria">
                                    Eligibility requires a <strong>Business Health Rating of 70%</strong> or higher at the end of the month.
                                </p>
                            </Grid>

                            <Grid item md={12} xs={12}>
                                <p className="ApeCriteria">
                                    The difference between the <strong>advisor's Actual Incentive</strong> and the <strong>"10 Ka Dum" Incentive</strong> will be <strong>paid out after 6 months</strong>, provided the advisor's persistent <strong>APE remains at ₹50,00,000 or above</strong>.
                                </p>
                            </Grid>
                        </Grid>
                   

               
            </div>




            <Grid item md={12} className="RedBg" >               
                <Grid container spacing={3}>
                    <Grid item md={6} xs={12}>
                    <h3>Agent Quality Slabs	</h3>
                        <div className="DarkRedBg padding10">
                            <ul>
                                <li>Quality Score<span>Deduction</span></li>
                                <li>Less than 70% <span>20%</span></li>
                            </ul>
                        </div>
                    </Grid>
                    <Grid item md={6}>
                    <h3>Supervisor Feedback Slab</h3>                                            
                        <div className="DarkRedBg padding10">
                            <ul>
                                <li>Threshold ( TAT - 72 Hours)<span>Deduction</span></li>
                                <li>More than or Equal to 90%<span>0%</span></li>
                                <li>Less Than 90% <span>25%</span></li>
                            </ul>
                        </div>

                    </Grid>
                </Grid>
            </Grid>

            <div className="fosAllowanceSection">

                <Grid container spacing={3}>
                    <Grid item md={6} xs={12}>
                      
                            <h4 className="FeedbackHeading">Supervisor Feedback Slabs	</h4>
                            <div className="allowanceTable">
                                <div className="tableHeader">
                                    <div className="slabColumn">Slab</div>
                                    <div className="payoutColumn">Payout</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">Below 20 Visits</div>
                                    <div className="payoutColumn">₹250 per visit</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">21-40 Visits</div>
                                    <div className="payoutColumn">₹300 per visit, if CR% on Visits &gt; 40%</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">Above 41 Visits</div>
                                    <div className="payoutColumn">₹350 per visit, if CR% on Visits &gt; 40%</div>
                                </div>
                            
                            <p className="noteText"><b>Note:</b> Agents with CR &lt; 25% receive a maximum of ₹5,000, regardless of count of visits.</p>
                            </div>
                    </Grid>
                    <Grid item md={6} xs={12}>
                     
                    <h4 className="FeedbackHeading">Dedicated FOS Travel Allowance</h4>
                            <div className="allowanceTable">
                                <div className="tableHeader">
                                    <div className="slabColumn">Slab</div>
                                    <div className="payoutColumn">Payout</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">Below 40 Visits</div>
                                    <div className="payoutColumn">₹250 per visit</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">41-50 Visits</div>
                                    <div className="payoutColumn">₹300 per visit, if CR% on Visits &gt; 40%</div>
                                </div>
                                <div className="tableRow">
                                    <div className="slabColumn">Above 51 Visits</div>
                                    <div className="payoutColumn">₹350 per visit, if CR% on Visits &gt; 40%</div>
                                </div>
                                <p className="noteText"><b>Note:</b> Agents with CR &lt; 25% receive a maximum of ₹5,000, regardless of count of visits.</p>
                            </div>
                           
                     
                    </Grid>
                    <Grid item md={12} xs={12}>
                      
                    <h4 className="FeedbackHeading">Secondary APE Credit for Appointments Created</h4>
                      <div className="allowanceTable">
                    <div className="tableHeader">
                        <div className="slabColumn">Secondary APE</div>
                        <div className="payoutColumn">Weightage on APE</div>
                    </div>
                    <div className="tableRow">
                        <div className="slabColumn">&gt;= ₹2,00,000</div>
                        <div className="payoutColumn">100%</div>
                    </div>
                    <div className="tableRow">
                        <div className="slabColumn">₹1,50,000 - ₹2,00,000</div>
                        <div className="payoutColumn">80%</div>
                    </div>
                    <div className="tableRow">
                        <div className="slabColumn">₹1,00,000 - ₹1,50,000</div>
                        <div className="payoutColumn">65%</div>
                    </div>
                    <div className="tableRow">
                        <div className="slabColumn">&lt; ₹1,00,000</div>
                        <div className="payoutColumn">50%</div>
                    </div>
            
                     
                      </div>
              </Grid>
                </Grid>
            </div>



        </div>



        // <div className="ViewIncetiveCriteriaPopup">
        //     {
        //         incentiveCriteria &&
        //         <>
        //             <CloseIcon onClick={handleClick} className="closebtn" />
        //             <h4>Incentive Criteria </h4>


        //             <div className="MainContent">
        //                 <Grid container spacing={3}>
        //                     <Grid item md={5} xs={12}>
        //                         <div className="howItWork">
        //                             <h2>How it works?</h2>
        //                             <p>Your Journey will be divided into levels based on <b> APE/Booking </b></p>
        //                         </div>
        //                     </Grid>
        //                     <Grid item md={7} xs={12}>
        //                         <ul className="diaGram">
        //                             {
        //                                 Array.isArray(incentiveCriteria['slab']) && incentiveCriteria['slab'].length > 0
        //                                 && incentiveCriteria['slab'].map((criteria, slab) => {
        //                                     return (
        //                                         <li>
        //                                             <div className='slab1'> Slab {slab + 1}
        //                                                 <p>
        //                                                     {(criteria['start'] ? convertToIndianNumber.format(criteria['start']) : convertToIndianNumber.format(0))
        //                                                         + ' - '
        //                                                         + (criteria['end'] ? convertToIndianNumber.format(criteria['end']) : convertToIndianNumber.format(0))
        //                                                     }
        //                                                 </p>
        //                                             </div>
        //                                             {/* <div className='percentage'>{criteria['factor'] ? Math.round((criteria['factor'] + Number.EPSILON) * 100)  + '%' : '-'} </div> */}
        //                                             <div className='percentage'>{criteria['factor'] ? roundOffToTwo(criteria['factor'] )+'%' : '-'} </div>
        //                                         </li>
        //                                     )

        //                                 })
        //                             }

        //                         </ul>

        //                     </Grid>
        //                     <h5 class="title">Weightage APE Criteria <hr /></h5>
        //                     {

        //                 Array.isArray(incentiveCriteria['weightageCriteria']) && incentiveCriteria['weightageCriteria'].length>0 &&

        //                 incentiveCriteria['weightageCriteria'].map((criteria)=>{
        //                     return(
        //                         <Grid item md={4} xs={12}>
        //                             <p className='ApeCriteria'>
        //                                 {
        //                                    React.createElement('div', { dangerouslySetInnerHTML: { __html: criteria } })
        //                                 }
        //                             </p>
        //                         </Grid>
        //                     )
        //                 })
        //             }
        //                     {/* <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             All Monthly Payment <strong> WITHOUT SI & eMandate </strong> or done by <strong> RM  </strong>will have <strong>50% weightage </strong>on APE
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> NRI: Auto-Pay </strong> attachment <strong> &#8804; 65% </strong> for monthly payment cases = <strong> 75% weightage </strong>on APE
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> T*ta SSR for </strong> APE Self Employed & Retainers Self Employed will have a  <strong>weightage of 125% </strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> HD*C  </strong> sold by <strong> HD*C Dedicated </strong> team will have a <strong> weightage of 125% (Exception Sampoor Nivesh: 100%) </strong> and weightage of  <strong> 50% </strong> on <strong> NON HD*C </strong> Bookings <strong> (Exception on Housewife Plan where it is 100%)</strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> HNI Process: Sum Assured &#8805; 5cr, weightage of 125%, </strong> If all issued policies &gt; 30. <strong> Excluding All HIGH Sum Assured ULIPs </strong> (List in the table below)
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> iP*u  </strong> sold by <strong> iP*u Dedicated </strong> team will have a <b> weightage of 125% </b> and weightage of <strong> 50% </strong> on <strong> NON iP*u </strong>Bookings
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> Single Premium / Single Pay / One Shot, </strong> will have <strong> 10% weightage </strong> of the Premium.
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> Working Women  </strong> as <strong> RIDER </strong> bookings will have <strong> 125% </strong>weightage
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             All <strong> GST waiver  </strong> bookings <strong> will be considered </strong> with GST in the APE <strong> for incentive calculation.</strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> All NRI </strong> Sum Assured  &#8805; 5cr will have a <strong> 125% </strong> weightage.
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> Baj*j Diabetic bkgs </strong> will have a <strong> weightage of 125% </strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             All <strong> HIGH Sum Assured ULIP </strong> (List in the table below) sold by NRI process will have a <strong> 50% weightage.</strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             All bkgs <strong> cancelled </strong> during <strong> FreeLook </strong> & <strong> Monthly Premium NOT PAID </strong> will result in <strong> CLAWBACK </strong> of the incentive
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> House Wife bookings </strong> will have <strong> 125% </strong> weightage. <strong> (Exception: HD*C & iP*u Dedicated Process where they will get 100%)</strong>
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={4} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> ZERO </strong> credit for bookings with overall Talk Time less than 5 minutes.
        //                         </p>
        //                     </Grid>
        //                     <Grid item md={12} xs={12}>
        //                         <p className="ApeCriteria">
        //                             <strong> Combined weightage cannot exceed 150%.</strong><br /><br />
        //                             E.g. If Working Women as Rider on 10yrs Limited Pay will NOT be 125% X 125% = 162.5%
        //                             <p className="textgreen"> Combined Multiplier will be capped at 150%</p>
        //                         </p>
        //                     </Grid> */}
        //                 </Grid>
        //             </div>

        //             <Grid container spacing={3}>
        //                 <Grid item md={6} xs={12}>
        //                     <div className="GreenBg">

        //                         <h2><img src="/images/TermDashboard/leaf1.svg" />Cross Sell Incentive<img src="/images/TermDashboard/leaf2.svg" /></h2>
        //                         <p>Every Cross- Sell lead which will get issued will translate to:</p>

        //                         <ul>

        //                             {
        //                                 Array.isArray(incentiveCriteria['crossSellIncentive']) && incentiveCriteria['crossSellIncentive'].length > 0 &&
        //                                 incentiveCriteria['crossSellIncentive'].map((buwise) => {
        //                                     if (!buwise) {
        //                                         return null
        //                                     }
        //                                     return (
        //                                         <li>
        //                                             {buwise['team'] || '-'}
        //                                             <span>
        //                                                 {buwise['amount'] ? rupee.format(buwise['amount']) + '/-' : '-'}
        //                                             </span>
        //                                         </li>
        //                                     )
        //                                 })

        //                             }
        //                         </ul>

        //                     </div>
        //                 </Grid>

        //                 <Grid item md={6} xs={12}>
        //                     <div className="GreenBg">


        //                         <h2><img src="/images/TermDashboard/leaf1.svg" />Booking Incentive<img src="/images/TermDashboard/leaf2.svg" /></h2>
        //                         <ul className="mt-2">
        //                             {Array.isArray(incentiveCriteria['bookingIncentive']) && incentiveCriteria['bookingIncentive'].length > 0
        //                                 &&
        //                                 incentiveCriteria['bookingIncentive'].map((criteria, index) => {
        //                                     if (!criteria) { return null }
        //                                     if (index != incentiveCriteria['bookingIncentive'].length - 1) {
        //                                         if(criteria['title']=='Incentive per bookings')
        //                                         {
        //                                             return(
        //                                                 <li>{criteria['title'] || '-'} <span>{criteria['value'] ? rupee.format(criteria['value']) : '-'}</span></li>
        //                                     )
        //                                 }
        //                                         return (
        //                                             <li>{criteria['title'] || '-'} <span>{criteria['value'] || '-'}</span></li>
        //                                         )
        //                                     }
        //                                 })

        //                             }
        //                         </ul>
        //                         <p className="comment"> {Array.isArray(incentiveCriteria['bookingIncentive']) &&
        //                     incentiveCriteria['bookingIncentive'].at(-1).hasOwnProperty("note") &&
        //                     incentiveCriteria['bookingIncentive'].at(-1).note
        //                 }</p>
        //                     </div>
        //                 </Grid>
        //             </Grid>

        //             <Grid item md={12} className="RedBg" >
        //                 <h3>Deductions</h3>
        //                 <Grid container spacing={3}>
        //                     {
        //                         Array.isArray(incentiveCriteria['deductions']) && incentiveCriteria['deductions'].length > 0
        //                         &&
        //                         incentiveCriteria['deductions'].map((deduction, index) => {
        //                             if (!deduction) { return null }

        //                             if (index == 0) {
        //                                 return (
        //                                     <Grid item md={6} xs={12}>
        //                                         <p>{deduction['title'] || '-'}</p>
        //                                         <div className="DarkRedBg">

        //                                             <ul>
        //                                                 <li>{deduction['slabColumnName'] || '-'}<span>{deduction['payoutColumnName'] || '-'}</span></li>
        //                                                 {
        //                                                     Array.isArray(deduction['tableData']) && deduction['tableData'].length > 0
        //                                                     &&
        //                                                     deduction['tableData'].map((tableData) => {
        //                                                         if (!tableData) { return null }
        //                                                         return (
        //                                                             <li>{tableData['slab'] || '-'}<span>{tableData['payout'] || '-'}</span></li>
        //                                                         )
        //                                                     })
        //                                                 }
        //                                             </ul>
        //                                         </div>
        //                                     </Grid>
        //                                 )

        //                             }
        //                             else {
        //                                 return null
        //                             }
        //                         })
        //                     }
        //                     <Grid item md={6}>
        //                         <img src="/images/TermDashboard/redbg.svg" />
        //                         {
        //                             Array.isArray(incentiveCriteria['deductions']) && incentiveCriteria['deductions'].length > 0
        //                             &&
        //                             incentiveCriteria['deductions'].map((deduction, index) => {
        //                                 if (!deduction) { return null }
        //                                 if (index == 0 || deduction['title']=='Supervisor Feedback Slab') { return null }

        //                                 // if ([0,1].includes(index) ) { return null }
        //                                 else {
        //                                     return (
        //                                         <>
        //                                             <p>{deduction['title'] || '-'}</p>
        //                                             <div className="DarkRedBg padding10">

        //                                                 <ul>
        //                                                     <li>{deduction['slabColumnName'] || '-'}<span>{deduction['payoutColumnName'] || '-'}</span></li>
        //                                                     {
        //                                                         Array.isArray(deduction['tableData']) && deduction['tableData'].length > 0
        //                                                         &&
        //                                                         deduction['tableData'].map((tableData) => {
        //                                                             if (!tableData) { return null }
        //                                                             return (
        //                                                                 <li>{tableData['slab'] || '-'}<span>{tableData['payout'] || '-'}</span></li>
        //                                                             )
        //                                                         })
        //                                                     }
        //                                                 </ul>
        //                                             </div>
        //                                         </>
        //                                     )
        //                                 }
        //                             })
        //                         }
        //                     </Grid>
        //                 </Grid>
        //             </Grid>
        //             <Grid item md={12} className="BlueBg" >
        //                 <h3>FOS Booster</h3>
        //                 <Grid container spacing={3}>
        //                     {
        //                         Array.isArray(incentiveCriteria['fosBooster']) && incentiveCriteria['fosBooster'].length > 0
        //                         &&
        //                         incentiveCriteria['fosBooster'].map((fosBooster, index) => {
        //                             if (!fosBooster) { return null }

        //                             return (
        //                                 <Grid item md={6} xs={12}>
        //                                     <p>{fosBooster['title'] || '-'}</p>
        //                                     <div className='DarkblueBg'>

        //                                         <ul>
        //                                             <li>{fosBooster['slabColumnName'] || '-'}<span>{fosBooster['payoutColumnName'] || '-'}</span></li>
        //                                             {
        //                                                 Array.isArray(fosBooster['tableData']) && fosBooster['tableData'].length > 0
        //                                                 &&
        //                                                 fosBooster['tableData'].map((tableData) => {
        //                                                     if (!tableData) { return null }
        //                                                     return (
        //                                                         <li>{tableData['slab'] || '-'}<span>{tableData['payout'] || '-'}</span></li>
        //                                                     )
        //                                                 })
        //                                             }
        //                                         </ul>
        //                                     </div>
        //                                 </Grid>
        //                             )

        //                         })
        //                     }

        //                 </Grid>

        //                 <h3 className="mt-2">FOS Visit Allowance</h3>
        //                 <Grid container spacing={3}>

        //                     {
        //                         Array.isArray(incentiveCriteria['fosVisitAllowance']) && incentiveCriteria['fosVisitAllowance'].length > 0
        //                         &&
        //                         incentiveCriteria['fosVisitAllowance'].map((visitAllowance, index) => {
        //                             if (!visitAllowance) { return null }
        //                             if (index != incentiveCriteria['fosVisitAllowance'].length - 1) {
        //                                 return (
        //                                     <Grid item md={6} xs={12}>
        //                                         <p>{visitAllowance['title'] || '-'}</p>
        //                                         <div className='DarkblueBg'>

        //                                             <ul>
        //                                                 <li>{visitAllowance['slabColumnName'] || '-'}<span>{visitAllowance['payoutColumnName'] || '-'}</span></li>
        //                                                 {
        //                                                     Array.isArray(visitAllowance['tableData']) && visitAllowance['tableData'].length > 0
        //                                                     &&
        //                                                     visitAllowance['tableData'].map((tableData) => {
        //                                                         if (!tableData) { return null }
        //                                                         return (
        //                                                             <li>{tableData['slab'] || '-'}<span>{tableData['payout'] || '-'}</span></li>
        //                                                         )
        //                                                     })
        //                                                 }
        //                                             </ul>
        //                                         </div>
        //                                     </Grid>
        //                                 )
        //                             }

        //                         })
        //                     }
        //                 </Grid>
        //                 <p className="note"><b>Note:</b> {Array.isArray(incentiveCriteria['fosVisitAllowance']) &&
        //                     incentiveCriteria['fosVisitAllowance'].at(-1).hasOwnProperty("note") &&
        //                     incentiveCriteria['fosVisitAllowance'].at(-1).note
        //                 }</p>
        //                 <div className="text-center"> <img src="/images/TermDashboard/moon.svg" /></div>
        //             </Grid>

        //         </>
        //     }
        // </div>

    );
}

export default ViewIncentiveCriteria;