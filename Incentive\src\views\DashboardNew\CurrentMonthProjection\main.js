/* eslint-disable no-undef */
import React, { useEffect, useState } from "react";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import * as services from "../../../services";
import { withSnackbar } from 'notistack';
import { useMediaQuery } from '@material-ui/core';

//import { useParams } from "react-router";
import {
    Grid,
    FormControl,

} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import moment from "moment";
import ThumbDownIcon from '@material-ui/icons/ThumbDown';
import ThumbUpIcon from '@material-ui/icons/ThumbUp';
import FeedbackIcon from '@material-ui/icons/Feedback';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import ProjectedRanking from "../ProjectedRanking";
import CriteriaTerm from "../Critaria/CriteriaTerm";
import CriteriaMotor from "../Critaria/CriteriaMotor";
import CriteriaInvestment from "../Critaria/CriteriaInvestment";

import Health from "./health";
import Term from "./term";
import Motor from "./motor";
import Savings from "./savings";



const useStyles = makeStyles((theme) => ({
    rightPopup: {
        position: "relative",
        zIndex: "999",
    },
    list: {
        width: 600,
    },
    mList: {
        width: '100%',
    },
    currentProjectedIncentive: {
        textAlign: 'left',
        marginLeft: '1rem',
        marginBottom: '0.5rem',
        fontSize: '13px',
        fontWeight: 'normal'
    }
}));

const main = (props) => {
    const { agentDetail } = props;
    const classes = useStyles();
    const theme = useTheme();
    const isDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
        defaultMatches: true
    });
    //const urlParams = useParams();
    //const [userId, setUserId] = useState(null);

    //const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));
    const [date, setDate] = useState(moment().subtract(0, 'months').startOf('month').format("DD-MM-YYYY"));
    const [ProjectedData, setProjectedData] = useState([]);
    const [TakeFeedback, setTakeFeedback] = useState(true);
    const [ShowThankYou, setShowThankYou] = useState(false);
    const [NegativeFeedbackReason, setNegativeFeedbackReason] = useState(false);
    const [RightPopup, setRightPopup] = useState(false);
    const [productId, setProductId] = useState(null);
    const [ProjectionDetailsMotor, setProjectionDetailsMotor] = useState([]);


    const toggleDrawer = (open) => (event) => {
        /*if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
          return;
        }*/

        setRightPopup(open);
    };
    const loadCriteria = (productId) => {

        if (productId == 7) {
            return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>
        }
        if (productId == 117) {
            return <CriteriaMotor productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaMotor>
        }
        if (productId == 115) {
            return <CriteriaInvestment productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaInvestment>
        }
        return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>



    }



    //const [userDetail, setUserDetail] = useState({});


    const handleFeedback = (event, value) => {
        setTakeFeedback(false);
        setShowThankYou(false);
        setNegativeFeedbackReason(false);
        if (value == "Yes") {
            setShowThankYou(true);
            submitFeedbackReason()
        } else {
            setNegativeFeedbackReason(true);
        }
    };
    
    const submitFeedbackReason = (value = "") => {
        var jsonParam = {
            "AgentId": agentDetail.AgentId,
            "Response": ShowThankYou || value === "" ? 1 : 0,
            "Reason": value,
            "Source": "ProjectedIncentive",

        }
        console.log(jsonParam);
        services
            .API_POST(`Incentive/InsertProjectedIncentiveFeedback`, jsonParam)
            .then(response => {
                //////debugger
                if (response && response.Status) {
                    setShowThankYou(true);
                    setNegativeFeedbackReason(false);
                }
            })
            .catch((err) => {
                console.log("Error", err);
            });
    }
    useEffect(() => {
        services
            .API_GET(`Incentive/GetProjectedIncentive/${agentDetail.AgentId}`).then(response => {
                //////debugger
                if (response && response.Status) {
                    response = JSON.parse(response.Response);
                    //console.log("Dashboard================", response[0]);
                    setProjectedData(response[0]);
                    setProductId(response[0].ProductId);
                    // let key = props.enqueueSnackbar('To make the projections more accurate, we are now showing the incentives post salary adjustment.', {
                    //   variant: 'error',
                    // })
                    ////debugger;
                    try {
                        const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
                        let url = `Incentive/InsertAgentIncentivLog/${response[0].UserId}/0/${response[0].ProductId}/${dt}?PageName=ProjectedIncentive&EventName=Click`;

                        services
                            .API_GET(url).then(response => { })
                            .catch((err) => {
                            });
                    }
                    catch (e) {

                    }
                    ////debugger;
                    if ([117, 115].indexOf(response[0].ProductId) > -1) {
                        try {
                            services
                                .API_GET(`Incentive/GetProjectionDetailsMotor/${agentDetail.AgentId}`).then(response => {
                                    //////debugger
                                    if (response && response.Status) {
                                        response = JSON.parse(response.Response);
                                        if (response.length > 0) {
                                            response = JSON.parse(response[0]);
                                            setProjectionDetailsMotor(response);
                                        }
                                    }
                                })
                                .catch((err) => {
                                    console.log("Error", err);
                                });
                        }

                        catch (e) {

                        }
                    }




                }
            })
            .catch((err) => {
                console.log("Error", err);
            });
    }, [agentDetail.AgentId, props]);

    useEffect(() => {
        return () => {
            console.log("cleaned up");
            props.closeSnackbar();
        };
    }, [props]);

    const bindTillNowData = (CalculationType, isWeightedAPE) => {
        let result = [];
        if (ProjectionDetailsMotor.length > 0) {

            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

                const element = ProjectionDetailsMotor[index];

                if (ProjectedData.ProductId == 117) {
                    if (element.CalculationType == CalculationType) {
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            <td>{element.PlanCategory}</td>
                            <td className={(element.CalculationType == "NextProjection" && element.PlanCategory == "Comprehensive") ? 'highlight' : ''}>{element.BookingCount}</td>
                        </tr>)
                    }
                }
                else if (ProjectedData.ProductId == 115) {
                    if (element.CalculationType == CalculationType) {
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}

                            <td className={(element.CalculationType == "NextProjection" && element.PlanCategory == "Comprehensive") ? 'highlight' : ''}>
                                {isWeightedAPE &&
                                    <><i className="fa fa-inr"></i> {element.TotalWeightedAPE && Math.round(element.TotalWeightedAPE).toLocaleString('en-IN')}</>}
                                {!isWeightedAPE &&
                                    <><i className="fa fa-inr"></i> {element.TotalAPE && Math.round(element.TotalAPE).toLocaleString('en-IN')}</>}


                            </td>
                        </tr>)
                    }
                }
            }
        }
        return result;
    }

    const bindProjectiondata = (CalculationType) => {
        let result = [];
        let totalIncentiveAmount = 0;
        if (ProjectionDetailsMotor.length > 0) {

            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

                const element = ProjectionDetailsMotor[index];

                if (ProjectedData.ProductId == 117) {
                    if (element.CalculationType == CalculationType) {
                        totalIncentiveAmount += parseInt(element.IncentiveAmount) || 0;
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            <td>{element.PlanCategory}</td>
                            <td>{element.BookingCount} x {element.IncentiveValue}</td>
                            <td>{element.IncentiveAmount}</td>
                        </tr>)
                    }
                }
                else if (ProjectedData.ProductId == 115) {
                    if (element.CalculationType == CalculationType) {
                        totalIncentiveAmount += parseInt(element.IncentiveAmount) || 0;
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}
                            {ProjectedData.ProcessId != 74 && <td>-</td>}
                            {/* <td>{element.PlanCategory}</td> */}
                            <td>
                                <i className="fa fa-inr"></i> {element.TotalWeightedAPE && Math.round(element.TotalWeightedAPE).toLocaleString('en-IN')}   {" "}   x   {" "}{element.IncentiveValue} {"%"}
                            </td>
                            <td>
                                <i className="fa fa-inr"></i> {element.IncentiveAmount && Math.round(element.IncentiveAmount).toLocaleString('en-IN')}
                            </td>


                        </tr>)
                    }
                }
            }
        }

        result.push(<tr>
            <td>{'-'}</td>
            <td>{' '}</td>
            <td>{' '}</td>
            <td>{' '}</td>
        </tr>);

        result.push(<tr>
            <td>Total</td>
            <td>{'-'}</td>
            <td>{'-'}</td>
            <td><i className="fa fa-inr"></i> {totalIncentiveAmount && Math.round(totalIncentiveAmount).toLocaleString('en-IN')}</td>

        </tr>);

        return result;
    }

    const bindDRR = () => {
        let result = [
            <tr>
                <th >Plan Type</th>
                <th >Plan Category</th>
                <th >Minimum Bookings Required (full month)</th>
                <th >Required DRR</th>
            </tr>
        ];


        let CalculationType = 'MinimumRequired';
        let sum_drr = 0;
        if (ProjectionDetailsMotor.length > 0) {
            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
                const element = ProjectionDetailsMotor[index];
                if (ProjectedData.ProductId == 117) {
                    if (element.CalculationType == CalculationType) {

                        sum_drr += Math.ceil(element.DRR);

                        result.push(<tr>
                            <td >{element.PlanType}</td>
                            <td>{element.PlanCategory}</td>
                            <td>#{element.BookingCount}</td>
                            <td>
                                #{Math.ceil(element.DRR).toLocaleString('en-IN')}
                            </td>


                        </tr>)

                        // console.log(element.DRR);

                        // console.log("This Works!");
                    }
                }
            }
            
        }

        return result;

    }

    const bindSourcedBooking = () => {
        let result = [
            <tr>
                <th >Plan Type</th>
                {ProjectedData.ProcessId == 74 && <th >Plan Category</th>}
                <th >Min Sourced APE Req. (full month)</th>
                <th >Required DRR</th>
            </tr>
        ];

        let CalculationType = 'MinimumRequired';
        let sum_drr = 0;

        if (ProjectionDetailsMotor.length > 0) {
            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
                const element = ProjectionDetailsMotor[index];
                if (ProjectedData.ProductId == 115) {
                    if (element.CalculationType == CalculationType) {

                        sum_drr += Math.ceil(element.DRR);


                        result.push(<tr>
                            <td >{element.PlanType}</td>
                            {ProjectedData.ProcessId == 74 && <td >{element.PlanCategory}</td>}
                            <td><i className="fa fa-inr"></i> {Math.ceil(element.TotalAPE).toLocaleString('en-IN')}</td>
                            <td><i className="fa fa-inr"></i> {Math.ceil(element.DRR).toLocaleString('en-IN')}</td>

                        </tr>)

                        // console.log(element.DRR);

                        // console.log("This Works!");
                    }
                }
            }
            
        }

        return result
    }



    const sumDRR = () => {
        let CalculationType = 'MinimumRequired';
        let sum_drr = 0;
        if (ProjectionDetailsMotor.length > 0) {
            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
                const element = ProjectionDetailsMotor[index];

                if (element.CalculationType == CalculationType) {

                    sum_drr += Math.ceil(element.DRR);


                }
            }

        }

        return sum_drr;
    }

    const LearnMorePI = () => {
        let result = []

        if ([115].indexOf(ProjectedData.ProductId) != -1) {

            result.push(<a href="/images/incentive/pdf/SavingWebPoster.pdf" target="_blank" className="incentiveCriteraPopup alignLeft">Learn More About Projected Incentive <ChevronRightIcon /> </a>)

        }
        if ([117].indexOf(ProjectedData.ProductId) != -1) {

            result.push(<a href="/images/incentive/pdf/WebPoster.pdf" target="_blank" className="incentiveCriteraPopup alignLeft">Learn More About Projected Incentive <ChevronRightIcon /> </a>)

        }

        return result
    }

    const bindNextProjectionDRR = () => {
        let result = [];
        let DRR = 0;
        let CalculationType = 'NextProjection';
        if (ProjectionDetailsMotor.length > 0) {

            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

                const element = ProjectionDetailsMotor[index];
                if (ProjectedData.ProductId == 115) {
                    if (element.CalculationType == CalculationType) {
                        DRR += parseInt(element.DRR) || 0;
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}
                            {ProjectedData.ProcessId != 74 && <td>-</td>}

                            <td>
                                <i className="fa fa-inr"></i> {element.DRR && Math.round(element.DRR).toLocaleString('en-IN')}
                            </td>


                        </tr>)
                    }
                }
            }
        }

        result.push(<tr>
            <td>{'-'}</td>
            <td>{' '}</td>

            <td>{' '}</td>
        </tr>);

        result.push(<tr>
            <td>Total</td>
            <td>{'-'}</td>

            <td><i className="fa fa-inr"></i> {DRR && Math.round(DRR).toLocaleString('en-IN')}</td>

        </tr>);

        return result;
    }


    return (
        <>

            <Grid container item xs={12} md={12}>
                <Grid item sm={12} md={12} xs={12}>
                    <ProjectedRanking projectedData={ProjectedData} userId={agentDetail.AgentId} date={date} />
                </Grid>
                <Grid item sm={6} md={6} xs={12}>
                    {LearnMorePI()}
                </Grid>
                <Grid item sm={6} md={6} xs={12} className={classes.rightPopup}>
                    <a href="javascript:void(0)" onClick={toggleDrawer(true)} className="incentiveCriteraPopup">View Current Month Incentive Criteria <ChevronRightIcon /> </a>
                    {
                        <SwipeableDrawer
                            anchor="right"
                            open={RightPopup}
                            onClose={toggleDrawer(false)}
                            onOpen={toggleDrawer(true)}
                            className="CriteraPopup"
                        >
                            <div
                                className={isDesktop ? classes.list : classes.mList}
                                role="presentation"
                            //onClick={toggleDrawer(false)}
                            //onKeyDown={toggleDrawer(false)}
                            >
                                <span className="crossButton" onClick={toggleDrawer(false)}>X</span>
                                {loadCriteria(productId)}
                            </div>
                        </SwipeableDrawer>
                    }
                </Grid>

                
                {ProjectedData.ProductId == 2 &&
                    <Health data={ProjectedData} />}
                {ProjectedData.ProductId == 7 &&
                    <Term data={ProjectedData} />}
                {ProjectedData.ProductId == 115 &&
                    <Savings data={ProjectedData} />}
                {ProjectedData.ProductId == 117 &&
                    <Motor data={ProjectedData} />}

            </Grid>
            {TakeFeedback && (
                <div className="feedbackBox">
                    <FeedbackIcon className="feedbackIcon" />
                    <p>Did you find this information useful?</p>
                    <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
                    <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
                </div>
            )
            }

            {NegativeFeedbackReason && (
                <div className="dislike">
                    <FeedbackIcon className="feedbackIcon" />
                    <p>Did you find this information useful?</p>
                    <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
                    <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
                    <div>
                        <p>Tell us why</p>
                        <FormControl component="fieldset">
                            <RadioGroup
                                aria-label="status"
                                name="status"
                                className="radioBtn"
                            >
                                {" "}

                                <FormControlLabel
                                    value="Too much information"
                                    control={<Radio onClick={() => submitFeedbackReason("Too much information")} />}
                                    label="Too much information"
                                    small
                                />
                                <FormControlLabel
                                    value="Not able to understand"
                                    control={<Radio onClick={() => submitFeedbackReason("Not able to understand")} />}
                                    label="Not able to understand"
                                    small
                                />
                                <FormControlLabel
                                    value="Data inaccuracy"
                                    control={<Radio onClick={() => submitFeedbackReason("Data inaccuracy")} />}
                                    label="Data Inaccuracy"
                                    small
                                />
                            </RadioGroup>
                        </FormControl>
                    </div>
                </div>
            )}

            {ShowThankYou && (
                <div className="verifiedBox">
                    <img src="/images/incentive/verified.svg" />
                    <p>Thank you for your response</p>
                </div>
            )}
        </>
    )
};




export default withSnackbar(main);
