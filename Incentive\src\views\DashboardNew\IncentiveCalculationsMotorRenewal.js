import React, { useEffect, useState, Fragment } from "react";
import { withStyles, makeStyles } from "@material-ui/styles";
import * as services from "../../services";
import Skeleton from "@material-ui/lab/Skeleton";
import _ from "lodash";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
} from "@material-ui/core";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';
import Tooltip from '@material-ui/core/Tooltip';


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(0),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  content: {
    paddingTop: 150,
    textAlign: "center",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#fff",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    height: "70px",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#303030",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
    "& svg": {
      background: " #c8dcfa 0% 0% no-repeat padding-box",
      borderRadius: "50%",
      padding: "8px",
      width: "35px",
      height: "35px",
      color: "#0052cc",
    },
    "& ul": {
      display: "table",
      width: "100%",
      "& li": {
        display: "table-cell",
        width: "auto",
      },
    },
  },
  expandIcon: {
    background: "#00398e",
    color: "#fff",
    borderRadius: "50%",
  },
  warningBtn: {
    background: "#c8dcfa",
    borderRadius: "17px",
    margin: "5px 0",
    fontSize: "12px",
    color: "#0052cc",
    "&:hover": {
      background: "#c8dcfa",
    },
  },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 6px 16px #3469CB29",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);


const IncentiveCalculationsMotorRenewal = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [PlanCategory, setPlanCategory] = useState([]);
  const response = JSON.parse(localStorage.getItem('user'));

  const SourcingDetails = props.MotorRenewal && props.MotorRenewal[0] &&
    props.MotorRenewal[0].Status && props.MotorRenewal[0].Response != "[]" && JSON.parse(props.MotorRenewal[0].Response);

    const agentLevelData = props.MotorRenewal && props.MotorRenewal[1] &&
    props.MotorRenewal[1].Status && props.MotorRenewal[1].Response != "[]" && JSON.parse(props.MotorRenewal[1].Response);

  const SlabDetails = props.MotorRenewal && props.MotorRenewal[3] &&
    props.MotorRenewal[3].Status && props.MotorRenewal[3].Response != "[]" && JSON.parse(props.MotorRenewal[3].Response);

  let TotalBookings = 0
  let TotalPayout = 0

  // useEffect(() => {
  //   setSourcingDetails([]);
  //   setPlanCategory([]);
  //   if (!userId) {
  //     return;
  //   }
  //   //http://localhost:53481/api/incentive/GetAgentIncMotor/33390/01-08-2021
  //   //let url = "http://localhost:53481/api/incentive/GetAgentIncMotor/9930/01-08-2021";
  //   let url =`Incentive/GetIncentiveMotorRenewalAgent/${userId}/${date}`;

  //   services
  //     .API_GET(url).then(response => {
  //       //debugger;
  //       if (response && response.length>0 ) {
  //           if(response[0].Status && response[0].Response != "[]"){
  //               response = JSON.parse(response[0].Response)

  //               //let sourcingDetails = JSON.parse(response[0]);

  //               setSourcingDetails(response[0]);
  //           }
  //       }
  //     })
  //     .catch((err) => {
  //       console.log("Error", err);
  //     });
  // }, [userId,date]);

  const renderPayoutIncentiveTooltip = (data) => {

    return <div className="common-tooltip-popup">
      <span>(As per the Issuance till {moment(data.IncentiveDate).format('Do MMMM')})</span>
    </div>
  }

  const formatAmount = function (value) {
    return value.length > 0 ?
        Number(value).toLocaleString('en-IN', {
            maximumSignificantDigits: 15,
        }) :
        '';
  };
  

  return (
    <div className={classes.root} className="incentive">


      <Card className={classes.card}>
        <ul className="incentive-box table-incentive">
          <h6>
            Incentive Calculations
          </h6>
          <li>
          <table className="motorTableCal">
              <thead>
              <tr>
              <th className="areaWidth">Category</th>
                  <th className="areaWidth">15 Years & TP Prev Year</th>
                  <th>Iffco</th>
                  <th>Others TP</th>
                  <th>Rollover</th>
                  <th>PB</th>
                  <th>NonPB</th>    
                  <th>Total EligibleBookings</th>             
              </tr>
              </thead>
              <tbody>
              <tr>
                <th>Booking</th>
                {SourcingDetails && SourcingDetails.length > 0 &&
                  <>
                    <td>{SourcingDetails[0].YearsTPPrevYear}</td>
                    <td>{SourcingDetails[0].Iffco}</td>
                    <td>{SourcingDetails[0].OthersTP}</td>
                    <td>{SourcingDetails[0].Rollover}</td>
                    <td>{SourcingDetails[0].PB}</td>
                    <td>{SourcingDetails[0].NonPB}</td>
                    <td>{SourcingDetails[0].TotalBooking}</td>
                  </>}
              </tr>
              <tr> <th colSpan={7}>Multipliers Used</th>   <th>Total Incentive before salary deduction</th></tr>

              <tr>
               
              <th>Per Booking Payout</th>
               
              {SourcingDetails && SourcingDetails.length > 0 && SlabDetails && SlabDetails.length > 0 && agentLevelData && agentLevelData.length>0 && 


                  SlabDetails.map((item)=>{
                    if(agentLevelData[0].Supergroupid == item.Supergroupid) {

                    
                      return(
                      <>                 
                        <td>{item.YearsTPPrevYear}</td>
                        <td>{item.Iffco}</td>
                        <td>{item.OthersTP}</td>
                        <td>{item.Rollover}</td>
                        <td>{item.PB}</td>
                        <td>{item.NonPB}</td>

                        <td>₹{formatAmount(
                                            `${Math.round(SourcingDetails[0].FinalIncentive)}`
                                        )}</td>
                      
                      </>)
                    }
                  }) 
                  }
              </tr>
              </tbody>
            </table>
            
          </li>
          {/* <li>
            <table className="motorIncentive">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>15 Years & TP Prev Year</th>
                  <th>Iffco</th>
                  <th>Others TP</th>
                  <th>Rollover</th>
                  <th>PB</th>
                  <th>NonPB</th>
                  <th>Total EligibleBookings</th>
                </tr>
              </thead>
              <tbody>
                <td>Booking</td>
                {SourcingDetails && SourcingDetails.length > 0 &&
                  <>
                    <td>{SourcingDetails[0].YearsTPPrevYear}</td>
                    <td>{SourcingDetails[0].Iffco}</td>
                    <td>{SourcingDetails[0].OthersTP}</td>
                    <td>{SourcingDetails[0].Rollover}</td>
                    <td>{SourcingDetails[0].PB}</td>
                    <td>{SourcingDetails[0].NonPB}</td>
                    <td>{SourcingDetails[0].TotalBooking}</td>
                  </>}
                <tr>
                  <td>&nbsp;</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>

                <tr>
                  <th>Multipliers Used</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th>Total Incentive before salary deduction</th>
                </tr>

                {SourcingDetails && SourcingDetails.length > 0 && SlabDetails && SlabDetails.length > 0 &&
                  <>
                    <td>Per Booking Payout</td>
                    <td>{SlabDetails[0].YearsTPPrevYear}</td>
                    <td>{SlabDetails[0].Iffco}</td>
                    <td>{SlabDetails[0].OthersTP}</td>
                    <td>{SlabDetails[0].Rollover}</td>
                    <td>{SlabDetails[0].PB}</td>
                    <td>{SlabDetails[0].NonPB}</td>

                    <td>{SourcingDetails[0].FinalIncentive}</td>
                  </>}


              </tbody>
            </table>
          </li> */}
        </ul>
      </Card>
    </div>

  );
};

export default IncentiveCalculationsMotorRenewal;
