import React, { useEffect, useState } from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../../components/Loader";
import exportFromJSON from 'export-from-json';

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  //debugger
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

const PayoutClawback = (props) => {
  const { show, handleClose, userId, Date, Type } = props;
  const [clawbackList, setClawbackList] = useState([]);
  const [missellList, setMissellList] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getPayoutClawback = () => {
    setClawbackList([]);
    services
      .API_GET(`Incentive/GetRevisedLeads/${userId}/${Date}/${Type}`)
      .then(response => {
        console.log(response && response != "[]");

        if (response && response != "[]" && Type != 'Missell') {//debugger;
          let res = JSON.parse(response.Response);
          setClawbackList(JSON.parse(res[0]));
        }
        else if(response && response != "[]" && Type == 'Missell') {
          let res = JSON.parse(response.Response);
          console.log(res)
          setMissellList(res);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  const handleClawbacksExport = () => {
    //const fileName = userId + '_clawbacks';
    //const data = clawbackList;

    let data;
    let fileName;
    if(Type == 'Missell'){
      fileName = userId + '_missell';
      data = missellList
    }
    else{
      fileName = userId + '_clawbacks';
      data = clawbackList;
    }
    const exportType = exportFromJSON.types.xls;
    exportFromJSON({ data, fileName, exportType });
  }

  useEffect(() => {
    getPayoutClawback();
  }, [props]);

  return (
    <div>
      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"md"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
          <a style={{ float: 'left', cursor: 'pointer' }}

            onClick={handleClawbacksExport}
          >
            Export
          </a>
          {Type != 'Missell' && <strong>Revised Leads</strong>}
        </DialogTitle>

        <DialogContent className="critiria-popup">

          <div className="popup-inner-box payoutClawPopup">
            {Type != 'Missell' &&
              <div className="topPerformance">
                <table>
                  <tr>
                    <th>S.No</th>
                    <th>LeadID</th>
                    <th>SourcedAPE</th>
                    <th>RevisedWTAPE</th>
                    <th>Reason</th>
                    <th>Month</th>
                  </tr>
                  {
                    clawbackList && clawbackList.length > 0 && clawbackList.map((item, index) => {
                      return <tr key={index}><td><span>{index + 1}</span></td> <td><span>{item.LeadID}</span></td> <td><span></span>{item.SourcedAPE}</td> <td><span></span>{item.RevisedWTAPE}</td>
                        <td><span></span>{item.Reason}</td><td><span></span>{item.Month}</td> </tr>
                    }
                    )
                  }
                  {clawbackList.length == 0 && 'No Data Found'}
                </table>
              </div>}
            {Type == 'Missell' &&
              <div className="topPerformance">
                <table>
                  <tr>
                    <th>S.No</th>
                    <th>LeadID</th>
                    <th>Booking Date</th>
                    <th>Rolling Cycle Startdate</th>
                    <th>Rolling Cycle EndDate</th>
                    <th>Identified Month</th>
                    <th>IdentifiedBy</th>
                    <th>Remark</th>
                  </tr>
                  {
                    missellList && missellList.length > 0 && missellList.map((item, index) => {
                      return <tr key={index}>
                        <td><span>{index + 1}</span></td>
                        <td><span>{item.LeadId}</span></td>
                        <td><span>{item.BookingDate}</span></td>
                        <td><span>{item.RollingCycleStartdate}</span></td>
                        <td><span>{item.RollingCycleEndDate}</span></td>
                        <td><span>{item.IdentifiedMonth}</span></td>
                        <td><span>{item.IdentifiedBy}</span></td>
                        <td><span title={item.Remark}>{item.Remark && item.Remark.substring(0,50)} {item.Remark && item.Remark.length>50 && "..."}</span></td>
                      </tr>
                    }
                    )
                  }
                  {missellList.length == 0 && 'No Data Found'}
                </table>
              </div>}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
export default PayoutClawback;