import React, { useEffect, useState, Fragment } from "react";
import * as services from "../.././../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
import moment from "moment";

const useStyles = makeStyles((theme) => ({
    root: {
      padding: theme.spacing(0),
    },
    // bookingSNo: {
    //   width: "6% !important",
    // },
    customWidth: {
      maxWidth: 500,
    },
    tollBox: {
      background: "red",
    },
    customWidth: {
      width: 300,
      maxWidth: "100%",
    },
    // noMaxWidth: {
    //   maxWidth: 'none',
    // },
  }));
  
//   const WrapperTooltip = withStyles(theme => ({
//     tooltip: {
//       backgroundColor: "#fff",
//       padding: "10px",
//       boxShadow: " 0px 6px 16px #3469CB29",
//       fontSize: 11,
//       borderRadius: "5px",
//       maxWidth: "300px",
//       "& ul": {
//         "& li": {
//           listStyle: "none",
//           color: "#808080",
//         },
//       },
//     },
//     arrow: {
//       color: "#fff",
//     }
//   }))(Tooltip);

  const UpsellHealth = (props) => {
      const classes = useStyles();
      const { userId, date, qualiUpsell} = props;
      let upselldata = JSON.parse(qualiUpsell)

      const renderUpsellData = (upselldata) =>{
        let t = []
        for(let i = 0; i < upselldata.length; i++){
          const element = upselldata[i]
          if( element.IncentiveType == "Upsell"){
            t.push(element)
          }
        }

        
    
        return t
        
      }

      let upselld  = renderUpsellData(upselldata)
      console.log(upselld[0].TargetAchieved)

      return (
        <div className={classes.root}>
     
          <div className="HealthRenewalCommanView height230">
            <h6>
            Upsell Incentive
            </h6>
            <table>
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Upsell On Main Bookings</th>
                  <th>Upsell APE on Add-On Bookings</th>
  
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Upsell Ape</td>
                  <td><i class="fa fa-rupee"></i>{upselld[0].TargetAchieved.toLocaleString('en-IN')}</td>
                  <td><i class="fa fa-rupee"></i>{upselld[1].TargetAchieved.toLocaleString('en-IN')}</td>
                </tr>
                <tr>
                  <td>Incentive Level</td>
                  <td>{upselld[0].Slab}%</td>
                  <td>{upselld[1].Slab}%</td>
                </tr>
                <tr>
                  <td>Incentive **</td>
                  <td className="green"><i class="fa fa-rupee"></i>{upselld[0].TotalIncentive.toLocaleString('en-IN')}</td>
                  <td className="green"><i class="fa fa-rupee"></i>{upselld[1].TotalIncentive.toLocaleString('en-IN')}</td>
                </tr>
             
             
              </tbody>
  
            </table>
  
  
  
  
          </div>
        </div>
    

      );

  }

  export default UpsellHealth