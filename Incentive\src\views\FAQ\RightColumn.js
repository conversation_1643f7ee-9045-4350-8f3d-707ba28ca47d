import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { Grid } from "@material-ui/core";
import Modal from "@material-ui/core/Modal";
import RaiseTicket from "./RaiseTicket";


function rand() {
    return Math.round(Math.random() * 20) - 10;
  }
  
function getModalStyle() {
  const top = 50 + rand();
  const left = 50 + rand();

  return {
    top: `${top}%`,
    left: `${left}%`,
    transform: `translate(-${top}%, -${left}%)`,
  };
}

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: "absolute",
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: "2px solid #000",
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));

const RightColumn = (props) => {
  const classes = useStyles();
  const [modalStyle] = React.useState(getModalStyle);
  const [open, setOpen] = React.useState(false);
  const [userId, setUserId] = useState(null);

  useEffect(() => {
    const tokenPath = window.location.pathname.split("/");
    if (tokenPath.length >= 3) {
      let urlToken = tokenPath[2];
      setUserId(urlToken);
    }
  }, []);

  
  const handleOpen = () => {
    alert(userId);
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const body = (
    // <div style={modalStyle} className={classes.paper}>
    <iframe
      style={modalStyle}
      className={classes.paper}
      src="https://matrixticket.policybazaar.com/Landing.html#/matrix/LandingPage/434953af-3299-48c0-8f32-c111e9af9173"
      width="600"
      height="450"
    />
    // </div>
  );

  return (
    <div className="faq-rightside">
      {/* <h6>Help Topics</h6>
    <p>Help related to popular topics</p> */}
      <Grid container spacing={3}>
        {/* <Grid item sm={6} md={6} xs={6}>  
    <div className="rightside-box">
    <img src="/images/trophy.png"/>
      <p>Contests</p>
      </div>
      </Grid> */}
        <Grid item sm={6} md={6} xs={6}>
          {/* <div className="rightside-box">
    <img src="/images/money.svg"/>
      <p>Payouts</p>
      </div> */}
        </Grid>
        <Grid item sm={6} md={6} xs={6}>
          {/* <div className="rightside-box">
    <img src="/images/surface1.svg"/>
      <p>Projections</p>
      </div> */}
        </Grid>
        <Grid item sm={6} md={6} xs={6}>
          {/* <div className="rightside-box">
    <img src="/images/top.svg" className="reward"/>
      <p>Reward</p>
      </div> */}
        </Grid>
        <div className="contact-us">
          <h6>Contact us</h6>
          <p>Get help from the Team Leads and HRs</p>
          <Grid item sm={12} md={12} xs={12}>
            <RaiseTicket />
            <Modal
              open={open}
              onClose={handleClose}
              aria-labelledby="simple-modal-title"
              aria-describedby="simple-modal-description"
            >
              {body}
            </Modal>
          </Grid>
          <Grid item sm={12} md={12} xs={12}>
            {/* <div className="rightside-box">
    <img src="/images/good.svg"/>
    <a href="https://www.google.com" target="_blank">Feedback</a>
      </div> */}
          </Grid>
        </div>
      </Grid>
    </div>
  );
};

export default RightColumn;
