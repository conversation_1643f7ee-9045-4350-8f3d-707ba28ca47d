* {
    margin: 0px;
    padding: 0px;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,900&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400;0,500;0,600;0,700;1,400;1,500;1,900&family=Poppins:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,400;1,700;1,800;1,900&family=Roboto:wght@100;300;400;500;700;900&display=swap');

body {
    background-color: #F8F8F8 !important;
}

.EmergingLayout {
    // background-image: url(/images/jag2023/EmergingStar.svg);
    background-image: url(/images/jag2023/EmergingStar.png);
}

.JagBgImage {
    // background-image: url(/images/jag2023/jagBg.svg);
    background-image: url(/images/jag2023/jagBg.png);
}

.MuiTooltip-popper{
    z-index: 9999999999 !important;
}

.AgentDetailsLayout {
    background-repeat: no-repeat;
    background-size: contain, cover;
    position: relative;
    padding: 20px;

    /* Header css */
    .Header {
        display: flex;

        .leftSide {
            display: flex;
            align-items: center;

            hr {
                width: 1px;
                margin: 0px 17px;
                height: 24px;
                background: #ffffff42;
            }
        }

        .rightSide {
            display: flex;
            align-items: center;

            .RetainerBtn {
                border-radius: 16px;
                margin-left: 6px;
                background: #ECF2FF;
                width: auto;
                display: inline-flex;
                padding: 4px 16px;
                justify-content: space-between;
                align-items: center;
                color: #253858;
                font-family: Poppins;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;

                img {
                    margin-right: 5px;
                }
            }

            .goldStar {
                margin-left: 18px;
            }
        }
    }

    /* End Css */

    /* Agent Details css */
    .AgentDetails {
        text-align: center;
        color: #FFF;
        font-family: Playfair Display;
        font-size: 40px;
        font-style: italic;
        font-weight: 800;
        line-height: normal;
        letter-spacing: 0.4px;

        p {
            color: rgba(255, 255, 255, 0.60);
            text-align: center;
            font-family: Playfair Display;
            font-size: 32px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
        }

        span {
            color: #FFF;
            font-family: Poppins;
            font-size: 24px;
            font-style: italic;
            font-weight: 800;
            line-height: normal;
            letter-spacing: 0.24px;
        }
    }

    .DeadlineReminder {
        font-size: 16px;
        font-weight: bold;
        text-align: center;
        color: yellow;
        font-family: 'Poppins';
        overflow: hidden;
        white-space: nowrap;
    }

    .DeadlineReminder span{
        display: inline-block;
        animation: deadline 30s linear infinite;

    }

    @keyframes deadline {
        from {
          transform: translateX(100%);
        }
        to {
          transform: translateX(-100%);
        }
      }


    /* End Agent Details css */
    /* Progress Score css */
    .ProgressScore {
        width: 880px;
        height: 410px;
        border-radius: 8px;
        background: #FFF;
        margin: 20px auto;
        padding: 20px;
        position: relative;
        box-shadow: 0px 6px 16px #3469CB29;
        h3 {
            color: #253858;
            font-family: Poppins;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        h3 span{
            color: red;
            text-align: right;
            font-family: Poppins;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
        }
       

        ul {
            display: flex;
            justify-content: space-between;

            li {
                text-align: center;
                display: flex;
                flex-direction: column;
                flex-wrap: wrap;
                color: #253858;
                font-family: Poppins;
                font-size: 12px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                width: 25%;
                height: 120px;
                position: relative;

                img {
                    display: block;
                }

                h4 {
                    color: #253858;
                    text-align: center;
                    font-family: Roboto;
                    font-size: 24px;
                    position: absolute;
                    font-style: normal;
                    font-weight: 700;
                    line-height: normal;
                    left: 0;
                    right: 0;
                    bottom: 0;

                    p {
                        color: #253858;
                        font-family: Poppins;
                        font-size: 10px;
                        font-style: normal;
                        font-weight: 600;
                        line-height: normal;
                    }
                }
            }
        }

        .CurrentGrowth {
            border-radius: 8px;
            background: rgba(0, 101, 255, 0.05);
            height: 111px;
            margin-top: 25px;
            padding: 15px;

            h4 {
                display: inline-block;
                line-height: 28px;
                width: 30%;
            }

            .TotalGrowth {
                display: inline-block;
                color: #253858;
                text-align: right;
                font-family: Poppins;
                font-size: 12px;
                font-style: normal;
                border-radius: 6px;
                font-weight: 600;
                line-height: normal;
                background-color: #fff;
                padding: 5px 8px;
                float: right;
            }

            .Dflex {
                display: flex;
                width: 100%;

                .GrowthPersantage {
                    color: #253858;
                    text-align: center;
                    font-family: Poppins;
                    font-size: 12px;
                    font-style: normal;
                    position: relative;
                    font-weight: 600;
                    line-height: normal;
                    top: 20px;
                    left: 9px;
                }
            }

            .Growthbar {
                width: 85%;

                .MuiLinearProgress-root {
                    border-top-right-radius: 24px;
                    border-bottom-right-radius: 24px;
                    background: #EBEBEB;
                    height: 32px;
                    margin-top: 15px;
                    width: 100%;

                    .MuiLinearProgress-barColorPrimary {
                        // border-radius: 24px 0px 0px 24px;
                        background-color: #4ACD50;
                    }
                }
            }

            .UnderGrowth {
                width: 85%;

                .MuiLinearProgress-root {
                    border-top-right-radius: 24px;
                    border-bottom-right-radius: 24px;
                    background: #EBEBEB;
                    height: 32px;
                    margin-top: 15px;
                    width: 100%;

                    .MuiLinearProgress-barColorPrimary {
                        // border-radius: 24px 0px 0px 24px;
                        background-color: #d5bb03;
                    }
                }
            }

            .Negativebar {
                border-top-right-radius: 24px;
                border-bottom-right-radius: 24px;
                background: #EBEBEB;
                height: 32px;
                margin-top: 15px;
                width: 25%;
                transform: scaleX(-1);

            }

            .NegativeGrowth {
                .MuiLinearProgress-barColorPrimary {
                    background-color: #f1453a;
                }
            }

            .NegativeUnderGrowth {
                .MuiLinearProgress-barColorPrimary {
                    background-color: #d5bb03;
                }
            }

            .GrowthAchieved {
                .MuiLinearProgress-barColorPrimary {
                    background-color: #4ACD50;
                }
            }

            .line-container {
                width: 100%;
                height: 32px;
                position: relative;
                top: -32px;
                background-color: transparent;
                z-index: 999999999999;

                .vertical-line {
                    position: absolute;
                    width: 1px;
                    height: 100%;
                    background-color: #253858;
                    color: #253858;
                    text-align: center;
                    font-family: Poppins;
                    font-size: 12px;

                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

                /* Set percentage positions for each line */

                .line-0 {
                    left: 0%;

                    &::after {
                        content: "0%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-10 {
                    left: 10%;

                    &::after {
                        content: "10%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-15 {
                    left: 15%;

                    &::after {
                        content: "15%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-20 {
                    left: 20%;

                    &::after {
                        content: "20%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-30 {
                    left: 30%;

                    &::after {
                        content: "30%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-40 {
                    left: 40%;

                    &::after {
                        content: "40%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-50 {
                    left: 50%;

                    &::after {
                        content: "50%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .line-60 {
                    left: 60%;

                    &::after {
                        content: "60%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }
                .line-70 {
                    left: 70%;

                    &::after {
                        content: "70%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-10 {
                    left: 5%;

                    &::after {
                        content: "10%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-15 {
                    left: 7.5%;

                    &::after {
                        content: "15%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-20 {
                    left: 10%;

                    &::after {
                        content: "20%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-30 {
                    left: 15%;

                    &::after {
                        content: "30%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }
                .lineDouble-40 {
                    left: 20%;

                    &::after {
                        content: "40%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-50 {
                    left: 25%;

                    &::after {
                        content: "50%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-60 {
                    left: 30%;

                    &::after {
                        content: "60%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

                .lineDouble-70 {
                    left: 35%;

                    &::after {
                        content: "70%";
                        position: absolute;
                        top: 30px;
                        left: -3px;
                    }
                }

            }
        }

        .lotteryTicekt {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            align-items: center;

            p {
                text-align: right;
                font-family: Poppins;
                font-size: 16px;
                display: flex;
                align-items: center;
                font-weight: 500;
                line-height: normal;

                img {
                    margin-right: 5px;
                }
            }

            a {
                color: #0065FF;
                text-align: right;
                font-family: Poppins;
                font-size: 14px;
                font-style: normal;
                font-weight: 600;
                line-height: normal;
                text-decoration-line: underline;
                margin: 0px 10px;
            }
        }

        .TotalScore {
            width: 100%;
            border-radius: 0px 0px 8px 8px;
            background: #14cca133;
            display: flex;
            padding: 8px 32px;
            justify-content: space-between;
            align-items: center;
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;

            hr {
                height: 19px;
                background: #0000001f;
                border: none;
                width: 1px;
            }

            p {
                color: #38374B;
                font-family: Poppins;
                font-size: 14px;
                font-style: normal;
                font-weight: 500;
                line-height: normal;
                display: flex;
                white-space: nowrap;

                strong {
                    margin: 0px 5px;
                }
            }
        }
    }

    /* End Progress score css */



    /* Message Criteria css*/
    .MessageCriteria {
        width: 100%;
        border-radius: 16px;
        background: #FFF;
        padding: 20px;
        height: 678px;
        box-shadow: 0px 6px 16px #3469CB29;
        .caption {
            font-family: Roboto;
            font-size: 10px;
            font-style: normal;
            font-weight: 500;
            margin: 5px 0px 0px;
            line-height: 13px;
            text-transform: uppercase;
            display: flex;
            align-items: center;

            p {
                height: 5px;
                width: 5px;
                border-radius: 20px;
                margin: 7px;
                background: #0065FF;
            }
        }

        hr {
            border: 1px dashed #0000003d;
            margin: 10px 0px;
        }

        h4 {
            color: #253858;
            font-family: Poppins;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: 26.8px;
        }

        .slick-list {
            height: 367px;
        }

        .slick-slide {
            padding: 0px;

            .banner {
                .MuiCardContent-root {
                    padding: 0px;
                }

                h3 {
                    color: #253858;
                    font-family: Poppins;
                    font-size: 22px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    margin-bottom: 8px;
                }

                ul {
                    list-style: none;

                    li {
                        color: #253858;
                        font-family: Poppins;
                        font-size: 14px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        display: flex;
                        margin-bottom: 8px;

                        &::before {
                            content: "\2022";
                            /* Use the Unicode bullet character */
                            color: #253858;
                            /* Set the color of the marker */
                            font-size: 1.4em;
                            /* Adjust the size of the marker */
                            margin-right: 8px;
                            /* Add spacing between the marker and content */
                            line-height: 20px;
                        }
                    }
                }

                ul.InvestmentMessageul {
                    list-style: none;

                    li {
                        color: #253858;
                        font-family: Poppins;
                        font-size: 12px;
                        font-style: normal;
                        font-weight: 500;
                        line-height: normal;
                        display: flex;
                        margin-bottom: 8px;

                        &::before {
                            content: "\2022";
                            /* Use the Unicode bullet character */
                            color: #253858;
                            /* Set the color of the marker */
                            font-size: 1.4em;
                            /* Adjust the size of the marker */
                            margin-right: 8px;
                            /* Add spacing between the marker and content */
                            line-height: 20px;
                        }
                    }
                }

            }

        }

        .slick-slider {
            .slick-dots {
                bottom: 0px;

                .slick-active {
                    button {
                        &::before {
                            background: #0065FF;
                        }
                    }
                }
            }

            ul.slick-dots li {
                width: 10px;
                height: 10px;

                button {
                    width: 10px;
                    height: 10px;

                    &::before {
                        content: "";
                        border-radius: 24px;
                        background: rgba(0, 101, 255, 0.50);
                        width: 100%;
                        height: 10px;
                        opacity: 1;
                    }
                }
            }
        }
    }

    /* END Message Criteria css*/

    /* Leader Board CSS */
    .LeaderBoard {
        width: 100%;
        border-radius: 16px;
        background: #FFF;
        padding: 20px;
        box-shadow: 0px 6px 16px #3469CB29;
        .Heading {
            color: #253858;
            font-family: Poppins;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            text-align: center;
            margin-bottom: 15px;
        }

        hr {
            margin: 20px 0px 15px;
            height: 1px;
            border: none;
            background: rgba(0, 0, 0, 0.12);
        }

        ul {
            display: flex;
            list-style-type: none;
            justify-content: space-around;
            align-items: flex-end;

            li {
                text-align: center;

                h4 {
                    font-family: Poppins;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                }

                &:first-child {
                    h4 {
                        font-size: 16px;
                        background: linear-gradient(100deg, #FD9727 -2.53%, #FF6D3A 90.43%);
                        background-clip: text;
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                    }
                }

                &:last-child {
                    h4 {
                        background: linear-gradient(100deg, #B9BBC1 -2.53%, #A0A3AA 90.43%);
                        background-clip: text;
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-size: 18px;
                    }
                }

                &:nth-child(2) {
                    h4 {
                        background: linear-gradient(100deg, #FED130 -2.53%, #F7BC14 90.43%);
                        background-clip: text;
                        -webkit-background-clip: text;
                        -webkit-text-fill-color: transparent;
                        font-size: 24px;
                    }

                    p {
                        font-size: 12px;
                    }

                    h3 {
                        font-size: 16px;
                    }
                }

                p {
                    color: #253858;
                    text-align: center;
                    font-family: Poppins;
                    font-size: 10px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 20px;
                    opacity: 0.6;
                }

                h3 {
                    color: #253858;
                    text-align: center;
                    font-family: Poppins;
                    font-size: 14px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: 23px;
                }
            }
        }

        .AgentListing {
            border-collapse: separate;

            thead {
                background-color: transparent;

                th {
                    color: #253858;
                    font-family: Poppins;
                    font-size: 12px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    opacity: 0.6;
                    border: none;
                }
            }

            tbody {
                td {
                    color: #253858;
                    font-family: Poppins;
                    font-size: 16px;
                    font-style: normal;
                    font-weight: 600;
                    line-height: normal;
                    border: none;
                }
            }

            .active {
                background: rgba(0, 101, 255, 0.05);
                border-radius: 8px;

                td {
                    color: #0065FF;
                    border-top: 1px solid #0065ff;
                    border-bottom: 1px solid #0065ff;

                    &:first-child {
                        border-left: 1px solid #0065ff;
                        border-top-left-radius: 8px;
                        border-bottom-left-radius: 8px;
                    }

                    &:last-child {
                        border-right: 1px solid #0065ff;
                        border-top-right-radius: 8px;
                        border-bottom-right-radius: 8px;
                    }
                }
            }
        }

        button {
            background: rgba(0, 101, 255, 0.05);
            border: none;
            color: #253858;
            font-family: Poppins;
            padding: 11px 37px;
            font-size: 16px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin: 15px auto;
            cursor: pointer;
        }
    }

    /* END Leader Board css */


}

/* AppBar css */
.AppBarSection {
    background: #6051CF;
    width: 100%;
    padding: 30px 55px;
    box-shadow: 0px 6px 16px #3469CB29;
    .purple-box {
        background-color: #fff;
        border-radius: 16px;
        padding: 20px;
    }

    .bar-head {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        align-items: center;

        h2 {
            color: #FFF;
            font-family: Poppins;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
        }

        .download-btn {
            background: #ffffff33;
            border-radius: 8px;
            color: #FFF;
            font-family: Roboto;
            font-size: 16px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
            padding: 15px 14px 15px 17px;
            cursor: pointer;
            margin-left: 10px;
            display: inline-flex;

            img {
                margin-right: 7px;
            }
        }
    }

}

/* END AppBar css */

@media screen and (max-width: 750px) {
    .AppBarSection{
        padding: 20px 15px 78px;
        position: relative;
        .bar-head {
            display: block;          
            margin-bottom: 15px;         
          
            h2 {
                font-size: 31px;
                margin-bottom: 10px;
                text-align: center;             
            }
    
            .download-btn {       
                width: 46%;
                margin-bottom: 0px;
                font-size: 13px;
                margin-left: 2.8%;
            }
        }
        .bar-head > div{
            position: absolute;
            bottom: 0;
            left: 0px;
            right: 0px;
            margin-bottom: 13px;
        }
    }
    .JagBgImage {
        background-image: url(/images/jag2023/jagBGMobile.png) !important;
    }

    .EmergingLayout {
        background-image: url(/images/jag2023/EmergingBGMobile.png) !important;
    }

    .AgentDetailsLayout {
        padding: 0px;

        .ProgressScore {
            width: 100%;
            height: auto;
            padding: 15px 15px 5px;

            ul {
                display: flex;
                margin-top: 16rem;
                flex-direction: column;

                li {
                    text-align: left;
                    line-height: 27px;
                    display: block;
                    width: 100%;
                    height: 70px;

                    h4 {
                        position: static;
                        text-align: left;
                        display: flex;
                    }

                    img {
                        display: block;
                        float: left;
                        margin: 7px 15px 0px 0px;
                    }
                }
            }

            .CurrentGrowth {
                position: absolute;
                width: 92%;
                left: 0;
                margin: auto;
                right: 0px;
                top: 60px;
                border-bottom-left-radius: 0px;
                margin-top: 0px;
                border-bottom-right-radius: 0px;
                height: 135px;

                .Negativebar {
                    height: 28px;
                }

                .Growthbar {
                    .MuiLinearProgress-root {
                        height: 28px;
                    }
                }

                .UnderGrowth {
                    .MuiLinearProgress-root {
                        height: 28px;
                    }
                }

                .line-container {
                    height: 26px;
                    top: -27px;
                }
            }

            .TotalScore {
                top: 196px;
                margin: auto;
                width: 92%;
                padding: 10px 12px;
                display: block;
                bottom: auto;

                hr {
                    display: none;
                }

                p {
                    display: flex;
                    justify-content: left;
                    flex-wrap: wrap;
                }
            }

            .lotteryTicekt {
                display: block;

                p {
                    text-align: right;
                    font-size: 16px;
                    display: flex;
                    align-items: center;
                    justify-content: space-around;
                    margin-bottom: 10px;

                    &:last-child {
                        display: block;
                        width: 100%;

                        a {
                            display: block;
                            text-align: left;
                            line-height: 35px;
                        }
                    }
                }

            }
        }

        .LeaderBoard {
            padding: 20px 15px;

            ul {
                position: relative;
                justify-content: space-between !important;

                li {
                    width: 37%;
                    text-align: center;
                    margin-top: 140px;

                    p {
                        line-height: 18px !important;
                    }

                    h4 {
                        font-size: 16px !important;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }

                    &:nth-child(2) {
                        display: flex;
                        position: absolute;
                        width: 100%;
                        top: -140px;
                        left: 24px;
                        align-items: center;

                        img {
                            margin-right: 18px;
                        }

                        p,
                        h3,
                        h4 {
                            text-align: left;
                            line-height: 22px !important;
                        }

                    }

                    img {
                        width: 60px;
                    }
                }
            }

            .AgentListing {
                thead {
                    th {
                        padding: 7px;
                        font-size: 10px;
                    }
                }

                tbody {
                    td {
                        font-size: 12px;
                        padding: 11px;
                    }
                }
            }

        }

        .Header {
            display: block;
            padding-top: 10px;

            .rightSide {
                // display: block;
                text-align: right;

                .RetainerBtn {
                    margin-bottom: 5px;
                    width: auto;
                    margin-left: 0px;
                }
            }

            .leftSide {
                justify-content: center;
                margin-bottom: 15px;
            }
        }

        .AgentDetails {
            font-size: 24px;
            text-align: left;
            line-height: 23px;

            p {
                font-size: 16px;
                text-align: left;
                margin-bottom: 8px;
            }

            span {
                font-size: 13px;
                font-weight: 600;
            }
        }
    }



}