import React, { useEffect, useState } from 'react';
import { Bar } from 'react-chartjs-2';
import ChartDataLabels from 'chartjs-plugin-datalabels';
import * as services from '../../../services';
import exportFromJSON from 'export-from-json'
import {
  Chart,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js';


import { JAG_MONTHS, JAG_VERSION } from '../JagConstants2023';

Chart.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend,
  //ChartDataLabels
);

const ApeBarGraph = (props)=> {

  const [agentsMonthlyData, setAgentsMonthlyData] = useState([]);
  const [agentBookingsData, setAgentsBookingsData] = useState([]);
  const [fileType, setfileType] = useState(0);
  
  const version = JAG_VERSION;
  const [isWebView, setIsWebView] = useState(true);
  const userId = props.agentDetails.AgentId;
  const productId = props.agentData.ProductId;
  const employeeId = props.agentDetails.EmpId;
  const BUId = props.agentData.BUId || 0;
  const [width, setWindowWidth] = useState(0);

  const updateDimensions = () => {
    const width = window.innerWidth;
    setWindowWidth(width);
  };

  useEffect(() => {
    updateDimensions();
    window.addEventListener('resize', updateDimensions);
    return () =>
      window.removeEventListener('resize', updateDimensions);
  }, []);

  useEffect(() => {
    if (width > 1023) {
      setIsWebView(true);
    } else {
      setIsWebView(false);
    }
  }, [width]);

  const filterBookingColumns = (bookings, productId, BUId) => {
    let filteredColumnBookings = [];
    for (let i = 0; i < bookings.length; i++) {
      const element = bookings[i];
      const {
        Booking_Date,
        BookingLeadID,
        StatusName,
        APE,
        IsEligible,
        Remark,
        isAddOn,
        IsCombo,
        IsNop,
        Wt_APE,
        Final_Category,
        PaymentPeriodicity
      } = element;

      if (productId === 7) {

        filteredColumnBookings.push({
          "BookingDate": Booking_Date.substr(0, 10),
          "LeadID": BookingLeadID,
          "BookingStatus": StatusName,
          "PaymentPeriodicity": PaymentPeriodicity,
          //"APE": APE,
          "RatedAPE": Wt_APE,
          "Eligible": IsEligible,
          "Reason": Remark,
        });

      } else if (productId === 2) {

        filteredColumnBookings.push({
          "BookingDate": Booking_Date.substr(0, 10),
          "LeadID": BookingLeadID,
          "IsAddon": isAddOn,
          "BookingStatus": StatusName,
          "PaymentPeriodicity": PaymentPeriodicity,
          "BookingCount": IsNop,
          //"APE": APE,
          "RatedAPE": Wt_APE,
          "Eligible": IsEligible,
          "Reason": Remark,
        });

      } else if (productId === 115) {

        filteredColumnBookings.push({
          "BookingDate": Booking_Date.substr(0, 10),
          "LeadID": BookingLeadID,
          "BookingStatus": StatusName,
          "PaymentPeriodicity": PaymentPeriodicity,
          "BookingCount": IsNop,
          "IsCombo": IsCombo ? 1 : 0,
          //"APE": APE,
          "RatedAPE": Wt_APE,
          "PlanCategory": Final_Category,
          "RatedAPE(SinglePay/Annuity APE considered as 10% of APE)": Wt_APE,
          "Eligible": IsEligible,
          "Reason": Remark,
        });

      } else if ([65,67].includes(BUId)) {

        filteredColumnBookings.push({
          "BookingDate": Booking_Date.substr(0, 10),
          "LeadID": BookingLeadID,
          "BookingStatus": StatusName,
          "PaymentPeriodicity": PaymentPeriodicity,
          "Eligible": IsEligible,
          "Reason": Remark,
        });

      } else {
        filteredColumnBookings.push({
          "BookingDate": Booking_Date.substr(0, 10),
          "LeadID": BookingLeadID,
          "BookingStatus": StatusName,
          "PaymentPeriodicity": PaymentPeriodicity,
          //"APE": APE,
          "RatedAPE": Wt_APE,
          "Eligible": IsEligible,
          "Reason": Remark,
        });

      }
    }
    return filteredColumnBookings;
  }


  const handleDownload = () => {
    if (!userId || !productId) return;
    const BASE_URL = process.env.API_BASE_URL;
    services
      .API_BASE_URL_GET(BASE_URL, `jag/GetUserBookingLevelData/${userId}/${productId}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          const bookings = JSON.parse(response.Response);
          setAgentsBookingsData(bookings);
          let filteredColumnBookings = filterBookingColumns(bookings, productId, BUId);
          const fileName = employeeId + "_bookings";
          const exportType = exportFromJSON.types.xls;
          exportFromJSON({ data: filteredColumnBookings, fileName, exportType });
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }
  const handlePrimaryDump = () => {
    if (!userId || !productId) return;
    const BASE_URL = process.env.API_BASE_URL;
    services
      .API_BASE_URL_GET(BASE_URL, `jag/GetUserBookingLevelData/${userId}/${productId}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          const bookings = JSON.parse(response.Response);
          setAgentsBookingsData(bookings);
          let filteredColumnBookings = filterBookingColumns(bookings, productId,BUId);
          const fileName = employeeId + "_bookings";
          const exportType = exportFromJSON.types.xls;
          exportFromJSON({ data: filteredColumnBookings, fileName, exportType });
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }
  const handleSecDump = () => {
    if (!userId || !productId) return;
    const BASE_URL = process.env.API_BASE_URL;
    services
      .API_BASE_URL_GET(BASE_URL, `jag/GetUserSecondaryBookingData/${userId}/${productId}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          const bookings = JSON.parse(response.Response);
          setAgentsBookingsData(bookings);
          let filteredColumnBookings = filterBookingColumns(bookings, productId, BUId);
          const fileName = employeeId + "_sec_bookings";
          const exportType = exportFromJSON.types.xls;
          exportFromJSON({ data: filteredColumnBookings, fileName, exportType });
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  useEffect(() => {
    if (!userId) return;

    services
      .API_GET(`jag/GetAgentsMonthlyData/${userId}?version=${version}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          let obj = JSON.parse(response.Response)
          let date = new Date();
          //let currentMonth = date.getMonth() - 2;
          //obj = obj.slice(0, currentMonth);
          setAgentsMonthlyData(obj);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [props]);

  const webOptions = {
    responsive: true,
    plugins: {
      legend: {
        labels: {
          color: "#25385899",
          font: {
            size: 12,
          }
        }
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: "#25385899",
          font: {
            size: 12,
          },
        }
      },
      y: {
        grid: {
          display: true,
        },
        ticks: {
          color: "#25385899",
          font: {
            size: 12,
          },
        }
      }
    }
  };

  const mobileOptions = {
    maintainAspectRatio: false,
    //responsive: true,
    plugins: {
      legend: {
        labels: {
          color: "#25385899",
          font: {
            size: 8,
          }
        }
      },
      title: {
        display: false,
      },
    },
    scales: {
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: "#25385899",
          font: {
            size: 8,
          },
        }
      },
      y: {
        grid: {
          display: true,
        },
        ticks: {
          color: "#25385899",
          font: {
            size: 8,
          },
        }
      }
    }
  };

  const agentsIsssuedPrevDataSet = () => {
    let data = [];
    if ([65, 67].includes(BUId)) {
      data = agentsMonthlyData.map(item => item.IssuedBKG_PrevYear || 0);
    } else {
      data = agentsMonthlyData.map(item => item.IssuedAPE_PrevYear || 0);
    }
    return data;
  }

  const agentsIssuedDataSet = () => {
    let data = [];
    if ([65, 67].includes(BUId)) {
      data = agentsMonthlyData.map(item => item.IssuedBKG_CurrYear || 0);
    } else {
      data = agentsMonthlyData.map(item => item.IssuedAPE_CurrYear || 0);
    }
    return data;
  }

  const labels = JAG_MONTHS
  const webData = {
    labels,
    datasets: [
      {
        label: [65,67].includes(BUId) ?  'Issued Bookings Last Year': 'Issued APE Last Year',
        data: agentsIsssuedPrevDataSet(),
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        fill: true,
        lineTension: 0.9,
        backgroundColor: '#FFD572',
        borderColor: 'rgba(75,192,192,1)',
        borderCapStyle: 'butt',
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'rgba(75,192,192,1)',
        pointBackgroundColor: '#25385899',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        borderRadius: 15,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        barPercentage: 5,
        barThickness: 15,
        maxBarThickness: 20,
      },
      {
        label: [65,67].includes(BUId) ? 'Issued Bookings Curr Year': 'Issued APE Curr Year',
        data: agentsIssuedDataSet(),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        fill: true,
        lineTension: 0.9,
        backgroundColor: '#6C93F9',
        borderColor: 'rgba(75,192,192,1)',
        borderCapStyle: 'butt',
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'rgba(75,192,192,1)',
        pointBackgroundColor: '#25385899',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        borderRadius: 15,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        barPercentage: 5,
        barThickness: 15,
        maxBarThickness: 20,
      },
    ]
  };

  const mobileData = {
    labels,
    datasets: [
      {
        label: [65,67].includes(BUId) ?  'Issued Bookings Last Year': 'Issued APE Last Year',
        data: agentsIsssuedPrevDataSet(),
        backgroundColor: 'rgba(255, 99, 132, 0.5)',
        fill: false,
        lineTension: 0.9,
        backgroundColor: '#FFD572',
        borderColor: 'rgba(75,192,192,1)',
        borderCapStyle: 'butt',
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'rgba(75,192,192,1)',
        pointBackgroundColor: '#25385899',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        borderRadius: 15,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        barPercentage: 5,
        barThickness: 8,
        maxBarThickness: 15,
      },
      {
        label: [65,67].includes(BUId) ? 'Issued Bookings Curr Year': 'Issued APE Curr Year',
        data: agentsIssuedDataSet(),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        fill: false,
        lineTension: 0.9,
        backgroundColor: '#6C93F9',
        borderColor: 'rgba(75,192,192,1)',
        borderCapStyle: 'butt',
        borderDashOffset: 0.0,
        borderJoinStyle: 'miter',
        pointBorderColor: 'rgba(75,192,192,1)',
        pointBackgroundColor: '#25385899',
        pointBorderWidth: 1,
        pointHoverRadius: 5,
        borderRadius: 15,
        pointHoverBackgroundColor: 'rgba(75,192,192,1)',
        pointHoverBorderColor: 'rgba(220,220,220,1)',
        pointHoverBorderWidth: 2,
        pointRadius: 1,
        pointHitRadius: 10,
        barPercentage: 5,
        barThickness: 8,
        maxBarThickness: 15,
      },
    ]
  };

  return (
    <>
    
        <div className='bar-head'>
        <h2>Month on Month Issuance trends</h2>
          <div>
           <div
            className='download-btn'
            onClick={handlePrimaryDump}
          >
            <img src='/images/jag/download.svg' alt='Download' />
            Primary Booking
          </div>
          {[115, 2, 7,117].includes(productId) && <div
            className='download-btn'
            onClick={handleSecDump}
          >
            <img src='/images/jag/download.svg' alt='Download' />
            Secondary Booking
          </div>}
          </div>
        </div>
        {
          isWebView ?
            <div className='purple-box'>
               <Bar
                options={webOptions}
                data={webData}
                height={100}
              />
          </div>
            :
            <div className='purple-box'>
              <Bar
                options={mobileOptions}
                data={mobileData}
                height={100}
              />
            </div>
        }
        {/* </div> */}

    </>
  );
};

export default ApeBarGraph;