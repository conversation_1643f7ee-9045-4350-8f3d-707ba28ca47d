<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="220" height="144" viewBox="0 0 220 144"><defs><style>.a{isolation:isolate;}.b{fill:#2a4e96;}.c{fill:#233862;}.d{fill:#b56f40;}.e{fill:#99592e;}.f{fill:#f99746;}.g{fill:#ed7d2b;}.h{fill:#2c3a64;}.i{fill:#ebf3fa;}.j{fill:#82b378;}.k{fill:#ffb27d;}.l{fill:#ed975d;}.m{fill:#fff;}.n{fill:#2b478b;}.o{fill:#b64c41;}.p{fill:#afcdfb;}.q{fill:#ffb93e;}.r{fill:#ffa412;}.s{fill:#e2810e;}.t{fill:#6bb7bf;}.u{fill:#86d1cd;}.v{fill:#f1a34f;}.w{fill:#db7714;}.x{fill:#5685d8;}.y{fill:#739af0;}.z{filter:url(#i);}.aa{filter:url(#g);}.ab{filter:url(#e);}.ac{filter:url(#c);}.ad{filter:url(#a);}</style><filter id="a" x="34.643" y="18.766" width="38.136" height="58.067" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="b"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="b"/><feComposite in="SourceGraphic"/></filter><filter id="c" x="17.431" y="14.005" width="39.696" height="38.807" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="d"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="d"/><feComposite in="SourceGraphic"/></filter><filter id="e" x="48.037" y="3.855" width="24.571" height="36.284" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="f"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="f"/><feComposite in="SourceGraphic"/></filter><filter id="g" x="46.888" y="0" width="22.32" height="23.211" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="h"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="h"/><feComposite in="SourceGraphic"/></filter><filter id="i" x="46.289" y="1.013" width="19.805" height="22.198" filterUnits="userSpaceOnUse"><feOffset dy="3" input="SourceAlpha"/><feGaussianBlur stdDeviation="3" result="j"/><feFlood flood-opacity="0.161"/><feComposite operator="in" in2="j"/><feComposite in="SourceGraphic"/></filter></defs><g transform="translate(0 6)"><g class="a" transform="translate(0 0)"><g class="ad" transform="matrix(1, 0, 0, 1, 0, -6)"><path class="b" d="M900.061,521.78a16.746,16.746,0,0,1,4.764-1.86c1.98-.256,8.62-15.458,11.321-16.275A4.778,4.778,0,0,1,920.2,506.2s-3.039,16.616-7.315,22.583-.45,14.933-.45,14.933l-4.4-.863Z" transform="translate(-856.42 -478.88)"/></g><g class="ac" transform="matrix(1, 0, 0, 1, 0, -6)"><path class="c" d="M744.693,477.291s-4.67.624-5.294-2.022-4.806-3.886-4.1-6.234c.871-2.88-.834-5.178.552-7.262,1.528-2.3,4.109-1.68,5.158-3.048a4.908,4.908,0,0,1,5.686-1.857c2.529.982,4.122-.093,5.863.981,1.924,1.187,1.647,3.18,3.195,3.948,1.419.7,1.383,3.351.717,5.62-.48,1.635,1.523,2.714-1.152,4.987-1.114.946-.51,2.7-4.235,3.706C749.409,476.563,745.565,476.449,744.693,477.291Z" transform="translate(-708.79 -436.54)"/></g><path class="d" d="M794.31,601.21a36.445,36.445,0,0,1,.449,6.165l-3.681,3.73,16.349-.562L802.664,606s-2.18-5.382-1.962-7.562S794.31,601.21,794.31,601.21Z" transform="translate(-758.799 -569.44)"/><path class="e" d="M822.269,626.266s1.818,2.8,4.175,2.291,2.4-3.284,2.4-3.284Z" transform="translate(-786.693 -593.924)"/><path class="d" d="M800.766,544.122c-1.723.287-5.306-1.414-5.829-4.246l-.817-4.418a1.793,1.793,0,0,1,1.254-2.176l6.551-1.212a1.793,1.793,0,0,1,1.949,1.583l.771,4.168C805.194,540.791,802.742,543.793,800.766,544.122Z" transform="translate(-761.466 -510.469)"/><path class="b" d="M801.875,709.522a75.46,75.46,0,0,1-11.862,3.46,59.454,59.454,0,0,1-7.529.806c-2.465.126-4.252.107-4.252.107s-1.261.349-1.673-6.11a90.317,90.317,0,0,0-2.252-13.666c-.107-.494-.2-1.083-.295-1.743-.718-5.253-.877-14.921.23-15.646a22.345,22.345,0,0,1,4.756-1.9c.011.061,2.336,2.4,4.564,2.3a7.656,7.656,0,0,0,3.34-3.669,43.056,43.056,0,0,1,6.309.048s.411.951,4.455,18.025C799.271,698.294,803.7,709.208,801.875,709.522Z" transform="translate(-743.057 -637.236)"/><g transform="translate(34.202 71.833)"><path class="c" d="M812.623,1595.715a10.357,10.357,0,0,1,.272-2.539c.271-.634,1.267-3.213,1.267-3.213h3.892a23.875,23.875,0,0,1,.769,3.122c.045.769,0,2.629,0,2.629Z" transform="translate(-812.323 -1529.695)"/><path class="c" d="M992.786,1595.715a10.366,10.366,0,0,0-.271-2.539c-.272-.634-1.267-3.213-1.267-3.213h-3.892a23.917,23.917,0,0,0-.77,3.122c-.045.769,0,2.629,0,2.629Z" transform="translate(-967.775 -1529.695)"/><path class="f" d="M810.589,1023.361s.681,20.793-.337,32.657.008,28.375.008,28.375a7.7,7.7,0,0,0,6.786,0s3.871-22.92,3.847-28.375c-.026-5.809,4.214-31.751,4.214-31.751Z" transform="translate(-809.801 -1023.326)"/><path class="f" d="M918.946,1084.1a7.7,7.7,0,0,1-6.785,0s-3.871-22.92-3.846-28.375c.011-2.453-.739-8.5-1.609-14.663-1.191-8.432-2.605-17.087-2.605-17.087l15.079-.942s-1.244,20.829-.225,32.693S918.946,1084.1,918.946,1084.1Z" transform="translate(-894.076 -1023.029)"/><path class="g" d="M930.217,1146.551c.011-2.453-.739-8.5-1.609-14.662a29.032,29.032,0,0,1,3.1-7.226C934.493,1132.47,930.217,1146.551,930.217,1146.551Z" transform="translate(-915.978 -1113.859)"/></g><path class="c" d="M791.82,524.079a4.828,4.828,0,0,1,4.554-4.141c4.01-.155,4.5.617,5.1,1.508s.562-3.763.562-3.763l-4.859-.516s-5.268,1.727-5.684,2.674c-.426.969-.307,1.016-.307,1.016Z" transform="translate(-758.856 -497.173)"/><g transform="translate(33.387 26.121)"><path class="h" d="M803.821,578.1c-.674.125-2.051.225-2.5-.9a1.079,1.079,0,0,1,.025-.948,1.276,1.276,0,0,1,.909-.6l2.072-.383a.93.93,0,0,1,.88.185,1.378,1.378,0,0,1,.18,1.172c-.18.83-.557,1.272-1.188,1.391C804.169,578.02,804.029,578.061,803.821,578.1Zm-1.544-2.316a1.138,1.138,0,0,0-.813.532.945.945,0,0,0-.018.829,1.523,1.523,0,0,0,1.553.893,4.158,4.158,0,0,0,1.16-.159l.008,0c.571-.106.915-.514,1.082-1.284.037-.173.137-.757-.144-1.047a.805.805,0,0,0-.756-.146Z" transform="translate(-801.221 -575.234)"/></g><g transform="translate(38.38 25.239)"><path class="h" d="M851.334,569.672a3.789,3.789,0,0,1-.382.053,1.524,1.524,0,0,1-1.607-.873,1.379,1.379,0,0,1-.252-1.159.93.93,0,0,1,.756-.488l2.073-.383a1.277,1.277,0,0,1,1.064.237,1.079,1.079,0,0,1,.362.876C853.329,569.148,852.008,569.548,851.334,569.672Zm-1.459-2.332a.8.8,0,0,0-.654.406c-.159.371.143.881.24,1.029.432.66.9.918,1.469.813h.008a4.169,4.169,0,0,0,1.14-.266,1.523,1.523,0,0,0,1.131-1.39.946.946,0,0,0-.313-.768,1.139,1.139,0,0,0-.949-.206Z" transform="translate(-849.037 -566.794)"/></g><g transform="translate(37.166 26.425)"><path class="h" d="M837.407,579.142a.083.083,0,0,1-.063-.15,1.968,1.968,0,0,1,1.568-.288.083.083,0,0,1-.05.158,1.825,1.825,0,0,0-1.422.265A.085.085,0,0,1,837.407,579.142Z" transform="translate(-837.309 -578.637)"/></g><path class="i" d="M835.219,615.792l1.665-.308,1.711-.317s-.482,1.3-1.484,1.463A2.464,2.464,0,0,1,835.219,615.792Z" transform="translate(-798.278 -584.867)"/><path class="j" d="M2474.182,1526.543h-12.743c-.683-.579.473-1.895,1.68-1.983a7.441,7.441,0,0,1,1.945.23c.175-2.329,1.177-12.8,4.951-12.379,4.243.471,0,4.611-1.2,7.062a58.272,58.272,0,0,0-2.255,5.647c.345.075.7.148,1.066.215.562-1.727,2.655-7.536,5.316-7.2,3.125.4,1.869,2.725-.346,4.108-1.625,1.017-3.621,2.462-4.568,3.16a13.529,13.529,0,0,0,2.492.2A5.371,5.371,0,0,1,2474.182,1526.543Z" transform="translate(-2254.815 -1388.543)"/><g transform="translate(5.265 123.823)"><path class="j" d="M582.416,1526.114s-5.554-15.252-9.453-13.952S582.416,1526.114,582.416,1526.114Z" transform="translate(-567.496 -1512.083)"/><path class="j" d="M564.232,1583.617c-.473,0-9.335-9.2-9.926-6.789S564.232,1583.617,564.232,1583.617Z" transform="translate(-551.439 -1569.586)"/><path class="j" d="M549.78,1621.952s-3.665-3.361-8.306-2.486-6.622,1.833-9.537,1.17a4.315,4.315,0,0,0-4.4,1.316Z" transform="translate(-527.533 -1607.921)"/></g><g transform="translate(152.425 24.596)"><path class="k" d="M1958.977,816.165l-4.211-10.439-1.444,6.828s4.465,11.2,7.128,10.247C1961.691,822.355,1958.977,816.165,1958.977,816.165Z" transform="translate(-1953.322 -781.589)"/><path class="c" d="M2062.573,586.292a3.721,3.721,0,0,0-1.161,5.2s-2.631-.481-3.2-1.267a2.087,2.087,0,0,0,.164.492s-2.418-.548-3.21-1.557,3.089-13.1,9.376-9.967c0,0,5.749,1.067,3.5,7.449s-.124,6.946-.124,6.946a3.133,3.133,0,0,1-.955.059s-.375-.27-.4-.522a1.557,1.557,0,0,0,.063.528s-2.354-.391-3.272-1.561S2061.889,587.55,2062.573,586.292Z" transform="translate(-2044.249 -578.676)"/><path class="k" d="M2084.689,746.973l-.011,0C2084.659,746.974,2084.663,746.973,2084.689,746.973Z" transform="translate(-2070.704 -729.082)"/><path class="k" d="M2092.18,682.735c.089.791.194,1.5.241,1.839.105.758-7.085.265-7.544.263.319-.139,1.3-3.233,1.6-4.413.374-1.431.668-2.978.668-2.978l5.078.321.3.019a17.652,17.652,0,0,0-.414,1.953c-.011.082-.024.165-.034.245A13.2,13.2,0,0,0,2092.18,682.735Z" transform="translate(-2070.892 -666.946)"/><path class="c" d="M2093.956,594.033a14.678,14.678,0,0,1-.656-4.612c.119-1.429,2.153-6.3,7.045-4.23a7.148,7.148,0,0,1,2.578,1.841c.906,1.038,2.073,3.069-.877,7.013l-.858,1.369Z" transform="translate(-2078.399 -584.045)"/><path class="l" d="M2111.043,683.957c-3.552-1.041-5.059-5.143-5.059-5.143a10.847,10.847,0,0,1,4.995,2.147c-.012.082-.024.164-.035.245A13.2,13.2,0,0,0,2111.043,683.957Z" transform="translate(-2089.755 -668.168)"/><path class="k" d="M2109.837,614.968s-6.417-1.586-5.368-5.567.928-6.867,5.069-6.022,4.534,2.572,4.507,3.986S2111.532,615.278,2109.837,614.968Z" transform="translate(-2088.297 -600.621)"/><path class="c" d="M2099.245,599.548s-2.985,4.206-5.852,4.186-3.842-1.048-3.842-1.048a7.865,7.865,0,0,0,3.676-3.833S2098.514,596.976,2099.245,599.548Z" transform="translate(-2075.069 -596.117)"/><path class="c" d="M2175.721,609.255a5.831,5.831,0,0,1,1.08,2.486c.05,1.1-.359,3.222.268,3.312,0,0,2.158-2.825,1.152-5.012C2177.155,607.721,2175.721,609.255,2175.721,609.255Z" transform="translate(-2152.078 -605.56)"/><path class="c" d="M2124.289,603.558s-2.541,4.56-6.343,5.229c0,0,4.183-2.107,5.325-5.259S2124.289,603.558,2124.289,603.558Z" transform="translate(-2100.445 -599.641)"/><path class="m" d="M2146.517,689.585s.08.606.466.749a5.6,5.6,0,0,0,1.517.339,5.167,5.167,0,0,0,.489-.537A8,8,0,0,0,2146.517,689.585Z" transform="translate(-2125.979 -677.79)"/><path class="k" d="M2023.763,1093.166q-.123.512-.243,1.012c-.484,2.01-.934,3.875-1.218,5.434-.369,2.03-.456,3.541.028,4.176,1.993,2.613,0,23.348,0,23.348l-2.268,2.337s-3.943-20.472-5.012-23.68c-.486-1.458-1.194-7.686-1.82-14.044q-.047-.477-.094-.954c-.108-1.112-.212-2.22-.313-3.3-.561-6.016-.981-11.167-.981-11.167s4.182-2.941,7.981-3.268a4.787,4.787,0,0,1,5.384,3.268C2026.722,1080.327,2025.187,1087.237,2023.763,1093.166Z" transform="translate(-2005.621 -1020.475)"/><path class="l" d="M2047.557,1093.166a22.325,22.325,0,0,1-9.066-12.784c-.3-3.254,2.263-6.347,5.127-7.324a4.786,4.786,0,0,1,5.383,3.268C2050.517,1080.327,2048.982,1087.237,2047.557,1093.166Z" transform="translate(-2029.416 -1020.475)"/><path class="k" d="M2220.586,890.52l15.271,3.945v1.783s-15.2-.178-16.177-1.871S2220.586,890.52,2220.586,890.52Z" transform="translate(-2191.112 -857.369)"/><path class="k" d="M2109.165,1081.637l-.714,2.947s-20.657-10.67-23.33-12.719c-1.073-.823-2.491-5.294-3.8-10.352-1.957-7.538-3.681-16.379-3.681-16.379s2.9-11.138,10.648-4.321c4.639,4.079,3.917,11.939,3.41,18.053-.338,4.1-.582,7.418.957,8.277,3,1.675,11.805,9.389,14.593,14.7Z" transform="translate(-2064.419 -989.712)"/><path class="f" d="M2008.424,755.531c-.28,6.83-1.3,13.593-4.275,13.158-3.645-.532-4.594-3.934-4.788-6.463a13.254,13.254,0,0,1,.379-3.544,2.11,2.11,0,0,0,2.231-.635c1.118-1.506.5-12.661,1.178-13.957s5.107-2.128,5.107-2.128S2008.7,748.78,2008.424,755.531Z" transform="translate(-1994.449 -724.604)"/><path class="g" d="M2008.58,830.186c-.279,6.83-1.3,13.593-4.275,13.158-3.645-.532-4.594-3.934-4.788-6.463.895-.045,2.47-.263,3.1-1.218.9-1.367,3.692-10.165,3.692-10.165S2007.405,827.473,2008.58,830.186Z" transform="translate(-1994.606 -799.259)"/><path class="f" d="M2056.99,737.506c.988,1.4.443,4.124-1.629,6.314-1.287,1.36-3.03,2.165-5.866,1.746-1.236-.182-4.691-2.132-5.054-4.511-.065-.431-.1-.683-.1-.683a13.561,13.561,0,0,0-1.631,1.872c-.027.134-.162,1.324-.184,1.486a5.731,5.731,0,0,0,.115.919s-7.513,3.286-13.725-.456c0,0-1.771-15.178-.5-17.7.633-1.255,4.178-3.7,5.413-4.778a1.974,1.974,0,0,0,.483-1.446c-.063-.4.4-1.07.779-.948.743.238,2.843,1.277,4.322.881.736-.2,1.67-1.047,2.384-.852.872.238-.049,2.338.445,2.365.681.034,4.361.77,4.67,1.363.107.207,1.164,3.007,2.49,6.713a.05.05,0,0,1,0,.01C2051.891,736.752,2055.54,735.447,2056.99,737.506Z" transform="translate(-2020.022 -704.355)"/><path class="g" d="M2127.345,869.4s-.183-5.79-.651-5.719-7.218,6.568-5.613,9.167S2127.345,869.4,2127.345,869.4Z" transform="translate(-2103.027 -833.384)"/><path class="n" d="M2030.343,968.4a44.936,44.936,0,0,1-6.434,1.334c-4.745.656-11.378.892-17.3-1.334,0,0-.488-8.815.915-13.41s2.06-6.795,2.06-6.795a29.7,29.7,0,0,0,11.335.316,18.478,18.478,0,0,0,2.4-.619s3.523,7.48,4.492,10.685S2030.343,968.4,2030.343,968.4Z" transform="translate(-2000.869 -908.642)"/><path class="c" d="M2139.2,968.4a44.965,44.965,0,0,1-6.434,1.334c-7.028-3.985-3.6-20.753-3.6-20.753q.313-.254.608-.469a18.5,18.5,0,0,0,2.4-.619s3.523,7.48,4.492,10.685S2139.2,968.4,2139.2,968.4Z" transform="translate(-2109.723 -908.642)"/><g transform="translate(8.406 37.718)"><path class="o" d="M2038.672,936.978c-4.167,0-5.884-1.434-6.01-1.531l-.27-1.962c.04.03,5.631,3.386,14.281.172l-.184,1.486A22.7,22.7,0,0,1,2038.672,936.978Z" transform="translate(-2032.392 -933.485)"/></g><path class="k" d="M2374.231,913.912a20.475,20.475,0,0,1,3.916-1.514,8.852,8.852,0,0,1,2.985,0,8.313,8.313,0,0,1-2.54,1.514c-.534.09-1.559.848-1.247.937s1.96-.29,1.982,0-1,.468-1.18.691a2.127,2.127,0,0,1-1.8.356c-1-.134-2.111-.2-2.111-.2Z" transform="translate(-2329.486 -876.816)"/><path class="n" d="M2140.108,1251.972l-.713,2.947s-20.658-10.67-23.33-12.719c-1.073-.824-2.492-5.294-3.806-10.352,1.763.362,5.571.554,10.377-2.648-.338,4.1-.581,7.418.957,8.278,3,1.675,11.806,9.389,14.593,14.7Z" transform="translate(-2095.364 -1160.046)"/><path class="f" d="M2362.153,1420.064l-.019,0a1.29,1.29,0,0,1-.8-.839c-.186-.445-.241-.81-.412-1.25l-.7-1.2c-.73-.96-.985-.735-.985-.735s-.191.244-.473.567a5.888,5.888,0,0,1-1.684,1.465,1.464,1.464,0,0,0-.721.565,1.392,1.392,0,0,0-.216.5s1.5.261,1.367,1c-.254,1.429-.194,1.689-.168,1.736l.009.01s2.555,1.337,3.372,1.6a6.374,6.374,0,0,1,2.684,1.905.84.84,0,0,0,.715.225c.383-.06.88-.183.99-.418Z" transform="translate(-2313.324 -1326.995)"/><path class="c" d="M2033.994,1261.169c1.993,2.613,0,23.349,0,23.349l-2.268,2.336s-3.943-20.472-5.012-23.68c-.486-1.458-1.195-7.686-1.82-14.045,1.89.844,6.5,2.68,10.29,2.43-.484,2.01-.934,3.875-1.218,5.434C2033.6,1259.024,2033.51,1260.534,2033.994,1261.169Z" transform="translate(-2017.285 -1177.856)"/><path class="f" d="M2052.116,1578.921l-1.225.662c-.444.157-.812.2-1.261.375a2.607,2.607,0,0,0-1.187.869c-.345,1.4-.836,1.314-.836,1.314l-4.916.047c-.232-.116.161-1.109.349-1.294.465-.458,1.855-.227,2.363-1.667.286-.809,1.7-3.324,1.7-3.324l.01-.009c.048-.025.309-.077,1.73.219.734.152,1.038-1.338,1.038-1.338a1.393,1.393,0,0,1,.489.229,1.478,1.478,0,0,1,.543.738,5.874,5.874,0,0,0,1.416,1.726c.314.291.553.489.553.489S2053.1,1578.219,2052.116,1578.921Z" transform="translate(-2033.127 -1468.884)"/></g><path class="b" d="M685.044,701.56a18.814,18.814,0,0,0-5.564,4.471c-2.407,2.926-6.951,23.822-6.613,25.178,0,0,4.086-.731,6.295-2.743s7.473-15.486,7.473-15.486Z" transform="translate(-652.899 -662.409)"/><g transform="translate(28.325 28.681)"><rect class="p" width="125.713" height="45.35" transform="translate(0 23.996) rotate(-11.004)"/><rect class="m" width="115.251" height="36.915" transform="matrix(0.982, -0.191, 0.191, 0.982, 5.219, 27.114)"/><path class="p" d="M1287.088,782.716l-12.431,2.417a.53.53,0,0,1-.62-.418l-.025-.13a.53.53,0,0,1,.418-.62l12.432-2.417a.53.53,0,0,1,.62.418l.025.129A.53.53,0,0,1,1287.088,782.716Z" transform="translate(-1219.516 -762.705)"/><path class="p" d="M1270.432,801.065l-18.167,3.533a.53.53,0,0,1-.62-.418l-.025-.129a.53.53,0,0,1,.418-.62l18.168-3.533a.531.531,0,0,1,.62.418l.025.13A.53.53,0,0,1,1270.432,801.065Z" transform="translate(-1199.504 -779.103)"/><g transform="translate(13.807 37.941)"><path class="p" d="M910.161,1094.376l-18.168,3.533a.53.53,0,0,1-.62-.418l-.025-.129a.53.53,0,0,1,.418-.62l18.168-3.533a.531.531,0,0,1,.62.418l.025.13A.531.531,0,0,1,910.161,1094.376Z" transform="translate(-891.339 -1079.174)"/><path class="p" d="M919.014,1110.622l-22.254,4.327a.585.585,0,0,1-.684-.461l0-.022a.585.585,0,0,1,.461-.684l22.254-4.327a.585.585,0,0,1,.684.461l0,.022A.585.585,0,0,1,919.014,1110.622Z" transform="translate(-895.559 -1093.693)"/><path class="p" d="M1155.506,1064.634l-22.254,4.328a.585.585,0,0,1-.684-.462l0-.022a.585.585,0,0,1,.461-.684l22.254-4.327a.585.585,0,0,1,.684.461l0,.022A.585.585,0,0,1,1155.506,1064.634Z" transform="translate(-1106.911 -1052.594)"/><path class="p" d="M1409.459,986.277l-39.969,7.772a.594.594,0,0,1-.7-.469v0a.6.6,0,0,1,.469-.7l39.969-7.772a.6.6,0,0,1,.7.469v0A.6.6,0,0,1,1409.459,986.277Z" transform="translate(-1318.028 -982.566)"/><path class="p" d="M1123.159,1028.68l-32.958,6.409a.6.6,0,0,1-.7-.47h0a.6.6,0,0,1,.47-.7l32.958-6.409a.6.6,0,0,1,.7.47h0A.6.6,0,0,1,1123.159,1028.68Z" transform="translate(-1068.427 -1020.461)"/><path class="p" d="M1463.741,962.451l-32.959,6.409a.6.6,0,0,1-.7-.47h0a.6.6,0,0,1,.47-.7l32.958-6.409a.6.6,0,0,1,.7.47h0A.6.6,0,0,1,1463.741,962.451Z" transform="translate(-1372.804 -961.273)"/></g><path class="f" d="M1809.172,924.073a24.428,24.428,0,0,1-3.7,2.495,3.028,3.028,0,0,0,.186-1.026c-.023-.441-.375-.634-.727-.817-.087-.045-.169-.093-.25-.141a.449.449,0,0,0,.116-.522.38.38,0,0,0-.707.029.56.56,0,0,0-.048.167.424.424,0,0,1-.138-.108c.215-.4.417-.809.6-1.226a.228.228,0,0,0-.087-.261.716.716,0,0,0-.98.135,1.316,1.316,0,0,0-.079,1.272c.009.028.021.055.032.083q-.41.74-.89,1.44l-.09-.089a1.941,1.941,0,0,1-.249-.3,4.331,4.331,0,0,0,.275-1.2.228.228,0,0,0-.325-.219.963.963,0,0,0-.6,1.043,1.285,1.285,0,0,0,.139.437,4.006,4.006,0,0,1-.344.6.211.211,0,0,0,.078.313,3.159,3.159,0,0,1-.653,1.284c-.264.275-.757.484-1.078.157a1.367,1.367,0,0,1-.257-.826,15.017,15.017,0,0,1-.094-2.053,3.136,3.136,0,0,1,.156-.951c.068-.189.164-.475.255-.149a1.782,1.782,0,0,1-.033.569.226.226,0,0,0,.45.035c.034-.34.185-1.053-.2-1.266-.319-.177-.612.077-.76.341a6.041,6.041,0,0,0-.24,3.3c.063.826.372,1.84,1.428,1.578.993-.247,1.358-1.5,1.587-2.361a.227.227,0,0,0-.007-.144l.012-.021.124.124c.059.052.115.107.169.163q-.156.217-.319.43a.229.229,0,0,0,.017.275.529.529,0,0,0,.924-.391.748.748,0,0,0-.076-.309q.471-.677.879-1.393a.711.711,0,0,0,.542.217l.045,0a1.7,1.7,0,0,0,.393.3,2.578,2.578,0,0,1,.391.211c.214.175.151.375.1.624a9.869,9.869,0,0,1-.262.973,2.042,2.042,0,0,0-1.055,1.114,1.136,1.136,0,0,0,.393,1.274.228.228,0,0,0,.339-.112c.256-.63.487-1.269.686-1.92l.02-.062c.4-.228.821-.429,1.217-.663a23.127,23.127,0,0,0,2.991-2.114C1809.693,924.226,1809.4,923.887,1809.172,924.073Zm-5.481-.6c0-.18.092-.511.3-.507q-.14.3-.292.6C1803.7,923.536,1803.692,923.5,1803.692,923.472Zm.572,5.211c-.189-.355.082-.794.393-1.084Q1804.475,928.147,1804.264,928.683Z" transform="translate(-1688.543 -888.702)"/><g transform="translate(41.454 22.457)"><path class="f" d="M1013.192,944.647l-1.942.378-1.456-7.488-2.179,1.17-.307-1.58,3.771-2.24.208-.041Z" transform="translate(-1007.307 -925.236)"/><path class="f" d="M1374.817,870.6a6.706,6.706,0,0,1,.129,1.938,3.654,3.654,0,0,1-.419,1.431,2.637,2.637,0,0,1-.9.952,4,4,0,0,1-2.7.524,2.663,2.663,0,0,1-1.191-.546,3.661,3.661,0,0,1-.932-1.169,6.627,6.627,0,0,1-.61-1.845l-.33-1.7a6.716,6.716,0,0,1-.13-1.939,3.583,3.583,0,0,1,.423-1.428,2.684,2.684,0,0,1,.9-.949,3.989,3.989,0,0,1,2.692-.523,2.679,2.679,0,0,1,1.19.543,3.626,3.626,0,0,1,.931,1.165,6.626,6.626,0,0,1,.61,1.845Zm-2.327-1.6a5.684,5.684,0,0,0-.3-1.078,2.1,2.1,0,0,0-.408-.664,1.083,1.083,0,0,0-.51-.309,1.358,1.358,0,0,0-.6-.009,1.335,1.335,0,0,0-.551.233,1.066,1.066,0,0,0-.351.476,2.155,2.155,0,0,0-.126.767,5.729,5.729,0,0,0,.122,1.113l.434,2.232a5.666,5.666,0,0,0,.31,1.091,2.275,2.275,0,0,0,.411.677,1.05,1.05,0,0,0,.508.316,1.469,1.469,0,0,0,1.156-.225,1.031,1.031,0,0,0,.349-.483,2.277,2.277,0,0,0,.12-.78,5.824,5.824,0,0,0-.125-1.127Z" transform="translate(-1358.512 -856.946)"/><path class="f" d="M1449.107,856.153a6.718,6.718,0,0,1,.13,1.939,3.66,3.66,0,0,1-.419,1.431,2.641,2.641,0,0,1-.9.952,4,4,0,0,1-2.695.524,2.662,2.662,0,0,1-1.191-.546,3.656,3.656,0,0,1-.932-1.169,6.633,6.633,0,0,1-.61-1.845l-.331-1.7a6.709,6.709,0,0,1-.129-1.938,3.588,3.588,0,0,1,.423-1.429,2.685,2.685,0,0,1,.9-.949,3.99,3.99,0,0,1,2.692-.523,2.681,2.681,0,0,1,1.19.543,3.627,3.627,0,0,1,.931,1.165,6.624,6.624,0,0,1,.61,1.845Zm-2.326-1.6a5.678,5.678,0,0,0-.3-1.078,2.1,2.1,0,0,0-.408-.664,1.083,1.083,0,0,0-.51-.309,1.358,1.358,0,0,0-.6-.008,1.333,1.333,0,0,0-.551.233,1.067,1.067,0,0,0-.35.476,2.145,2.145,0,0,0-.126.768,5.687,5.687,0,0,0,.123,1.113l.434,2.231a5.6,5.6,0,0,0,.31,1.091,2.275,2.275,0,0,0,.411.677,1.045,1.045,0,0,0,.508.316,1.468,1.468,0,0,0,1.156-.225,1.03,1.03,0,0,0,.349-.483,2.281,2.281,0,0,0,.12-.78,5.846,5.846,0,0,0-.125-1.127Z" transform="translate(-1424.905 -844.035)"/><path class="f" d="M1523.4,841.707a6.724,6.724,0,0,1,.129,1.938,3.651,3.651,0,0,1-.42,1.431,2.634,2.634,0,0,1-.9.952,4,4,0,0,1-2.7.524,2.663,2.663,0,0,1-1.191-.546,3.654,3.654,0,0,1-.931-1.168,6.624,6.624,0,0,1-.61-1.845l-.331-1.7a6.711,6.711,0,0,1-.129-1.939,3.591,3.591,0,0,1,.423-1.428,2.686,2.686,0,0,1,.9-.949,3.988,3.988,0,0,1,2.692-.523,2.676,2.676,0,0,1,1.19.543,3.629,3.629,0,0,1,.931,1.165,6.639,6.639,0,0,1,.61,1.845Zm-2.327-1.6a5.671,5.671,0,0,0-.3-1.078,2.09,2.09,0,0,0-.408-.663,1.082,1.082,0,0,0-.51-.309,1.363,1.363,0,0,0-.6-.009,1.338,1.338,0,0,0-.551.233,1.067,1.067,0,0,0-.35.476,2.132,2.132,0,0,0-.126.767,5.658,5.658,0,0,0,.122,1.113l.434,2.231a5.6,5.6,0,0,0,.31,1.091,2.281,2.281,0,0,0,.411.677,1.05,1.05,0,0,0,.508.316,1.469,1.469,0,0,0,1.156-.225,1.035,1.035,0,0,0,.349-.483,2.294,2.294,0,0,0,.12-.78,5.808,5.808,0,0,0-.125-1.127Z" transform="translate(-1491.297 -831.125)"/><path class="f" d="M1629.932,820.99a6.718,6.718,0,0,1,.13,1.939,3.656,3.656,0,0,1-.42,1.431,2.636,2.636,0,0,1-.9.952,4,4,0,0,1-2.7.524,2.663,2.663,0,0,1-1.191-.546,3.649,3.649,0,0,1-.932-1.169,6.613,6.613,0,0,1-.61-1.845l-.331-1.7a6.719,6.719,0,0,1-.129-1.938,3.587,3.587,0,0,1,.423-1.429,2.68,2.68,0,0,1,.9-.949,3.99,3.99,0,0,1,2.692-.524,2.677,2.677,0,0,1,1.19.543,3.62,3.62,0,0,1,.931,1.165,6.644,6.644,0,0,1,.61,1.845Zm-2.327-1.6a5.683,5.683,0,0,0-.3-1.078,2.1,2.1,0,0,0-.408-.664,1.08,1.08,0,0,0-.51-.309,1.357,1.357,0,0,0-.6-.009,1.338,1.338,0,0,0-.552.233,1.068,1.068,0,0,0-.35.476,2.158,2.158,0,0,0-.127.768,5.709,5.709,0,0,0,.123,1.113l.434,2.231a5.624,5.624,0,0,0,.31,1.091,2.283,2.283,0,0,0,.411.677,1.046,1.046,0,0,0,.508.316,1.469,1.469,0,0,0,1.156-.225,1.031,1.031,0,0,0,.349-.483,2.29,2.29,0,0,0,.12-.78,5.809,5.809,0,0,0-.125-1.127Z" transform="translate(-1586.507 -812.61)"/><path class="f" d="M1704.223,806.544a6.724,6.724,0,0,1,.129,1.938,3.653,3.653,0,0,1-.42,1.431,2.634,2.634,0,0,1-.9.952,4,4,0,0,1-2.7.524,2.668,2.668,0,0,1-1.191-.546,3.667,3.667,0,0,1-.932-1.169,6.622,6.622,0,0,1-.609-1.845l-.331-1.7a6.705,6.705,0,0,1-.129-1.938,3.58,3.58,0,0,1,.423-1.429,2.685,2.685,0,0,1,.9-.949,3.987,3.987,0,0,1,2.692-.524,2.682,2.682,0,0,1,1.19.543,3.632,3.632,0,0,1,.931,1.165,6.628,6.628,0,0,1,.609,1.845Zm-2.327-1.6a5.682,5.682,0,0,0-.3-1.078,2.1,2.1,0,0,0-.408-.664,1.085,1.085,0,0,0-.51-.309,1.36,1.36,0,0,0-.6-.009,1.338,1.338,0,0,0-.551.233,1.068,1.068,0,0,0-.35.476,2.14,2.14,0,0,0-.126.767,5.666,5.666,0,0,0,.122,1.113l.434,2.232a5.612,5.612,0,0,0,.31,1.091,2.271,2.271,0,0,0,.41.677,1.051,1.051,0,0,0,.508.316,1.468,1.468,0,0,0,1.156-.225,1.026,1.026,0,0,0,.349-.483,2.283,2.283,0,0,0,.12-.78,5.8,5.8,0,0,0-.125-1.127Z" transform="translate(-1652.9 -799.7)"/><path class="f" d="M1778.513,792.1a6.719,6.719,0,0,1,.129,1.938,3.648,3.648,0,0,1-.419,1.431,2.636,2.636,0,0,1-.9.952,4,4,0,0,1-2.7.524,2.661,2.661,0,0,1-1.191-.546,3.661,3.661,0,0,1-.932-1.169,6.637,6.637,0,0,1-.61-1.845l-.331-1.7a6.72,6.72,0,0,1-.129-1.938,3.583,3.583,0,0,1,.423-1.429,2.684,2.684,0,0,1,.9-.949,3.988,3.988,0,0,1,2.692-.523,2.681,2.681,0,0,1,1.191.543,3.629,3.629,0,0,1,.931,1.165,6.62,6.62,0,0,1,.61,1.845Zm-2.327-1.6a5.672,5.672,0,0,0-.3-1.078,2.1,2.1,0,0,0-.408-.664,1.083,1.083,0,0,0-.51-.309,1.358,1.358,0,0,0-.6-.009,1.336,1.336,0,0,0-.551.233,1.067,1.067,0,0,0-.35.476,2.141,2.141,0,0,0-.126.767,5.668,5.668,0,0,0,.122,1.113l.434,2.231a5.588,5.588,0,0,0,.31,1.091,2.269,2.269,0,0,0,.41.677,1.05,1.05,0,0,0,.508.316,1.466,1.466,0,0,0,1.156-.225,1.03,1.03,0,0,0,.349-.483,2.285,2.285,0,0,0,.12-.78,5.848,5.848,0,0,0-.125-1.127Z" transform="translate(-1719.292 -786.79)"/></g><g transform="translate(111.427 8.643)"><g transform="translate(1.504 5.365)"><path class="q" d="M1828.331,736.233l.06.311a.137.137,0,0,1-.005.07.11.11,0,0,1-.041.054.085.085,0,0,1-.018.01l-.018.005-4.331.832a.114.114,0,0,1-.126-.094v-.006l-.06-.311V737.1a.115.115,0,0,1,.083-.134l4.33-.831.019,0,.02,0a.114.114,0,0,1,.058.035A.136.136,0,0,1,1828.331,736.233Z" transform="translate(-1823.788 -736.132)"/><path class="r" d="M1826.285,740.808l-2.32.446a.11.11,0,0,1-.115-.1v-.006l-.059-.31v-.006a.11.11,0,0,1,.072-.131l2.035-.391A1.04,1.04,0,0,0,1826.285,740.808Z" transform="translate(-1823.786 -739.868)"/><g transform="translate(1.571)"><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 2.373, 0.092)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.206, 0.124)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.006 0.162) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.7 0.221) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.376, 0.283)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.88, 0.379)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.415 0.468) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0, 0.548)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.531, 0.062)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.636, 0.042)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.737, 0.022)"/><path class="r" d="M1865.027,736.139l.106.55.031-.006.018-.005-.1-.546-.019,0Z" transform="translate(-1862.214 -736.132)"/><path class="r" d="M1865.685,736.155l.1.533a.107.107,0,0,0,.041-.054l-.085-.444A.111.111,0,0,0,1865.685,736.155Z" transform="translate(-1862.802 -736.153)"/></g></g><g transform="translate(0.935 4.904)"><path class="q" d="M1822.979,731.9l.06.31a.13.13,0,0,1-.006.07.107.107,0,0,1-.04.054.07.07,0,0,1-.018.01l-.018.006-4.33.831a.115.115,0,0,1-.127-.093.027.027,0,0,1,0-.006l-.06-.31v-.006a.115.115,0,0,1,.083-.134l4.33-.831h.019l.02,0h.006l.006,0h.006l.006,0,.006,0,.005,0h0l0,0,.009.007,0,0,0,0,.005.005A.138.138,0,0,1,1822.979,731.9Z" transform="translate(-1818.436 -731.795)"/><path class="r" d="M1820.933,736.472l-2.32.446a.11.11,0,0,1-.115-.1v-.006l-.06-.31V736.5a.11.11,0,0,1,.072-.131l2.035-.391.005.013A1.038,1.038,0,0,0,1820.933,736.472Z" transform="translate(-1818.434 -735.533)"/><g transform="translate(1.571 0)"><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.373, 0.092)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.206 0.124) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.006, 0.162)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 1.7, 0.221)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.376, 0.283)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.879 0.379) rotate(-10.931)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.415, 0.468)"/><rect class="r" width="0.05" height="0.56" transform="translate(0 0.548) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 2.531, 0.062)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.636, 0.042)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.737, 0.022)"/><path class="r" d="M1859.676,731.8l.106.55.031-.006.019-.005-.105-.546-.019,0Z" transform="translate(-1856.863 -731.796)"/><path class="r" d="M1860.334,731.819l.1.534a.112.112,0,0,0,.04-.054l-.085-.444A.111.111,0,0,0,1860.334,731.819Z" transform="translate(-1857.451 -731.817)"/></g></g><path class="q" d="M1824.113,726.448l.06.31a.128.128,0,0,1,0,.07.111.111,0,0,1-.041.054.083.083,0,0,1-.018.01l-.018.005-4.331.831a.091.091,0,0,1-.051,0,.124.124,0,0,1-.074-.084v-.005l0-.006-.06-.31a.058.058,0,0,1,0-.006.113.113,0,0,1,.083-.133l4.331-.832.019,0,.02,0a.115.115,0,0,1,.058.035A.132.132,0,0,1,1824.113,726.448Z" transform="translate(-1818.515 -722.022)"/><path class="r" d="M1822.067,731.023l-2.32.445a.077.077,0,0,1-.046-.005.125.125,0,0,1-.068-.085l0-.005v-.006l-.06-.311a.031.031,0,0,1,0-.006.109.109,0,0,1,.072-.131l2.035-.391a1.057,1.057,0,0,0,.375.484Z" transform="translate(-1818.512 -725.759)"/><path class="q" d="M1824.092,721.088l.06.31a.133.133,0,0,1-.005.07.111.111,0,0,1-.04.054l-.018.01-.018.005-4.331.831a.115.115,0,0,1-.127-.094.049.049,0,0,0,0-.006l-.06-.311a.054.054,0,0,0,0-.006.116.116,0,0,1,.071-.13l.012,0,4.33-.831.019,0,.02,0a.112.112,0,0,1,.058.035A.131.131,0,0,1,1824.092,721.088Z" transform="translate(-1818.496 -717.232)"/><path class="r" d="M1822.047,725.664l-2.32.445a.11.11,0,0,1-.115-.1.048.048,0,0,0,0-.006l-.06-.311s0,0,0-.006a.114.114,0,0,1,.061-.129l.011,0,2.035-.391h0A1.041,1.041,0,0,0,1822.047,725.664Z" transform="translate(-1818.494 -720.969)"/><path class="q" d="M1820.1,716.5l.06.311a.133.133,0,0,1-.005.07.112.112,0,0,1-.04.054l-.018.01-.006,0h-.012l-4.33.831a.114.114,0,0,1-.126-.093l0-.006-.06-.31s0,0,0-.006a.044.044,0,0,1,0,0,.119.119,0,0,1,.059-.12h0a.087.087,0,0,1,.022-.007l4.33-.831h.02l.02,0a.111.111,0,0,1,.058.035A.133.133,0,0,1,1820.1,716.5Z" transform="translate(-1814.93 -713.131)"/><path class="r" d="M1818.056,721.075l-2.32.445a.11.11,0,0,1-.115-.1.028.028,0,0,1,0-.006l-.06-.31a.021.021,0,0,0,0-.006.024.024,0,0,0,0-.005.117.117,0,0,1,.053-.119l0,0,.016-.005,2.035-.391v0a1.044,1.044,0,0,0,.388.491Z" transform="translate(-1814.928 -716.868)"/><path class="q" d="M1819.029,711.38l.06.31a.129.129,0,0,1-.005.07.112.112,0,0,1-.041.054l-.014.008h0l0,0-.018.005-4.331.831-.014,0h-.005a.118.118,0,0,1-.108-.1v-.006l-.06-.311a.055.055,0,0,1,0-.006.114.114,0,0,1,.083-.134l4.331-.831h.019l.02,0h.006l.006,0,.006,0,.005,0,.006,0,0,0h0l0,0,.009.008,0,0,0,0,.005.005A.135.135,0,0,1,1819.029,711.38Z" transform="translate(-1813.971 -708.556)"/><path class="r" d="M1816.983,715.956l-2.32.446h-.012a.114.114,0,0,1-.1-.1V716.3l-.06-.311a.03.03,0,0,1,0-.006.11.11,0,0,1,.071-.131l2.035-.391.005.013a1.041,1.041,0,0,0,.38.479Z" transform="translate(-1813.969 -712.294)"/><path class="q" d="M1820.162,705.932l.06.311a.132.132,0,0,1-.005.07.11.11,0,0,1-.041.054.09.09,0,0,1-.018.01l-.019.005-4.33.831a.093.093,0,0,1-.051,0,.124.124,0,0,1-.074-.084v-.005a.017.017,0,0,1,0-.006l-.06-.311V706.8a.114.114,0,0,1,.083-.133l4.33-.831.019,0,.02,0a.113.113,0,0,1,.058.035A.137.137,0,0,1,1820.162,705.932Z" transform="translate(-1814.984 -703.687)"/><path class="q" d="M1822.458,700.1l.06.31a.141.141,0,0,1,0,.07.113.113,0,0,1-.041.054.081.081,0,0,1-.018.01l-.018.005-4.33.832a.1.1,0,0,1-.051,0,.124.124,0,0,1-.074-.084v-.005a.02.02,0,0,0,0-.006l-.059-.311v-.006a.115.115,0,0,1,.083-.134l4.33-.831h.019l.021,0a.113.113,0,0,1,.058.035A.133.133,0,0,1,1822.458,700.1Z" transform="translate(-1817.036 -698.479)"/><path class="r" d="M1818.116,710.507l-2.32.445a.077.077,0,0,1-.046-.005.126.126,0,0,1-.068-.085.012.012,0,0,1,0-.005l0-.006-.06-.31v-.006a.11.11,0,0,1,.072-.131l2.035-.391a1.057,1.057,0,0,0,.374.484Z" transform="translate(-1814.981 -707.424)"/><path class="r" d="M1820.449,704.7l-2.32.445a.11.11,0,0,1-.115-.1.031.031,0,0,1,0-.006l-.06-.311v-.006a.115.115,0,0,1,.061-.128l.011,0,2.035-.391h0A1.043,1.043,0,0,0,1820.449,704.7Z" transform="translate(-1817.066 -702.231)"/><path class="q" d="M1816.152,695.983l.06.311a.128.128,0,0,1,0,.07.112.112,0,0,1-.04.054l-.018.01-.006,0h-.012l-4.331.831a.114.114,0,0,1-.126-.093.047.047,0,0,1,0-.006l-.06-.31a.035.035,0,0,0,0-.006.114.114,0,0,1,.083-.134l4.331-.832h.019l.021,0,.01,0,.006,0h0l.006,0h0l.005,0,.012.008,0,0h0l0,0h0l0,0,.005.006A.129.129,0,0,1,1816.152,695.983Z" transform="translate(-1811.4 -694.796)"/><path class="r" d="M1814.106,700.558l-2.32.445a.11.11,0,0,1-.115-.1.029.029,0,0,0,0-.006l-.06-.31v-.006a.109.109,0,0,1,.072-.131l2.035-.391.005.012a1.037,1.037,0,0,0,.385.482Z" transform="translate(-1811.398 -698.532)"/><path class="q" d="M1818.175,690.355l.06.31a.133.133,0,0,1-.005.07.11.11,0,0,1-.04.054l-.018.01-.018.005-4.33.831a.1.1,0,0,1-.049,0,.125.125,0,0,1-.076-.085.024.024,0,0,1,0,0v-.006l-.06-.31a.052.052,0,0,1,0-.006.116.116,0,0,1,.07-.13l.013,0,4.33-.831.019,0,.02,0a.11.11,0,0,1,.058.035A.136.136,0,0,1,1818.175,690.355Z" transform="translate(-1813.208 -689.766)"/><path class="r" d="M1816.128,694.93l-2.32.446a.077.077,0,0,1-.045,0,.127.127,0,0,1-.07-.086.025.025,0,0,1,0-.005v-.006l-.06-.31a.021.021,0,0,1,0-.006.114.114,0,0,1,.06-.128l.011,0,2.035-.391h0a1.053,1.053,0,0,0,.375.484Z" transform="translate(-1813.205 -693.503)"/><path class="q" d="M1814.185,685.766l.06.311a.133.133,0,0,1-.005.07.112.112,0,0,1-.041.054l-.018.01-.006,0h-.012l-4.33.831a.115.115,0,0,1-.127-.093v-.006l-.06-.31v-.006a.114.114,0,0,1,.083-.133l4.33-.831.019,0,.02,0a.11.11,0,0,1,.058.035A.132.132,0,0,1,1814.185,685.766Z" transform="translate(-1809.642 -685.665)"/><path class="r" d="M1812.139,690.341l-2.32.445a.11.11,0,0,1-.115-.1v-.006l-.06-.31a.027.027,0,0,0,0-.006.109.109,0,0,1,.072-.131l2.035-.391a1.042,1.042,0,0,0,.39.495Z" transform="translate(-1809.639 -689.402)"/><g transform="translate(1.571)"><rect class="r" width="0.05" height="0.56" transform="translate(3.428 4.417) rotate(-10.921)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.262 4.449) rotate(-10.851)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.062, 4.487)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.756, 4.546)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.432 4.608) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.935 4.703) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.47, 4.793)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.055, 4.872)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.587, 4.386)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.692, 4.366)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.793 4.347) rotate(-10.859)"/><path class="r" d="M1860.81,726.354l.105.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1856.941 -722.022)"/><path class="r" d="M1861.467,726.37l.1.534a.112.112,0,0,0,.041-.054l-.085-.444A.114.114,0,0,0,1861.467,726.37Z" transform="translate(-1857.529 -722.043)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.933, 4.134)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.468, 4.223)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.053 4.303) rotate(-10.837)"/><path class="r" d="M1855.658,708.314l-.106-.55-.043.008.007,0-.105-.546h-.01l-.1-.533.02,0,.1.528a.111.111,0,0,0,.041-.054l-.085-.444a.118.118,0,0,0-.047-.032l.017,0-.106-.55-.05.01.1.537-.016,0-.031.006.106.55.009,0,.105.546.031-.006h.005l.106.549Z" transform="translate(-1851.919 -703.957)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.689, 3.797)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.791, 3.777)"/><path class="r" d="M1860.789,720.994l.106.55.031-.006.018-.005-.1-.546-.019,0Z" transform="translate(-1856.923 -717.232)"/><path class="r" d="M1861.447,721.011l.1.534a.112.112,0,0,0,.041-.054l-.086-.444A.11.11,0,0,0,1861.447,721.011Z" transform="translate(-1857.51 -717.253)"/><path class="r" d="M1851.849,713.152l-.106-.55-.009,0-.105-.546-.049.009.106.55.01,0,.1.545-.048.009.106.55.049-.009-.105-.549Z" transform="translate(-1848.693 -709.252)"/><path class="r" d="M1848.3,713.263l-.009,0-.1-.546-.049.009.106.55.009,0,.105.546.012,0,.106.549.049-.01-.106-.55-.012,0Z" transform="translate(-1845.613 -709.843)"/><path class="r" d="M1857.457,716.421l.1.534a.112.112,0,0,0,.04-.054l-.085-.444A.111.111,0,0,0,1857.457,716.421Z" transform="translate(-1853.945 -713.151)"/><path class="r" d="M1845.3,713.272l-.049.009.106.55.009,0,.1.545h-.006l.106.55.049-.009-.106-.549h.006l-.106-.55-.009,0Z" transform="translate(-1843.038 -710.337)"/><path class="r" d="M1842.256,713.857l-.049.01.106.55.009,0,.105.546.049-.009-.106-.55-.009,0Z" transform="translate(-1840.316 -710.86)"/><path class="r" d="M1837.586,714.753l-.049.01.106.55.009,0,.1.546.049-.009-.106-.55-.009,0Z" transform="translate(-1836.142 -711.661)"/><path class="r" d="M1833.213,715.593l-.05.009.106.55.009,0,.105.546.049-.01-.106-.55-.009,0Z" transform="translate(-1832.234 -712.412)"/><path class="r" d="M1829.309,716.343l-.049.01.106.55.009,0,.1.546.05-.009-.106-.55-.009,0Z" transform="translate(-1828.745 -713.081)"/><path class="r" d="M1853.122,711.771l-.049.009.106.55.009,0,.1.545h-.006l.106.55.049-.01-.106-.549h.007l-.106-.55-.009,0Z" transform="translate(-1850.026 -708.996)"/><path class="r" d="M1852.906,707.137l-.039.008-.1-.536-.049.009.106.55.04-.008.1.536.009,0,.105.546.049-.009-.106-.55-.009,0Z" transform="translate(-1849.706 -704.382)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.842, 2.268)"/><path class="r" d="M1849.371,707.816l-.024,0,.106.55.009,0,.105.546.049-.01-.106-.55-.009,0-.1-.532.024,0-.106-.55-.049.009Z" transform="translate(-1846.626 -704.974)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.336 2.365) rotate(-10.894)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.012 2.427) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.515 2.522) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.051, 2.612)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.636 2.691) rotate(-10.851)"/><path class="r" d="M1854.308,706.868l-.018,0,.106.55.009,0,.1.545-.046.009.106.55.049-.01-.106-.549.047-.009-.106-.55-.009,0-.1-.532.017,0-.106-.55-.049.009Z" transform="translate(-1851.038 -704.126)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.373 2.166) rotate(-10.859)"/><path class="r" d="M1857.013,706.377l-.105-.546-.019,0-.031.006.106.55.031-.006Z" transform="translate(-1853.409 -703.687)"/><path class="r" d="M1857.66,706.333l-.086-.444a.11.11,0,0,0-.058-.035l.1.534A.112.112,0,0,0,1857.66,706.333Z" transform="translate(-1853.998 -703.708)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.256 1.618) rotate(-10.919)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.584 1.747) rotate(-10.837)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.26, 1.809)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.763, 1.905)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.299, 1.994)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.883, 2.074)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.415, 1.588)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.52 1.567) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.621, 1.548)"/><path class="r" d="M1859.191,700.026l.106.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1855.494 -698.493)"/><path class="r" d="M1859.85,700.042l.1.534a.113.113,0,0,0,.041-.054l-.086-.444A.11.11,0,0,0,1859.85,700.042Z" transform="translate(-1856.083 -698.514)"/><path class="r" d="M1847.052,688.088l.049-.01-.106-.55-.048.009-.1-.536.048-.009-.106-.55-.049.009.106.549-.048.009.106.55.048-.009Z" transform="translate(-1844.363 -686.36)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.415, 1.21)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.585, 1.37)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.089, 1.465)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.624 1.554) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.209, 1.634)"/><path class="r" d="M1848.544,687.8l.044-.008.105.549.049-.009-.106-.55-.044.008-.106-.549h-.007l-.1-.536h.007l-.106-.55-.049.009.106.549h-.007l.106.55h.006Z" transform="translate(-1845.697 -686.104)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.845, 1.128)"/><path class="r" d="M1850.53,686.757l.009,0-.106-.55-.043.008.007,0-.105-.546-.018,0-.031.006.106.55.031-.006.006,0,.1.537h-.006l-.031.006.106.549-.027.005-.106-.549-.046.009-.1-.536.046-.009-.106-.55-.049.009.106.549-.046.009.106.55.046-.009.1.537.037-.007.106.549.049-.01-.106-.549.02,0,.018-.005-.1-.535.019,0,.1.529a.11.11,0,0,0,.04-.054l-.085-.444A.113.113,0,0,0,1850.53,686.757Z" transform="translate(-1847.428 -685.665)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.304, 0.867)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.839, 0.956)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.424 1.036) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.06, 0.529)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 3.161, 0.51)"/><path class="r" d="M1854.871,690.261l.106.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1851.633 -689.766)"/><path class="r" d="M1855.672,690.756l-.086-.444a.111.111,0,0,0-.058-.035l.1.534A.11.11,0,0,0,1855.672,690.756Z" transform="translate(-1852.222 -689.787)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.206 0.124) rotate(-10.859)"/><path class="r" d="M1843.339,687.1l-.049.009.106.55.012,0,.1.536-.012,0,.106.55.049-.009-.1-.537.012,0-.106-.55-.013,0Z" transform="translate(-1841.284 -686.951)"/><path class="r" d="M1840.515,688.216h-.006l.106.55h.005l.1.537.049-.01-.106-.55h-.006l-.1-.536h.006l-.106-.55-.049.01Z" transform="translate(-1838.709 -687.446)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 1.377, 0.283)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.88, 0.379)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.415 0.468) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0, 0.548)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.636, 0.042)"/><path class="r" d="M1851.539,685.688l.1.534a.11.11,0,0,0,.041-.054l-.085-.444A.113.113,0,0,0,1851.539,685.688Z" transform="translate(-1848.655 -685.685)"/></g></g><g transform="translate(106.419 10.743)"><g transform="translate(1.295 4.279)"><path class="q" d="M1779.247,745.778l.06.311a.134.134,0,0,1-.005.07.111.111,0,0,1-.041.054l-.018.01-.018.005-4.33.831a.114.114,0,0,1-.127-.093v-.006l-.06-.311s0,0,0-.006a.114.114,0,0,1,.083-.134l4.33-.831.019,0,.02,0a.114.114,0,0,1,.058.035A.136.136,0,0,1,1779.247,745.778Z" transform="translate(-1774.704 -745.677)"/><path class="r" d="M1777.2,750.353l-2.32.445a.11.11,0,0,1-.115-.1V750.7l-.06-.31s0,0,0-.006a.11.11,0,0,1,.071-.131l2.035-.391A1.045,1.045,0,0,0,1777.2,750.353Z" transform="translate(-1774.702 -749.414)"/><g transform="translate(1.571)"><rect class="r" width="0.05" height="0.56" transform="translate(2.373 0.092) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.206, 0.124)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 2.006, 0.162)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 1.7, 0.221)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.376 0.283) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.88 0.379) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.415 0.468) rotate(-10.931)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0, 0.548)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.531, 0.062)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.636 0.042) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.737 0.022) rotate(-10.859)"/><path class="r" d="M1815.943,745.684l.106.55.03-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1813.13 -745.677)"/><path class="r" d="M1816.6,745.7l.1.534a.111.111,0,0,0,.041-.054l-.086-.444A.113.113,0,0,0,1816.6,745.7Z" transform="translate(-1813.719 -745.698)"/></g></g><g transform="translate(0.726 3.818)"><path class="q" d="M1773.9,741.441l.06.311a.135.135,0,0,1-.005.07.112.112,0,0,1-.041.054l-.018.01-.018.005-4.33.831a.114.114,0,0,1-.126-.093.025.025,0,0,1,0-.006l-.06-.31a.047.047,0,0,0,0-.006.114.114,0,0,1,.083-.134l4.331-.831h.019l.021,0h.006l.006,0h.007l.005,0,.006,0,0,0h0l0,0,.01.008,0,0,0,0,0,.005A.136.136,0,0,1,1773.9,741.441Z" transform="translate(-1769.352 -741.34)"/><path class="r" d="M1771.849,746.017l-2.32.446a.11.11,0,0,1-.115-.1.028.028,0,0,1,0-.006l-.06-.31v-.006a.11.11,0,0,1,.072-.131l2.035-.391.005.013A1.038,1.038,0,0,0,1771.849,746.017Z" transform="translate(-1769.35 -745.077)"/><g transform="translate(1.571)"><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.373, 0.092)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.206, 0.124)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.188, 0.188, 0.982, 2.007, 0.163)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.701 0.221) rotate(-10.837)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.376, 0.283)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.88, 0.379)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.415 0.468) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(0 0.548) rotate(-10.919)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.532 0.062) rotate(-10.851)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.636, 0.042)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.738, 0.022)"/><path class="r" d="M1810.592,741.348l.106.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1807.779 -741.34)"/><path class="r" d="M1811.25,741.364l.1.534a.108.108,0,0,0,.04-.054l-.085-.444A.112.112,0,0,0,1811.25,741.364Z" transform="translate(-1808.366 -741.361)"/></g></g><path class="q" d="M1775.029,735.993l.06.311a.137.137,0,0,1-.005.07.111.111,0,0,1-.04.054.085.085,0,0,1-.018.01l-.018.005-4.33.831a.1.1,0,0,1-.051,0,.124.124,0,0,1-.074-.084.043.043,0,0,1,0-.005v-.006l-.06-.311v-.006a.115.115,0,0,1,.083-.134l4.33-.831.019,0,.02,0a.111.111,0,0,1,.058.035A.135.135,0,0,1,1775.029,735.993Z" transform="translate(-1769.639 -732.653)"/><path class="r" d="M1772.983,740.568l-2.32.445a.078.078,0,0,1-.046-.005.126.126,0,0,1-.068-.085l0-.005v-.006l-.06-.31a.036.036,0,0,0,0-.006.11.11,0,0,1,.072-.132l2.035-.391a1.057,1.057,0,0,0,.374.484Z" transform="translate(-1769.637 -736.39)"/><path class="q" d="M1775.008,730.633l.06.311a.134.134,0,0,1-.005.07.11.11,0,0,1-.04.054l-.018.01-.018.005-4.331.831a.114.114,0,0,1-.126-.093l0-.006-.059-.311V731.5a.117.117,0,0,1,.071-.13l.012,0,4.33-.831.019,0,.02,0a.111.111,0,0,1,.058.035A.132.132,0,0,1,1775.008,730.633Z" transform="translate(-1769.621 -727.863)"/><path class="r" d="M1772.963,735.208l-2.32.445a.11.11,0,0,1-.115-.1s0,0,0-.006l-.06-.31s0,0,0-.006a.114.114,0,0,1,.061-.129l.011,0,2.035-.391h0A1.039,1.039,0,0,0,1772.963,735.208Z" transform="translate(-1769.619 -731.6)"/><path class="q" d="M1771.019,726.044l.06.31a.136.136,0,0,1-.005.07.11.11,0,0,1-.041.054.078.078,0,0,1-.018.01l-.006,0H1771l-4.331.831a.115.115,0,0,1-.127-.093v-.006l-.06-.31v-.006a.032.032,0,0,0,0,0,.119.119,0,0,1,.058-.12h0a.087.087,0,0,1,.022-.007l4.331-.831h.019l.021,0a.112.112,0,0,1,.058.035A.138.138,0,0,1,1771.019,726.044Z" transform="translate(-1766.056 -723.762)"/><path class="r" d="M1768.972,730.619l-2.32.445a.11.11,0,0,1-.115-.1v-.006l-.06-.31s0,0,0-.006v-.005a.116.116,0,0,1,.052-.119l0,0,.016-.005,2.035-.391,0,0a1.04,1.04,0,0,0,.388.491Z" transform="translate(-1766.053 -727.499)"/><path class="q" d="M1769.945,720.925l.059.311a.13.13,0,0,1,0,.07.107.107,0,0,1-.041.054l-.014.008h0l0,0-.018.005-4.331.831-.014,0h-.005a.118.118,0,0,1-.107-.095v-.006l-.06-.31s0,0,0-.006a.115.115,0,0,1,.083-.134l4.331-.832h.019l.021,0h.006l.006,0,.007,0,.006,0,.006,0,0,0h0l0,0,.01.007,0,0,0,0,0,.005A.135.135,0,0,1,1769.945,720.925Z" transform="translate(-1765.096 -719.187)"/><path class="r" d="M1767.9,725.5l-2.32.445h-.012a.114.114,0,0,1-.1-.1v-.006l-.06-.311v-.006a.109.109,0,0,1,.072-.131l2.035-.391.005.013a1.04,1.04,0,0,0,.38.479Z" transform="translate(-1765.094 -722.924)"/><path class="q" d="M1771.078,715.477l.06.311a.134.134,0,0,1,0,.07.111.111,0,0,1-.041.054l-.018.01-.018.006-4.331.831a.094.094,0,0,1-.051,0,.125.125,0,0,1-.074-.084s0,0,0-.005l0-.006-.059-.311v-.006a.115.115,0,0,1,.083-.134l4.331-.831.019,0,.021,0a.112.112,0,0,1,.057.035A.134.134,0,0,1,1771.078,715.477Z" transform="translate(-1766.109 -714.318)"/><path class="q" d="M1773.374,709.649l.06.311a.131.131,0,0,1-.005.07.11.11,0,0,1-.041.054l-.018.01-.018.005-4.331.831a.1.1,0,0,1-.051,0,.124.124,0,0,1-.074-.084v-.005s0,0,0-.006l-.06-.311v-.006a.115.115,0,0,1,.083-.134l4.33-.831.019,0,.02,0a.112.112,0,0,1,.058.035A.132.132,0,0,1,1773.374,709.649Z" transform="translate(-1768.161 -709.11)"/><path class="r" d="M1769.032,720.052l-2.32.445a.079.079,0,0,1-.046-.005.127.127,0,0,1-.068-.085.019.019,0,0,1,0-.005l0-.006-.06-.31v-.006a.11.11,0,0,1,.072-.132l2.035-.391a1.051,1.051,0,0,0,.374.484Z" transform="translate(-1766.106 -718.055)"/><path class="r" d="M1771.365,714.24l-2.32.445a.11.11,0,0,1-.115-.1v-.006l-.06-.31a.026.026,0,0,1,0-.006.113.113,0,0,1,.061-.128l.011,0,2.035-.391h0A1.044,1.044,0,0,0,1771.365,714.24Z" transform="translate(-1768.191 -712.861)"/><path class="q" d="M1767.068,705.528l.06.311a.138.138,0,0,1-.005.07.112.112,0,0,1-.041.054.08.08,0,0,1-.017.01l-.006,0,0,0h-.007l-4.33.831a.115.115,0,0,1-.127-.093s0,0,0-.006l-.06-.311a.036.036,0,0,0,0-.006.114.114,0,0,1,.083-.134l4.331-.831h.019l.02,0,.01,0,.006,0h0l.006,0h0l.005,0,.013.008,0,0h0l0,0h0l0,0,.005.006A.131.131,0,0,1,1767.068,705.528Z" transform="translate(-1762.525 -705.427)"/><path class="r" d="M1765.022,710.1l-2.32.445a.11.11,0,0,1-.115-.1v-.006l-.06-.311a.029.029,0,0,0,0-.006.111.111,0,0,1,.072-.132l2.035-.391.005.012a1.041,1.041,0,0,0,.384.482Z" transform="translate(-1762.523 -709.163)"/><g transform="translate(1.571 0.032)"><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.219, 3.299)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.053 3.331) rotate(-10.837)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.853, 3.369)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.546, 3.428)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 2.223, 3.49)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.726, 3.585)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.261, 3.675)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.846 3.754) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.377 3.268) rotate(-10.919)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.482, 3.248)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.584, 3.229)"/><path class="r" d="M1811.725,735.9l.106.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1808.065 -732.685)"/><path class="r" d="M1812.383,735.915l.1.534a.113.113,0,0,0,.04-.054l-.085-.444A.109.109,0,0,0,1812.383,735.915Z" transform="translate(-1808.654 -732.706)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.724, 3.015)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.259 3.105) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(0.844 3.184) rotate(-10.859)"/><path class="r" d="M1806.575,717.859l-.106-.55-.043.008.007,0-.105-.546h-.009l-.1-.533.019,0,.1.528a.111.111,0,0,0,.04-.054l-.085-.444a.121.121,0,0,0-.046-.032l.017,0-.106-.55-.049.01.1.538h-.016l-.031.006.106.55.009,0,.105.546.031-.006h.006l.106.549Z" transform="translate(-1803.045 -714.62)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.48, 2.678)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.581, 2.659)"/><path class="r" d="M1811.705,730.539l.106.55.031-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1808.048 -727.895)"/><path class="r" d="M1812.363,730.555l.1.534a.111.111,0,0,0,.041-.054l-.086-.444A.111.111,0,0,0,1812.363,730.555Z" transform="translate(-1808.635 -727.916)"/><path class="r" d="M1802.766,722.7l-.106-.55-.009,0-.1-.546-.049.009.106.55.009,0,.1.545-.048.009.106.55.049-.009-.106-.549Z" transform="translate(-1799.818 -719.915)"/><path class="r" d="M1799.213,722.808l-.009,0-.105-.546-.049.009.106.55.009,0,.1.546.012,0,.106.549.049-.01-.106-.55-.012,0Z" transform="translate(-1796.738 -720.506)"/><path class="r" d="M1808.374,725.966l.1.534a.109.109,0,0,0,.041-.054l-.085-.444A.112.112,0,0,0,1808.374,725.966Z" transform="translate(-1805.07 -723.815)"/><path class="r" d="M1796.218,722.817l-.049.01.106.55.009,0,.1.545h-.006l.106.55.049-.009-.105-.549h.005l-.106-.55-.009,0Z" transform="translate(-1794.163 -721)"/><path class="r" d="M1793.172,723.4l-.049.009.106.55.009,0,.105.546.049-.01-.106-.55-.009,0Z" transform="translate(-1791.441 -721.523)"/><path class="r" d="M1788.5,724.3l-.049.009.106.55.009,0,.105.546.049-.009-.106-.55-.009,0Z" transform="translate(-1787.268 -722.324)"/><path class="r" d="M1784.129,725.138l-.049.009.106.55.009,0,.105.546.049-.009-.106-.55-.009,0Z" transform="translate(-1783.359 -723.074)"/><path class="r" d="M1780.225,725.888l-.049.009.106.55.009,0,.1.546.049-.009-.106-.55-.009,0Z" transform="translate(-1779.87 -723.745)"/><path class="r" d="M1804.037,721.315l-.049.009.106.55.009,0,.105.545h-.007l.106.55.049-.01-.106-.549h.007l-.106-.55-.009,0Z" transform="translate(-1801.151 -719.658)"/><path class="r" d="M1803.822,716.682l-.039.008-.1-.536-.049.009.106.55.04-.008.1.536.009,0,.105.546.049-.01-.106-.55-.009,0Z" transform="translate(-1800.831 -715.045)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.632 1.15) rotate(-10.859)"/><path class="r" d="M1800.287,717.361l-.024,0,.106.55.009,0,.105.546.05-.009-.106-.55-.01,0-.1-.532.024,0-.106-.55-.049.01Z" transform="translate(-1797.751 -715.636)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.126 1.247) rotate(-10.931)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.803, 1.309)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.306 1.404) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.841, 1.493)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.426, 1.573)"/><path class="r" d="M1805.225,716.412l-.018,0,.106.55.009,0,.1.545-.046.009.106.55.049-.009-.105-.549.046-.009-.106-.55-.009,0-.1-.532.018,0-.106-.55-.049.009Z" transform="translate(-1802.164 -714.789)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.164, 1.048)"/><path class="r" d="M1807.929,715.921l-.1-.546-.019,0-.031.006.106.55.031-.006Z" transform="translate(-1804.535 -714.35)"/><path class="r" d="M1808.576,715.878l-.085-.444a.115.115,0,0,0-.058-.035l.1.534A.111.111,0,0,0,1808.576,715.878Z" transform="translate(-1805.124 -714.37)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 3.047, 0.5)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.374 0.629) rotate(-10.91)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.051 0.691) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.554, 0.786)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 1.089, 0.876)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.674, 0.955)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.205 0.469) rotate(-10.919)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.31 0.449) rotate(-10.837)"/><rect class="r" width="0.05" height="0.56" transform="translate(3.412 0.43) rotate(-10.859)"/><path class="r" d="M1810.107,709.571l.106.55.03-.006.018-.005-.105-.546-.019,0Z" transform="translate(-1806.62 -709.156)"/><path class="r" d="M1810.766,709.587l.1.534a.11.11,0,0,0,.04-.054l-.085-.444A.112.112,0,0,0,1810.766,709.587Z" transform="translate(-1807.208 -709.177)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.206 0.092) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="translate(1.376 0.251) rotate(-10.859)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.88, 0.347)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0.415, 0.436)"/><rect class="r" width="0.05" height="0.56" transform="matrix(0.982, -0.189, 0.189, 0.982, 0, 0.516)"/><rect class="r" width="0.05" height="0.56" transform="translate(2.636 0.009) rotate(-10.89)"/></g></g><g transform="translate(103.875 12.357)"><path class="s" d="M1740.438,724.105a2.134,2.134,0,1,1,3.006.27A2.134,2.134,0,0,1,1740.438,724.105Z" transform="translate(-1739.799 -720.603)"/><path class="q" d="M1742.17,725.436l-.07.061-.024.02-.056.044a2.134,2.134,0,0,1-2.843-3.163,2.167,2.167,0,0,1,.188-.177c.024-.02.048-.04.073-.058l.072-.053a2.147,2.147,0,0,1,.6-.291,2.134,2.134,0,0,1,2.064,3.617Z" transform="translate(-1738.599 -721.606)"/><g transform="translate(0.161 0.284)"><path class="r" d="M1742.106,727.252c-.06,0-.121,0-.182-.008a1.978,1.978,0,0,1-1.348-.706h0a1.989,1.989,0,1,1,1.53.714Zm-.007-3.773a1.783,1.783,0,0,0-1.365,2.926h0a1.783,1.783,0,1,0,1.528-2.92C1742.208,723.482,1742.153,723.479,1742.1,723.479Z" transform="translate(-1740.113 -723.274)"/></g><g transform="translate(1.179 0.965)"><path class="r" d="M1751.623,730.99l-.019-.027a.568.568,0,0,0-.162-.157.577.577,0,0,0-.223-.079.971.971,0,0,0-.263,0c-.095.013-.2.031-.306.055a1.442,1.442,0,0,1-.175.03.443.443,0,0,1-.128,0,.217.217,0,0,1-.093-.039.3.3,0,0,1-.069-.076.324.324,0,0,1-.043-.1.214.214,0,0,1,0-.1.233.233,0,0,1,.042-.094.344.344,0,0,1,.094-.086.275.275,0,0,1,.229-.042.4.4,0,0,1,.215.183l.393-.256a.794.794,0,0,0-.184-.2.611.611,0,0,0-.458-.125.8.8,0,0,0-.252.076l-.179-.274-.218.142.176.27a.907.907,0,0,0-.186.2.672.672,0,0,0-.1.217.551.551,0,0,0-.009.225.645.645,0,0,0,.272.406.572.572,0,0,0,.223.078.966.966,0,0,0,.263,0,3.029,3.029,0,0,0,.306-.06,1.179,1.179,0,0,1,.167-.031.448.448,0,0,1,.127,0,.237.237,0,0,1,.1.041.322.322,0,0,1,.076.084.264.264,0,0,1,.04.1.227.227,0,0,1,0,.1.271.271,0,0,1-.052.1.4.4,0,0,1-.1.09.493.493,0,0,1-.119.057.29.29,0,0,1-.129.012.3.3,0,0,1-.13-.052.469.469,0,0,1-.123-.133l-.393.256a.749.749,0,0,0,.219.228.619.619,0,0,0,.248.095.686.686,0,0,0,.258-.013,1.081,1.081,0,0,0,.25-.093l.163.25.216-.141-.164-.251a.992.992,0,0,0,.19-.2.664.664,0,0,0,.1-.2l0-.016a.539.539,0,0,0,.01-.224A.6.6,0,0,0,1751.623,730.99Z" transform="translate(-1749.694 -729.68)"/></g></g><path class="f" d="M7,1.455a.273.273,0,0,0,.273-.273V.273A.273.273,0,0,0,7,0H.273A.273.273,0,0,0,0,.273V1.29a.273.273,0,0,0,.273.273H2.211A1.64,1.64,0,0,1,3.6,2.182H.273A.273.273,0,0,0,0,2.455v.909a.273.273,0,0,0,.273.273H3.881a1.571,1.571,0,0,1-1.7,1.333H.273A.273.273,0,0,0,0,5.243V6.448a.273.273,0,0,0,.088.2L3.84,10.111a.273.273,0,0,0,.185.072H5.9a.273.273,0,0,0,.185-.473L2.657,6.544A3.114,3.114,0,0,0,5.8,3.637H7a.273.273,0,0,0,.273-.273V2.455A.273.273,0,0,0,7,2.182H5.667a3.114,3.114,0,0,0-.324-.727Z" transform="translate(28.535 35.264) rotate(-13)"/></g><g transform="translate(195.786 35.791)"><path class="t" d="M2366.959,704.858a4.694,4.694,0,0,1-2.723.872,4.249,4.249,0,0,1-2.235-1.071l-.79,1.534a6.158,6.158,0,0,0,3.689.7,4.285,4.285,0,0,0,1.685-1.077A1.464,1.464,0,0,0,2366.959,704.858Z" transform="translate(-2361.211 -700.754)"/><path class="u" d="M2400.02,668.923l2.117-.994s-.088,4.5-3.732,6.043C2398.406,673.972,2400.605,670.792,2400.02,668.923Z" transform="translate(-2394.452 -667.929)"/></g><g transform="translate(0 83.759)"><path class="t" d="M482.239,1134.652a6.6,6.6,0,0,1-2.233-3.342,5.978,5.978,0,0,1,.583-3.435l-2.378-.473s-.7,3.156.488,5.254a6.017,6.017,0,0,0,2.107,1.859A2.054,2.054,0,0,0,482.239,1134.652Z" transform="translate(-478.005 -1127.401)"/><path class="u" d="M494.1,1180.093l2.163,2.476s-6.115,1.624-9.611-2.7C486.655,1179.866,491.8,1181.607,494.1,1180.093Z" transform="translate(-485.735 -1174.289)"/></g><g transform="translate(115.3 94.831)"><path class="t" d="M1586.612,1241.54a6.3,6.3,0,0,1,.794-3.759,5.708,5.708,0,0,1,2.738-1.9l-1.264-1.943s-2.628,1.626-3.266,3.843a5.754,5.754,0,0,0,.136,2.683A1.965,1.965,0,0,0,1586.612,1241.54Z" transform="translate(-1585.491 -1233.943)"/><path class="u" d="M1590.2,1278.814l-.247,3.132s-5.195-3.1-4.571-8.376C1585.376,1273.57,1587.624,1278.253,1590.2,1278.814Z" transform="translate(-1585.324 -1269.358)"/></g><g transform="translate(69.047 78.34)"><path class="v" d="M1147.9,1075.743s.526-1.134,2.869-.538c1.05.267,2.868,1.812.465,3.116-2.5,1.356-3.29-.061-3.29-.061s4.026-.661,3.452-2.287c-.635-1.795-2.322.149-2.322.149A3.092,3.092,0,0,0,1147.9,1075.743Z" transform="translate(-1147.337 -1075.032)"/><path class="w" d="M1142.741,1091.31a1.248,1.248,0,0,1,.27-1.592l1,.8s-.972.766,0,1.456C1144.009,1091.97,1142.88,1091.66,1142.741,1091.31Z" transform="translate(-1142.564 -1088.157)"/></g><g transform="translate(145.66 9.933)"><path class="x" d="M1902.355,419.61a3.818,3.818,0,0,1,5.386,1.049l2.536-1.091s-1.253-1.977-6.168-1.1C1900.892,419.041,1902.355,419.61,1902.355,419.61Z" transform="translate(-1899.325 -418.243)"/><path class="y" d="M1876.647,421.7s.837-3.457,7.147-3.457c0,0-2.364.224-4.212,3.293Z" transform="translate(-1876.647 -418.244)"/></g><path class="g" d="M1682.717,541.82s-1.7-1.073-1.878-2.37c0,0-1.654.089-2.012.626,0,0,.76,2.146,1.654,2.549C1680.481,542.625,1682.493,542.759,1682.717,541.82Z" transform="translate(-1553.75 -516.706)"/><path class="g" d="M557.843,950.354s-1.7-1.073-1.878-2.37c0,0-1.654.09-2.012.626,0,0,.76,2.146,1.654,2.549C555.607,951.159,557.62,951.293,557.843,950.354Z" transform="translate(-545.879 -882.913)"/><path class="f" d="M1232.387,1473.56s1.745-1.1,1.928-2.433c0,0,1.7.092,2.066.643,0,0-.781,2.2-1.7,2.617C1234.682,1474.386,1232.616,1474.525,1232.387,1473.56Z" transform="translate(-1153.949 -1351.658)"/><path class="k" d="M1944.164,747.037a7.934,7.934,0,0,1-.594-3.672,6.349,6.349,0,0,0,.056-2.313,22.579,22.579,0,0,0-2.294-3.327c.033.167.109,1.011.235,1.8-.486-.35-.984-.685-1.034-.605-.174.277.651,1.066,1.22,1.518a1.564,1.564,0,0,0,.2.495.673.673,0,0,1,.135.689c-.02.114-.037.217-.051.3-.541-.329-1.418-.739-1.5-.556-.113.263.75,1.05,1.05,1.726a1.336,1.336,0,0,0,.644.769c.218.083.4.6.4.6l.573,2.945Z" transform="translate(-1788.091 -694.646)"/><path class="d" d="M673.017,954.085s10.676,11.193,12.31,13.867l1.332-3.856s-6.753-12.9-7.12-12.987C675.368,950.109,673.017,954.085,673.017,954.085Z" transform="translate(-653.049 -885.696)"/><path class="d" d="M788.724,1074.635s.379,1.28,1.121,1.57c.722.281,2.274,3.552,2.837,3.749,0,0-.225.445-1.21,0a37.619,37.619,0,0,1-4.248-3.293Z" transform="translate(-755.303 -996.354)"/><path class="d" d="M782.031,1093.629a4.787,4.787,0,0,1,.394,1.171c.225,2.631.53,3.044.943,3.681s2.7,2.153,2.891,1.988l-.92-4.733c-.081-.419-.55-2.981-.55-2.981l-3.564-1.182Z" transform="translate(-749.929 -1011.56)"/><g class="ab" transform="matrix(1, 0, 0, 1, 0, -6)"><path class="d" d="M1027.664,362.43c0,.159.316,16.032.316,16.032s4.567,1.783,6.255.32l-4.127-17.707Z" transform="translate(-970.63 -351.22)"/></g><g class="aa" transform="matrix(1, 0, 0, 1, 0, -6)"><path class="d" d="M1017.733,330.021a14.182,14.182,0,0,1-1.007-2.781c-.047-.985-.607-1.923.563-2.26s2.767-.122,2.973.225a6.045,6.045,0,0,1,.567,2.856c-.188.563-.591,1.491-.591,1.491Z" transform="translate(-960.66 -318.81)"/></g><g class="z" transform="matrix(1, 0, 0, 1, 0, -6)"><path class="d" d="M1012.491,338.54a12.735,12.735,0,0,0-1.581-1.252c-.45-.225-.1-2.833-.007-2.945,0,0,.548-.009.5,1.154a1.021,1.021,0,0,0,.643.994Z" transform="translate(-955.4 -327.33)"/></g></g></g></svg>