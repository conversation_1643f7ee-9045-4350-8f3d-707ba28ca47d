<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="175.584" height="153.576" viewBox="0 0 175.584 153.576">
  <defs>
    <filter id="Path_1" x="4.891" y="10.85" width="142.726" height="142.726" filterUnits="userSpaceOnUse">
      <feOffset dy="3" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="3" result="blur"/>
      <feFlood flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#dae3fe"/>
      <stop offset="1" stop-color="#e9effd"/>
    </linearGradient>
    <linearGradient id="linear-gradient-3" x1="13.484" y1="0.247" x2="-15.569" y2="0.813" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#09005d"/>
      <stop offset="1" stop-color="#1a0f91"/>
    </linearGradient>
    <linearGradient id="linear-gradient-4" x1="4.031" y1="-2.973" x2="0.659" y2="0.433" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-5" x1="0.571" y1="-0.551" x2="0.471" y2="1.401" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#020039"/>
      <stop offset="1" stop-color="#090056"/>
    </linearGradient>
    <linearGradient id="linear-gradient-6" x1="0.5" y1="-18.763" x2="0.5" y2="-9.415" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-7" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ff928e"/>
      <stop offset="1" stop-color="#feb3b1"/>
    </linearGradient>
    <linearGradient id="linear-gradient-8" x1="0.5" y1="-1.537" x2="0.5" y2="0.565" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-9" x1="0" y1="0.5" x2="1" y2="0.5" xlink:href="#linear-gradient-5"/>
    <linearGradient id="linear-gradient-11" x1="0.729" y1="0.083" x2="-0.282" y2="0.8" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-12" x1="-0.704" y1="3.433" x2="0.513" y2="0.468" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#4042e2"/>
      <stop offset="1" stop-color="#4f52ff"/>
    </linearGradient>
    <linearGradient id="linear-gradient-14" x1="1.881" y1="1.731" x2="-0.462" y2="-0.335" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-15" x1="0.956" y1="-0.148" x2="0.142" y2="1.368" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-16" x1="1.116" y1="-0.192" x2="0.358" y2="0.736" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-17" x1="0.281" y1="0.154" x2="1.01" y2="1.105" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-18" x1="3.53" y1="-2.437" x2="0.427" y2="0.592" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-19" x1="-0.928" y1="1.227" x2="0.081" y2="0.646" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-20" x1="0.04" y1="-0.018" x2="2.425" y2="2.52" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-21" x1="-41.403" y1="-0.346" x2="-41.04" y2="0.297" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-22" x1="-27.969" y1="11.937" x2="-27.023" y2="12.985" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-23" x1="-134.264" y1="0.5" x2="-133.264" y2="0.5" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-24" x1="-96.381" y1="1.602" x2="-95.346" y2="-1.187" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-25" x1="-82.979" y1="0.5" x2="-81.979" y2="0.5" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-26" x1="-42.93" y1="1.698" x2="-42.467" y2="-1.193" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-27" x1="-42.853" y1="0.411" x2="-42.621" y2="2.003" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-28" x1="-35.461" y1="-0.331" x2="-36.446" y2="0.684" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-29" x1="0.406" y1="0.373" x2="1.037" y2="2.247" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-30" x1="-74.768" x2="-73.768" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-31" x1="-0.148" y1="0.567" x2="0.739" y2="0.459" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-32" x1="-74.73" y1="-3.425" x2="-74.099" y2="-2.022" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-33" x1="-3.447" y1="0.892" x2="-0.097" y2="0.55" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-34" x1="-82.869" y1="33.861" x2="-80.04" y2="36.778" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-35" x1="1.087" y1="-16.428" x2="0.49" y2="-14.504" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#2b3582"/>
      <stop offset="1" stop-color="#150e42"/>
    </linearGradient>
    <linearGradient id="linear-gradient-36" x1="0.5" y1="0.059" x2="0.5" y2="1.189" xlink:href="#linear-gradient-3"/>
    <linearGradient id="linear-gradient-37" x1="1.601" y1="-0.387" x2="0.656" y2="0.384" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-38" x1="1.598" y1="0.056" x2="0.064" y2="0.682" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-39" x1="0.511" y1="-0.188" x2="0.467" y2="2.503" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-40" x1="0.038" y1="-0.92" x2="1.08" y2="-0.92" xlink:href="#linear-gradient-35"/>
    <linearGradient id="linear-gradient-41" x1="1.802" y1="-1.066" x2="-3.491" y2="-1.09" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-42" x1="2.58" y1="-4.058" x2="-0.86" y2="3.366" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-43" x1="-184.494" y1="-3.197" x2="-183.448" y2="-3.197" xlink:href="#linear-gradient-35"/>
    <linearGradient id="linear-gradient-44" x1="-1026.09" y1="-3.487" x2="-1031.22" y2="-3.512" xlink:href="#linear-gradient-12"/>
    <linearGradient id="linear-gradient-45" x1="3.788" y1="-6.36" x2="0.301" y2="1.144" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-46" x1="-1.115" y1="3.22" x2="0.031" y2="1.342" xlink:href="#linear-gradient-35"/>
    <linearGradient id="linear-gradient-47" x1="1.013" y1="1.043" x2="2.063" y2="1.947" xlink:href="#linear-gradient-35"/>
    <linearGradient id="linear-gradient-48" x1="0.497" y1="0.606" x2="0.451" y2="2.362" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-49" x1="0.242" y1="-0.345" x2="0.616" y2="0.87" xlink:href="#linear-gradient-7"/>
    <linearGradient id="linear-gradient-50" x1="1.081" y1="-12.628" x2="0.417" y2="-10.941" xlink:href="#linear-gradient-35"/>
    <linearGradient id="linear-gradient-51" x1="-0.921" y1="-2.341" x2="1.492" y2="2.388" xlink:href="#linear-gradient-7"/>
  </defs>
  <g id="img_achive_weighted" transform="translate(-193.777 -115.51)">
    <g id="Group_6" data-name="Group 6" transform="translate(181.836 106.523)">
      <g id="Group_4" data-name="Group 4">
        <g transform="matrix(1, 0, 0, 1, 11.94, 8.99)" filter="url(#Path_1)">
          <path id="Path_1-2" data-name="Path 1" d="M62.412,0A62.245,62.245,0,1,1,18.231,18.231,62.363,62.363,0,0,1,62.412,0Z" transform="translate(138.62 16.85) rotate(90)" fill="#ed7d2b"/>
        </g>
        <g id="Group_1" data-name="Group 1" transform="translate(12.957 12.957)">
          <path id="Path_2" data-name="Path 2" d="M53.251,0a53.083,53.083,0,1,1-37.7,15.547A53.2,53.2,0,0,1,53.251,0Z" transform="translate(128.436 22.038) rotate(90)" fill="#f99746"/>
          <path id="Path_3" data-name="Path 3" d="M800.107,550.422a54.373,54.373,0,1,1,52.87-42.859l-1.108-.241,1.108.241A54.277,54.277,0,0,1,800.107,550.422ZM799.9,443.968a52.11,52.11,0,1,0,50.865,63.113h0A52.057,52.057,0,0,0,799.9,443.968Z" transform="translate(-724.761 -420.82)" fill="url(#linear-gradient)"/>
        </g>
        <g id="Group_2" data-name="Group 2" transform="translate(42.19 42.19)">
          <circle id="Ellipse_1" data-name="Ellipse 1" cx="32.531" cy="32.531" r="32.531" transform="translate(0 46.005) rotate(-45)" fill="#ed7d2b"/>
          <path id="Path_4" data-name="Path 4" d="M975.92,705.454a33.959,33.959,0,0,1-7.2-.775,33.494,33.494,0,1,1,7.2.775Zm-.13-65.083a31.487,31.487,0,1,0,6.708.722,31.486,31.486,0,0,0-6.708-.722Z" transform="translate(-929.847 -625.766)" fill="url(#linear-gradient)"/>
        </g>
        <g id="Group_3" data-name="Group 3" transform="translate(70.371 70.371)">
          <circle id="Ellipse_2" data-name="Ellipse 2" cx="12.603" cy="12.603" r="12.603" transform="translate(0 17.824) rotate(-45)" fill="#ed7d2b"/>
          <path id="Path_5" data-name="Path 5" d="M1143.81,853.5a13.745,13.745,0,1,1,13.429-10.836A13.745,13.745,0,0,1,1143.81,853.5Zm0-25.21a11.474,11.474,0,1,0,2.429.261A11.474,11.474,0,0,0,1143.81,828.29Z" transform="translate(-1125.988 -821.931)" fill="#fff"/>
        </g>
      </g>
      <g id="Group_5" data-name="Group 5" transform="translate(87.408 21.583)">
        <rect id="Rectangle_1" data-name="Rectangle 1" width="2.383" height="90.238" transform="matrix(-0.541, -0.841, 0.841, -0.541, 16.078, 59.4)" fill="url(#linear-gradient-3)"/>
        <path id="Path_6" data-name="Path 6" d="M1271.81,831.6c1.15-.74,4.639,4.679,3.49,5.419l-19.333,9.409a.671.671,0,0,1-.722-1.121Z" transform="translate(-1254.995 -777.922)" fill="url(#linear-gradient-4)"/>
        <path id="Path_7" data-name="Path 7" d="M1942.09,425.92l8.209,2.651-14.012,9.023a18.218,18.218,0,0,1-15.462,2.019h0Z" transform="translate(-1850.182 -415.346)" fill="#f99746"/>
        <path id="Path_8" data-name="Path 8" d="M1929.93,334.824l.984-8.57-14.012,9.023a18.218,18.218,0,0,0-8.236,13.24h0Z" transform="translate(-1839.312 -326.254)" fill="#f99746"/>
      </g>
    </g>
    <g id="Group_11" data-name="Group 11" transform="translate(193.777 184.702)">
      <g id="Group_7" data-name="Group 7" transform="translate(0 19.916)">
        <path id="Path_9" data-name="Path 9" d="M537.51,1097.37a.578.578,0,0,1-.513-.844l26.127-50.392a.578.578,0,1,1,1.026.532l-26.127,50.392A.577.577,0,0,1,537.51,1097.37Z" transform="translate(-536.932 -1045.322)" fill="#7d97f4"/>
        <path id="Path_10" data-name="Path 10" d="M654.336,1093.58a.578.578,0,0,1-.53-.809l22.332-51.311a.578.578,0,1,1,1.06.462l-22.332,51.311A.578.578,0,0,1,654.336,1093.58Z" transform="translate(-641.364 -1041.117)" fill="#7d97f4"/>
        <path id="Path_11" data-name="Path 11" d="M750.564,1134.71h-9.058a.362.362,0,1,1,0-.723h9.058a.362.362,0,0,1,0,.723Z" transform="translate(-719.479 -1124.139)" fill="#7d97f4"/>
        <path id="Path_12" data-name="Path 12" d="M727.749,1187.61h-9.058a.361.361,0,1,1,0-.722h9.058a.361.361,0,1,1,0,.722Z" transform="translate(-699.085 -1171.42)" fill="#7d97f4"/>
        <path id="Path_13" data-name="Path 13" d="M692.8,1250.94h-9.635a.362.362,0,0,1,0-.723H692.8a.362.362,0,1,1,0,.723Z" transform="translate(-667.329 -1228.035)" fill="#7d97f4"/>
        <path id="Path_14" data-name="Path 14" d="M776.757,1083.41h-8.642a.362.362,0,0,1,0-.723h8.642a.362.362,0,0,1,0,.723Z" transform="translate(-743.266 -1078.279)" fill="#7d97f4"/>
        <path id="Path_15" data-name="Path 15" d="M655.809,1323.81H645.34a.362.362,0,0,1,0-.723h10.469a.362.362,0,1,1,0,.723Z" transform="translate(-633.516 -1293.172)" fill="#7d97f4"/>
        <path id="Path_16" data-name="Path 16" d="M626.22,1380.33H614.991a.362.362,0,0,1,0-.723H626.22a.362.362,0,0,1,0,.723Z" transform="translate(-606.387 -1343.698)" fill="#7d97f4"/>
      </g>
      <g id="Group_8" data-name="Group 8" transform="translate(11.846 47.447)">
        <path id="Path_17" data-name="Path 17" d="M654.531,1302.43a13.308,13.308,0,0,0-3.641-1.813l-1.495.033a3.748,3.748,0,0,0-.624,1.772,4.007,4.007,0,0,0,1.776.363,14.554,14.554,0,0,0,3.984-.355Z" transform="translate(-648.752 -1300.613)" fill="url(#linear-gradient-5)"/>
        <path id="Path_18" data-name="Path 18" d="M654.367,1317.64a14.555,14.555,0,0,1-3.984.354,4.009,4.009,0,0,1-1.776-.362c-.006.042-.011.082-.016.119a.441.441,0,0,0,.31.475,21.28,21.28,0,0,0,5.54.211.308.308,0,0,0,.221-.5,2.614,2.614,0,0,0-.295-.3Z" transform="translate(-648.588 -1315.822)" fill="url(#linear-gradient-6)"/>
      </g>
      <path id="Path_19" data-name="Path 19" d="M655.8,1288.76v1.189a.485.485,0,0,0,.383.474,1.89,1.89,0,0,0,.837.008.464.464,0,0,0,.346-.4l.15-1.274Z" transform="translate(-643.193 -1242.568)" fill="url(#linear-gradient-7)"/>
      <g id="Group_9" data-name="Group 9" transform="translate(18.597 37.709)">
        <path id="Path_20" data-name="Path 20" d="M713.8,1219.49c-.464-1.032-.331-1.5-1.249-1.394a1.255,1.255,0,0,0-.279.061.826.826,0,0,0,0,.538,10.213,10.213,0,0,0,1.607,2.932,1.009,1.009,0,0,0,.774.378l1.012.017a6.463,6.463,0,0,1-1.865-2.532Z" transform="translate(-712.223 -1217.099)" fill="url(#linear-gradient-8)"/>
        <path id="Path_21" data-name="Path 21" d="M716.1,1212.22c-.777-.547-1.067-2.781-1.067-2.781l-1.2-.609-.9.654a.831.831,0,0,0-.291.4,1.254,1.254,0,0,1,.279-.061c.918-.1.785.361,1.249,1.394a6.465,6.465,0,0,0,1.866,2.532l.7.011a.374.374,0,0,0,.3-.6,4.523,4.523,0,0,0-.936-.94Z" transform="translate(-712.6 -1208.834)" fill="url(#linear-gradient-9)"/>
      </g>
      <path id="Path_22" data-name="Path 22" d="M724.184,1198.23l-.723,1.124a.327.327,0,0,0,.073.435,1.074,1.074,0,0,0,1.125.175l1.161-.8Z" transform="translate(-703.625 -1161.65)" fill="url(#linear-gradient-7)"/>
      <path id="Path_23" data-name="Path 23" d="M655.715,1086.36a31.583,31.583,0,0,0-.932,4.836c-.552,3.765-.914,6.5-1.215,8a65.487,65.487,0,0,0-.683,8.433.515.515,0,0,0,.531.51l1.423-.044a.461.461,0,0,0,.441-.384c.376-2.214,2.979-13.638,4.548-16.924,0,0,4.175,1.18,5.216,1.655,0,0-1.346.539-4.1,5.709a7.038,7.038,0,0,0,2.034,1.144s6.035-5.005,6.672-5.529,1.218-1.426-.409-3.07-6.62-4.237-6.62-4.237Z" transform="translate(-640.582 -1061.648)" fill="url(#linear-gradient-11)"/>
      <path id="Path_24" data-name="Path 24" d="M694.423,1109.58a.073.073,0,0,1-.031-.007L692.9,1109a1.87,1.87,0,0,1-1.059-1.669.072.072,0,0,1,.072-.073.069.069,0,0,1,.073.072,1.724,1.724,0,0,0,.977,1.539l1.489.574a.072.072,0,0,1-.031.138Z" transform="translate(-675.409 -1080.328)" fill="url(#linear-gradient-12)"/>
      <path id="Path_25" data-name="Path 25" d="M806.817,1075.02s1.568-1.187,2.282-.964a2.955,2.955,0,0,1,1.24.891h-1.577s-.376.723,0,.723h.557S807.847,1077.21,806.817,1075.02Z" transform="translate(-778.184 -1050.616)" fill="url(#linear-gradient-7)"/>
      <path id="Path_26" data-name="Path 26" d="M760.107,977.869s1.84,7.276,4.333,10.2a3.489,3.489,0,0,1-1.677.992s-3.826-3.353-4.991-5.684S760.107,977.869,760.107,977.869Z" transform="translate(-734.129 -964.663)" fill="url(#linear-gradient-14)"/>
      <path id="Path_27" data-name="Path 27" d="M743.209,912.587l-1.828,3.344h2.885a5.9,5.9,0,0,1,.991-2.449Z" transform="translate(-719.69 -906.307)" fill="url(#linear-gradient-15)"/>
      <g id="Group_10" data-name="Group 10" transform="translate(20.972)">
        <path id="Path_28" data-name="Path 28" d="M753.773,875.055a13.491,13.491,0,0,1,.977,3.208c.062,1.1.3,1.821-.977,2.043a2.714,2.714,0,0,1-2.935-1.514s-.913-1.7-.708-2.389S753.773,875.055,753.773,875.055Z" transform="translate(-748.457 -872.757)" fill="url(#linear-gradient-16)"/>
        <path id="Path_29" data-name="Path 29" d="M737.429,858.641h.161s-.665-1.364-.081-1.6,2.9-.712,3.4-1.533a1.527,1.527,0,0,0-.59-2.1.086.086,0,0,0-.109.04l-.053.1a.087.087,0,0,1-.16-.016h0a.086.086,0,0,0-.155-.024,2.342,2.342,0,0,1-1.677.85c-1.576.291-2.617.928-2.643,2.017a.087.087,0,0,1-.134.071c-.116-.071-.3-.1-.551.1-.433.364-.255,1.307.693,2.084a.086.086,0,0,1,0,.139c-.172.112-.428.378.236.733a1.771,1.771,0,0,0,1.233.285.086.086,0,0,0,.066-.09l-.06-.965a.087.087,0,0,1,.087-.092Z" transform="translate(-734.611 -853.396)" fill="url(#linear-gradient-17)"/>
        <path id="Path_30" data-name="Path 30" d="M751.849,896.023s-.349-1.344-.952-.967.313,1.506.8,1.556Z" transform="translate(-748.992 -890.577)" fill="url(#linear-gradient-18)"/>
      </g>
      <path id="Path_31" data-name="Path 31" d="M683.825,941.475s-.867-.114,4.463.214c2.771.171,8.147,10.336,9.535,9.12,0,0-.287.285.546,1.157,0,0-5.106,1.745-9.874-5.1,0,0-3.956,8.349-3.92,10.052a8.383,8.383,0,0,1-7.125.577,18.58,18.58,0,0,1,1.475-6.948A45.248,45.248,0,0,1,683.825,941.475Z" transform="translate(-662.54 -932.114)" fill="url(#linear-gradient-19)"/>
      <path id="Path_32" data-name="Path 32" d="M867.62,1028.47a1.149,1.149,0,0,1,1.658,0c.829.829-1.382,2.833-1.382,2.833l-.025-1.773S867,1029.3,867.62,1028.47Z" transform="translate(-832.337 -1009.567)" fill="url(#linear-gradient-20)"/>
    </g>
    <g id="Group_14" data-name="Group 14" transform="translate(313.292 194.885)">
      <path id="Path_33" data-name="Path 33" d="M1791.66,1018.61s1.227.6,1.452,1.282c.181.55,1.537.621,1.671,1.7a1.241,1.241,0,0,0,1.039,1.091c1.385.212,3.52.932,3.862,3.337.261,1.836-.675,2.532-1.657,2.773a1.391,1.391,0,0,0-1.055,1.607c.14.7-.069,1.506-1.279,2.145-2.712,1.431-5.42-3.39-5.42-3.39l-.555-8.418Z" transform="translate(-1776.319 -1011.261)" fill="url(#linear-gradient-21)"/>
      <path id="Path_34" data-name="Path 34" d="M1685.24,984.316s3.834,9.379,9.884,12.133c0,0-1.86,3.3-2.885,3.532,0,0-6.644-7.892-7.534-14.525Z" transform="translate(-1682.454 -980.609)" fill="url(#linear-gradient-22)"/>
      <path id="Path_35" data-name="Path 35" d="M1913.06,1511.46l.176,1.443a.6.6,0,0,0,.523.521,9.232,9.232,0,0,0,1.931.03.51.51,0,0,0,.428-.662,5.253,5.253,0,0,0-.968-1.885h-1.6a.493.493,0,0,0-.49.553Z" transform="translate(-1886.576 -1451.333)" fill="url(#linear-gradient-23)"/>
      <path id="Path_36" data-name="Path 36" d="M1784.81,1356.82l-.27,1.589s.9,9.162,1.066,12.827a2.874,2.874,0,0,1-.425,1.632l-1.573,2.554,4-2.359.079-.488-.654-.9.857-10.264a3.149,3.149,0,0,0-.358-1.742l-.827-1.553Z" transform="translate(-1770.862 -1313.597)" fill="url(#linear-gradient-24)"/>
      <path id="Path_37" data-name="Path 37" d="M1778.29,1503.7h1.276a.768.768,0,0,0,.61-.3c.4-.517,1.222-1.525,1.615-1.551.531-.035.463,1.85.463,1.85h.3l.376-2.343a1.387,1.387,0,0,0-.728-1.45h0a1.96,1.96,0,0,1-.779,1.293c-.657.4-2.675,1.817-2.675,1.817a1.823,1.823,0,0,0-.518.014.337.337,0,0,0,.06.67Z" transform="translate(-1765.803 -1441.499)" fill="url(#linear-gradient-25)"/>
      <path id="Path_38" data-name="Path 38" d="M1846.55,1358.37s5.3,10.052,6.141,12.347a22.06,22.06,0,0,1,1.149,4.391c.017.283.37.555,1.341.555a.932.932,0,0,0,.867-.4.565.565,0,0,0,.021-.548c-.307-.62-1.279-2.6-1.64-3.5-.43-1.074-1.355-5.939-1.863-7.63a8.974,8.974,0,0,0-3.291-5.217S1847.76,1356.89,1846.55,1358.37Z" transform="translate(-1827.127 -1314.392)" fill="url(#linear-gradient-26)"/>
      <path id="Path_39" data-name="Path 39" d="M1752.18,1106.57a9.634,9.634,0,0,1,2.1-9.068c-2.185-2.1-6.166-3.052-6.166-3.052s-3.8,2.777-3.348,4.951c.264,1.274,2.67,7.631,2.67,7.631A5.286,5.286,0,0,0,1752.18,1106.57Z" transform="translate(-1736.104 -1079.058)" fill="url(#linear-gradient-27)"/>
      <path id="Path_40" data-name="Path 40" d="M1775.7,1211.56c-.557-1.676-3.031-2.868-3.031-2.868a5.286,5.286,0,0,1-4.748.463,9.522,9.522,0,0,0-.155,3.68c.344,1.772,2.5,13.163,2.5,13.163,3.4.69,8.64-.694,8.64-.694C1776.74,1221.45,1776.26,1213.23,1775.7,1211.56Z" transform="translate(-1756.592 -1181.178)" fill="url(#linear-gradient-28)"/>
      <path id="Path_41" data-name="Path 41" d="M1781.95,1054.36l1.111,4.992c.106.44-.3.368-.739.284a2.963,2.963,0,0,1-1.98-.986.537.537,0,0,1-.057-.188l-.478-3.461a.622.622,0,0,1,.364-.654l.921-.408a.622.622,0,0,1,.858.421Z" transform="translate(-1767.455 -1042.797)" fill="url(#linear-gradient-29)"/>
      <path id="Path_42" data-name="Path 42" d="M1819.77,1123.21s7.757,5.55.992,11.381a1.748,1.748,0,0,1-.765.443,8.409,8.409,0,0,0-1.4.949.263.263,0,0,1-.385-.069h0a.36.36,0,0,1-.02-.345l.96-2.031a.74.74,0,0,1,.962-.042h0a.366.366,0,0,0,.554-.125c.523-1.065,1.51-4.2-2.531-7.737a.439.439,0,0,1-.1-.532A3.984,3.984,0,0,1,1819.77,1123.21Z" transform="translate(-1801.591 -1104.771)" fill="url(#linear-gradient-30)"/>
      <g id="Group_12" data-name="Group 12" transform="translate(10.516 5.089)">
        <path id="Path_43" data-name="Path 43" d="M1764.41,1013.98a4.925,4.925,0,0,0-1.692,2.84c-.508,2.04.318,2.771,1.165,2.726a3.93,3.93,0,0,0,3.139-1.77C1768.09,1016.31,1765.96,1013,1764.41,1013.98Z" transform="translate(-1762.567 -1012.055)" fill="url(#linear-gradient-31)"/>
        <path id="Path_44" data-name="Path 44" d="M1769.41,1002.24s.433-2.923-1.993-2.476l-.491.511a1.58,1.58,0,0,1,.127-2.349,2.686,2.686,0,0,1,4.148,1.089c1.009,1.841,1.375,3.134.529,4.278s-1.482.163-1.482.163.388-.99.066-1.415-.737.247-.737.247Z" transform="translate(-1766.026 -997.339)" fill="url(#linear-gradient-32)"/>
        <path id="Path_45" data-name="Path 45" d="M1797.61,1036.17s.214-1.5.982-1.074-.277,1.4-.7,1.736Z" transform="translate(-1793.894 -1031.023)" fill="url(#linear-gradient-33)"/>
      </g>
      <g id="Group_13" data-name="Group 13">
        <path id="Path_46" data-name="Path 46" d="M1665.96,954.821c-.047.171.578.406.916-.3a6.524,6.524,0,0,0-.057-2.023,26.483,26.483,0,0,0-3.219-3.128.487.487,0,0,0-.036.678l1.03,1a4.821,4.821,0,0,0-.537,1.167,1.392,1.392,0,0,0,.176,1.018c.186.307.438.707.674,1.018a1.374,1.374,0,0,0,1.053.57Z" transform="translate(-1663.446 -949.371)" fill="url(#linear-gradient-34)"/>
      </g>
    </g>
    <g id="Group_20" data-name="Group 20" transform="translate(258.608 115.51)">
      <g id="Group_19" data-name="Group 19">
        <path id="Path_47" data-name="Path 47" d="M1228.52,223.552c-.022.043-1.436,1.082-1.436,1.082s.312,2.233,1.579,3.114c1.393.969,3.361-.8,3.361-.8s1.566.372,2.4-2.888c.191-.748-.912-.811-2.243-.673C1230.46,223.568,1228.52,223.552,1228.52,223.552Z" transform="translate(-1218.695 -220.985)" fill="url(#linear-gradient-35)"/>
        <path id="Path_48" data-name="Path 48" d="M1195.69,289.548a5.962,5.962,0,0,1,1.812-.843c.243-.066,1.558.316,2.37.3,1.115-.026,2.25-.533,3.17-.3a6.629,6.629,0,0,1,2.092.832,7.733,7.733,0,0,1,2.512,4.328,5.915,5.915,0,0,1-1.976,1.3l-.2,8.248-4.877,2.5-5.566-2.8.2-8.06a8.412,8.412,0,0,1-1.9-1.123,7.231,7.231,0,0,1,2.363-4.387Z" transform="translate(-1188.506 -279.363)" fill="url(#linear-gradient-36)"/>
        <path id="Path_49" data-name="Path 49" d="M1189.84,338.091s-2.369,6.567,3.033,9.266c1.122-.149.69-2.012.69-2.012s-2.347-.519-1.642-6A15.186,15.186,0,0,0,1189.84,338.091Z" transform="translate(-1184.943 -323.569)" fill="url(#linear-gradient-37)"/>
        <path id="Path_50" data-name="Path 50" d="M1295.28,338.091s1.63,6.855-3.033,9.266c-1.121-.149-.69-2.012-.69-2.012s1.891-.931,1.642-6a15.184,15.184,0,0,1,2.081-1.254Z" transform="translate(-1276.256 -323.569)" fill="url(#linear-gradient-38)"/>
        <rect id="Rectangle_2" data-name="Rectangle 2" width="10.695" height="7.222" rx="3.611" transform="translate(6.452 17.027)" fill="url(#linear-gradient-39)"/>
        <g id="Group_16" data-name="Group 16" transform="translate(16.818 26.173)">
          <path id="Path_51" data-name="Path 51" d="M1317.12,448.524a21.552,21.552,0,0,0-4.053-.13l-.115.312-.191,1.549a32.177,32.177,0,0,0,4.022.484,1.245,1.245,0,0,0,.373-.056q.061-.055.117-.115a1.459,1.459,0,0,0-.153-2.044Z" transform="translate(-1312.101 -448.306)" fill="url(#linear-gradient-40)"/>
          <path id="Path_52" data-name="Path 52" d="M1353.96,449.991c-.038-.011-.079-.021-.121-.031a1.46,1.46,0,0,1,.154,2.044c-.038.041-.077.078-.117.115a1.127,1.127,0,0,0,.084-2.128Z" transform="translate(-1348.822 -449.743)" fill="url(#linear-gradient-41)"/>
          <path id="Path_53" data-name="Path 53" d="M1306.69,447.913l.479.02a.922.922,0,0,1,.673.348,1.136,1.136,0,0,1,.247.777.894.894,0,0,1-.259.591.726.726,0,0,1-.56.213l-.733-.052Z" transform="translate(-1306.532 -447.913)" fill="url(#linear-gradient-42)"/>
          <g id="Group_15" data-name="Group 15" transform="translate(2.001 0.446)">
            <path id="Path_54" data-name="Path 54" d="M1326.32,453.466a.073.073,0,0,1-.068-.058,2.684,2.684,0,0,0-.6-.987.085.085,0,0,1,0-.111.066.066,0,0,1,.1,0,2.8,2.8,0,0,1,.643,1.061.08.08,0,0,1-.049.1.066.066,0,0,1-.026-.005Z" transform="translate(-1325.603 -452.267)" fill="#91b3fa"/>
            <path id="Path_55" data-name="Path 55" d="M1325.47,453.272a.066.066,0,0,1-.021,0,.081.081,0,0,1-.047-.1,2.365,2.365,0,0,1,.789-1.044.067.067,0,0,1,.1.021.084.084,0,0,1-.019.109,2.254,2.254,0,0,0-.733.96.072.072,0,0,1-.069.054Z" transform="translate(-1325.396 -452.113)" fill="#91b3fa"/>
            <path id="Path_56" data-name="Path 56" d="M1338.96,453.913a.073.073,0,0,1-.069-.061,2.722,2.722,0,0,0-.559-1.018.085.085,0,0,1,0-.111.066.066,0,0,1,.1,0,2.835,2.835,0,0,1,.6,1.094.079.079,0,0,1-.053.094Z" transform="translate(-1336.939 -452.637)" fill="#91b3fa"/>
            <path id="Path_57" data-name="Path 57" d="M1337.74,454.02a.068.068,0,0,1-.024,0,.081.081,0,0,1-.042-.1,2.333,2.333,0,0,1,.834-1,.067.067,0,0,1,.1.027.084.084,0,0,1-.024.108,2.22,2.22,0,0,0-.774.92.072.072,0,0,1-.07.045Z" transform="translate(-1336.368 -452.819)" fill="#91b3fa"/>
          </g>
        </g>
        <g id="Group_18" data-name="Group 18" transform="translate(0 26.132)">
          <path id="Path_58" data-name="Path 58" d="M1151.68,447.632a21.547,21.547,0,0,1,4.054-.005l.107.315.152,1.554a32.143,32.143,0,0,1-4.032.361,1.255,1.255,0,0,1-.371-.067c-.04-.038-.078-.076-.115-.118A1.458,1.458,0,0,1,1151.68,447.632Z" transform="translate(-1150.812 -447.522)" fill="url(#linear-gradient-43)"/>
          <path id="Path_59" data-name="Path 59" d="M1148.75,448.582c.039-.01.079-.019.122-.027a1.458,1.458,0,0,0-.2,2.039,1.425,1.425,0,0,0,.115.118,1.127,1.127,0,0,1-.037-2.13Z" transform="translate(-1148.01 -448.445)" fill="url(#linear-gradient-44)"/>
          <path id="Path_60" data-name="Path 60" d="M1190.04,447.917l-.479.006a.917.917,0,0,0-.681.327,1.13,1.13,0,0,0-.266.769.9.9,0,0,0,.244.6.73.73,0,0,0,.555.23l.734-.03Z" transform="translate(-1184.301 -447.875)" fill="url(#linear-gradient-45)"/>
          <g id="Group_17" data-name="Group 17" transform="translate(1.625 0.405)">
            <path id="Path_61" data-name="Path 61" d="M1176.92,452.887a.072.072,0,0,0,.069-.056,2.666,2.666,0,0,1,.627-.968.085.085,0,0,0,.006-.111.066.066,0,0,0-.1-.007,2.775,2.775,0,0,0-.669,1.04.08.08,0,0,0,.047.1A.061.061,0,0,0,1176.92,452.887Z" transform="translate(-1175.411 -451.684)" fill="#91b3fa"/>
            <path id="Path_62" data-name="Path 62" d="M1176.83,452.522a.063.063,0,0,0,.021,0,.08.08,0,0,0,.049-.1,2.387,2.387,0,0,0-.763-1.068.067.067,0,0,0-.1.018.084.084,0,0,0,.017.11,2.273,2.273,0,0,1,.709.982.073.073,0,0,0,.067.058Z" transform="translate(-1174.673 -451.34)" fill="#91b3fa"/>
            <path id="Path_63" data-name="Path 63" d="M1164.65,452.946a.073.073,0,0,0,.07-.059,2.7,2.7,0,0,1,.584-1,.085.085,0,0,0,0-.111.066.066,0,0,0-.1,0,2.817,2.817,0,0,0-.623,1.075.08.08,0,0,0,.051.1Z" transform="translate(-1164.449 -451.706)" fill="#91b3fa"/>
            <path id="Path_64" data-name="Path 64" d="M1164.17,452.881a.064.064,0,0,0,.024,0,.081.081,0,0,0,.045-.1,2.352,2.352,0,0,0-.809-1.025.067.067,0,0,0-.1.024.084.084,0,0,0,.021.109,2.239,2.239,0,0,1,.751.943.072.072,0,0,0,.068.049Z" transform="translate(-1163.324 -451.697)" fill="#91b3fa"/>
          </g>
        </g>
        <path id="Path_65" data-name="Path 65" d="M1215.02,394.022a2.1,2.1,0,0,0-3.068-.888,76.935,76.935,0,0,1-13.791,5.42,7.016,7.016,0,0,1,.3,1.842q0,.207-.007.408c4.948-.254,10.319-1.9,14.934-3.355a2.551,2.551,0,0,0,1.632-3.427Z" transform="translate(-1192.842 -372.487)" fill="url(#linear-gradient-46)"/>
        <path id="Path_66" data-name="Path 66" d="M1175.9,399.024a4.254,4.254,0,0,1,.416-1.994c.009-.028.019-.055.028-.083-.369-.114-.844-.269-1.47-.481a70.549,70.549,0,0,1-11.414-4.974,2.089,2.089,0,0,0-3.185,1.3,2.4,2.4,0,0,0,1.677,2.949c3.693,1.229,1.77.888,2.977,1.276a50.476,50.476,0,0,0,10.979,2.163C1175.91,399.127,1175.9,399.075,1175.9,399.024Z" transform="translate(-1158.894 -371.017)" fill="url(#linear-gradient-47)"/>
        <path id="Path_67" data-name="Path 67" d="M1232.8,271.388a2.827,2.827,0,0,0,2.886,2.091,2.492,2.492,0,0,0,2.654-2.1l-1.592-.341-.133-1.774-2.4.177-.044,1.685Z" transform="translate(-1223.802 -262.047)" fill="url(#linear-gradient-48)"/>
        <path id="Path_68" data-name="Path 68" d="M1240.24,221.9s-2.744,5.316.511,6.6c2.3.91,3.736-3.53,3.306-4.732-.664-1.859-1.1-1.639-1.1-1.639Z" transform="translate(-1229.492 -219.702)" fill="url(#linear-gradient-49)"/>
        <path id="Path_69" data-name="Path 69" d="M1233.73,202.923c1.356.373.2,3.313-1.844,2.618s-2.843-.685-3.161-.125-.386,2.551-1.076,1.567c-.757-1.079-1.178-3.243-.251-3.941.829-.624,1.023-1.591,2.017-1.557,1.32.044,2.341-.541,2.936-.119C1233.04,201.85,1232.74,202.648,1233.73,202.923Z" transform="translate(-1218.505 -201.212)" fill="url(#linear-gradient-50)"/>
        <path id="Path_70" data-name="Path 70" d="M1237.33,243.077s-.585-.63-.679.129.207,1.737.559,1.2a1.832,1.832,0,0,0,.12-1.329Z" transform="translate(-1227.234 -238.416)" fill="url(#linear-gradient-51)"/>
      </g>
      <path id="Path_71" data-name="Path 71" d="M1253.89,374.821a.652.652,0,0,1-.532.788.7.7,0,0,1-.8-.591.667.667,0,0,1,.5-.82.755.755,0,0,1,.832.623Z" transform="translate(-1241.462 -355.839)" fill="#91b3fa"/>
    </g>
    <path id="Path_72" data-name="Path 72" d="M1377.33,333.746a.308.308,0,0,1-.141-.034l-.413-.21a.311.311,0,0,1,.281-.555l.413.21a.311.311,0,0,1-.141.588Z" transform="translate(-1093.743 -203.431)" fill="#fff"/>
  </g>
</svg>
