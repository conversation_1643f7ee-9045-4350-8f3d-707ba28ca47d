import React from "react";
import { useTheme } from "@mui/material/styles";
import withStyles from '@mui/styles/withStyles';
import Dialog from "@mui/material/Dialog";
import MuiDialogTitle from "@mui/material/DialogTitle";
import MuiDialogContent from "@mui/material/DialogContent";
import IconButton from "@mui/material/IconButton";
import CloseIcon from "@mui/icons-material/Close";
import Typography from "@mui/material/Typography";
import { useMediaQuery } from "@mui/material";
import PropTypes from "prop-types";



const styles = (theme) => ({

  closeButton: {
    position: "absolute !important",
    right: 0,
    top: 0,
    //color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, showCloseButton, ...other } = props;
  return (  
      <>      {(showCloseButton && onClose )? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          size="large">
          <CloseIcon />
        </IconButton>
      ) : null}
 </>

  );
});

const DialogContent = withStyles((theme) => ({

}))(MuiDialogContent);


function ModalPopup(props) {
  const { title, content, open, handleClose, children, className, disableBackdropClick=false, showCloseButton=true} = props;
  const theme = useTheme();
  const fullScreen = useMediaQuery(theme.breakpoints.down('lg'));
  return (
    <div>
      <Dialog
        fullScreen={fullScreen}
        open={open}
        onClose={(e, reason) => {
          if (!disableBackdropClick ||(disableBackdropClick && reason !== 'backdropClick')) {
            handleClose();
          }
        }}
        aria-labelledby={`${title}`}
        className={className}
        disableEscapeKeyDown={!disableBackdropClick ? false : true}>
        {title && <DialogTitle id="responsive-dialog-title" onClose={handleClose} showCloseButton={showCloseButton}>
          {""}
        </DialogTitle>}
        <DialogContent>
          {children || content}
        </DialogContent>
      </Dialog>
    </div>
  );

}
ModalPopup.propTypes = {
  handleClose: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  content: PropTypes.node,
  children: PropTypes.node,
  title: PropTypes.string

};
export default ModalPopup;