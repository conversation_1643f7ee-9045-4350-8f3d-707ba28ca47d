import React, { useEffect } from 'react';
import { Router, <PERSON><PERSON>er<PERSON>outer } from 'react-router-dom';
import { connect } from 'react-redux';
import { createBrowserHistory } from 'history';
import { ThemeProvider } from '@material-ui/styles';
import validate from 'validate.js';
import theme from './theme';
import 'react-perfect-scrollbar/dist/css/styles.css';
import './assets/scss/main.scss';
//import './assets/scss/index.scss';
// import './assets/jag2022/scss/index.scss';

// Import css files
// import "slick-carousel/slick/slick.css";
// import "slick-carousel/slick/slick-theme.css";

import Routes from './Routes';
import { SnackbarProvider } from 'notistack';
const browserHistory = createBrowserHistory();

const App = props => {

  useEffect(() => {
    if (window.location.search) {
      let searchParams = new URLSearchParams(window.location.search);
      // if (searchParams.has("access")) {

      // }
    }
  }, []);

  const notistackRef = React.createRef();
  const onClickDismiss = key => () => {
    notistackRef.current.closeSnackbar(key);
  }
  return <ThemeProvider theme={theme}>
    <BrowserRouter>
      <SnackbarProvider
        ref={notistackRef}
        hideIconVariant={true}
        autoHideDuration={null}
        preventDuplicate={true}
        dense={true}
        maxSnack={2}
        // action={(key) => (
        //   <a onClick={onClickDismiss(key)}>
        //     'Dismiss'
        //   </a>
        // )}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}>
        <Router history={browserHistory}>
          <Routes isAuth={true} />
        </Router>
        
      </SnackbarProvider>
    </BrowserRouter>
  </ThemeProvider>
}
const mapStateToProps = state => {
  return {
    isAuth: state.auth.isAuth,
    user: state.userDetail
  };
};

const mapDispatchToProps = dispatch => {
  return {
    login: (userDetail) => dispatch(actions.setAuthSuccess({ user: userDetail }))
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(App);
