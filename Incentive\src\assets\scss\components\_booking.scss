.booking_details {
  position: relative;
  ul {
    width: 100%;
    position: relative;
    display: table;
    margin: 0;
    & > span {
      content: "";
      position: absolute;
      top: -17px;
      left: 0;
      background-color: #fff;
      padding: 5px 13px 5px 0;
      display: inline-block;
      font-size: 14px;
    }
    li {
      display: table-cell;
      font-size: 14px;
      padding: 6px 0;
      p,
      span {
        color: #808080;
        font-size: 12px;
      }
      p {
        color: #0052cc;
        font-weight: bold;
      }
      span {
        font-weight: 500;
      }
    }
    div {
      margin: 0 0 0 0;
      width: 100%;
      display: table;
    }
  }
  .card-body {
    padding: 0;
  }
  ul.top_section {
    width: 100%;
    display: table;
    margin: 0;
    padding: 0;
    position: absolute;
    top: 0;
    left: 0;
    overflow: hidden;
    li {
      background: #ffffff;
      vertical-align: middle;
      border-radius: 0 18px 0 0;
      &:first-child {
        display: table-cell;
        background-color: #0052cc;
        padding: 0 0 0 25px;
        margin: 0;
        border-radius: 20px 0 0 0;
        text-align: left;
        width: 40%;
        font-size: 12px;
        font-weight: bold;
        vertical-align: middle;
        position: relative;
        height: 40px;
        color: #ffffff;
        &:after {
          position: absolute;
          right: -21px;
          top: -31px;
          width: 30px;
          height: 80px;
          content: "";
          background: #0052cc;
          transform: rotate(40deg);
        }
      }
      &:last-child {
        color: #0052cc;
        text-align: right;
        font-size: 16px;
        padding: 10px 15px;
        vertical-align: bottom;
        font-weight: bold;
      }
    }
  }
  ul.top_section1 {
    width: 100%;
    display: table;
    margin: 0;
    padding: 0;
    position: absolute;
    left: 0;
    top: 0;
    overflow: hidden;
    display: none;
    li {
      background: #ffffff;
      vertical-align: middle;
      border-radius: 0 20px 0 0;
      &:first-child {
        display: table-cell;
        background-color: #e25151;
        padding: 2px 0 0 20px;
        margin: 0;
        border-radius: 20px 0 0 0;
        text-align: left;
        width: 40%;
        font-size: 11px;
        font-weight: bold;
        vertical-align: middle;
        position: relative;
        height: 40px;
        color: #ffffff;
        & > span {
          padding: 0;
          display: inline-block;
          font-size: 10px;
          color: #fff;
          text-align: left;
          display: block;
        }
        &:after {
          position: absolute;
          right: -21px;
          top: -31px;
          width: 30px;
          height: 80px;
          content: "";
          background: #e25151;
          transform: rotate(40deg);
        }
      }
      &:last-child {
        color: #0052cc;
        text-align: right;
        font-size: 16px;
        padding: 10px 15px;
        font-weight: bold;
        vertical-align: bottom;
      }
      & > em {
        font-style: normal;
        vertical-align: middle;
        &.reject {
          display: block;
          font-style: normal;
          font-size: 12px;
          padding: 5px 0 0 10px;
          margin: 0;
          line-height: normal;
        }
      }
      & > img {
        vertical-align: middle;
      }
    }
  }
  &:hover ul.top_section1 {
    display: table;
    width: 100%;
    cursor: pointer;
  }
  .icons_block {
    display: table;
    width: 100%;
    padding: 0;
    margin: 50px 0 7px 0;
    & > div {
      display: table-cell;
      width: 50%;
      margin: 0;
      padding: 0;
      vertical-align: middle;
      position: relative;
      & > em {
        font-style: normal;
        margin: 0 10px 0 0;
        border-radius: 100%;
        width: 30px;
        height: 30px;
        background: #fdd6cc;
        display: inline-block;
        text-align: center;
        vertical-align: middle;
        & > img {
          margin: 8px 0 0 0;
          vertical-align: middle;
        }
        & > i.fa-ticket-alt {
          transform: rotate(40deg);
        }
      }
      & > span {
        color: #fff;
        margin: 2px 0;
        display: inline-block;
        padding: 0px 8px;
        font-size: 10px;
        border-radius: 10px 0px 10px 11px;
        background-color: #174cff;
        position: absolute;
        right: 0;
        z-index: 0;
        top: -35px;
      }
      & > hr {
        width: 100%;
        border-top: 1px dashed #0152cc;
        border-width: 2px;
      }
    }
  }
}
.text-right {
  text-align: right;
}

.toggle_section {
  text-align: center;
}

.toggle_box {
  background-color: #fff;
  width: 90%;
  padding: 0px;
  font-size: 10px;
  text-align: center;
  color: #935d2d;
  position: relative;
  font-weight: bold;
  border-radius: 0 0 20px 20px;
  box-shadow: 0px 27px 16px #0000001a;
  margin: 0 auto;
  .inner-box {
    background: #ffd474;
    color: #9a5c1b;
    padding: 6px 0;
    width: 100%;
    right: 0;
    margin: 0;
    top: 0;
    border-radius: 0 0 20px 20px;
    font-weight: bold;
    font-size: 12px;
  }
  .topshadow {
    opacity: 0.2;
    width: 100%;
    border-top: 1px solid #0000002b;
  }
}

.txt-area {
  text-align: left;
  padding-left: 10px;
  label {
    color: #050505;
    font-size: 12px;
    margin: 0;
    line-height: 13px;
    font-weight: 500;
    span {
      display: block;
      color: #ff0000;
      &.done-txt {
        color: #1ab975;
      }
    }
  }
}
