import {GETAPI_INCENTIVE, POSTAPI_INCENTIVE} from '../services/api.service';

export const GetCommonData = async (method , body, cb) =>{
    let res ={};

    if(method=="POST")
    {

        res = await POSTAPI_INCENTIVE (method, body);
 
    }
    else{
        res =await GETAPI_INCENTIVE (method);

    }
    if(res.errorStatus==false)
    {
      
        return cb(null, JSON.parse(res.data));       
    }
    else{
       return cb(res.errorStatus);
    }
}
