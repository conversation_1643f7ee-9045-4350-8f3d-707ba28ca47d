const path = require('path');
const webpack = require('webpack');
require('dotenv').config();

const HtmlWebpackPlugin = require('html-webpack-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'development',
  devtool: 'inline-source-map',
  entry: './src/index.js',
  output: {
    path: path.resolve(__dirname, 'dist'),
    filename: 'new.js',
    publicPath: '/',
  },
  devServer: {
    port: 3000,
    host: 'localhost',
    disableHostCheck: true,
    historyApiFallback: true,
  },
  module: {
    rules: [{
      exclude: /node_modules/,
      test:/\.(js|jsx|mjs)$/,
      loader: 'babel-loader'
    },
    {
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto', // This allows Webpack to process .mjs files as JS
    },
    {
      test: /\.(sa|sc|c)ss$/,
      use: [
        {
          loader: MiniCssExtractPlugin.loader,
        },
        'css-loader',
        'postcss-loader',
        'sass-loader',
      ],
    },
    {
      test: /.(ttf|otf|eot|svg|woff(2)?)(\?[a-z0-9]+)?$/,
      use: [{
        loader: 'file-loader',
        options: {
          name: '[name].[ext]',
          outputPath: '/fonts/'
        }
      }]
    },
    // {
    //   test: /pdfjs-dist\/build\/pdf.worker\.min\.js/,
    //   use: [
    //     {
    //       loader: 'file-loader',
    //       options: {
    //         name: 'pdf.worker.min.js',
    //         publicPath: '/assets/',
    //         outputPath: 'assets/',
    //       },
    //     },
    //   ],
    // },
    // {
    //   test: /pdfjs-dist\/build\/pdf.worker\.min\.js$/,
    //   use: 'worker-loader',
    // },
    // {
    //   test: /\.[tj]sx?$/i,
    //   include: [
    //     /node_modules\/pdfjs-dist/, // Include pdfjs-dist in Babel processing
    //     /src/ // Include your own source code
    //   ],
    //   loader: 'babel-loader',
    //   options: {
    //     // Make sure to add any necessary Babel presets and plugins
    //     presets: ['@babel/preset-env', '@babel/preset-react'],
    //     plugins: [],
    //   },
    // },
  ]
  },

  resolve: {
    extensions: ['.js', '.jsx', '.scss', '.css', '.mjs','.json'],
  },
  plugins: [
    new webpack.DefinePlugin({
      "API_BASE_URL": JSON.stringify(process.env.API_BASE_URL),
      "TOKEN_KEY": JSON.stringify(process.env.AUTH_COOKIE_NAME),
      "COOKIE_LOGIN": JSON.stringify(process.env.COOKIE_LOGIN)

    }),

    new HtmlWebpackPlugin({
      template: './public/index.html',
      filename: './index.html',
    }),
    new CleanWebpackPlugin(),
    new MiniCssExtractPlugin({
      filename: '[name].css',
      chunkFilename: '[id].css',
    }),
    new CopyWebpackPlugin([{ from: 'public/images', to: 'images' }]),
  ],
};