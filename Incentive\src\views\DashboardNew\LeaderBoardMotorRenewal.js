import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import { makeStyles } from "@material-ui/styles";
import {
    Grid,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    Button,
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(0),
    },
    margin: {
        margin: theme.spacing(0),
    },
    extendedIcon: {
        marginRight: theme.spacing(1),
    },
    content: {
        paddingTop: 150,
        textAlign: "center",
    },
    card: {
        background: "#ffffff",
        boxShadow: "0px 6px 16px #3469CB29",
        borderRadius: "20px",
    },
    textRight: {
        textAlign: "right",
    },
    textCenter: {
        textAlign: "center",
    },
    cardBody: {
        padding: "15px 15px 0 !important",
    },
    image: {
        marginTop: 50,
        display: "inline-block",
        maxWidth: "100%",
        width: 560,
    },
    headTop: {
        background: "#fff",
        boxShadow: "0px 0px 16px #00000014",
        borderRadius: "18px",
        padding: "0 10px",
        alignItems: "center",
        height: "70px",
        margin: 0,
        "& h5": {
            display: "block",
            fontSize: "16px",
            color: "#303030",
            fontWeight: 500,
            margin: "5px 15px 0",
        },
        "& svg": {
            background: " #c8dcfa 0% 0% no-repeat padding-box",
            borderRadius: "50%",
            padding: "8px",
            width: "35px",
            height: "35px",
            color: "#0052cc",
        },
        "& ul": {
            display: "table",
            width: "100%",
            "& li": {
                display: "table-cell",
                width: "auto",
            },
        },
    },
    expandIcon: {
        background: "#00398e",
        color: "#fff",
        borderRadius: "50%",
    },
    warningBtn: {
        background: "#c8dcfa",
        borderRadius: "17px",
        margin: "5px 0",
        fontSize: "12px",
        color: "#0052cc",
        "&:hover": {
            background: "#c8dcfa",
        },
    },
}));

const LeaderBoardMotorRenewal = (props) => {
    const classes = useStyles();
    const { userId, date, productId, EmployeeId, userName } = props;
    const leaderBoardData = props.MotorRenewal && props.MotorRenewal[2] && 
    props.MotorRenewal[2].Status && props.MotorRenewal[2].Response != "[]" && JSON.parse(props.MotorRenewal[2].Response);
    let LeaderData = []
    const response = JSON.parse(localStorage.getItem('user'));

    // useEffect(() => {
    //     setLeaderBoardData([]);
    //     if (!userId) {
    //         return;
    //     }

    //     let url = `Incentive/GetLeaderBoardMotorRenewal/${date}`;

    //     services
    //         .API_GET(url).then(response => {
    //             //debugger;
    //             if (response) {
    //                 if (response.Status && response.Response != "[]") {
    //                     response = JSON.parse(response.Response)

    //                     let item = response
    //                     item.sort(function (a, b) {
    //                         return b.TotalBookings - a.TotalBookings;
    //                     });

    //                     for (let i = 0; i < item.length; i++) {
    //                         item[i] = { ...item[i], idx: i }
    //                     }
    //                     console.log(item)
    //                     // setitems(item);

    //                     //let sourcingDetails = JSON.parse(response[0]);


    //                     setLeaderBoardData(item);
    //                 }
    //             }
    //         })
    //         .catch((err) => {
    //             console.log("Error", err);
    //         });
    // }, [userId, date]);

    if(leaderBoardData && leaderBoardData.length>0){
            let item = leaderBoardData;
            item.sort(function (a, b) {
                return b.TotalBookings - a.TotalBookings;
            });
        
            for (let i = 0; i < item.length; i++) {
                item[i] = { ...item[i], idx: i }
            }
            console.log(item)
            LeaderData = item;
    }

    return (
        <div className={classes.root} className="leader-board">

            <Card className={classes.card}>
                <ul className="lader-ranking">
                    <h6>
                        Leader Board {<i className="fa fa-angle-right" aria-hidden="true"></i>}
                        <span>Show Current rankings
                            {productId == 217 && " (Based on Eligible Bookings)"}
                        </span>
                    </h6>
                    {
                        LeaderData && LeaderData.length > 0 && LeaderData.map((item, index) =>
                            item.idx < 3 ?
                                <div key={index + 1} className={item.Ecode == EmployeeId ? "active" : ""}>
                                    <li>
                                        <span>
                                            Rank #{item.idx + 1}

                                            {index != 0 || <img src="/images/Trophy.svg" />}
                                        </span>
                                        <span><strong>{(item.Ecode != EmployeeId) ? item.UserName : (<strong className={index == 0 ? "" : ""}>YOU</strong>)}  </strong></span>
                                    </li>

                                    {productId == 217 &&
                                        <li>
                                            <span>Bookings {item.TotalBookings}</span>
                                        </li>}
                                </div> 
                                : 
                                item.idx>=3 && (item.Ecode == EmployeeId) && 
                                <div key={index + 1} className={item.Ecode == EmployeeId ? "active" : ""}>
                                    <li>
                                        <span>
                                            Rank #{item.idx + 1}

                                            {index != 0 || <img src="/images/Trophy.svg" />}
                                        </span>
                                        <span> <strong className={index == 0 ? "" : ""}>YOU</strong> </span>
                                    </li>

                                    {productId == 217 &&
                                    <li>
                                        <span>Bookings {item.TotalBookings}</span>
                                    </li>}
                                </div>
                        )
                    }
                </ul>
            </Card>
        </div>
    );
};

export default LeaderBoardMotorRenewal;
