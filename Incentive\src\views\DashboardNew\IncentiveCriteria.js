import { Button } from "@mui/material";
import React, { useEffect, useState } from "react";
import { GetCriterialURL } from "../../services";
import PDFViewer from "./PDFViewer";
import CloseIcon from '@mui/icons-material/Close';
import Tabs from '@mui/material/Tabs';
import Tab from '@mui/material/Tab';
import Box from '@mui/material/Box';




const IncentiveCriteria=({ProductId,Date})=>{

const [openPdf, setOpenPdf] = useState(false);

const [pdfUrl, setPdfUrl]= useState(null);

const [productData, setProductData]= useState([]);


// const handleClose=()=>{
//     setOpenPdf(false);
// }

const onViewClick=(e)=>{

    setPdfUrl(e.currentTarget.value);
    setOpenPdf(true);

}

const handleChange = (event, newValue) => {
    setOpenPdf(false);
    setPdfUrl(newValue);
    setOpenPdf(true);
  };


useEffect(()=>{

    if(productData.length>0)
    {
        setPdfUrl(productData[0].CriteriaHtml);
        setOpenPdf(true);
    }

},[productData])

useEffect(() => {
        const fetchData = async () => {
        let data = await GetCriterialURL(ProductId, Date);
        //   for(let )
        
        if(!data.errorStatus)
        {
            setProductData(data.data);
        }
    };
      
    fetchData();
}, [ProductId]);

 return(
    
    // <div className="Criteria">
    // <h3>Incentive Criteria</h3>
   
    // {
    //     productData.length>0 &&
    //     <>
    //     <Tabs value={pdfUrl} onChange={handleChange} aria-label="basic tabs example">
    //     {
    //         productData.map((process,index)=>{
    //             return(
    //                 <Tab label={process.TabName} value={process.CriteriaHtml}/>
    //             )
    //         })
    //     }
    //     </>
    // }
   

    // {pdfUrl && <PDFViewer pdfUrl={pdfUrl}/> }
   
    
    // </div>
    <div className="Criteria">
        <h3>Incentive Criteria</h3>
        {
            productData.length>0 && 
            <>
            <Tabs value={pdfUrl} onChange={handleChange} className="CriteriaTab">
            {
            productData.map((process,index)=>{
                 return(
                     <Tab label={process.TabName} value={process.CriteriaHtml} />
                 )
             })
            }
            </Tabs>
            </>
        }
        {
            openPdf && <PDFViewer pdfUrl={pdfUrl}/>
        }
    </div>

 )   
}
export default IncentiveCriteria;