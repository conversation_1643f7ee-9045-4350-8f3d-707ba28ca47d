import React, { useEffect, useState } from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../../components/Loader";

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

const JagCriteria = (props) => {
  const { show, handleClose, BU, productId, version } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getCriteria = () => {
    setCriteriaList([]);
    let v = version? version: '2020';
    services
      .API_GET(`jag/GetJagSeeCritariaData/0?version=${v}`)
      .then(response => {
        console.log(response && response != "[]");
        
        if (response && response != "[]") {
          setCriteriaList(JSON.parse(response.Response));
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }
  const getCriteriaHTML = () => {
    setCriteriaListHtml([]);
    let v = version? version: '2020';
    services
      .API_GET(`jag/GetJagSeeCritariaHTML/${BU}?version=${v}`)
      .then(response => {
        console.log(response && response != "[]");
        
        if (response && response != "[]") {
          setCriteriaListHtml(JSON.parse(response.Response));
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  useEffect(() => {
    getCriteria();
    getCriteriaHTML();
  }, [props]);

  return (
    <div>
      {/* <Button variant="outlined" color="primary" onClick={handleClickOpen}>
        Open dialog
      </Button> */}

      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"sm"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
          <strong>CRITERIA</strong>
        </DialogTitle>
        <DialogContent className="critiria-popup">
          <div className="popup-inner-box">
            <div className="jag-criteriaHtml" dangerouslySetInnerHTML={{ __html: (criteriaListHtml && criteriaListHtml.length > 0) ? criteriaListHtml[0].critariaHTML : null }}>

            </div>

            <br />

            <div className="jag-criteriaList">
              <table>
                <tr>
                  <th>Point Slab</th>
                  <th>Cash Reward</th>
                  <th>Lottery Tickets</th>
                </tr>

                {
                  criteriaList && criteriaList.length > 0 && criteriaList.map((item, index) => {
                    if (item.MinValue <= 15000)
                      return <tr key={index}> <td>{item.MinValue}</td> <td>{item.CashReward}</td> <td>{item.LotteryTickets}</td> </tr>
                  }
                  )
                }
              </table>
              <table>
                <tr>
                  <th>Point Slab</th>
                  <th>Cash Reward</th>
                  <th>Lottery Tickets</th>
                </tr>

                {
                  criteriaList && criteriaList.length > 0 && criteriaList.map((item, index) => {
                    if (item.MinValue > 15000)
                      return <tr key={index}> <td>{item.MinValue}</td> <td>{item.CashReward}</td> <td>{item.LotteryTickets}</td> </tr>
                  }
                  )
                }
              </table>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
export default JagCriteria;