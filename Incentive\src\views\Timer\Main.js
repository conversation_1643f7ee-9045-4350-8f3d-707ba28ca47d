import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import moment from "moment";


import * as services from "../../services";
import STORAGE from '../../store/storage'
// import { SRLWrapper } from "simple-react-lightbox";




const calculateTimeLeft = () => {

  let difference = +new Date(`06/15/2021 20:00:00`) - +new Date();
  let timeLeft = {};

  if (difference > 0) {
    timeLeft = {
      DAYS: Math.floor(difference / (1000 * 60 * 60 * 24)),
      HOURS: Math.floor((difference / (1000 * 60 * 60)) % 24),
      MINS: Math.floor((difference / 1000 / 60) % 60),
    };
  }

  return timeLeft;

}


export default function Main() {


  const urlParams = useParams();
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());
  const [AgentData, setAgentData] = useState(null);
  const [nextMsg, setNextMsg] = useState(0);
  const [AgentMsgs, setAgentMsgs] = useState([
    "Congratulations on your fantastic run in Jeeto Apna Ghar contest",
    "See you at the finale when winners will be announced live"

  ]);


  useEffect(() => {

    setTimeout(function () {

      if (nextMsg >= AgentMsgs.length - 1) {
        setNextMsg(0);
      }
      else {
        setNextMsg(nextMsg + 1);
      }
    }, 6000)

  }, [nextMsg]);

  useEffect(() => {

    setTimeout(function () {
      setTimeLeft(calculateTimeLeft());
    }, 60000)

  }, [timeLeft]);

  useEffect(() => {

    if (AgentData) {
      //debugger;
      let agentmsg = AgentMsgs;

      if ((AgentData.LotteryTicket + AgentData.BonusTickets) > 0) {
        agentmsg.splice(1, 0, `You have won total ${(AgentData.LotteryTicket + AgentData.BonusTickets)} lottery tickets from your performance `)
      }

      if (AgentData.BonusTickets > 0) {
        agentmsg.splice(2, 0, `You also have won additional ${AgentData.BonusTickets} lottery tickets in Feb and March Bonanza`)
      }
      setAgentMsgs(agentmsg);

    }

  }, [AgentData]);


  useEffect(() => {

    console.log("urlParams", urlParams);
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");

    let urlToken = urlParams.Token;
    STORAGE.setAuthToken(urlToken);
    //debugger;

    services
      .API_GET(`Jag/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {
          if (response.status && response.status == 401) {
          } else {
            //debugger;
            setAgentData(response);
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

  }, []);


  return (

    <div className="jagBanner" >
      <img src="/images/JAGlogoTimer.png" className="logo" />
      <h2>JEETO APNA GHAR FINALE</h2>
      <p className="beginsIN">BEGINS IN</p>
      <div className="calender">

        <div>          {timeLeft.DAYS} <p>DAYS</p>        </div><span>:</span>
        <div>          {timeLeft.HOURS} <p>HOURS</p>        </div><span>:</span>
        <div>          {timeLeft.MINS} <p>MINS</p>        </div>




      </div>
      <div className="msg">
        <p className="jagmsg"><img src="/images/Keys_transparent_bg.gif" className="keyimg" />
          <span>{AgentMsgs[nextMsg]}</span></p>
      </div>
    </div>
  );
}
