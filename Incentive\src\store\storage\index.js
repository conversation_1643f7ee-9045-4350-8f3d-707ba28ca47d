// import STORAGE from './storage'

// export default STORAGE

import {CONFIG} from './../../appconfig';
const appConfig = CONFIG;
const setLSToken=(tokenkey,token)=>{
    localStorage.setItem(tokenkey || 'token',token);
}

const getLSToken=(tokenkey)=>{
    const name=tokenkey || 'token';
    if(localStorage.getItem(name)){
        return localStorage.getItem(name);
    }
}
const STORAGE = {
    setAuthToken: (token) => {
        setLSToken(appConfig.AUTH_COOKIE_NAME, token)
        return Promise.resolve(true)
    },
    getAuthToken: () => {
        return Promise.resolve(getLSToken(appConfig.AUTH_COOKIE_NAME))
    },
    checkAuth: () => {
        return !!getAuthToken(appConfig.AUTH_COOKIE_NAME)
    }
}

export default STORAGE
