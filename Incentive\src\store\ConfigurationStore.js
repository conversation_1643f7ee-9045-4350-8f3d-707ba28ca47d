import { createStore, applyMiddleware, compose } from 'redux';
import { persistStore, persistReducer } from 'redux-persist';
import thunk from 'redux-thunk';
import storage from 'redux-persist/lib/storage';
import rootReducer from '../store/reducers';
const persistConfig = {
    key: 'root',
    storage,
    whitelist: ['auth']

};

/* eslint-disable no-underscore-dangle */
const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;
const persistedReducer = persistReducer(persistConfig, rootReducer)
export default () => {
    let store = createStore(
        persistedReducer,
        composeEnhancers(applyMiddleware(thunk))
    )
    let persistor = persistStore(store)
    return { store, persistor }
}
