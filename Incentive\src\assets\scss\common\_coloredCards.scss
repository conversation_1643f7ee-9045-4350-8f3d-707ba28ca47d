.main-cards {
    display: flex;
    padding-bottom: 16px;
    flex-wrap: wrap;
    justify-content: center;
    @media (min-width: 992px){
      justify-content: flex-start;
    }
    .main-card {
        flex-basis: 51%;
        color: $white;
        padding: 16px 0;
        border-radius: 10px;
        margin-bottom: 16px;
        background: url(/images/cardBg.png) no-repeat 100%/cover;
        p, h2{
            color: $white;
        }
        > div{
            padding: 0 16px;
        }
        @media (min-width: 600px) {
            flex-basis: 30%;
            margin-right: 16px;
        }
        @media (min-width: 992px) {
            max-width: 19%;
        }
    }
    .main-card-footer {
        margin-left: 0;
        padding: 10px 0 0;
    }
}

