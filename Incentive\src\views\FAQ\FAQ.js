import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from '@material-ui/core/styles';
import { Grid } from "@material-ui/core";
import STORAGE from '../../store/storage'
import * as services from "../../services";
import moment from "moment";
import HelpAndSupport from "./HelpAndSupport";
import RightColumn from "./RightColumn";

import { useParams } from "react-router";


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: 'absolute',
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: '2px solid #000',
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));

const FAQ = (props) => {
  const classes = useStyles();
  const urlParams = useParams();

  useEffect(() => {
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
    //const tokenPath = window.location.pathname.split('/');
    //if (tokenPath.length >= 3) {
    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);
    services
      .API_GET(`Incentive/InsertAgentIncentivLog/${urlToken}/0/7/${dt}?PageName=FAQ&EventName=PageLoad`).then(response => { })
      .catch((err) => {
      });
    //}

  }, []);


  return (
    <div className={classes.root}>
      <div className="wrapper">
        <Grid container spacing={3}>
          <Grid item sm={8} md={8} xs={12} className='help-section'>
            <HelpAndSupport />
          </Grid>
          <Grid item sm={4} md={4} xs={12} className='contact-us'>
            <RightColumn />
          </Grid>
        </Grid>
      </div>
    </div>
  );
};

export default FAQ;
