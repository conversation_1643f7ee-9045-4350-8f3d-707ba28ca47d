import React, { useEffect, useState, Fragment } from "react";
import moment from "moment";
import { makeStyles } from "@material-ui/core/styles";
import LoaderComponent from "../../components/Loader";
import {
  Grid, Card, CardContent
} from "@material-ui/core";


import Ranking from "./Ranking";
import LotteryBanner from "./LotteryBanner";
import MessagingCenter from "./MessagingCenter";
import Performance from "./Performance";
import RewardCalculator from "./RewardCalculator";
import LeaderBoard from "./LeaderBoard";
import FinaleUpdate from './FinaleUpdate';
import IncentiveCalculations from "./IncentiveCalculations";
import BookingTable from "./bookingTable";
import Bonanza from "./Bonanza";
import { useParams } from "react-router";
import STORAGE from '../../store/storage'
import * as services from "../../services";
//import * as actions from './store/actions';

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
}));

const getLastMonths = (months) => {
  let monthNames = moment.monthsShort()
  let today = new Date();
  let d; let mth;
  let monthsList = [];
  for (let i = months; i >= 1; i -= 1) {
    d = new Date(today.getFullYear(), today.getMonth() - i, 1);
    mth = d.getMonth() + 1
    mth = mth < 10 ? "0" + mth : mth;
    monthsList.push({
      value: "01-" + mth + "-" + d.getFullYear(),
      name: monthNames[d.getMonth()] + " " + d.getFullYear()
    });
  }
  return monthsList;
};

export default function jag(props) {

  const urlParams = useParams();
  const classes = useStyles();
  //const [isLoading, setIsLoading] = useState(false);
  const [date, setDate] = useState(moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY"));
  const [months] = useState(getLastMonths(2));
  const [userId, setUserId] = useState(null);
  const [AgentData, setAgentData] = useState(null);



  //const [userDetail, setUserDetail] = useState({});
  useEffect(() => {
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");

    //const tokenPath = window.location.pathname.split('/');
    //if (tokenPath.length >= 3) {
    //Set Token Using URL parameter
    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);

    services
      .API_GET(`Incentive/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {
          if (response.status && response.status == 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            setUserId(response.AgentId);
            localStorage.setItem('user', JSON.stringify(response));

            let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JAG&EventName=Pageload`;
            if (Source) {
              url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JAG&EventName=` + Source
            }

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
    services
      .API_GET(`Jag/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {
          if (response.status && response.status == 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            setAgentData(response);
            localStorage.setItem('jaguser', JSON.stringify(response));
          }

        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

    // let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/1/${dt}?PageName=JAG&EventName=Pageload`;
    // if (Source) {
    //   url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/1/${dt}?PageName=JAG&EventName=` + Source
    // }

    // services
    //   .API_GET(url).then(response => { })
    //   .catch((err) => {
    //   });
    // //}
  }, []);

  const handleChange = (event, newValue) => {
    setDate(event.target.value || moment().startOf('month').format("DD-MM-YYYY"));
  };

  const showGraph = () => {
    let show = true;
    if (AgentData && AgentData.BU && (AgentData.BU == 'Health Renewal' || AgentData.BU.indexOf('SME') > -1)) {
      show = false;
    }
    return show;
  };

  return (
    <>
      <LotteryBanner agentDetail={AgentData}/>
      <div className={classes.root} className="wrapper">

        {/*isLoading ? <LoaderComponent open={true} /> : null*/}
        {/* <Bonanza userId={userId} productId={AgentData == null ? null : AgentData.ProductId}/> */}
        <Grid container spacing={3}>
          {/* <Grid item sm={12} md={12} xs={12} className="paddingTopBottom">
          <span className="month-view">
            <i className="fa fa-calendar" aria-hidden="true"></i>
            <select onChange={handleChange} id="month" defaultValue={date} >
              <option disabled="disabled" value="">Select Month</option>
              {months.map((item, index) =>
                <option
                  key={index}
                  value={item.value}
                >
                  {item.name}
                </option>

              )}
            </select>
          </span>
        </Grid> */}

          <Grid item sm={12} md={4} xs={12}>
            <Ranking userId={userId} date={date} />
          </Grid>
          <Grid item sm={12} md={4} xs={12}>
            <LeaderBoard userId={userId} memberType={AgentData == null ? null : AgentData.MemberType} BU={AgentData == null ? null : AgentData.BU} />
          </Grid>
          <Grid item sm={12} md={4} xs={12}>
            <MessagingCenter userId={userId} productId={AgentData == null ? null : AgentData.ProductId} />

          </Grid>



          {/* <Grid item sm={12} md={12} xs={12}>
          <RewardCalculator userId={userId} date={date} />
        </Grid> */}
          <Grid item sm={12} md={12} xs={12}>
            <Performance userId={userId} date={date} />
          </Grid>
          <Grid item sm={12} md={12} xs={12}>
            <div className="graphbox">

              {showGraph() && <BookingTable userId={userId} date={date} agentDetail={AgentData} />
              }
            </div>
          </Grid>


        </Grid>
        {/* <FinaleUpdate userId={userId} productId={AgentData == null ? null : AgentData.ProductId} /> */}


      </div>
    </>
  );
}
