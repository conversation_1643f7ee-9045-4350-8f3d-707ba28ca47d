import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { 
    Input,

  } from '@material-ui/core';
import SearchIcon from '@material-ui/icons/Search';
import Fab from '@material-ui/core/Fab';

const useStyles = makeStyles(theme => ({
  root: {
    borderRadius: '5px',
    alignItems: 'center',
    padding: theme.spacing(1, 2),
    display: 'flex',
    flexBasis: 280,
    border: '1px solid #ccc',
    width: 280,
  },
  icon: {
    marginRight: theme.spacing(1),
    color: theme.palette.text.secondary
  },
  input: {
    flexGrow: 1,
    fontSize: '14px',
    lineHeight: '16px',
    letterSpacing: '-0.05px',
    '&:before':{
      borderBottom: 'none',
    },
    '&:after':{
      borderBottom: 'none',
    },
    '& input':{
      padding: '3px 10px',
    }
  },
  search: {
    fontSize: '1.2rem'
    //margin: theme.spacing(1),
  },
}));

const SearchInput = props => {
  const { className, onChange, style, ...rest } = props;

  const classes = useStyles();

  return (
    <div
      {...rest}
      className={clsx(classes.root, className)}
      style={style}
    >
      
      <SearchIcon className={classes.search}/>
      <Input
        {...rest}
        className={classes.input}
        onChange={onChange}
      />
    </div>
  );
};

SearchInput.propTypes = {
  className: PropTypes.string,
  onChange: PropTypes.func,
  style: PropTypes.object
};

export default SearchInput;
