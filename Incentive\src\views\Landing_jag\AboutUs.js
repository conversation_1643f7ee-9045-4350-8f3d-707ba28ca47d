import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";

import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';



export default function AboutUs(props) {


  var mainSlider = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 800,
    slidesToShow: 1,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  return (


    <Grid item md={9} xs={12} className="main-slider">
      {/* <h6>About Us</h6> */}
      <Slider {...mainSlider}>
        <div className="banner slide1">
          <Card className="card">
            <CardContent>
              <div class="carousel-caption d-none d-md-block">
                <p><span className="h5">Jeeto Apna Ghar</span> is a Pb Initiative, where the agents get an opportunity to win their very own house and cash  reward up to Rs 5 lakhs among other amazing privileges.</p>
                <button className="btn">Get Started</button>
              </div>
              {/* <img src="/images/slider_img_rewards.svg" alt="" /> */}
            </CardContent>
          </Card>
        </div>
        <div className="banner slide2">
          <Card className="card">
            <CardContent>
              <div class="carousel-caption d-none d-md-block">
                <p>The winners for the contest will be declared with the help of lucky draw, at the end of FY 20-21, once the issuance cycle is completed.</p>
                <button className="btn">Get Started</button>
              </div>
              {/* <img src="/images/slider_img_rewards.svg" alt="" /> */}
            </CardContent>
          </Card>
        </div>
        <div className="banner slide3">
          <Card className="card">
            <CardContent>
              <div class="carousel-caption d-none d-md-block">
                <p>Keep selling more to improve your chance to make sure you win. Greater the bookings more lottery tickets and greater probability to win.</p>
                <button className="btn">Get Started</button>
              </div>
              {/* <img src="/images/slider_img_rewards.svg" alt="" /> */}
            </CardContent>
          </Card>
        </div>
        {/* <div>
          <Card className="card ceo_msge">
            <CardContent>
              <div className="image">
                <img src="/images/img-sarabvir.png" alt="" />
              </div>
              <div class="carousel-caption d-none d-md-block">
                <h5>CEO's Message</h5>
                <p>Traditionally, life insurance products in India had insurance elements but were bought by the consumers mostly as an investment product. </p>
                <div className="name">
                  Sarbvir Singh
                    <small>CEO - Policybazaar.com</small>
                </div>
              </div>
            </CardContent>
          </Card>
        </div> */}
      </Slider>
    </Grid>

  );
}
