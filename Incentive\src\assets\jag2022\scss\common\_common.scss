/*-----------jag-design-2022-------------*/
.d-flex{display: flex;align-items: center;}
ul{list-style: none;padding: 0;margin: 0;}
img{max-width: 100%;outline: none!important;box-shadow: none!important;vertical-align: middle;}
button{outline: none!important;box-shadow: none!important;}
.text-center{text-align: center;}
.d-block{display: block;}
em{font-style: normal;}

/*------------lottie-css-----------*/
.react-lottie{z-index: 1; position: absolute; width: 100%; height: 100%; pointer-events: none; 
left: 0;}
.react-lottie div{max-width: 668px;height: 608px!important;outline: none!important;float: right;
width: 100%!important;margin-top: -103px!important;clip-path: polygon(0% 9%, 100% 0%, 100% 100%, 0% 100%);
-webkit-clip-path: polygon(0% 9%, 100% 0%, 100% 100%, 0% 100%);}





/*-----------media-css-------------*/
@media (max-width:1024px) and (min-width:320px){
.jag-design{height: 480px;}

.jag-design .top-header, .jag-design .main-header ul li:first-child, .agentdetails .house-image:after,
.agentdetails .house-image img, .bar-graph:before{display: none;}

.performance-details h2, .calculate-opportunity h2, .bar-graph h2, .message-criteria h2
{font-size: 20px!important;line-height: 24px!important;}
.performance-details ul.performance-box li:nth-child(2), .performance-details ul.performance-box li:last-child
{padding-right: 0;}

.ape-slider .range-slider.percentage-slider p{margin-bottom: 15px;}

.bar-graph .purple-box canvas{height: 200px!important;width: 100%!important}

.react-lottie{z-index: auto;}

.criteria-content ul li span.d-block{display: inline;}

.calculation-modal{left: 0!important;background: #253858e0!important;box-shadow: none!important;}

.agentdetails ul.rewards-listing li:first-child{padding-right: 45px!important;}



}
@media (max-width:900px) and (min-width:768px){
.react-lottie div{max-width: 490px;height: 100%!important;}

}
@media (max-width:767px) and (min-width:320px){
.projected-growth{padding: 30px 0px 0px 4px!important}    
// .projected-growth ul{display: block!important;}
.projected-growth ul li:last-child h4{margin-bottom: 25px;}

.react-lottie{height: auto!important;}
.react-lottie div{max-width: 100%;height: 100%!important;margin-top: 100%!important;
clip-path: none;-webkit-clip-path: none;}
 
}
@media (max-width:540px) and (min-width:500px){
.react-lottie div{margin-top: 60%!important;}

}