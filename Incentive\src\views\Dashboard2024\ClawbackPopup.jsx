
import React, { useContext, useEffect, useState } from "react";
import ModalPopup from "./ModalPopup";
import IncentiveCalculationPopup from "./Popups/IncentiveCalculationPopup";
import FosAllowance from "./Popups/FosAllowance";
import MissellDeduction from "./Popups/MissellDeduction";
import { DashboardContext } from "./Context/Context";
import ArrearClawbackCalculation from "./Popups/ArrearClawbackCalculation";


function ClawbackPopup() {

    const [className, setClassName]= useState("");

    const popupHandle= useContext(DashboardContext);

    useEffect(()=>{
        // console.log("Popupup handle is ", popupHandle.popup);
        switch(popupHandle.popup){
            case 1:
                setClassName("BookingIncentivePopop");
                break;
            case 2:
                setClassName("NetArrearClawbackPopup");
                break;
            case 3:
                setClassName("FOSVisitAllowance");
                break;
            case 4:
                setClassName("NetArrearClawbackPopup");
                break;
            default:
                break;
        }

    },[popupHandle.popup])


    const closeModal=()=>{
        popupHandle.closeFunction();
    }

    return (
       
            <ModalPopup open={popupHandle.popup!=5 && popupHandle.open}  title='NC LeadPopup' handleClose={closeModal} className={className}>
                {popupHandle.popup==1 && <IncentiveCalculationPopup/>}
                {popupHandle.popup==2 && <ArrearClawbackCalculation/>}
                {popupHandle.popup==3 && <FosAllowance/>}
                {popupHandle.popup==4 && <MissellDeduction/>}
            </ModalPopup>
      
    )
}
export default ClawbackPopup;