@import '../variables/variables';
@import '../common/mixins';

.app-bar{
    box-shadow: none !important;
    background: transparent !important;
    .tabs{
        padding: 0;
        border-bottom: 1px solid $grey-lighter;
        margin: 0 8px;
        min-height: 37px;
        > div > span {
            display: none;
        }
        @media (min-width: 992px) {
            margin: 0;
            width: 670px;
        }
        & div:nth-child(1), div:nth-child(4){
            color: $primary-color;

        }
        .tab {
            text-transform: capitalize;
            min-width: 140px;
            font: 400 16px/20px Lato;
            color: $primary-color;
            min-height: 37px;
            &.active-tab{
                border-bottom: 2px solid $secondary-color;
                color: $secondary-color;
            }
        }
    }
}
.tab-panel{
    padding: 0;
    > div{
        padding: 8px 0 !important;
    }
}


