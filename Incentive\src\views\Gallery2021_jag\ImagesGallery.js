import { Grid } from '@material-ui/core';
import React from 'react';
import FilterIcon from '@material-ui/icons/Filter';
import moment from "moment";

const ImagesGallery = (props) => {
    //debugger;
    const { index, Categories } = props;


    return (
        Categories && Categories.data && Categories.data[index] &&
        <>
           <Grid item sm={12} md={12} xs={12}>  <div  className="cateGoryIcon"><FilterIcon/></div>  <h3 className="version">  {Categories.data[index].CategoryName}</h3><p className="versiondate">Created on {(Categories.data[index].CategoryDate)?moment(Categories.data[index].CategoryDate, 'YYYY-MM-DD').format('Do MMM YYYY'):''}</p> </Grid>
            {Categories.data[index].Images.map((data, index) =>
             <Grid item sm={12} md={3} xs={12}> 
             <img src={(data.Path)?data.Path:''} />   
             <p className="Name">{(data.Name)?data.Name:''}</p>                 
              <h4 className="date">{(data.Date)?moment(data.Date, 'YYYY-MM-DD').format('Do MMM YYYY'):''}</h4>
              <p className="Description">{(data.Description)?data.Description:''}</p>    
            </Grid>
             )}
            </>
    )
}

export default ImagesGallery;