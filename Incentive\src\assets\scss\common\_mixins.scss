@import '../variables/variables';

@function responsive-px($min-px, $max-px, $min-vw: 320, $max-vw: 1920) {
    @return calc(#{$min-px}px + (#{$max-px} - #{$min-px}) * ((100vw - #{$min-vw}px) / (#{$max-vw} - #{$min-vw})));
}

@mixin secondary-button {
    display: inline-block;
    padding: 5px 10px;
    background: $secondary-color;
    color: $white;
    border-radius: 5px;
    text-transform: uppercase;
    margin: 0;
    min-width: 100px;
}

@mixin disabled-button {
    display: inline-block;
    padding: 5px 10px;
    background: transparent;
    color: $grey-light;
    border-radius: 5px;
    text-transform: uppercase;
    margin: 0;
    min-width: 100px;
}

@mixin grey-button {
    display: inline-block;
    padding: 5px 10px;
    background: $grey-light;
    color: $primary-color;
    border-radius: 5px;
    text-transform: uppercase;
    margin: 0 10px 0 0;
    min-width: 100px;

}
