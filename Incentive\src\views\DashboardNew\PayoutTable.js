import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';
import Tooltip from '@material-ui/core/Tooltip';
import PayoutClawback from "../../components/Dialogs/PayoutClawback";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: " 0px 6px 16px #3469CB29",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },  
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);
const PayoutTable = (props) => {

  const classes = useStyles();
  const { userId, date, handleTotalPayout } = props;
  const [payoutList, setPayoutList] = useState(null);
  const [showClawbacks, setShowClawbacks] = React.useState(false);
  const [showArrears, setShowArrears] = React.useState(false);
  const [showMisssell, setshowMisssell] = React.useState(false);

  const filterAdjustments = (data, type) => {
    if (!data)
      return [];
    let result = [];
    data.forEach(element => {
      if (element.AdjustmentType === type)
        result.push(element)
    });
    return result;
  };


  useEffect(() => {
    setPayoutList(null);
    if (!userId) {
      return;
    }
    // if(date == "01-08-2021"){
    //   return;
    // }
    services
      .API_GET(`Incentive/GetAgentPayout/${userId}/${date}`)
      .then(response => {
        //debugger;
        //
        // if (response.Status && response.Response !== "[]" && response.Response.length > 0) {
        //   setPayoutList(JSON.parse(response.Response));
        // }
        setPayoutList(response);


        handleTotalPayout(response.TotalPayout)


      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [date, userId]);

  const renderTooltipWrapper = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                      {
                        moment(breakup.AmountMonth).format('MMM YYYY')
                      }

                    </td>

                    <td style={breakup.Amount < 0 ? { width: "20%", textAlign: "right", color: "red" } : { width: "20%", textAlign: "right", color: "#00458b" }} >{breakup.Amount}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderTooltipArray = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                      {breakup.Name}

                    </td>

                    <td style={{ width: "20%", textAlign: "right", color: "red" }} >{breakup.Value}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderTooltipOtherArray = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                      {breakup.Name}

                    </td>
                    {breakup.Amount > 0 && <td style={{ width: "20%", textAlign: "right", color: "green" }} >{breakup.Amount}</td>}
                    {breakup.Amount == 0 && <td style={{ width: "20%", textAlign: "right" }} >{breakup.Amount}</td>}
                    {breakup.Amount < 0 && <td style={{ width: "20%", textAlign: "right", color: "red" }} >{breakup.Amount}</td>}
                    
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderIncentiveTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>( As per the issuance till {moment(payoutList.IncentiveCycle).format('Do MMMM YYYY')}. Incentive for the remaining policies will be paid as arrears with the next month incentive if the policy gets issued)</span>
    </div>
  }
  const renderWarningsTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>{payoutList.WarningCount} WARNINGS</span>
    </div>
  }

  const renderQualityScoreTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>Quality Score : {payoutList.QScore} %</span>
    </div>
  }

  const handleClickOpen = (value) => {
    setShowClawbacks(value);
  };
  const handleArrearsOpen = (value) => {
    setShowArrears(value);
  };
  const handleMisssellOpen = (value) => {
    setshowMisssell(value);
  };

  //

  if (payoutList == null || payoutList.length === 0 || payoutList.IsDisplay === false || payoutList.ProductId === -7 || payoutList.ProductId === -115)
    return <></>;
  else
    return payoutList && (

      <div className={classes.root}>
      <div className="pading-zero" >
        <div className="payout">

          <ul className="payout-box">
            <h6>
              Payouts
              <span>Payouts as per performance</span>
            </h6>
            <li className="firstbox">
              <div>
                <strong>Actual Incentive Payout (In {moment(payoutList.IncentiveCycle).format('MMMM')}) </strong>
                <span>
                  <i className="fa fa-inr"></i> {Math.round(payoutList.TotalPayout).toLocaleString('en-IN')}

                </span>
              </div>
            </li>
            <li className='modal-popup'>
              <span>
          
                {payoutList.ProductId == 117 ? "Incentive before salary deduction" : "Incentive"}


                <button className='tooltip'>
                  <WrapperTooltip
                    disableFocusListener
                    TransitionComponent={Zoom}
                    placement="top"
                    enterTouchDelay={0} leaveTouchDelay={9000}
                    arrow
                    title={renderIncentiveTooltip(payoutList)}
                    classes={{ tooltip: classes.customWidth }}
                  >
                    <i className="fa fa-info-circle"></i>
                  </WrapperTooltip>
                </button>  
              </span>
              <p>
              <i class="fa fa-rupee"></i>{Math.round(payoutList.Incentive).toLocaleString('en-IN')}
              </p>
            </li>
            <li className='modal-popup'>
              <span className="ancor cursorPointer" onClick={(e) => handleArrearsOpen(true)}>{`Arrears`}</span>
              <button className='tooltip'>
                <WrapperTooltip
                  disableFocusListener
                  TransitionComponent={Zoom}
                  placement="top"
                  enterTouchDelay={0} leaveTouchDelay={5000}
                  arrow
                  title={renderTooltipWrapper(filterAdjustments(payoutList.Adjustments, "ARREARS"))}
                  classes={{ tooltip: classes.customWidth }}
                >
                  <i className="fa fa-info-circle"></i>
                </WrapperTooltip>
              </button>

            {/* </span> */}
              <p className='border-text' style={payoutList.TotalArrears < 0 ? { color: "#ED3434", width: "100%", paddingLeft: "0", float: "right", position: "relative" } : {}}>
                {Math.round(payoutList.TotalArrears).toLocaleString('en-IN')}
              </p></li>
              <li className='modal-popup'>
                <span className="ancor cursorPointer" onClick={(e) => handleClickOpen(true)}>{`Clawbacks`}</span>
                <button className='tooltip'>
                  <WrapperTooltip
                    disableFocusListener
                    TransitionComponent={Zoom}
                    placement="top"
                    enterTouchDelay={0} leaveTouchDelay={5000}
                    arrow
                    title={renderTooltipWrapper(filterAdjustments(payoutList.Adjustments, "Clawback"))}
                    classes={{ tooltip: classes.customWidth }}
                  >
                    <i className="fa fa-info-circle"></i>
                  </WrapperTooltip>
                </button>  
            {/* </span> */}
              <p className='border-text' style={payoutList.TotalClawbacks < 0 ? { color: "#ED3434", width: "100%", paddingLeft: "0", float: "right", position: "relative" } : {}}>
                {payoutList.TotalClawbacks && Math.round(payoutList.TotalClawbacks).toLocaleString('en-IN')}
              </p></li>
            <li><span>Other Components
              <button className='tooltip'>
                <WrapperTooltip
                  disableFocusListener
                  TransitionComponent={Zoom}
                  placement="top"
                  enterTouchDelay={0} leaveTouchDelay={5000}
                  arrow
                  title={renderTooltipOtherArray(filterAdjustments(payoutList.Adjustments, "Other"))}
                  classes={{ tooltip: classes.customWidth }}
                >
                  <i className="fa fa-info-circle"></i>
                </WrapperTooltip>
              </button>
            </span>
              <p className='border-text' style={payoutList.OtherTotalArrears < 0 ? { color: "#ED3434", width: "100%", paddingLeft: "0", float: "right", position: "relative" } : {}}>
                {payoutList.OtherTotalArrears && Math.round(payoutList.OtherTotalArrears).toLocaleString('en-IN')}
              </p></li>
            {/* <li><span>Warning Deduction
              <button className='tooltip'>
                <WrapperTooltip
                  disableFocusListener
                  TransitionComponent={Zoom}
                  placement="top"
                  enterTouchDelay={0} leaveTouchDelay={5000}
                  arrow
                  title={renderWarningsTooltip(payoutList)}
                  classes={{ tooltip: classes.customWidth }}
                >
                  <i className="fa fa-info-circle"></i>
                </WrapperTooltip>
              </button>  

            </span> */}
              {/* <em>{payoutList.WarningCount} WARNINGS</em> */}
              {/* <p>
                {payoutList.WarningDeduction && Math.round(payoutList.WarningDeduction).toLocaleString('en-IN')}
              </p></li> */}

              <li className='modal-popup'>
                <span className="ancor cursorPointer" onClick={(e) => handleMisssellOpen(true)}>{`MisSell Deduction`}</span>
               
            {/* </span> */}
              <p className='border-text' style={payoutList.Misssell < 0 ? { color: "#ED3434", width: "100%", paddingLeft: "0", float: "right", position: "relative" } : {}}>
                {payoutList.Misssell && Math.round(payoutList.Misssell).toLocaleString('en-IN')}
              </p>
              </li>

            {/* <li><span>Quality Deduction
              <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderTooltipArray(payoutList.Deductions)}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
            </span>
              <p style={payoutList.QScoreDeduction < 0 ? { color: "red", borderBottom: "dashed", borderWidth: "thin" } : {}}>
                {payoutList.QScoreDeduction && Math.round(payoutList.QScoreDeduction).toLocaleString('en-IN')}
              </p>
            </li> */}

          </ul>
          {showClawbacks ? <PayoutClawback userId={userId} Type='Clawback' Date={date} show={showClawbacks} handleClose={() => handleClickOpen(false)}  /> : null}
          {showArrears ? <PayoutClawback userId={userId} Type='Arrears' Date={date} show={showArrears} handleClose={() => handleArrearsOpen(false)}  /> : null}
          {showMisssell ? <PayoutClawback userId={userId} Type='Missell' Date={date} show={showMisssell} handleClose={() => handleMisssellOpen(false)}  /> : null}

        </div>
      </div>
      </div>


    );
};

export default PayoutTable;