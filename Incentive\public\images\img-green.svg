<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="318" height="242" viewBox="0 0 318 242">
  <defs>
    <linearGradient id="linear-gradient" x1="0.958" y1="0.434" x2="0" y2="0.434" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffb893"/>
      <stop offset="1" stop-color="#ed7d39"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_2228" data-name="Rectangle 2228" width="318" height="242" rx="16" fill="url(#linear-gradient)"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <ellipse id="Ellipse_481" data-name="Ellipse 481" cx="86" cy="86.5" rx="86" ry="86.5" transform="translate(0)" fill="rgba(255,255,255,0.24)" opacity="0.3"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_4" data-name="Mask Group 4" clip-path="url(#clip-path)">
    <g id="Group_1564" data-name="Group 1564" transform="translate(180 87)">
      <g id="Mask_Group_1" data-name="Mask Group 1" transform="translate(0 0)" clip-path="url(#clip-path-2)">
        <rect id="Rectangle_2200" data-name="Rectangle 2200" width="402" height="234" rx="16" transform="translate(-261 -16)" fill="#fff" opacity="0.09"/>
      </g>
      <g id="Leaderboard_Empty_1" data-name="Leaderboard Empty 1" transform="translate(-11.01 -2.255)">
        <path id="Path_17" data-name="Path 17" d="M198.108,281.644l-13.8,3.85h41.648l-13.889-4.038Z" transform="translate(-114.923 -183.885)" fill="#5773bc" opacity="0.2"/>
        <g id="Group_9" data-name="Group 9" transform="translate(38.291 94.011)">
          <g id="Group_4" data-name="Group 4" transform="translate(71.712 14.06)">
            <path id="Path_18" data-name="Path 18" d="M355.524,317.025H330.913l-3.377-4.61h22.536Z" transform="translate(-327.535 -312.413)" fill="#bbe0fb"/>
            <rect id="Rectangle_1" data-name="Rectangle 1" width="24.611" height="13.669" transform="translate(3.377 4.613)" fill="#91cefa"/>
            <path id="Path_19" data-name="Path 19" d="M327.535,312.413v12.312l3.38,5.968V317.027Z" transform="translate(-327.535 -312.413)" fill="#5773bc"/>
            <g id="Group_3" data-name="Group 3" transform="translate(12.348 6.769)">
              <path id="Path_20" data-name="Path 20" d="M375.721,336.3v.156l-.008.167-.023.156-.038.216-.038.148-.023.058-.1.206-.085.137-.038.058-.085.121-.078.1-.085.089-.186.176-.1.069-.156.078-.109.05-.163.068-.109.039-.171.058-.125.039-.132.029-.132.019-.171.01h-.124L373,338.53l-.264-.019-.132-.019-.116-.029-.124-.029-.179-.058-.116-.049-.116-.059-.117-.069-.163-.121-.1-.079-.217-.226-.125-.157-.078-.121-.07-.147-.062-.137-.061-.206-.038-.137-.062-.2-.023-.137-.039-.226.015-.108.07-.068.124-.029.264-.019.264.019h.125l.357-.039h.186l.109.029.062.068.046.121.015.089.061.147.046.059.1.108.046.039.116.068.055.039.132.049.132.039.133.019h.124l.192-.029.124-.048.07-.029.108-.089.046-.048.078-.108.038-.058.038-.127.015-.079v-.147l-.015-.078-.038-.147-.031-.058-.078-.121-.14-.121-.046-.029-.1-.058-.124-.029-.132-.019-.133-.01h-.192l-.248.019-.171.01h-.171l-.1-.039-.055-.079-.023-.121-.015-.147-.008-.137.008-.137v-.147l-.008-.216.008-.176.023-.127.031-.068.046-.029.264-.029.124.01.24.029.117.01.054.01.116-.01.171-.029.132-.039.062-.029.124-.068.054-.048.085-.108.038-.058.047-.147.008-.1-.008-.1-.055-.147-.038-.079-.124-.157-.1-.058-.116-.029-.109-.019-.179-.01-.116.01-.192.019-.116.029-.062.029-.109.059-.055.049-.1.1-.031.059-.055.127-.038.121-.07.058-.108.029h-.192l-.327-.019-.116-.01-.233.019-.116-.01-.171-.01-.1-.029-.038-.029-.023-.059v-.108l.023-.216.023-.147.078-.274.055-.2.054-.137.069-.147.085-.127.1-.121.1-.108.116-.089.116-.1.116-.087.116-.079.124-.068.132-.058.133-.039.132-.029.132-.019.264-.058.139-.019.133-.01.124.01.179.01.365.059.179.049.116.029.124.049.116.048.179.078.109.059.109.068.109.089.116.108.1.108.1.121.078.108.031.058.07.127.054.156.046.148.039.137.046.206.023.147.008.166v.166l-.008.069-.023.147-.015.058-.039.121-.109.255-.1.187-.07.108-.132.158-.054.079-.023.078.031.079.179.156.1.1.078.1.078.108.069.121.085.216.038.158.023.166.008.156Z" transform="translate(-370.801 -331.213)" fill="#fff"/>
            </g>
          </g>
          <g id="Group_6" data-name="Group 6" transform="translate(0 11.615)">
            <rect id="Rectangle_2" data-name="Rectangle 2" width="28.517" height="17.202" transform="translate(28.517 22.604) rotate(-179.922)" fill="#91cefa"/>
            <path id="Path_21" data-name="Path 21" d="M74.448,310.965l28.512.05,3.919-5.338-26.1-.045Z" transform="translate(-74.424 -305.632)" fill="#bbe0fb"/>
            <path id="Path_22" data-name="Path 22" d="M178.231,305.758l-.017,15.651-3.924,6.907.022-17.216Z" transform="translate(-145.776 -305.711)" fill="#5773bc"/>
            <g id="Group_5" data-name="Group 5" transform="translate(10.8 9.538)">
              <path id="Path_23" data-name="Path 23" d="M113.949,338.4l.01.057.09.023.127.012.345-.012h.734l.462.012h.145l1.057-.023.145-.011.3-.034h.154l.118.034.055.1.018.16.01.172.01.183v.47l.018.286-.01.183-.009.16-.018.114-.036.069-.054.034-.288.057-.127.012-.2-.012-.135-.011-.662-.046H115.7l-.8.057h-1.314l-.662-.011-.507-.023-.109-.011-.063-.046-.018-.069-.01-.121.018-.687V339.3l-.027-.7.01-.183.028-.343.054-.343.027-.183.045-.194.045-.183.027-.091.063-.183.072-.149.154-.206.1-.114.163-.16.109-.091.182-.149.118-.08.192-.114.135-.057.227-.092.154-.045.154-.034.145-.023.462-.035.082-.011.163-.012.227-.012.163-.046.072-.046.135-.1.063-.08.082-.149.055-.243v-.252l-.018-.091-.045-.183-.037-.08-.09-.137-.118-.068-.2-.069-.127-.023-.135-.012h-.135l-.127.012-.127.034-.192.08-.118.08-.054.045-.09.121-.045.068-.063.172-.045.16-.082.069-.136.046h-.082l-.154.011-.384-.023h-.384l-.154-.011-.235-.034-.118-.023-.063-.046-.027-.057v-.194l.018-.171.018-.1.027-.183.018-.08.045-.171.09-.217.072-.16.082-.149.1-.149.109-.121.109-.114.172-.148.127-.092.272-.183.154-.08.145-.057.2-.057.145-.034.136-.034.127-.023.2-.011.145-.011h.3l.208-.012h.145l.136.012.135.034.192.057.135.058.288.114.136.057.135.069.192.114.127.091.118.121.117.148.1.149.09.149.118.206.063.16.063.172.045.171.045.16.028.172.027.274v.172l-.01.251-.018.16-.045.263-.036.171-.055.194-.063.171-.1.217-.082.149-.118.194-.1.121-.145.171-.117.091-.192.121-.136.069-.154.057-.145.057-.154.034-.145.034-.208.023-.145.023-.154.034-.145.034-.217.023-.154.012h-.072l-.163.011h-.072l-.154.034-.072.034-.135.08-.055.057-.1.121-.072.137-.055.16-.018.091-.027.149Z" transform="translate(-112.21 -332.078)" fill="#fff"/>
            </g>
          </g>
          <g id="Group_8" data-name="Group 8" transform="translate(31.089)">
            <path id="Path_24" data-name="Path 24" d="M225.958,280.345H184.311l5.319-6.94h31.011Z" transform="translate(-184.311 -273.405)" fill="#bbe0fb"/>
            <rect id="Rectangle_3" data-name="Rectangle 3" width="41.648" height="28.957" transform="translate(0 6.94)" fill="#91cefa"/>
            <g id="Group_7" data-name="Group 7" transform="translate(17.006 12.728)">
              <path id="Path_25" data-name="Path 25" d="M250.886,323.149v.364l-.05.274-.117.121-.117.106-.117.063H250l-.718-.042h-.267l-2.257-.042h-.269l-1.688.085-.551-.042-.183-.063-.067-.064-.034-.121-.017-.211v-.485l.034-.633-.017-.339-.067-.633v-.317l.016-.148.067-.232.183-.042.672.063h.267l.384-.021.2-.042.1-.063.05-.148v-1.5l.016-.337.067-1.267v-.317l-.017-.465-.034-.317-.017-.528-.016-.337.034-.548.034-.718v-.233l-.05-.121-.1-.085-.151-.022-.6.254-.251.085-.251.042h-.22l-.151-.085-.067-.148-.05-.211-.017-.169v-.337l.034-.528v-.364l-.034-.654v-.364l.034-.274.067-.121.084-.085.167-.106.151-.063.251-.121.48-.19.234-.106.887-.422.251-.106.887-.364.251-.121.384-.232.288-.148.251-.085.2-.022.167.063.084.148.05.254.034.254-.017.254-.034.824-.016.339v3.294l-.05,1.287v.339l.067,1.309-.016.337-.067,1.288-.016.337.067,1.309.017.211.05.121.1.085.2.021,1.2.022.1.042.067.19.017.148.016.317-.034.633v.338Z" transform="translate(-243.901 -308.696)" fill="#fff"/>
            </g>
          </g>
        </g>
        <g id="Group_11" data-name="Group 11" transform="translate(61.89 37.455)">
          <g id="Group_10" data-name="Group 10" transform="translate(0 0.056)">
            <path id="Path_26" data-name="Path 26" d="M273.787,191.808l.481-.484a12.071,12.071,0,0,1,1.3-1.052,10.039,10.039,0,0,1,2.219-1.185,5.942,5.942,0,0,1,2.949-.351,4.271,4.271,0,0,1,1.508.53c.15.095.333.192.458.284a3.407,3.407,0,0,0,.288.244,4.622,4.622,0,0,1,.63.667,7.536,7.536,0,0,1,1.481,4.148,8.506,8.506,0,0,1-.5,3.638l-.05.143s-.035.093,0,.027l-.038.086-.152.347c-.1.223-.167.347-.268.537a11.635,11.635,0,0,1-3.332,3.725,21.682,21.682,0,0,1-4.425,2.41c-1.554.661-3.142,1.229-4.7,1.839a29.63,29.63,0,0,0-4.575,1.907,7.617,7.617,0,0,0-2.109,1.883,6.557,6.557,0,0,0-1.333,2.879,5.179,5.179,0,0,0-.033,1.789,4.421,4.421,0,0,0,.548,1.618,3.234,3.234,0,0,0,2.011,1.528,2.48,2.48,0,0,0,1.946-.349,2.33,2.33,0,0,0,1.05-1.778,3.527,3.527,0,0,0-.19-1.515,1.6,1.6,0,0,0-.591-.859.571.571,0,0,0-.533-.053l-.155.086a.617.617,0,0,1,.48.316,2.369,2.369,0,0,1-.038,1.707c-.1.41-.888.671-1.479.244a1.519,1.519,0,0,1-.64-.856,2.284,2.284,0,0,1-.048-.507,2.114,2.114,0,0,1,.092-.568,4.732,4.732,0,0,1,2.141-2.582,28.787,28.787,0,0,1,4.23-1.669c1.516-.572,3.127-1.122,4.775-1.8a23.847,23.847,0,0,0,4.966-2.637,14.038,14.038,0,0,0,4.233-4.8c.133-.257.293-.579.4-.816l.15-.349.038-.086.058-.154.078-.21a12.813,12.813,0,0,0,.72-5.444,11.367,11.367,0,0,0-2.311-6.256,4.436,4.436,0,0,0-.5-.566,5.927,5.927,0,0,0-.545-.511c-.2-.162-.411-.314-.616-.463-.18-.114-.333-.187-.488-.282a6.408,6.408,0,0,0-2.347-.758,8.019,8.019,0,0,0-3.972.585,11.694,11.694,0,0,0-2.714,1.566,14.653,14.653,0,0,0-1.583,1.414l-.465.509Z" transform="translate(-231.854 -185.404)" fill="#f5911e"/>
            <path id="Path_27" data-name="Path 27" d="M274.135,191.7l.475-.495A12.9,12.9,0,0,1,276,190.028a10.482,10.482,0,0,1,2.392-1.317,6.671,6.671,0,0,1,3.307-.421,5.052,5.052,0,0,1,1.8.61c.155.095.322.183.468.284l.4.322a5.553,5.553,0,0,1,.776.808,8.875,8.875,0,0,1,1.778,4.882,10.063,10.063,0,0,1-1.166,5.591,12.378,12.378,0,0,1-3.648,4.11,22.493,22.493,0,0,1-4.618,2.483c-1.588.665-3.185,1.221-4.726,1.816a29.009,29.009,0,0,0-4.455,1.816,6.631,6.631,0,0,0-1.859,1.641,5.608,5.608,0,0,0-1.124,2.355,3.95,3.95,0,0,0,.318,2.593,2.68,2.68,0,0,0,1.533,1.292,2,2,0,0,0,1.588-.158,1.7,1.7,0,0,0,.876-1.307,2.528,2.528,0,0,0-.5-2.14.587.587,0,0,0-.615-.088.574.574,0,0,1,.553.194,2.471,2.471,0,0,1,.25,1.94,1.417,1.417,0,0,1-2.011.9,2.2,2.2,0,0,1-1.118-1.092,3.018,3.018,0,0,1-.152-1.894,5.574,5.574,0,0,1,2.592-3.346A28.863,28.863,0,0,1,273,210.144c1.529-.589,3.132-1.145,4.746-1.818a23.159,23.159,0,0,0,4.77-2.563,13.282,13.282,0,0,0,3.913-4.419,11.321,11.321,0,0,0,1.3-6.256,10.026,10.026,0,0,0-2.013-5.52,6.4,6.4,0,0,0-.9-.936l-.5-.387c-.167-.107-.32-.187-.478-.282a5.672,5.672,0,0,0-2.056-.678,7.31,7.31,0,0,0-3.613.507,11.233,11.233,0,0,0-2.541,1.433,13.585,13.585,0,0,0-1.483,1.284l-.471.5Z" transform="translate(-232.726 -186.092)" fill="#f9ae40"/>
            <path id="Path_28" data-name="Path 28" d="M215.037,190.193l-.473-.5a14.359,14.359,0,0,0-1.606-1.372,11.53,11.53,0,0,0-2.74-1.492,7.991,7.991,0,0,0-3.982-.478,6.46,6.46,0,0,0-2.332.821c-.167.1-.3.177-.483.3-.2.154-.406.311-.608.48a5.88,5.88,0,0,0-.535.526,4.491,4.491,0,0,0-.5.579,11.479,11.479,0,0,0-2.2,6.313,12.761,12.761,0,0,0,.811,5.423l.082.21.06.152.038.086.157.345c.11.234.275.553.411.806a13.832,13.832,0,0,0,4.313,4.684,23.623,23.623,0,0,0,5.008,2.5c1.666.631,3.28,1.136,4.8,1.667a28.6,28.6,0,0,1,4.257,1.553,4.668,4.668,0,0,1,2.184,2.525,2.1,2.1,0,0,1,.1.566,2.275,2.275,0,0,1-.038.507,1.538,1.538,0,0,1-.625.873c-.585.442-1.374.21-1.484-.21a2.365,2.365,0,0,1-.068-1.7.63.63,0,0,1,.475-.33l-.157-.082a.571.571,0,0,0-.531.067,1.61,1.61,0,0,0-.576.873,3.54,3.54,0,0,0-.167,1.519,2.3,2.3,0,0,0,1.08,1.74,2.472,2.472,0,0,0,1.951.3,3.275,3.275,0,0,0,1.984-1.58,4.459,4.459,0,0,0,.521-1.631,5.169,5.169,0,0,0-.063-1.787,6.488,6.488,0,0,0-1.374-2.841,7.505,7.505,0,0,0-2.139-1.825,29.3,29.3,0,0,0-4.606-1.782c-1.566-.57-3.165-1.092-4.728-1.711A21.4,21.4,0,0,1,206.8,204a11.471,11.471,0,0,1-3.392-3.634c-.1-.187-.167-.307-.277-.528l-.167-.343-.04-.086c.03.065,0-.027,0-.027l-.052-.141a8.441,8.441,0,0,1-.561-3.624,7.6,7.6,0,0,1,1.409-4.186,4.6,4.6,0,0,1,.618-.684c.1-.078.192-.164.285-.252.13-.1.3-.2.453-.3a4.336,4.336,0,0,1,1.5-.57,5.931,5.931,0,0,1,2.955.271,9.9,9.9,0,0,1,2.239,1.124,11.921,11.921,0,0,1,1.313,1.014l.49.471Z" transform="translate(-199.545 -185.731)" fill="#f5911e"/>
            <path id="Path_29" data-name="Path 29" d="M215.5,191.688l-.48-.486a13.331,13.331,0,0,0-1.5-1.244,11.071,11.071,0,0,0-2.564-1.364,7.282,7.282,0,0,0-3.622-.408,5.732,5.732,0,0,0-2.042.732c-.157.1-.312.185-.471.3l-.5.4a6.486,6.486,0,0,0-.883.96,10.117,10.117,0,0,0-1.919,5.572,11.251,11.251,0,0,0,1.4,6.22,13.074,13.074,0,0,0,3.987,4.308,22.851,22.851,0,0,0,4.813,2.435c1.624.631,3.237,1.143,4.776,1.683a28.539,28.539,0,0,1,4.376,1.641,5.5,5.5,0,0,1,2.649,3.274,3.028,3.028,0,0,1-.12,1.894,2.234,2.234,0,0,1-1.1,1.122,1.4,1.4,0,0,1-2.026-.842,2.477,2.477,0,0,1,.217-1.947.589.589,0,0,1,.548-.21.592.592,0,0,0-.613.107,2.544,2.544,0,0,0-.456,2.153,1.683,1.683,0,0,0,.9,1.284,1.994,1.994,0,0,0,1.591.114,2.716,2.716,0,0,0,1.511-1.332,3.962,3.962,0,0,0,.275-2.6,5.537,5.537,0,0,0-1.166-2.315,6.525,6.525,0,0,0-1.886-1.591,28.72,28.72,0,0,0-4.485-1.7c-1.551-.553-3.165-1.069-4.756-1.683a22.194,22.194,0,0,1-4.665-2.357,12.2,12.2,0,0,1-3.717-4.011,10.013,10.013,0,0,1-1.253-5.56,8.957,8.957,0,0,1,1.694-4.928,5.649,5.649,0,0,1,.763-.829l.4-.332c.147-.1.312-.2.463-.3a5.09,5.09,0,0,1,1.791-.659,6.653,6.653,0,0,1,3.314.341,10.354,10.354,0,0,1,2.414,1.263,12.576,12.576,0,0,1,1.414,1.143l.483.482Z" transform="translate(-200.524 -186.416)" fill="#f9ae40"/>
          </g>
          <path id="Path_30" data-name="Path 30" d="M226.572,186.216a.572.572,0,0,1,.5-.526l12.448-.168,2.847-.038,12.448-.168a.564.564,0,0,1,.515.511c1.262,7.278-.466,14.87-4.6,20.189-7.23,9.5-7.9,11.153-7.1,19.061s3.87,10.311,3.87,10.311l-6.059.082h-.167l-6.059.082s3.03-2.483,3.693-10.408-.028-9.564-7.417-18.866c-4.216-5.21-6.069-12.754-4.926-20.063Z" transform="translate(-213.025 -185.315)" fill="#f5911e"/>
          <path id="Path_31" data-name="Path 31" d="M.012.056,19.725,0l-.012,5.547c0,2.224-1.437,4.031-3.2,4.035L3.178,9.619C1.418,9.624,0,7.826,0,5.6Z" transform="translate(38.29 58.652) rotate(179.387)" fill="#404041"/>
          <path id="Path_32" data-name="Path 32" d="M0,.068,24.217,0h0c0,1.7-1.1,3.09-2.451,3.094L2.437,3.148C1.087,3.152,0,1.773,0,.068Z" transform="translate(40.543 60.177) rotate(179.387)" fill="#58595b"/>
          <path id="Path_33" data-name="Path 33" d="M230.121,191.6a.937.937,0,0,1,.813-.987l11.693-.158h.595l11.693-.158a.922.922,0,0,1,.833.964,1.658,1.658,0,0,0,.012.236,17.5,17.5,0,0,1-3.247,11.067,10.929,10.929,0,0,1-8.569,4.7H242.2a10.8,10.8,0,0,1-8.647-4.47,17.362,17.362,0,0,1-3.434-10.976C230.116,191.752,230.118,191.674,230.121,191.6Z" transform="translate(-214.973 -187.177)" fill="#f9ae40"/>
          <path id="Path_34" data-name="Path 34" d="M230.742,186.992l24.27-.328a.633.633,0,0,1,.5.253,1.012,1.012,0,0,1,.211.625h0a1.021,1.021,0,0,1-.2.63.64.64,0,0,1-.495.266l-24.27.328a.806.806,0,0,1-.708-.878h0a.816.816,0,0,1,.693-.9Z" transform="translate(-214.946 -185.82)" fill="#f9ae40"/>
          <path id="Path_35" data-name="Path 35" d="M251.142,196.169l1.509,3.864,3.375.619-2.442,3.007.576,4.247-3.019-2.005-3.019,2.005.576-4.247-2.442-3.007,3.375-.619Z" transform="translate(-223.131 -189.382)" fill="#fff"/>
        </g>
        <g id="Group_12" data-name="Group 12" transform="translate(31.01 26.561)">
          <path id="Path_36" data-name="Path 36" d="M328.269,149.361a6.97,6.97,0,0,0,1.126,1.987l1.718-1.569a3.188,3.188,0,0,1-1.311-2.864l-1.986,2.126" transform="translate(-276.717 -146.915)" fill="#de5549"/>
          <path id="Path_37" data-name="Path 37" d="M96.326,240.554a3.594,3.594,0,0,1-.1,2.4,2.467,2.467,0,0,1-1.5,1.478l1.158,2.275a5.768,5.768,0,0,0,2.689-5.1,2.56,2.56,0,0,1-1.723-.7" transform="translate(-81.637 -182.424)" fill="#de5549"/>
          <path id="Path_38" data-name="Path 38" d="M138.159,208.562a2.107,2.107,0,0,1-2.031,1.911,1.357,1.357,0,0,1,.186,1.081,3.916,3.916,0,0,0,1.9-.192,2.4,2.4,0,0,0,1.372-1.577l-1.58-.878" transform="translate(-111.341 -161.964)" fill="#e6e9ed"/>
          <path id="Path_39" data-name="Path 39" d="M74.772,191.029a6.558,6.558,0,0,1,.036,3.067,1.346,1.346,0,0,0,1.4.506,13.717,13.717,0,0,0,.672-2.811,2.385,2.385,0,0,1-1.77-.451" transform="translate(-67.375 -150.75)" fill="#ffc30c"/>
          <path id="Path_40" data-name="Path 40" d="M169.1,156.252a6.38,6.38,0,0,1,1.413,2.877.816.816,0,0,0,.73-.232,1.312,1.312,0,0,0,.373-.826,2.807,2.807,0,0,0-.523-1.81,4.46,4.46,0,0,0-.9-1.007,1.83,1.83,0,0,1-1.065,1.316" transform="translate(-134.945 -142.873)" fill="#e6e9ed"/>
          <path id="Path_42" data-name="Path 42" d="M48.872,233.019a5.073,5.073,0,0,1,.718,2.454,2.381,2.381,0,0,0,1.192-.157,1.271,1.271,0,0,0,.686-1.139,1.568,1.568,0,0,0-.279-.8,1.794,1.794,0,0,0-2.338-.718" transform="translate(-48.852 -177.25)" fill="#ffc30c"/>
          <path id="Path_43" data-name="Path 43" d="M58.193,135.9a2.171,2.171,0,0,1,.217,1.273c.264.243.661-.042.768-.429a2.514,2.514,0,0,0-.071-1.206l-1.081.121" transform="translate(-55.406 -115.258)" fill="#e6e9ed"/>
          <path id="Path_44" data-name="Path 44" d="M143.813,252.587a3,3,0,0,1,.019,3.008,5.36,5.36,0,0,1,2.126.406l.384-1.85a.575.575,0,0,0,.008-.449.378.378,0,0,0-.3-.249l-2.088-.87" transform="translate(-116.842 -190.117)" fill="#e6e9ed"/>
          <path id="Path_50" data-name="Path 50" d="M197.927,248.639a2.293,2.293,0,0,1-.046,1.435,1.641,1.641,0,0,1-.847.958,3.379,3.379,0,0,1,1.249.744l1.228-2.6-1.388-.039" transform="translate(-154.947 -187.595)" fill="#de5549"/>
          <path id="Path_52" data-name="Path 52" d="M301.407,123.407a3.887,3.887,0,0,1,1.693,3.4,5.572,5.572,0,0,0,1.713-.849,11.54,11.54,0,0,0-1.276-3.847,2.885,2.885,0,0,1-2.178,1.092" transform="translate(-229.646 -116.049)" fill="#e6e9ed"/>
          <path id="Path_53" data-name="Path 53" d="M330.365,211.683a2.434,2.434,0,0,0-.63,2.017,2.075,2.075,0,0,0,1.1,1.669,1.427,1.427,0,0,0,1.686-.41,3.36,3.36,0,0,1-.7-2.7l-1.882-.427" transform="translate(-249.981 -163.959)" fill="#ffc30c"/>
          <path id="Path_54" data-name="Path 54" d="M333.309,254.521a4.125,4.125,0,0,1-.208,4.034l2.132.207a21.19,21.19,0,0,0,0-2.79c-.027-.391-.1-.849-.394-1-.493-.243-1.249.59-1.47-.018" transform="translate(-252.368 -191.356)" fill="#de5549"/>
          <path id="Path_55" data-name="Path 55" d="M365.562,182.259a7.029,7.029,0,0,0-.083,4.109l1.864-.546a3.592,3.592,0,0,1,.309-3.95,3.356,3.356,0,0,1-2.4.87" transform="translate(-275.36 -144.893)" fill="#e6e9ed"/>
          <path id="Path_56" data-name="Path 56" d="M369.491,129.839a2.121,2.121,0,0,0,.526,2.185,1.226,1.226,0,0,0,1.8-.164,5.031,5.031,0,0,1-.576-3.913,2.354,2.354,0,0,1-1.665,1.371" transform="translate(-278.356 -110.406)" fill="#ffc30c"/>
          <path id="Path_58" data-name="Path 58" d="M374.876,219.917a2.755,2.755,0,0,1,1.511,2.149,1.364,1.364,0,0,0,1.271-1.248,1.742,1.742,0,0,0-.721-1.818,1.928,1.928,0,0,0-2.244.9" transform="translate(-282.245 -168.543)" fill="#de5549"/>
          <path id="Path_59" data-name="Path 59" d="M405.79,171.459a2.287,2.287,0,0,1,1.721,1.641,14.4,14.4,0,0,0,.54-2.669l-1.878-1.279a3.328,3.328,0,0,1-.747,2.639" transform="translate(-304.207 -136.755)" fill="#ffc30c"/>
          <path id="Path_61" data-name="Path 61" d="M407.866,228.383a4.547,4.547,0,0,1-.824,2.458.664.664,0,0,1,.9.4,3.85,3.85,0,0,0,1.249-2.343,2.2,2.2,0,0,1-1.133-.193" transform="translate(-305.362 -174.64)" fill="#de5549"/>
          <path id="Path_64" data-name="Path 64" d="M382.121,256.563a4.281,4.281,0,0,0,1.249,2.992,1.511,1.511,0,0,1,1.4-.764,4.98,4.98,0,0,1-.855-1.5,2,2,0,0,1,.2-1.741,12.793,12.793,0,0,0-2.233.9" transform="translate(-287.385 -192.011)" fill="#e6e9ed"/>
          <path id="Path_65" data-name="Path 65" d="M407.13,281.1a2.35,2.35,0,0,1-.833,2.5,1.084,1.084,0,0,0,.925.343,1.216,1.216,0,0,0,.845-.586,1.53,1.53,0,0,0,.044-1.174,1.033,1.033,0,0,0-.752-.693" transform="translate(-304.829 -208.357)" fill="#e6e9ed"/>
          <path id="Path_66" data-name="Path 66" d="M99.1,280.408a3.808,3.808,0,0,0,.576,2.824,2.912,2.912,0,0,1,1.466-.217l.036-2.532a4.56,4.56,0,0,1-2.488.655" transform="translate(-84.469 -207.912)" fill="#ffc30c"/>
        </g>
      </g>
    </g>
  </g>
</svg>
