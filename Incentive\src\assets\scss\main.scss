@import "../scss//common/header";
@import "../scss//common/card";
@import "../scss//common/faq";
@import "../scss//common/slickslider";
@import "../scss//common/button";
@import "../scss//common/form";
@import "../scss//common/accordion";
@import "./variables/variables";

a{
  color: #0065FF;
}
:focus{outline: none;}
#scrollbar::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  border-radius: 10px;
  background-color: #f2f2f2;
}

#scrollbar::-webkit-scrollbar
{
  width: 5px;
  background-color: #f2f2f2;
}

#scrollbar::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  // -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,.3);
  background-color: #a7a8aa;
}

.content-panel{
  color: #253858;
  font-size: 14px;
  .form-group{
    margin-bottom: 1rem;
  }
  h1,h2,h3,h4,h5,h6{
    font-weight: 500;
    margin-bottom: 15px;
  }
  h6{font-size: 20px;}
  h5{
    font-size: 16px;
    .viewall{
      float: right;
      font-size: 11px;
      text-transform: uppercase;
      cursor: pointer;
      &:hover{
        text-decoration: underline;
      }
    }
  }

  .slick-slider{margin-bottom: 25px;}


// main slider Cs
.main-slider{
  .slick-slider{
    .slick-slide{
      padding: 0;
      .banner{
        border-radius: 16px;
        padding: 35px 350px 35px 50px;
        height: 330px;
        position: relative;
        .card{
          background: none;
          color: #fff;
          padding: 0;
          border-radius: 0;
          // padding: 25px;
          // min-height: 300px;
          // &.reward{
          //   background: transparent linear-gradient(180deg, #FFFFFF 0%, #F3F4FE 78%, #E5E7FC 100%) 0% 0% no-repeat padding-box;
          //   padding-right: 350px;
          //   h5{
          //     &::after{
          //       background: transparent linear-gradient(90deg, #503083 0%, #A65DAE 100%) 0% 0% no-repeat padding-box;
          //     }
          //   }
          //   img{
          //     position: absolute;
          //     right: -32px;
          //     bottom: -140px;
          //   }
            
            
          // }
          .carousel-caption{
            margin-top: 15px;
            margin-bottom: 15px;
            h5{
              font-size: 18px;
              position: relative;
              padding-bottom: 15px;
              &::after{
                width: 35px;
                height: 2px;
                background: blue;
                content: "";
                position: absolute;
                left: 0;
                bottom: 0;
              }
            }
            p{
              font-size: 19px;
              line-height: 26px;
              margin-bottom: 15px;
              min-height: 120px;
              span{
                &.h5{
                  color: #FFC839;
                  font-size: 20px;
                }
              }
            }
          }
          button{
            &.btn{
              border-radius: 24px;
              height: 48px;
              min-width: 125px;
              text-align: center;
              color: #fff;
              border: none;
              padding: 0 15px;
              min-width: 100px;
              font-size: 13px;
              background: #fff;
              font-weight: 500;
            }
          }
          &.ceo_msge{
            background: transparent linear-gradient(180deg, #FFFFFF 0%, #fbf0f2 78%, #FCEBEE 100%) 0% 0% no-repeat padding-box;
            .MuiCardContent-root{
              margin-top: 25px;
              display: flex;
              .image {
                text-align: center;
                margin-right: 50px;
                display: inline-flex;
                float: left;
                // background: #eee;
                align-items: center;
                img{
                  width: 160px;
                  height: 160px;
                  border-radius: 100px;
                }
              }
              .carousel-caption {
                margin-top: 15px;
                margin-bottom: 15px;
                float: left;
                width: 65%;
                .name {
                  text-align: right;
                  color: #EE803D;
                  margin-top: 15px;
                  small{
                    display: block;
                    text-align: right;
                    color: #253858;
                  }
                }
              }
            }
          }
        }
        button{
          &.btn{
            margin-top: 25px;
          }
        }
        &.slide1{
          background: #6D59D4;
          background: url("/images/img-slide1.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #8AA6FC 0%, #6D59D4 100%) ; /* W3C */
          button{
            &.btn{
              background: #5855A9;
            }
          }
        }
        &.slide2{
          background: #1B146C;
          background: url("/images/img-slide2.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #87238F 0%, #1B146C 100%) ; /* W3C */
          button{
            &.btn{
              background: #814595;
            }
          }
        }
        &.slide3{
          background: #0187C4;
          background: url("/images/img-slide3.svg") no-repeat right 50px bottom 30px, linear-gradient(270deg, #15C78B 0%, #0187C4 100%) ; /* W3C */
          button{
            &.btn{
              background: #38C6A6;
            }
          }
        }
        
      }
     
    }
  }
}

// Leaderboard Css
  .leaderboard{
    .slick-slider{
          min-height: 300px;
          padding-bottom: 40px;
          background: #fff;
          border-radius: 16px;
          padding: 20px;
      .slick-slide{
        padding: 0;
        .card{
          background: none;
          border-radius: 0;
          padding: 0;
          .card-header{
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            img{
              &.circle-fluid{
                width: 90px;
                height: 90px;
                border-radius: 100px;
                margin-right: 10px;
              }
            }
            h1{
              font-size: 13px;
              color: #EE803D;
              margin: 0;
              font-weight: 600;
              small{
                color: #808080;
                display: flex;
                font-size: 13px;
                font-weight: 500;
              }
            }
          }
          .card-body{
            height: 126px;
            text-align: justify;
            overflow-y: auto;
            padding-right: 4px;
          }
          // .carousel-indicators {
          //   position: absolute;
          //   right: 0;
          //   bottom: 20px;
          //   left: 0;
          //   z-index: 15;
          //   padding-left: 0; 
          //   margin-right: 15%;
          //   margin-left: 15%;
          // }
          
        }
      }
    }

    .slick-dots {
      position: absolute;
      bottom: 25px;
    }
  }

  // Rewards Css
  .rewards{
    .slick-slider{
      .slick-slide{
        padding: 0;
      }
      .bg{
        border-radius: 16px;
        padding: 30px 20px;
        height: 360px;
        position: relative;
        &.orange{
          background: #ED7D39;
          background: url("/images/img-orange.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #FFB893 0%, #ED7D39 100%) ; /* W3C */
        }
        &.blue{
          background: #13D0CA;
          background: url("/images/img-blue.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #A9E6E0 0%, #13D0CA 100%) ; /* W3C */
        }
        &.yellow{
          background: #F1A817;
          background: url("/images/img-yellow.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #FFDD66 0%, #F1A817 100%) ; /* W3C */
        }
        &.green{
          background: #36B97E;
          background: url("/images/img-green.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #71D6A9 0%, #36B97E 100%) ; /* W3C */
        }
        .card{
          background: none;
          color: #fff;
          padding: 0;
          border-radius: 0;
          .MuiTypography-body2{
            color: #fff;
            padding-right: 100px;
          }
          .slide{
            min-height: 150px;
            position: relative;
            color: #fff;
            .MuiTypography-h5{
              color: #fff;
              font-size: 14px;
              font-weight: 500;
              position: relative;
              padding-bottom: 10px;
              margin-bottom: 25px;
              &::after{
                content: "";
                position: absolute;
                height: 1px;
                width: 60px;
                background: #fff;
                bottom: 0;
                left: 0;
              }
            }
            .btn-rewards{
              color: #fff;
              text-transform: capitalize;
              position: absolute;
              bottom: 0;
            }
          }
          
          // .carousel-indicators {
          //   position: absolute;
          //   right: 0;
          //   bottom: 20px;
          //   left: 0;
          //   z-index: 15;
          //   padding-left: 20px; 
          //   justify-content: end;
          //   li{
          //     &.active {
          //       background-color: #fff;
          //       opacity: 1;
          //     }
          //   }
          // }
        }
        .slick-slide{
          padding: 0;
         
        }
        .trophy{
          position: absolute;
          right: -30px;
          bottom: -15px;
        }
      }
    }
    .slick-dots {
      position: absolute;
      bottom: 25px;
      text-align: left;
      width: auto;
      left: 20px;
      li{
        margin: 0 5px 0 0;
        button::before{
          background: rgba(255,255,255,.5);
        }
        &.slick-active {
          button::before{
            background: #fff;
          }
        }
      }
    }
  }

  // Gallery CSS
  .gallery{
    .card{
      background: none;
      padding: 0;
      .slick-slider {
        .slick-slide{
          &:first-child{
            // padding-left: 0;
          }
          &:last-child{
            // padding-right: 0;
          }
          .card{
            &.image{
              background: #fff;
              padding: 15px;
              height: 360px;
              img{
                &.soon{
                  margin: -15px auto auto auto;
                }
              }
              .media{
                height: 328px;
                overflow: hidden;
                border-radius: 8px;
                margin-bottom: 15px;
                transition: 0.3s;
                img{
                  width: 100%;
                }
                .overlay {
                  position: absolute;
                  background: rgba(255,255,255,.5);
                  left: 15px;
                  right: 15px;
                  top: 15px;
                  bottom: 15px;
                  vertical-align: middle;
                  display: none;
                  height: 330px;
                  border-radius: 8px;
                  transition: 0.3s;
                  img{
                    width: auto;
                    margin: auto;
                    cursor: pointer;
                  }
                }
                &:hover{
                  .overlay{
                    display: flex;
                    transition: 0.3s;
                  }
                }
              }
              
            }
            p{
              font-weight: 500;

            }
          }
        }
        .slick-arrow{
          z-index: 1;
        }
        .slick-prev {
          left: 0;
        }
        .slick-next {
          right: 0;
        }
      }
    }
  }

  // Explainer Video CSS
  .explainer_video{
    .card{
      background: none;
      padding: 0 0 35px 0;
      .slick-slider {
        .slick-slide{
          &:first-child{
            // padding-left: 0;
          }
          &:last-child{
            // padding-right: 0;
          }
          .card{
            &.image{
              background: #fff;
              padding: 15px;
              height: 240px;
              .media{
                width: 100%;
                display: flex;
                padding: 10px;
                border-radius: 10px;
                background: rgb(221,232,253);
                background: linear-gradient(180deg, rgba(221,232,253,1) 0%, rgba(255,255,255,1) 49%, rgba(255,255,255,1) 100%);
                .image{
                  img{
                    width: 114px;
                    border: 2px solid #fff;
                    margin-right: 15px;
                    margin-bottom: 10px;
                  }
                  strong{
                    color: #EE803D;
                    display: flex;
                    font-size: 13px;
                    font-weight: 500;
                    small{
                      font-size: 13px;
                      color: #808080;
                      position: absolute;
                      bottom: 15px;
                    }
                  }
                  
                }
                .description{
                  padding-right: 10px;
                  h5{
                    font-weight: 600;
                    font-size: 16px;
                    line-height: 24px;
                  }
                  p{
                    font-weight: 500;
                    line-height: 22px;
                  }
                }
              }
              .more{
                position: absolute;
                right: 15px;
                bottom: 15px;
                font-size: 12px;
                &:hover{
                  text-decoration: underline;
                }
              }
            }
            
          }
        }
        .slick-arrow{
          z-index: 1;
        }
        .slick-prev {
          left: 0;
        }
        .slick-next {
          right: 0;
        }
      }
    }
  }

}

.incentive_cal{height: 100%;max-height: 760px;
  .card{
    // background: transparent linear-gradient(293deg, #424770 0%, #696694 100%) 0% 0% no-repeat padding-box;
    box-shadow: 0px 0px 16px #3469CB29;background: #001458;
    color: #fff;
    border-radius: 16px;
    min-height: 300px;height: 92%;
    .ul{
      margin-top: 15px;
      .li{
        min-height: 235px;
        .slick-slider{
          .slick-list{
            .slick-slide{
              .slide_img{
                text-align: center;
                .imgDiv{
                  img{
                    margin: auto;
                  }
                }
                label{
                  margin-top: 15px;
                  display: block;
                }
              }
            }
          }
        }
        form{
          background: none;
          max-width: 80%;
          width: 100%;
          text-align: center;
          margin: auto;
          div{
            &.hide{
              display: none;
            }
          }
          .form-group{
            margin-bottom: 1.5rem;
            label{
              &.MuiFormLabel-root{
                color: #fff;
              }
            }
            .MuiInput-underline{
              .MuiInputBase-input{
                color: #fff;
              }
              &::before{
                border-bottom: 1px solid rgba(255, 255, 255, 0.42);
              }
              &:after{
                border-bottom: 2px solid #fff;
              }
            }
          }
          
          button{
            width: 125px;
            &.MuiButton-root{
              &.MuiButton-containedPrimary{
                background: #F99746;
                &:hover{
                  background: #ED7D2B;
                }
              }
              &.MuiButton-containedSecondary{
                background: #161840;
                &:hover{
                  background: #101230;
                }
              }
            }
          }

          .needHave{

            .heading{
              position: relative;
              margin-bottom: 10px;
              span{
                text-transform: uppercase;
                font-size: 14px;
                font-weight: 500;
                position: relative;
                background: #4F527C;
                z-index: 1;
                padding: 0 5px;
              }
              &:after{
                height: 1px;
                background: #fff;
                position: absolute;
                top: 8px;
                content: "";
                left: 0;
                right: 0;
              }
            }
            ul{
              &.list{
                list-style-type: none;
                display: table;
                width: 100%;
                border-spacing: 2px;
                li{
                  background: rgba(255,255,255,.2);
                  border-radius: 8px;
                  padding: 10px;
                  display: table-cell;
                  width: 33.33%;
                  text-align: left;
                  font-size: 16px;
                  padding-top: 30px;
                  label{
                    &.text{
                      display: block;
                      font-size: 11px;
                      margin-top: 3px;
                    }
                  }
                }
              }
            }
            .amount{
              font-size: 18px;
              margin: 15px 0;
              i.fa{
                font-size: 11px;
              }
              small{
                display: inline;
              }
            }
          }

        }
      }
    }
    small{
      text-align: center;
      display: block;
    }
    .MuiCardContent-root{
      height: 100%; 
      &::-webkit-scrollbar{
        display: none;
      }
      .incentive-calculator-section{
        height: 100%; position: relative;
        .MuiAppBar-colorPrimary{
          background: #868CF1;
          border-radius: 20px;
          max-width: 100%;
          width: 100%;
          height: 40px;
          .MuiTabs-root{
            min-height: 40px;    position: absolute;
            max-width: 100%;
            width: 100%;
            top: 0;
            .MuiTab-root{
              min-height: 40px;
              text-transform: capitalize;
              font-weight: bold;width: 50%;
              opacity: 1;
              &.MuiTab-textColorInherit.Mui-selected{
                background: #fff; border-radius: 20px;
                color: #273A59;text-transform: capitalize;
              }
            }
            .PrivateTabIndicator-colorSecondary-71{
              display: none;
            }
          }
        }
        .tab-panel{
          height: 100%;
         
          > div{position: relative;height: 100%;
            .tab-panel-content{
              height: 83%; overflow-y: auto;
              &::-webkit-scrollbar{display: none;}
            }
          }
          .milestones-timeline, .set-goals-timeline{
            flex-direction: column-reverse;
            .milestones-timeline-item{
              height: 100%; align-items: flex-end;
              .MuiTimelineSeparator-root{
                flex-direction: column-reverse;
                .timeline-separator-dot{
                  border: 0;    box-shadow: none;
                }
                .timeline-separator-connector{
                  height: 100px;border: 2px dashed #F99746;
                  background: transparent;
                }
                
              }
              h4{
                color: #fff;font-size: 20px;
              }
              &.milestone-locked{
                flex-direction: row-reverse;
                .timeline-separator-dot{
                  position: relative;
                  &::before{
                    content: "";
                    background: url(/images/icon-locked.svg) no-repeat center/contain;
                    position: absolute;
                    bottom: -6px;
                    left: 50%;
                    height: 20px;
                    width: 20px;
                    transform: translate(-50%, 0px);
                  }
                }
              }
              &.milestone-achieved{
                .timeline-separator-dot{
                  position: relative;
                  &::before{
                    content: "";
                    background: url(/images/icon-achieved.svg) no-repeat center/contain;
                    position: absolute;
                    bottom: -6px;
                    left: 50%;
                    height: 20px;
                    width: 20px;
                    transform: translate(-50%, 0px);
                  }
                }
              }
              .milestone-locked-content{
                background: #FFFFFF 0% 0% no-repeat padding-box;
                box-shadow: 0px 3px 16px #3469cb29;
                border-radius: 8px;
                display: flex; flex-direction: row-reverse;justify-content: space-around;
                padding: 14px 10px;
                position: relative;
                margin-left: -30px;
                margin-bottom: -47px;
                img{max-width: 18px;}
                &::before{
                  position: absolute;
                  content: "";
                  width: 0px;
                  height: 0px;
                  border-left: 32px solid #fff;
                  border-top: 39px solid transparent;
                  border-bottom: 40px solid transparent;
                  top: 50%;
                  transform: translate(0, -50%);
                  right: -31px;
                }
                >img{
                  position: absolute;
                  right: 0;
                  top: 33px;
                }
                >div{
                  text-align: left;
                  margin-right: 17px;
                  height: 60px;
                  width: 100%;
                  .label{
                    font-size: 10px;font-weight: bold;
                    color: $primary-dark; line-height: 13px;
                    span{font-size: 14px;line-height: 16px;display: block;}
                  }
                  .value{
                    font-size: 10px;font-weight: bold;
                    color: #1DD1A1; line-height: 16px;
                    span{font-size: 11px;display: block;}
                  }
                }
                
              }
              .milestone-location-content{
                background: #FFFFFF 0% 0% no-repeat padding-box;
                box-shadow: 0px 3px 16px #3469cb29;
                border-radius: 8px;
                display: flex;
                padding: 14px 10px;
                position: relative;
                margin-left: -30px;
                margin-bottom: -15px;
                
                >div{
                  text-align: right;
                  margin-left: 25px;
                  .label{
                    font-size: 10px;font-weight: bold;
                    color: $primary-dark; line-height: 13px;
                  }
                  .value{
                    font-size: 12px;font-weight: bold;
                    color: #F99746; line-height: 16px;
                  }
                }
                
              }
              .milestone-achieved-content{
                background: #CBFFED;
                text-align: right;
                border-radius: 4px;
                padding: 9px 5px;
                position: relative;
                margin-left: 10px;
                margin-bottom: -15px;
                &::before{
                  position: absolute;
                  content: "";
                  width: 0px;
                  height: 0px;
                  border-right: 23px solid #CBFFED;
                  border-top: 22px solid transparent;
                  border-bottom: 23px solid transparent;
                  top: 50%;transform: translate(0, -50%);
                  left: -22px;
                }
                .label{
                  font-size: 10px;font-weight: bold;
                  color: $primary-dark; line-height: 13px;
                }
                .value{
                  font-size: 12px;font-weight: bold;
                  color: #0F8D61; line-height: 16px;
                }
              }
              .milestone-achieved-content-left{
                background: #CBFFED;
                text-align: left;
                border-radius: 4px;
                padding: 9px 5px;
                position: relative;
                margin-left: 10px;
                margin-bottom: -15px;
                &::before{
                  position: absolute;
                  content: "";
                  width: 0px;
                  height: 0px;
                  border-left: 23px solid #CBFFED;
                  border-top: 22px solid transparent;
                  border-bottom: 23px solid transparent;
                  top: 50%;transform: translate(0, -50%);
                  right: -22px;
                }
                .label{
                  font-size: 10px;font-weight: bold;
                  color: $primary-dark; line-height: 13px;
                }
                .value{
                  font-size: 12px;font-weight: bold;
                  color: #0F8D61; line-height: 16px;
                }
              }
            }
          }
          .disclaimer{
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border-radius: 8px;
            padding: 5px;
            text-align: CENTER;
            position: absolute;
            width: 100%;
            bottom: 30px;
            > div{
              display: flex;align-items: center;
              border: 2px dashed #ccc;padding: 8px;
              img{margin-right: 12px;}
              border-radius: 8px;text-align: left;
              p.label{
                font-size: 10px;line-height: 16px;
                color: $primary-dark;  text-transform: uppercase;font-weight: bold;
                span{text-transform: capitalize; font-weight: 400;} 
              }
              p.description{
                font-size: 12px;line-height: 16px;
                color: $primary-dark;  
                span{font-size: 14px; font-weight: bold;} 
              }
            }
            
          }
          .note{
            background: #FFFFFF 0% 0% no-repeat padding-box;
            border-radius: 8px;
            padding: 16px 0;
            text-align: CENTER;
            position: absolute;
            width: 100%;
            bottom: 30px;
            p{
              font-size: 14px;line-height: 19px;
              color: $primary-dark;    max-width: 200px;
              margin: 0 auto;
            }
          }
        }
      }
    }
    
  }
}

.MuiTimelineItem-alignAlternate:nth-child(even) .MuiTimelineItem-content{
  .milestone-achieved-content{
    background: #CBFFED;
    text-align: left !important;
    border-radius: 4px;
    padding: 9px 5px;
    position: relative;
    margin-left: 10px;
    margin-bottom: -15px;
    &::before{
      position: absolute;
      content: "";
      width: 0px;
      height: 0px;
      border-left: 23px solid #CBFFED !important;
      border-right: unset !important;
      border-top: 22px solid transparent;
      border-bottom: 23px solid transparent;
      top: 50%;transform: translate(0, -50%);
      right: -22px !important;
      left: unset !important;
    }
    .label{
      font-size: 10px;font-weight: bold;
      color: $primary-dark; line-height: 13px;
    }
    .value{
      font-size: 12px;font-weight: bold;
      color: #0F8D61; line-height: 16px;
    }
  }
}

.ReactModalPortal {
  z-index: 5100;
  position: relative;
}

@media all and (max-width: 768px) {
  .content-panel{
    // Main Slider css
    .main-slider {
      .slick-slider {
        .slick-slide {
          .card{
            min-height: 244px;
            &.reward{
              padding: 25px;
              img{
                display: none;
              }
              button{
                &.btn{
                  margin-top: 0;
                }
              }
            }
            .carousel-caption{
              p{
                font-size: 13px;
              }
            }
            &.ceo_msge {
              .MuiCardContent-root {
                margin-top: 0;
                .image{

                }
              }
            }
          }
        }
      }
    }
  // Leadership CSS
    .leaderboard {
      .slick-slider{
        min-height: 240px;
      }
    }
  // Gallery CSS
    .gallery {
      .card {
        .slick-slider {
          .slick-slide {
            .card {
              p{
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    // Explainer Video CSS
    .explainer_video{
      .card{
        .slick-slider {
          .slick-slide {
            .card{
              &.image {
                min-height: 270px;
                .media {
                  .description{
                    h5{
                      
                    }
                  }
                }
                .media{
                  flex-direction: column;
                  .image{
                    img{
                      width: 80px;
                      float: left;
                    }
                    strong{
                      display: grid;
                      width: auto;
                      small{
                        clear: both;
                        display: table-caption;
                        position: relative;
                        bottom: inherit;
                      }
                    }
                  }
                  .description{
                    padding-right: 0;
                    h5{
                      font-size: 14px;
                      line-height: 20px;
                      margin-bottom: 10px;
                    }
                    p{
                      line-height: inherit;
                      font-size: 13px;
                    }
                  }
                }
                .more{
                  position: inherit;
                  right: inherit;
                  bottom: inherit;
                  font-size: 12px;
                  float: right;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media all and (max-width: 600px) {
  .content-panel {
    .main-slider {
      .slick-slider {
        .slick-slide {
          .banner{
            padding: 15px;
            &.slide1{
              background-size: contain;
            }
            &.slide2{
              background-size: contain;
            }
            &.slide3{
              background: linear-gradient(270deg, #15C78B 0%, #0187C4 100%);
              
            }
          }
          .card{
            &.ceo_msge {
              .MuiCardContent-root {
                .image{
                  display: none;
                }
                .carousel-caption{
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
  
}

.set-goal-popup-wrapper{
  .MuiDialog-paperFullWidth{
    border-radius: 8px;
    max-width: 672px !important;
    .set-goal-popup-content{
      background: transparent linear-gradient(302deg, #424770 0%, #696694 100%) 0% 0% no-repeat padding-box;
      box-shadow: 0px 3px 16px #86868629;
      border-radius: 8px;
      .set-goal-popup-content-text {
        .set-goal-container{
          .set-goal-form{
            p{
              color: #FFFFFF; font-size: 18px;
              font-weight: bold;margin-bottom: 24px;
            }
            .form{
              .form-group{
                .MuiFormControl-fullWidth{
                  margin-bottom: 24px;
                  .MuiInputLabel-formControl{
                    color: #fff;
                  }
                  .MuiInputBase-root{
                    color: #fff;font-size: 24px;
                    &.MuiInput-underline{
                      &:before, &:after{
                        border-bottom-color: #fff;
                      }
                      &:hover:not(.Mui-disabled):before{
                        border-bottom-color: #fff;
                      }
                    }
                  }
                }
              }
            }
          }
          .set-goal-details{
            .required-run-rate-wrapper{
              background: #2a2c57;
              border-radius: 8px;
              padding: 3px;
              .required-run-rate-content{
                border: 0.800000011920929px dashed #5D73E5;
                border-radius: 8px;
                padding: 8px;
                display: flex;
                justify-content: space-between;
                > div{
                  margin-left: 10px;
                  .heading{
                    color: #F9BB46;
                    text-transform: uppercase;
                    font-size: 10px;
                    line-height: 16px;font-weight: 900;
                  }
                  .description{
                    color: #fff; font-size: 12px; line-height: 16px;
                    span{color: #F9BB46;font-size: 14px; font-weight: bold;}
                  }
                }
              }
              
            }
            .required-details-wrapper{
              margin: 23px auto 25px;
              >p {
                font-size: 12px; line-height: 16px;
                color: #fff; font-weight: bold;
                margin-bottom: 16px;    text-align: center;
                text-transform: uppercase;position: relative;
                &::before{
                  content: "";
                  background: #fff;
                  height: 1px;
                  width: 27%;
                  position: absolute;
                  left: 0;
                  top: 50%;
                  transform: translate(0, 50%);
                }
                &:after{
                  content: "";
                background: #fff;
                height: 1px;
                width: 27%;
                position: absolute;
                right: 0;
                top: 50%;
                transform: translate(0, 50%);
                }
              }
              .required-details-content{
                display: flex;
                align-items: center;
                justify-content: space-between;
                .details{
                  background: #6f709b;
                  border-radius: 8px;    width: 100%;
                  padding: 16px 0px 26px 8px;max-width: 104px;
                  p.heading{
                    font-size: 10px; line-height: 13px;
                    color: #fff; font-weight: bold;
                    margin-bottom: 4px;
                  }
                  .description{
                    font-size: 16px; line-height: 23px;
                    color: #fff; font-weight: 500;
                  }
                }
              }
            }
            .set-goal-as-wrapper{
              background: #2a2c57;
              border-radius: 8px;
              padding: 9px 14px;display: flex;
              align-items: center;
              justify-content: space-between;
              .label{
                font-size: 14px; line-height: 19px;
                color: #fff; font-weight: 500;
              }
              .goal{
                font-size: 24px; line-height: 32px;
                color: #fff; font-weight: 400;
                span{
                  font-size: 16px; line-height: 21px;
                  opacity: 0.6;    text-decoration: line-through;
                  display: inherit;
                  text-align: right;
                }
              }
            }
          }
          .set-goal-buttons{
            text-align: center;
            .disclaimer{
              font-size: 14px; line-height: 19px;
              color: #fff; font-weight: 400; margin-bottom: 14px;
            }
            .buttons{
              .set-goal{
                background: #F99746 0% 0% no-repeat padding-box;
                border-radius: 8px;
                color: #fff;
                font-size: 14px;
                font-weight: bold;    max-width: 158px;
                width: 100%;
                margin-right: 8px;
              }
              .cancel{
                border: 1px solid #F99746; 
                font-weight: bold;
                border-radius: 8px;
                color: #F99746;max-width: 158px;
                width: 100%;
                margin-right: 8px;
              }
            }
          }
        }
      }
    }
  }
}
.MuiTabs-indicator {
  display: none !important;
}