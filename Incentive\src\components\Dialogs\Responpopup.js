import React from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
  TextField,
  TextareaAutosize,
  Select,
  FormControl,
  InputLabel,
  MenuItem,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import FormLabel from "@material-ui/core/FormLabel";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
  popupBox: {
    width: "300px",
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: "360px",
    maxWidth: "100%",
    border: "1px solid #ccc",
    borderRadius: "5px",
    padding: "0 8px",
    margin: "10px 0",
    "&:hover": {
      outline: "none",
      boxShadow: "none",
    },
  },
  DialogTitle: {
    padding: "15px 0 10px",
  },

  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  submitBtn: {
    color: "#0052CC",
    marginRight: "15px",
    fontWeight: "600",
  },
}));

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function AlertDialogSlide() {
  const classes = useStyles();
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <div>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Button color="primary" onClick={handleClickOpen}>
            Submit
          </Button>
        </Grid>
      </Grid>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        fullWidth={true}
        maxWidth={"xs"}
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <DialogTitle
              id="alert-dialog-title"
              className={classes.DialogTitle}
            >
              {"Select a reason"}
            </DialogTitle>
            <FormControl component="fieldset">
              <RadioGroup
                aria-label="status"
                name="status"
                className={classes.statusBar}
                onChange={handleChange}
              >
                {" "}
                <FormControlLabel
                  value="Lunch"
                  control={<Radio />}
                  label="Lunch"
                  small
                />
                <FormControlLabel
                  value="Tea"
                  control={<Radio />}
                  label="Tea"
                  small
                />
                <FormControlLabel
                  value="Training"
                  control={<Radio />}
                  label="Training"
                  small
                />
                <FormControlLabel
                  value="Meeting"
                  control={<Radio />}
                  label="Meeting"
                  small
                />
                <FormControlLabel
                  value="Day End"
                  control={<Radio />}
                  label="Day End"
                  small
                />
              </RadioGroup>
            </FormControl>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={handleClose}
            color="primary"
            className={classes.submitBtn}
          >
            Submit
          </Button>
        </DialogActions>
      </Dialog>
    </div>
  );
}
