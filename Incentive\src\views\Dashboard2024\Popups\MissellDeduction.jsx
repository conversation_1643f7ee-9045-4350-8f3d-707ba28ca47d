import React,{useContext, useEffect, useState} from "react";
import { GetCommonData } from "../../../common/CommonAction";
import DataTable2 from "../Components/DataTable2";
import { DashboardContext } from "../Context/Context";
import { getUserDetails } from "../Utility/Utility";
import Loader from "../Components/Loader";

const MissellDeduction=()=>{
    const dashboardData = useContext(DashboardContext);
    const [rows, setRows]= useState([]);
    const [columns, setColumns]= useState([]);
    const [loader, setLoader]= useState(true);
    const sortColumns =[];

    let MissellDataProcess=(data)=>{

        let cols= (data.hasOwnProperty('label') && data['label'] ) || {};
        let missellData= (Array.isArray(data['data']) && data['data']) || [];

        if(missellData.length==0)
        {
            dashboardData.closeFunction('missell');
            setLoader(false);
            return;
        }

        let columns=[{
            id:'Id',
            type: typeof(1),
            sort: true,
            label: 'Sr. No',
            disablePadding: true
        }];
        let tempSort = (sortColumns && Array.isArray(sortColumns) && sortColumns.length>0 && sortColumns)|| [] ;
        Object.keys(cols).length>0 && Object.keys(cols).map((key)=>{
            let column ={
                id:key,
                type: typeof(1),
                sort:  tempSort.includes(key)?true: false,
                label: cols[key],
                disablePadding: false
            }
            columns.push(column);
       });
        missellData.map((missell, index)=>{
        missell['Id']= index+1;
        missell['ClassName']= 'statusNo';
        });
        setColumns(columns);
        setRows(missellData);
        setLoader(false);
        

    }

    useEffect(()=>{
        if(dashboardData.dashboardData && dashboardData.dashboardData.MissellDeduction)
        {
           
            MissellDataProcess(JSON.parse(JSON.stringify(dashboardData.dashboardData.MissellDeduction)));
        }
        else{
            let body={
                eCode: dashboardData.agentDetails && dashboardData.agentDetails.EmpId,
                monthYear:  dashboardData.monthChosen,
                userid: getUserDetails('UserId')
            }
        // let body={
        //     eCode: "PW00000" || dashboardData.agentDetails.EmpId,
        //     monthYear: "NOV2023" || dashboardData.monthChosen
        // }
        let data = {EndPoint:"missel", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo:"term", body: JSON.stringify(body)};
        GetCommonData(
            "POST", data
            ,(errorStatus,data)=>{
            if(!errorStatus)
            {
                
                dashboardData.setDashboardData(data, "MissellDeduction");
                MissellDataProcess(JSON.parse(JSON.stringify(data)));
                    
                
            }
            else{
                dashboardData.setDashboardData(null, "MissellDeduction")
                setRows([]);
                setColumns([]);
            }
        });
    }
    
    },[])

    return(
        <>
        {
        loader? <Loader/>:
        <div className="BookingBreakdown missellDeduction">
        <div className="Heading">
            <h3>Mis Sell</h3>              
        </div>
       
        {rows.length>0 && columns.length>0 && <DataTable2 data={rows} columns={columns} />}
        </div>
    }
    </>
    )

}
export default MissellDeduction;