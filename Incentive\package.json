{"name": "IncentiveJag", "version": "1.0.0", "description": "IncentiveAndJag", "main": "index.js", "scripts": {"start": "webpack-dev-server --open --mode development --config webpack.dev.js", "build": "webpack --mode production", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "jest": {"setupFiles": ["./test/jestsetup.js"], "snapshotSerializers": ["enzyme-to-json/serializer"], "moduleNameMapper": {"^.+\\.(css|scss)$": "identity-obj-proxy"}}, "keywords": [], "author": "Incentive", "license": "ISC", "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dependencies": {"@date-io/date-fns": "^1.3.11", "@emotion/react": "^11.8.2", "@emotion/styled": "^11.8.1", "@material-ui/core": "^4.12.3", "@material-ui/icons": "^4.2.1", "@material-ui/lab": "^4.0.0-alpha.31", "@material-ui/pickers": "^3.2.7", "@material-ui/styles": "^4.2.1", "@material/snackbar": "^4.0.0", "@mui/icons-material": "^5.15.11", "@mui/material": "^5.15.10", "@mui/styles": "^5.8.7", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "chart.js": "^3.8.0", "chartjs-plugin-datalabels": "^2.0.0", "clsx": "^1.0.4", "copy-webpack-plugin": "^5.0.4", "date-fns": "^2.6.0", "export-from-json": "^1.6.0", "google-map-react": "^1.1.6", "history": "^4.9.0", "html-react-parser": "^1.2.4", "material-ui-flat-pagination": "^4.0.0", "material-ui-slider": "^3.0.8", "moment": "^2.24.0", "node-sass": "^4.12.0", "notistack": "^0.9.6", "pdfjs-dist": "^2.5.207", "prop-types": "^15.7.2", "react": "^16.8.6", "react-chartjs-2": "^4.2.0", "react-csv": "^2.2.2", "react-data-table-component": "^4.0.2", "react-data-table-component-extensions": "^1.2.2", "react-dom": "^16.8.6", "react-google-charts": "^3.0.15", "react-image-lightbox": "^5.1.1", "react-intl": "^3.8.0", "react-pdf": "^4.0.3", "react-perfect-scrollbar": "^1.5.3", "react-redux": "7.0.2", "react-router-dom": "^5.3.4", "react-scripts": "^3.0.1", "react-slick": "^0.27.13", "recompose": "^0.30.0", "redux-persist": "5.10.0", "slick-carousel": "^1.8.1", "start": "^5.1.0", "styled-components": "^5.3.5", "underscore": "^1.11.0", "validate.js": "^0.13.1", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/core": "^7.22.8", "@babel/plugin-proposal-class-properties": "^7.10.1", "@babel/plugin-transform-runtime": "7.4.3", "@babel/preset-env": "^7.22.7", "@babel/preset-react": "7.0.0", "autoprefixer": "^9.6.4", "axios": "^0.19.0", "babel-core": "6.26.3", "babel-eslint": "10.0.1", "babel-loader": "8.0.5", "babel-plugin-emotion": "^10.0.23", "babel-plugin-syntax-dynamic-import": "6.18.0", "babel-plugin-transform-runtime": "6.23.0", "babel-polyfill": "6.26.0", "babel-preset-es2015": "6.24.1", "babel-preset-stage-0": "6.24.1", "clean-webpack-plugin": "^2.0.2", "dotenv": "^8.1.0", "enzyme": "^3.10.0", "enzyme-adapter-react-16": "^1.15.1", "enzyme-to-json": "^3.4.3", "eslint": "5.16.0", "eslint-plugin-prettier": "^3.0.1", "eslint-plugin-react": "^7.12.4", "html-webpack-plugin": "^3.2.0", "jest": "^24.9.0", "mini-css-extract-plugin": "^0.6.0", "postcss-loader": "^3.0.0", "prettier": "^1.17.1", "prettier-eslint": "^8.8.2", "prettier-eslint-cli": "^4.7.1", "redux": "4.0.1", "redux-thunk": "2.3.0", "sass-loader": "7.1.0", "style-loader": "0.23.1", "typescript": "^3.5.1", "url-loader": "^1.1.2", "webpack": "^4.30.0", "webpack-cli": "3.3.0", "webpack-dev-server": "3.2.1", "worker-loader": "^3.0.8"}}