
import React,{useEffect, useState, useContext} from "react";
import { GetCommonData } from "../../../common/CommonAction";
import  Grid  from "@mui/material/Grid";
import { DashboardContext } from "../Context/Context";
import {rupeeConverter, getUserDetails} from '../Utility/Utility';

const Leaderboard = () => {

    const [leaderBoard, setLeaderBoard]= useState([]);

    const popupHandler = useContext(DashboardContext);

    let rupee = rupeeConverter();

    useEffect(()=>{
        let body={
            eCode: popupHandler.agentDetails && popupHandler.agentDetails.EmpId,
            monthYear:  popupHandler.monthChosen,
            userid: getUserDetails('UserId')
        }
        // let body={
        //     eCode: "PW00000" || popupHandler.agentDetails.EmpId,
        //     monthYear: "NOV2023" || popupHandler.monthChosen,
        //     userid: 8223
        // }
        let data = {EndPoint:"wtapeRank", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo:"term", body: JSON.stringify(body)};
        GetCommonData(
            "POST", data
            ,(errorStatus,data)=>{
            if(!errorStatus)
            {
                // console.log("wtape",new Date().getTime());
                let obj={};
                if(Array.isArray(data) && data.length>0)
                {
                    data.map((element, index)=>{
                        if(!element)
                        return null;
                        if(element.rank==1)
                        {
                            obj[2]= <><img src="/images/jag2023/1st.png" />
                                   <h3>{rupee.format(element.wtAPE) || '-'}</h3></>;
                        }
                        else if(element.rank==2)
                        {
                            obj[1]=<><img src="/images/jag2023/2nd.png" />
                            <h3>{rupee.format(element.wtAPE) || '-'}</h3></>;
                        }
                        else if(element.rank==3)
                        {
                            obj[3]=<><img src="/images/jag2023/3rd.png" />
                            <h3>{rupee.format(element.wtAPE) || '-'}</h3></>;
                        }

                        if(element.isSelf)
                        {
                         
                            popupHandler.setRank(element.rank || null);
                            let rank= element.rank || 0;
                            let rem= rank%10;
                        
                            while(rank>100)
                            {
                                rank= Math.floor(rank/10);
                            }

                            let quotient= Math.floor(rank/10);
                            let suffix= "th";
                            if(quotient!=1)
                            {
                                switch(rem) {
                                    case 1:
                                        suffix='st';
                                        break;
                                    case 2:
                                        suffix='nd';
                                        break;
                                    case 3:
                                        suffix='rd';
                                        break;
                                    default:
                                        break;
                                }
                            }
                            obj[4]=<li className="active"><span>{element.rank || '-'}<sup>{suffix}</sup> </span> <span className="ml-2">{popupHandler.agentDetails ? (popupHandler.agentDetails.UserName || ''): ''}</span>{rupee.format(element.wtAPE) || '-'}</li>;
                        }
                    })
                }
                const sortedKeys = Object.keys(obj).sort((a, b) => a - b);
                let leaderboardData=[];
                sortedKeys.length>0 && sortedKeys.map((key)=>{
                    leaderboardData.push(obj[key]);
                });
              
                setLeaderBoard(leaderboardData);
            }
            else{
                setLeaderBoard([]);
            }
        });
    },[popupHandler.monthChosen])

    return (
        <>
            <Grid item md={4} sm={4} xs={12}>
                <div className="box3">
                    <h4>Process Wise Leaderboard</h4>
                    {/* {console.log("before useEFFECT wtape ", new Date().getTime())} */}
                    {
                     leaderBoard.length>0 &&
                     <>
                    <ul>
                    {
                        leaderBoard.map((Leader, index)=>{
                            if(index<3)
                            {
                                return(
                                <li>
                                  {Leader}
                                <p>WT. APE</p>
                                </li>
                                )
                            }
                        })
                    }
                    </ul>
                    <hr />
                    <ul className="incentive-box">
                         <li className="Heading"><span>Rank</span>WT. APE</li>
                         {/* {console.log("wtape ",new Date().getTime())} */}
                         {leaderBoard[3]}
                    </ul>
                    </>
                    }
                </div>
            </Grid>
        </>
    )
}
export default Leaderboard;