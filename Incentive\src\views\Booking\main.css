.pull-request {
  background: #00458b;
  -webkit-box-shadow: 0px 0px 16px #0000000f;
          box-shadow: 0px 0px 16px #0000000f;
  border-radius: 5px;
  height: 135px;
  padding: 5px 10px;
}

.pull-request h3 {
  font-size: 20px;
  font-weight: 600;
  color: #fff;
}

.pull-request p {
  color: #fff;
  font-size: 16px;
  line-height: 30px;
  letter-spacing: 1px;
}

.pull-request .pullBtn {
  color: #00458b;
  height: 35px;
  margin: 20px 10px 0 0;
  padding: 0 12px;
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 16px #0000001a;
          box-shadow: 0px 0px 16px #0000001a;
  line-height: 28px;
  border-radius: 24px;
}

.pull-request .pullBtn:hover {
  background: #ffffff;
}

.pull-request .pullInbound {
  color: #fff;
  height: 35px;
  margin: 20px 10px 0 0;
  padding: 0 12px;
  background: #608bb7;
  -webkit-box-shadow: 0px 0px 16px #0000001a;
          box-shadow: 0px 0px 16px #0000001a;
  line-height: 28px;
  border-radius: 24px;
}

.pull-request .pullInbound:hover {
  background: #608bb7;
}

.rank-box {
  background: #dfe6fb;
  -webkit-box-shadow: 0px 0px 16px #0000000f;
          box-shadow: 0px 0px 16px #0000000f;
  border-radius: 5px;
  height: 135px;
  padding: 5px 10px;
}

.rank-box h3 {
  font-size: 20px;
  font-weight: 600;
  color: #414141;
  color: #00458b;
}

.rank-box p {
  color: #00458b;
  font-size: 28px;
  line-height: 30px;
  letter-spacing: 2px;
}

.rank-box .incentives {
  background: #dfe6fb;
  color: #00458b;
  border: 2px solid #00458b;
  height: 35px;
  margin: 20px 10px 0 0;
  padding: 0 12px;
  -webkit-box-shadow: 0px 0px 16px #0000001a;
          box-shadow: 0px 0px 16px #0000001a;
  line-height: 28px;
  border-radius: 24px;
}

.rank-box .incentives:hover {
  background: #dfe6fb;
}

button img {
  margin: 0 5px 0 0;
}

.rating-card {
  background: #ffffff 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0px 0px 16px #0000000f;
          box-shadow: 0px 0px 16px #0000000f;
  border-radius: 8px;
  position: relative;
  height: 92px;
}
/*# sourceMappingURL=main.css.map */