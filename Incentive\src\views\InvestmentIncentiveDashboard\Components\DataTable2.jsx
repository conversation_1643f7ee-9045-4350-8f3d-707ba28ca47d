import React, { useContext, useEffect, useState } from "react";
import { Box, TableHead, Table, TableBody, TableCell, TableContainer, TablePagination, TableRow, TableSortLabel, Collapse, Experimental_CssVarsProvider, Button } from "@mui/material";
import PropTypes from 'prop-types';
import { DataTableContext } from "../Context/Context";
import _ from "lodash";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import { parseCurrencyString } from "../../Dashboard2024/Utility/Utility";


const EnhancedTableHead = (props) => {


    const { columns, headerSpan, headerColor } =
        props;

    const datatable = useContext(DataTableContext);

    const handleClick = (e, column, order) => {
        datatable.handleSort(column.type || '', column.id, order || 'asc');
    }


    return (
        <TableHead>
            <TableRow style={{ background: headerColor }} key={Math.random()}>

                {columns.map((column, index) => {


                    return (
                        <>
                            <TableCell
                                key={column.id}
                                align={"right"}
                                padding={column.disablePadding ? 'none' : 'normal'}
                                rowSpan={2}
                            >
                                {
                                    column.sort ?
                                        <>
                                            {column.label}
                                            <div className="SortingIcon">
                                                <img src="/images/TermDashboard/up-icon.svg" direction="asc"
                                                    active={true}
                                                    onClick={(e) => handleClick(e, column, 'asc')} />

                                                <img src="/images/TermDashboard/down-icon.svg" direction="desc"
                                                    active={true}
                                                    onClick={(e) => handleClick(e, column, 'desc')} />
                                            </div>

                                            {/* <TableSortLabel
                                               
                                                direction="asc"
                                                active={true}
                                                onClick={(e) => handleClick(e, column, 'asc')}>
                                                {column.label}</TableSortLabel> */}
                                            {/* <TableSortLabel
                                               
                                                direction="desc"
                                                active={true}
                                                onClick={(e) => handleClick(e, column, 'desc')}
                                            /> */}
                                        </>
                                        :
                                        <>{column.label}</>
                                }


                            </TableCell>
                        </>
                    )
                })}
            </TableRow>

        </TableHead>
    );
}

EnhancedTableHead.propTypes = {
    rowCount: PropTypes.number.isRequired,
};

const DataTable2 = ({ data, columns, cellSpan, headerSpan, extraRow, headerColor, pagination, breakup }) => {

    const [page, setPage] = useState(0);
    const [tableData, setTableData] = useState([]);

    const [showBreakup, setShowBreakup] = useState([])


    const [rowsPerPage, setRowsPerPage] = useState(8);

    let noOfPages = (Array.isArray(data) && data.length > 0) ? (data.length % rowsPerPage == 0 ? Math.floor(data.length / rowsPerPage) : Math.floor(data.length / rowsPerPage) + 1) : 0;

    if (noOfPages > 10) {
        let rows = data.length % 10 == 0 ? Math.floor(data.length / 10) : Math.floor(data.length / 10) + 1;
        setRowsPerPage(rows);
    }

    useEffect(() => {

        if (data.length > 0) {


            let fRow = page * rowsPerPage;
            let lRow = fRow + rowsPerPage - 1;
            if (lRow > data.length - 1) {
                lRow = data.length - 1;
            }
            let tableData = [];
            for (let i = fRow; i <= lRow; i++) {
                tableData.push(data[i]);
            }
            setTableData(tableData);
        }
    }, [data, page])




    const handleChangePage = (event, newPage) => {

        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {

        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handlePageChange = (e) => {

        setPage(e.target.value);
    }

    const handleSort = (type, key, order) => {


        let tempData = tableData || [];

        if (type == "currency") {
            tempData.map((data) => {
                data[key + '1'] = parseCurrencyString(data[key]);
            });
            let sortedTempData = tempData.length > 0 &&
                _.orderBy(tempData, key + '1', order);
            setTableData(sortedTempData)
        }
        else {
            let sortedTempData = tempData.length > 0 &&
                _.orderBy(tempData, key, order);
            setTableData(sortedTempData);
        }


    }

    // const handleArrowClick = (e) => {
    //     if (e.target.id == 0) {

    //         console.log("The event is ", 0);
    //     }
    //     else {
    //         console.log("The evtns is ", e.target.id);
    //     }
    // }


    useEffect(()=>{

        if(breakup)
        {
            let data = [];
           
            let totalRows=tableData.length;
            let index=0;
            while(totalRows>0)
            {
                data.push({
                    id:index,
                    show:false
                })
                index+=1;
                totalRows-=1;
            }
           
            setShowBreakup(data);
        }


    },[tableData])


    const handleOnShowCalculation=(e,index)=>{

        let showBreakupState = [...showBreakup];

        for(let i=0;i<showBreakupState.length;i++)
        {
            if(showBreakupState[i].id==index)
            {
                showBreakup[i].show = !showBreakup[i].show;
                break;
            }
        }

        setShowBreakup(showBreakupState);

    }

    const TextToDisplayOnTheButton=(index)=>{
        let obj =showBreakup.find((button)=>button.id==index);
        if(obj)
        {
            if(obj.show)
            {
                return "Hide Calculation"
            }
            return "Show Calculation"
        }
        return null
    }


    return (
        <DataTableContext.Provider value={{ handleSort: handleSort }}>
            <TableContainer>
                <Table
                    sx={{ minWidth: 750 }}
                >
                    <EnhancedTableHead

                        columns={columns}
                        rowCount={tableData.length}
                        headerSpan={headerSpan ? headerSpan : null}
                        headerColor={headerColor ? headerColor : null}
                    />
                    <TableBody>

                        {
                            tableData.map((row, index) => {

                                return (
                                    <>
                                        <TableRow
                                            hover
                                            className={row.ClassName}
                                            key={index}
                                            sx={{ cursor: 'pointer' }}
                                        >
                                            {
                                                columns.map((key, columnIndex) => {


                                                    if (!cellSpan || key.id != cellSpan) {

                                                        if (typeof (row[key.id]) != 'boolean') {
                                                            if(key.id=='button')
                                                            {
                                                                    return (
                                                                        <TableCell><Button variant='contained'  onClick={(e)=>{handleOnShowCalculation(e,index)}} style={{color:'#FFF',fontFamily:'Roboto',fontSize:'12px',fontStyle:'normal',fontWeight:500, lineHeight:'normal'}}>{TextToDisplayOnTheButton(index)}</Button></TableCell>
                                                                    )
                                                            }


                                                            return (

                                                                <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} align={columnIndex == 0 ? "right" : "center"} >{row[key.id] || '-'}</TableCell>
                                                            )


                                                        }
                                                        else {

                                                            let data = row[key.id] ? 'Yes' : 'No';

                                                            return (
                                                                <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} align={columnIndex == 0 ? "right" : "center"} >{data || '-'}</TableCell>
                                                            )
                                                        }
                                                    }

                                                    else {

                                                        if (row.hasOwnProperty('span')) {

                                                            return (
                                                                <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} rowSpan={row['span']} align={columnIndex == 0 ? "right" : "center"}>
                                                                    {row[key.id]}
                                                                </TableCell>
                                                            )
                                                        }

                                                    }


                                                })
                                            }
                                        </TableRow>
                                        {
                                            showBreakup.find((breakup)=>breakup.id==index) ?  (showBreakup.find((breakup)=>breakup.id==index).show ? 

                                            Array.isArray(row['breakup']) && row['breakup'].length > 0 &&

                                            row['breakup'].map((r) => {

                                                return (
                                                    <TableRow
                                                        hover
                                                        className={r.ClassName}
                                                        key={index}
                                                        sx={{ cursor: 'pointer' }}
                                                    >
                                                        <TableCell></TableCell>
                                                        {
                                                            columns.map((key, columnIndex) => {


                                                                if (!cellSpan || key.id != cellSpan) {

                                                                    if (typeof (r[key.id]) != 'boolean') {

                                                                        return (

                                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word', color: '#8090AC'}} align={columnIndex == 0 ? "right" : "center"} >{r[key.id] || null}</TableCell>
                                                                        )


                                                                    }
                                                                    else {

                                                                        let data = r[key.id] ? 'Yes' : 'No';

                                                                        return (
                                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word', color: '#8090AC' }} align={columnIndex == 0 ? "right" : "center"} >{data || null}</TableCell>
                                                                        )
                                                                    }
                                                                }

                                                                else {

                                                                    if (r.hasOwnProperty('span')) {

                                                                        return (
                                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word', color: '#8090AC' }} rowSpan={r['span']} align={columnIndex == 0 ? "right" : "center"}>
                                                                                {r[key.id]}
                                                                            </TableCell>
                                                                        )
                                                                    }

                                                                }


                                                            })
                                                        }
                                                    </TableRow>
                                                )
                                            
                                            
                                            }) 
                                            : 
                                            null 
                                        )
                                            :
                                            null
                                        }
                                    </>



                                )
                            })
                        }

                        {
                            extraRow && extraRow > 0 &&
                            Array(extraRow).fill().map((val, index) => {
                                return (
                                    <TableRow hover key='extra' sx={{ cursor: 'pointer' }}>
                                        {
                                            columns.map((key, columnIndex) => {
                                                if (!key) { return null; }
                                                if (Array.isArray(key.extraRow) && key.extraRow[index]) {
                                                    return (
                                                        <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} className="Blue" align={columnIndex == 0 ? "right" : "center"} >{key.extraRow[index]}</TableCell>
                                                    )
                                                }
                                                else {
                                                    return (
                                                        <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} />
                                                    )
                                                }
                                            })
                                        }
                                    </TableRow>
                                )
                            })
                        }


                    </TableBody>
                </Table>
            </TableContainer>

            {/* <TablePagination
                rowsPerPageOptions={[8, 16, 32]}
                count={data.length}
                rowsPerPage={rowsPerPage}
                page={page}
                component="div"
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            /> */}

            {pagination &&
                <div className="TablePagination">
                    {
                        Array.isArray(data) && data.length > 0 &&
                        <>
                            {/* <ArrowBackIcon id={0} onClick={handleArrowClick} color={page != 0 ? "primary" : "disabled"}  /> */}
                            {


                                Array(noOfPages).fill(0).map((val, index) => {
                                    // Array(10).fill(0).map((val, index) => {
                                    return (
                                        <Button className={page == index ? "activeBtn" : ""} value={index} onClick={handlePageChange}>{index + 1}</Button>
                                    )
                                })

                            }
                            {/* <ArrowForwardIcon id={1} onClick={handleArrowClick} color={page != noOfPages ? "primary" : "disabled"} /> */}
                        </>
                    }

                    {/* {
                    Array.isArray(data) && data.length > 0 &&
                    Array((Math.floor(data.length / 8)) + 1).fill(0).map((val, index) => {
                        // Array(10).fill(0).map((val, index) => {
                        return (
                            <Button className={page==index?"activeBtn":""} value={index} onClick={handlePageChange}>{index + 1}</Button>
                        )
                    })

                } */}



                </div>
            }
        </DataTableContext.Provider>
    )

}

export default DataTable2;