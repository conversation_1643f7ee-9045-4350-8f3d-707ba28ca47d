import React, {useEffect, useState, useRef} from "react";
import { makeStyles, useTheme } from "@material-ui/core";
import * as services from "../../services";
import { withSnackbar } from 'notistack';
import { useMediaQuery } from '@material-ui/core';
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Grid,
  FormControl,
  Box,
  Tabs,
  Tab

} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import moment from "moment";
import ThumbDownIcon from '@material-ui/icons/ThumbDown';
import ThumbUpIcon from '@material-ui/icons/ThumbUp';
import FeedbackIcon from '@material-ui/icons/Feedback';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import ProjectedRanking from "./ProjectedRanking";
import CriteriaTerm from "./Critaria/CriteriaTerm";
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import CriteriaMotor from "./Critaria/CriteriaMotor";
import CriteriaInvestment from "./Critaria/CriteriaInvestment";

const CurrentMonthProjections = (props) =>{

  const {agentDetail} = props;
  const [ProjectedData, setProjectedData] = useState([])
  const [productId, setProductId] = useState(null)
  const [ProjectionDetailsMotor, setProjectionDetailsMotor] = useState([]);
  const loadCriteria = (productId) => {

    if (productId == 7) {
      return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>
    }
    if (productId == 117) {
      return <CriteriaMotor productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaMotor>
    }
    if (productId == 115) {
      return <CriteriaInvestment productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaInvestment>
    }
    return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>
  }

  useEffect(() => {
    services
      .API_GET(`Incentive/GetProjectedIncentive/${agentDetail.AgentId}`).then(response => {
        ////debugger
        if (response && response.Status) {
          response = JSON.parse(response.Response);
          //console.log("Dashboard================", response[0]);
          setProjectedData(response[0]);
          setProductId(response[0].ProductId);
          // let key = props.enqueueSnackbar('To make the projections more accurate, we are now showing the incentives post salary adjustment.', {
          //   variant: 'error',
          // })
          //debugger;
          try {
            const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
            let url = `Incentive/InsertAgentIncentivLog/${response[0].UserId}/0/${response[0].ProductId}/${dt}?PageName=ProjectedIncentive&EventName=Click`;

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
          catch (e) {

          }
          //debugger;
          if ([117, 115].indexOf(response[0].ProductId) > -1) {
            try {
              services
                .API_GET(`Incentive/GetProjectionDetailsMotor/${agentDetail.AgentId}`).then(response => {
                  ////debugger
                  if (response && response.Status) {
                    response = JSON.parse(response.Response);
                    if (response.length > 0) {
                      response = JSON.parse(response[0]);
                      setProjectionDetailsMotor(response);
                    }
                  }
                })
                .catch((err) => {
                  console.log("Error", err);
                });
            }

            catch (e) {

            }
          }




        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [agentDetail.AgentId, props]);

  useEffect(() => {
    return () => {
      console.log("cleaned up");
      props.closeSnackbar();
    };
  }, [props]);

  const checker = () =>{

    if(ProjectedData.productId == 117){
      console.log("We are In")
    }
  }


}

export default withSnackbar(CurrentMonthProjections);


