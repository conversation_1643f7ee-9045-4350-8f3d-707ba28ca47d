.data-card {
    flex-basis: 100%;
    margin: 10px;
    
    @media (min-width: 600px) {
        flex-basis: 45%;
    }
    @media (min-width: 992px) {
        flex-basis: 30%;
    }
    .data-card-content {
        padding: 16px 16px 0;
        counter-reset: serial-number;
        .data-card-content-row{
            padding-bottom: 16px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            > div{
                flex-basis: 50%;
            }
            .s-no{
                h6{
                    display: inline-block;
                    background: #e4e4e4;
                    padding: 6px 8px;
                    border-radius: 50%;
                    // &:after{
                    //     counter-increment: serial-number;
                    //     content: counter(serial-number);
                    // }
                }
            }
            .status {
                text-align: right;
            }
            .labelTitle {
                font-size: 12px;
            }
            .profile-title {
                font-weight: 400;
            }
            @media (min-width: 992px){
                margin-bottom: 16px;
            }
        }
    }
    .link-name {
        justify-content: flex-end;
        a{
            color: #6DD1D6;
            font-weight: bold;
        }
    }
}
