import Grid from "@mui/material/Grid";
import DataTable from "./DataTable2";
import React, { useEffect, useState, useContext, useRef } from "react";
import { GetCommonData } from "../../../common/CommonAction";
import { DashboardContext } from "../Context/Context";
import { handleDownload, getUserDetails, roundOffToTwo, rupeeConverter } from "../../Dashboard2024/Utility/Utility";


const Bookings = () => {

    const [rows, setRows] = useState([]);
    const [columns, setColumns] = useState([]);


    const dataRef =  useRef([]);

    const dashboardData = useContext(DashboardContext);

    const sortColumns = ['ape','wtApe','bookingDate','remarks']

    const rupeeConvert= rupeeConverter();

    // const sortColumns = ['eligible', 'ape', 'wtApe', 'bookingDate']

    useEffect(() => {

        let body={
            eCode: dashboardData.agentDetails && dashboardData.agentDetails.EmpId,
            monthYear:  dashboardData.monthChosen,
            userid: getUserDetails('UserId'),
            bu: "investment"
        }
       
        // let body = {
        //     eCode: "PW00000" || dashboardData.agentDetails.EmpId,
        //     monthYear: "FEB2025" || dashboardData.monthChosen,
        //     userid: 91072,
        //     bu: "investment"
        // }
        let data = { EndPoint: "bookingListInvestment", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "investment", body: JSON.stringify(body) };
        GetCommonData(
            "POST", data
            , (errorStatus, data) => {
               
                if (!errorStatus) {
                    let bookingList  = (data.hasOwnProperty('data') && data['data']) || [];
                    let bookingLabels= (data.hasOwnProperty('label') && data['label']) || {};
                    let columns=[];
                    if(Object.keys(bookingLabels).length>0 )
                    {
                        
                        columns.push({
                            id: 'bookingIdOrder',
                            type: typeof (1),
                            sort: true,
                            label: 'Sr. No',
                            disablePadding: true
                        })
                        let tempSort =  (sortColumns && Array.isArray(sortColumns) && sortColumns.length > 0 && sortColumns) || [];
                        Object.keys(bookingLabels).map((key) => {
                            let type = ['ape','wtApe'].includes(key)?"currency":"string";
                            let column = {
                                id: key,
                                type: type,
                                sort: tempSort.includes(key) ? true : false,
                                label: bookingLabels[key],
                                disablePadding: false
                            }
                            columns.push(column);
                        });
                    }
                    dataRef.current= JSON.parse(JSON.stringify(bookingList));
                    if (Array.isArray(bookingList) && bookingList.length > 0) {
                       
                        bookingList.map((Booking, index) => {
                            Booking['bookingIdOrder'] = index + 1;
                            Booking['ape'] = rupeeConvert.format(roundOffToTwo(Booking['ape']));
                            Booking['wtApe']=rupeeConvert.format(roundOffToTwo(Booking['wtApe']));
                            // Booking['remarks']= Booking['remarks']? (Booking['remarks'].length>15?Booking['remarks'].slice(0,12)+'...':Booking['remarks']):'-';
                            Booking['ClassName'] = Booking['eligible'] ? 'statusYes' : 'statusNo';
                        })
                     
                    }
                
                    
                    setColumns(columns);
                    setRows(bookingList);
                }
                else{
                    setRows([]);
                    setColumns([]);
                }
            })
        

    }, [dashboardData.monthChosen])



    // useEffect(()=>{

    //     if(rows.length>0 && columns.length>0)
    //     {
            
            
    //     }

    // },[columns, rows])


    const handleDownloadButton=()=>{
       
        handleDownload(dataRef.current, 'BookingList')
    }

    return (
        <>
            <Grid item md={12}>
                <div className="BookingBreakdown">
                    <div className="Heading">
                        <h3>Booking Breakdown</h3>
                        {columns.length>0 && rows.length>0 &&
                        <button onClick={handleDownloadButton}><img src="/images/TermDashboard/download.svg" /> 
          
                            Download
            
                        </button>
                        }

                    </div>
                 
                    <DataTable data={rows} columns={columns} pagination={true}/>

                </div>
            </Grid>
        </>

    )
}
export default Bookings;