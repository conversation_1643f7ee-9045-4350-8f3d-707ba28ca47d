import React from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Avatar from "@material-ui/core/Avatar";
import List from "@material-ui/core/List";
import ListItem from "@material-ui/core/ListItem";
import ListItemAvatar from "@material-ui/core/ListItemAvatar";
import ListItemText from "@material-ui/core/ListItemText";
import DialogTitle from "@material-ui/core/DialogTitle";
import Dialog from "@material-ui/core/Dialog";
import PersonIcon from "@material-ui/icons/Person";
import AddIcon from "@material-ui/icons/Add";
import Typography from "@material-ui/core/Typography";
import { blue } from "@material-ui/core/colors";

const ActionsList = [
  "Log",
  "Customer History",
  "My Account",
  "Questionnaire",
  "Booking History",
  "Lead History",
];
const useStyles = makeStyles({
  avatar: {
    backgroundColor: blue[100],
    color: blue[600],
  },
  callLog: {
    background: "transparent",
    borderRadius: "20px",
  },
  callList: {
    minWidth: "170px",
  },
});

const MoreActions = (props) => {
  const classes = useStyles();
  const { onClose, selectedValue, open } = props;
  return (
    <Dialog
      className={classes.callLog}
      onClose={() => props.handleClose()}
      aria-labelledby="simple-dialog-title"
      open={props.open}
    >
      {/* <DialogTitle id="simple-dialog-title">Set backup account</DialogTitle> */}
      <div className={classes.callList}>
        <List>
          {ActionsList.map((item) => (
            <ListItem button onClick={() => props.handleClose(item)} key={item}>
              {/* <ListItemAvatar>
              <Avatar className={classes.avatar}>
                <PersonIcon />
              </Avatar>
            </ListItemAvatar> */}
              <ListItemText primary={item} />
            </ListItem>
          ))}
        </List>
      </div>
    </Dialog>
  );
};

MoreActions.propTypes = {
  onClose: PropTypes.func.isRequired,
  open: PropTypes.bool.isRequired,
  selectedValue: PropTypes.string.isRequired,
};
export default MoreActions;
// export default function MoreActions() {
//   const [open, setOpen] = React.useState(false);
//   const [selectedValue, setSelectedValue] = React.useState(ActionsList[1]);

//   const handleClickOpen = () => {
//     setOpen(true);
//   };

//   const handleClose = (value) => {
//     setOpen(false);
//     setSelectedValue(value);
//   };

//   return (
//     <div>
//       <Typography variant="subtitle1">Selected: {selectedValue}</Typography>
//       <br />
//       <Button variant="outlined" color="primary" onClick={handleClickOpen}>
//         Open simple dialog
//       </Button>
//       <SimpleDialog selectedValue={selectedValue} open={open} onClose={handleClose} />
//     </div>
//   );
// }
