header {
  background: #fff;
  position: relative;
  z-index: 1200;
}

.content-panel .card {
  border-radius: 16px;
  -webkit-box-shadow: none;
          box-shadow: none;
  padding: 20px;
  position: relative;
}

.content-panel .card .MuiCardContent-root {
  padding: 0;
}

.content-panel .card:focus {
  -webkit-box-shadow: none;
          box-shadow: none;
  outline: none;
}

.faq-section .faq-scroll {
  height: 250px;
  overflow-y: auto;
}

.faq-section .faq-scroll .orderList {
  padding-left: 16px;
}

.faq-section .faq-scroll .MuiAccordion-root.Mui-expanded:last-child {
  margin-bottom: 16px;
}

.slick-slider {
  margin: 0px auto;
  position: relative;
  display: -ms-grid;
  display: grid;
  width: 100%;
}

.slick-slider .slick-slide {
  padding: 0 15px;
}

.slick-slider .slick-slide .MuiPaper-elevation1 {
  -webkit-box-shadow: none;
          box-shadow: none;
}

.slick-slider .slick-arrow {
  background: #ddd;
  display: none !important;
}

.slick-slider ul.slick-dots li {
  width: auto;
  height: auto;
}

.slick-slider ul.slick-dots li button {
  padding: 0;
  width: 10px;
  height: auto;
}

.slick-slider ul.slick-dots li button::before {
  content: "";
  background: #D5D5D5;
  width: 100%;
  height: 5px;
  opacity: 1;
}

.slick-slider ul.slick-dots li.slick-active button::before {
  opacity: 1;
  background: #EE803D;
}

button.MuiButton-root {
  height: 38px;
  line-height: 38px;
  border-radius: 24px;
  -webkit-box-shadow: none;
          box-shadow: none;
  text-transform: capitalize;
}

button.MuiButton-root:hover {
  -webkit-box-shadow: none;
          box-shadow: none;
}

button.MuiButton-root.MuiButton-containedPrimary {
  background: #0065FF;
}

button.MuiButton-root.MuiButton-containedPrimary:hover {
  background: #0059e1;
}

.MuiFormControl-root .MuiInputLabel-outlined {
  -webkit-transform: translate(20px, 18px) scale(0.9);
          transform: translate(20px, 18px) scale(0.9);
}

.MuiFormControl-root .MuiInputLabel-outlined.MuiInputLabel-shrink {
  -webkit-transform: translate(14px, -3px) scale(0.75);
          transform: translate(14px, -3px) scale(0.75);
}

.MuiFormControl-root .MuiOutlinedInput-root {
  border-radius: 25px;
}

.MuiFormControl-root .MuiOutlinedInput-root .MuiOutlinedInput-input {
  padding: 14.5px 14px;
}

.MuiFormControl-root .MuiOutlinedInput-root .MuiOutlinedInput-input.MuiOutlinedInput-inputMultiline {
  padding: 0;
}

.MuiAccordion-root .MuiCollapse-container .MuiCollapse-wrapper .MuiCollapse-wrapperInner .MuiAccordionDetails-root {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
  color: #000;
}

.MuiAccordion-root .MuiCollapse-container .MuiCollapse-wrapper .MuiCollapse-wrapperInner .MuiAccordionDetails-root p {
  margin-bottom: 15px;
  color: #000;
}

a {
  color: #0065FF;
}

:focus {
  outline: none;
}

#scrollbar::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  background-color: #f2f2f2;
}

#scrollbar::-webkit-scrollbar {
  width: 5px;
  background-color: #f2f2f2;
}

#scrollbar::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background-color: #a7a8aa;
}

.content-panel {
  color: #253858;
  font-size: 14px;
}

.content-panel .form-group {
  margin-bottom: 1rem;
}

.content-panel h1, .content-panel h2, .content-panel h3, .content-panel h4, .content-panel h5, .content-panel h6 {
  font-weight: 500;
  margin-bottom: 15px;
}

.content-panel h6 {
  font-size: 20px;
}

.content-panel h5 {
  font-size: 16px;
}

.content-panel h5 .viewall {
  float: right;
  font-size: 11px;
  text-transform: uppercase;
  cursor: pointer;
}

.content-panel h5 .viewall:hover {
  text-decoration: underline;
}

.content-panel .slick-slider {
  margin-bottom: 25px;
}

.content-panel .main-slider .slick-slider .slick-slide {
  padding: 0;
}

.content-panel .main-slider .slick-slider .slick-slide .banner {
  border-radius: 16px;
  padding: 35px 350px 35px 50px;
  height: 330px;
  position: relative;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card {
  background: none;
  color: #fff;
  padding: 0;
  border-radius: 0;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card .carousel-caption {
  margin-top: 15px;
  margin-bottom: 15px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card .carousel-caption h5 {
  font-size: 18px;
  position: relative;
  padding-bottom: 15px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card .carousel-caption h5::after {
  width: 35px;
  height: 2px;
  background: blue;
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card .carousel-caption p {
  font-size: 19px;
  line-height: 26px;
  margin-bottom: 15px;
  min-height: 120px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card .carousel-caption p span.h5 {
  color: #FFC839;
  font-size: 20px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card button.btn {
  border-radius: 24px;
  height: 48px;
  min-width: 125px;
  text-align: center;
  color: #fff;
  border: none;
  padding: 0 15px;
  min-width: 100px;
  font-size: 13px;
  background: #fff;
  font-weight: 500;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge {
  background: transparent -webkit-gradient(linear, left top, left bottom, from(#FFFFFF), color-stop(78%, #fbf0f2), to(#FCEBEE)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(180deg, #FFFFFF 0%, #fbf0f2 78%, #FCEBEE 100%) 0% 0% no-repeat padding-box;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root {
  margin-top: 25px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root .image {
  text-align: center;
  margin-right: 50px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  float: left;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root .image img {
  width: 160px;
  height: 160px;
  border-radius: 100px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root .carousel-caption {
  margin-top: 15px;
  margin-bottom: 15px;
  float: left;
  width: 65%;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root .carousel-caption .name {
  text-align: right;
  color: #EE803D;
  margin-top: 15px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner .card.ceo_msge .MuiCardContent-root .carousel-caption .name small {
  display: block;
  text-align: right;
  color: #253858;
}

.content-panel .main-slider .slick-slider .slick-slide .banner button.btn {
  margin-top: 25px;
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide1 {
  background: #6D59D4;
  background: url("/images/img-slide1.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#8AA6FC), to(#6D59D4));
  background: url("/images/img-slide1.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #8AA6FC 0%, #6D59D4 100%);
  /* W3C */
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide1 button.btn {
  background: #5855A9;
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide2 {
  background: #1B146C;
  background: url("/images/img-slide2.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#87238F), to(#1B146C));
  background: url("/images/img-slide2.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #87238F 0%, #1B146C 100%);
  /* W3C */
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide2 button.btn {
  background: #814595;
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide3 {
  background: #0187C4;
  background: url("/images/img-slide3.svg") no-repeat right 50px bottom 30px, -webkit-gradient(linear, right top, left top, from(#15C78B), to(#0187C4));
  background: url("/images/img-slide3.svg") no-repeat right 50px bottom 30px, linear-gradient(270deg, #15C78B 0%, #0187C4 100%);
  /* W3C */
}

.content-panel .main-slider .slick-slider .slick-slide .banner.slide3 button.btn {
  background: #38C6A6;
}

.content-panel .leaderboard .slick-slider {
  min-height: 300px;
  padding-bottom: 40px;
  background: #fff;
  border-radius: 16px;
  padding: 20px;
}

.content-panel .leaderboard .slick-slider .slick-slide {
  padding: 0;
}

.content-panel .leaderboard .slick-slider .slick-slide .card {
  background: none;
  border-radius: 0;
  padding: 0;
}

.content-panel .leaderboard .slick-slider .slick-slide .card .card-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  margin-bottom: 10px;
}

.content-panel .leaderboard .slick-slider .slick-slide .card .card-header img.circle-fluid {
  width: 90px;
  height: 90px;
  border-radius: 100px;
  margin-right: 10px;
}

.content-panel .leaderboard .slick-slider .slick-slide .card .card-header h1 {
  font-size: 13px;
  color: #EE803D;
  margin: 0;
  font-weight: 600;
}

.content-panel .leaderboard .slick-slider .slick-slide .card .card-header h1 small {
  color: #808080;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  font-weight: 500;
}

.content-panel .leaderboard .slick-slider .slick-slide .card .card-body {
  height: 126px;
  text-align: justify;
  overflow-y: auto;
  padding-right: 4px;
}

.content-panel .leaderboard .slick-dots {
  position: absolute;
  bottom: 25px;
}

.content-panel .rewards .slick-slider .slick-slide {
  padding: 0;
}

.content-panel .rewards .slick-slider .bg {
  border-radius: 16px;
  padding: 30px 20px;
  height: 360px;
  position: relative;
}

.content-panel .rewards .slick-slider .bg.orange {
  background: #ED7D39;
  background: url("/images/img-orange.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#FFB893), to(#ED7D39));
  background: url("/images/img-orange.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #FFB893 0%, #ED7D39 100%);
  /* W3C */
}

.content-panel .rewards .slick-slider .bg.blue {
  background: #13D0CA;
  background: url("/images/img-blue.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#A9E6E0), to(#13D0CA));
  background: url("/images/img-blue.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #A9E6E0 0%, #13D0CA 100%);
  /* W3C */
}

.content-panel .rewards .slick-slider .bg.yellow {
  background: #F1A817;
  background: url("/images/img-yellow.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#FFDD66), to(#F1A817));
  background: url("/images/img-yellow.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #FFDD66 0%, #F1A817 100%);
  /* W3C */
}

.content-panel .rewards .slick-slider .bg.green {
  background: #36B97E;
  background: url("/images/img-green.svg") no-repeat right 0px bottom 0px, -webkit-gradient(linear, right top, left top, from(#71D6A9), to(#36B97E));
  background: url("/images/img-green.svg") no-repeat right 0px bottom 0px, linear-gradient(270deg, #71D6A9 0%, #36B97E 100%);
  /* W3C */
}

.content-panel .rewards .slick-slider .bg .card {
  background: none;
  color: #fff;
  padding: 0;
  border-radius: 0;
}

.content-panel .rewards .slick-slider .bg .card .MuiTypography-body2 {
  color: #fff;
  padding-right: 100px;
}

.content-panel .rewards .slick-slider .bg .card .slide {
  min-height: 150px;
  position: relative;
  color: #fff;
}

.content-panel .rewards .slick-slider .bg .card .slide .MuiTypography-h5 {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  padding-bottom: 10px;
  margin-bottom: 25px;
}

.content-panel .rewards .slick-slider .bg .card .slide .MuiTypography-h5::after {
  content: "";
  position: absolute;
  height: 1px;
  width: 60px;
  background: #fff;
  bottom: 0;
  left: 0;
}

.content-panel .rewards .slick-slider .bg .card .slide .btn-rewards {
  color: #fff;
  text-transform: capitalize;
  position: absolute;
  bottom: 0;
}

.content-panel .rewards .slick-slider .bg .slick-slide {
  padding: 0;
}

.content-panel .rewards .slick-slider .bg .trophy {
  position: absolute;
  right: -30px;
  bottom: -15px;
}

.content-panel .rewards .slick-dots {
  position: absolute;
  bottom: 25px;
  text-align: left;
  width: auto;
  left: 20px;
}

.content-panel .rewards .slick-dots li {
  margin: 0 5px 0 0;
}

.content-panel .rewards .slick-dots li button::before {
  background: rgba(255, 255, 255, 0.5);
}

.content-panel .rewards .slick-dots li.slick-active button::before {
  background: #fff;
}

.content-panel .gallery .card {
  background: none;
  padding: 0;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image {
  background: #fff;
  padding: 15px;
  height: 360px;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image img.soon {
  margin: -15px auto auto auto;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image .media {
  height: 328px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 15px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image .media img {
  width: 100%;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image .media .overlay {
  position: absolute;
  background: rgba(255, 255, 255, 0.5);
  left: 15px;
  right: 15px;
  top: 15px;
  bottom: 15px;
  vertical-align: middle;
  display: none;
  height: 330px;
  border-radius: 8px;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image .media .overlay img {
  width: auto;
  margin: auto;
  cursor: pointer;
}

.content-panel .gallery .card .slick-slider .slick-slide .card.image .media:hover .overlay {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-transition: 0.3s;
  transition: 0.3s;
}

.content-panel .gallery .card .slick-slider .slick-slide .card p {
  font-weight: 500;
}

.content-panel .gallery .card .slick-slider .slick-arrow {
  z-index: 1;
}

.content-panel .gallery .card .slick-slider .slick-prev {
  left: 0;
}

.content-panel .gallery .card .slick-slider .slick-next {
  right: 0;
}

.content-panel .explainer_video .card {
  background: none;
  padding: 0 0 35px 0;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image {
  background: #fff;
  padding: 15px;
  height: 240px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media {
  width: 100%;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 10px;
  border-radius: 10px;
  background: #dde8fd;
  background: -webkit-gradient(linear, left top, left bottom, from(#dde8fd), color-stop(49%, white), to(white));
  background: linear-gradient(180deg, #dde8fd 0%, white 49%, white 100%);
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image img {
  width: 114px;
  border: 2px solid #fff;
  margin-right: 15px;
  margin-bottom: 10px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image strong {
  color: #EE803D;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  font-size: 13px;
  font-weight: 500;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image strong small {
  font-size: 13px;
  color: #808080;
  position: absolute;
  bottom: 15px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description {
  padding-right: 10px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description h5 {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description p {
  font-weight: 500;
  line-height: 22px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .more {
  position: absolute;
  right: 15px;
  bottom: 15px;
  font-size: 12px;
}

.content-panel .explainer_video .card .slick-slider .slick-slide .card.image .more:hover {
  text-decoration: underline;
}

.content-panel .explainer_video .card .slick-slider .slick-arrow {
  z-index: 1;
}

.content-panel .explainer_video .card .slick-slider .slick-prev {
  left: 0;
}

.content-panel .explainer_video .card .slick-slider .slick-next {
  right: 0;
}

.incentive_cal {
  height: 100%;
  max-height: 760px;
}

.incentive_cal .card {
  -webkit-box-shadow: 0px 0px 16px #3469CB29;
          box-shadow: 0px 0px 16px #3469CB29;
  background: #001458;
  color: #fff;
  border-radius: 16px;
  min-height: 300px;
  height: 92%;
}

.incentive_cal .card .ul {
  margin-top: 15px;
}

.incentive_cal .card .ul .li {
  min-height: 235px;
}

.incentive_cal .card .ul .li .slick-slider .slick-list .slick-slide .slide_img {
  text-align: center;
}

.incentive_cal .card .ul .li .slick-slider .slick-list .slick-slide .slide_img .imgDiv img {
  margin: auto;
}

.incentive_cal .card .ul .li .slick-slider .slick-list .slick-slide .slide_img label {
  margin-top: 15px;
  display: block;
}

.incentive_cal .card .ul .li form {
  background: none;
  max-width: 80%;
  width: 100%;
  text-align: center;
  margin: auto;
}

.incentive_cal .card .ul .li form div.hide {
  display: none;
}

.incentive_cal .card .ul .li form .form-group {
  margin-bottom: 1.5rem;
}

.incentive_cal .card .ul .li form .form-group label.MuiFormLabel-root {
  color: #fff;
}

.incentive_cal .card .ul .li form .form-group .MuiInput-underline .MuiInputBase-input {
  color: #fff;
}

.incentive_cal .card .ul .li form .form-group .MuiInput-underline::before {
  border-bottom: 1px solid rgba(255, 255, 255, 0.42);
}

.incentive_cal .card .ul .li form .form-group .MuiInput-underline:after {
  border-bottom: 2px solid #fff;
}

.incentive_cal .card .ul .li form button {
  width: 125px;
}

.incentive_cal .card .ul .li form button.MuiButton-root.MuiButton-containedPrimary {
  background: #F99746;
}

.incentive_cal .card .ul .li form button.MuiButton-root.MuiButton-containedPrimary:hover {
  background: #ED7D2B;
}

.incentive_cal .card .ul .li form button.MuiButton-root.MuiButton-containedSecondary {
  background: #161840;
}

.incentive_cal .card .ul .li form button.MuiButton-root.MuiButton-containedSecondary:hover {
  background: #101230;
}

.incentive_cal .card .ul .li form .needHave .heading {
  position: relative;
  margin-bottom: 10px;
}

.incentive_cal .card .ul .li form .needHave .heading span {
  text-transform: uppercase;
  font-size: 14px;
  font-weight: 500;
  position: relative;
  background: #4F527C;
  z-index: 1;
  padding: 0 5px;
}

.incentive_cal .card .ul .li form .needHave .heading:after {
  height: 1px;
  background: #fff;
  position: absolute;
  top: 8px;
  content: "";
  left: 0;
  right: 0;
}

.incentive_cal .card .ul .li form .needHave ul.list {
  list-style-type: none;
  display: table;
  width: 100%;
  border-spacing: 2px;
}

.incentive_cal .card .ul .li form .needHave ul.list li {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 10px;
  display: table-cell;
  width: 33.33%;
  text-align: left;
  font-size: 16px;
  padding-top: 30px;
}

.incentive_cal .card .ul .li form .needHave ul.list li label.text {
  display: block;
  font-size: 11px;
  margin-top: 3px;
}

.incentive_cal .card .ul .li form .needHave .amount {
  font-size: 18px;
  margin: 15px 0;
}

.incentive_cal .card .ul .li form .needHave .amount i.fa {
  font-size: 11px;
}

.incentive_cal .card .ul .li form .needHave .amount small {
  display: inline;
}

.incentive_cal .card small {
  text-align: center;
  display: block;
}

.incentive_cal .card .MuiCardContent-root {
  height: 100%;
}

.incentive_cal .card .MuiCardContent-root::-webkit-scrollbar {
  display: none;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section {
  height: 100%;
  position: relative;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .MuiAppBar-colorPrimary {
  background: #868CF1;
  border-radius: 20px;
  max-width: 100%;
  width: 100%;
  height: 40px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .MuiAppBar-colorPrimary .MuiTabs-root {
  min-height: 40px;
  position: absolute;
  max-width: 100%;
  width: 100%;
  top: 0;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .MuiAppBar-colorPrimary .MuiTabs-root .MuiTab-root {
  min-height: 40px;
  text-transform: capitalize;
  font-weight: bold;
  width: 50%;
  opacity: 1;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .MuiAppBar-colorPrimary .MuiTabs-root .MuiTab-root.MuiTab-textColorInherit.Mui-selected {
  background: #fff;
  border-radius: 20px;
  color: #273A59;
  text-transform: capitalize;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .MuiAppBar-colorPrimary .MuiTabs-root .PrivateTabIndicator-colorSecondary-71 {
  display: none;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel {
  height: 100%;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel > div {
  position: relative;
  height: 100%;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel > div .tab-panel-content {
  height: 83%;
  overflow-y: auto;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel > div .tab-panel-content::-webkit-scrollbar {
  display: none;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item {
  height: 100%;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .MuiTimelineSeparator-root, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .MuiTimelineSeparator-root {
  -webkit-box-orient: vertical;
  -webkit-box-direction: reverse;
      -ms-flex-direction: column-reverse;
          flex-direction: column-reverse;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .MuiTimelineSeparator-root .timeline-separator-dot, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .MuiTimelineSeparator-root .timeline-separator-dot {
  border: 0;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .MuiTimelineSeparator-root .timeline-separator-connector, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .MuiTimelineSeparator-root .timeline-separator-connector {
  height: 100px;
  border: 2px dashed #F99746;
  background: transparent;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item h4, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item h4 {
  color: #fff;
  font-size: 20px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item.milestone-locked, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item.milestone-locked {
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item.milestone-locked .timeline-separator-dot, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item.milestone-locked .timeline-separator-dot {
  position: relative;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item.milestone-locked .timeline-separator-dot::before, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item.milestone-locked .timeline-separator-dot::before {
  content: "";
  background: url(/images/icon-locked.svg) no-repeat center/contain;
  position: absolute;
  bottom: -6px;
  left: 50%;
  height: 20px;
  width: 20px;
  -webkit-transform: translate(-50%, 0px);
          transform: translate(-50%, 0px);
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item.milestone-achieved .timeline-separator-dot, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item.milestone-achieved .timeline-separator-dot {
  position: relative;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item.milestone-achieved .timeline-separator-dot::before, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item.milestone-achieved .timeline-separator-dot::before {
  content: "";
  background: url(/images/icon-achieved.svg) no-repeat center/contain;
  position: absolute;
  bottom: -6px;
  left: 50%;
  height: 20px;
  width: 20px;
  -webkit-transform: translate(-50%, 0px);
          transform: translate(-50%, 0px);
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0px 3px 16px #3469cb29;
          box-shadow: 0px 3px 16px #3469cb29;
  border-radius: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: horizontal;
  -webkit-box-direction: reverse;
      -ms-flex-direction: row-reverse;
          flex-direction: row-reverse;
  -ms-flex-pack: distribute;
      justify-content: space-around;
  padding: 14px 10px;
  position: relative;
  margin-left: -30px;
  margin-bottom: -47px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content img, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content img {
  max-width: 18px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content::before, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content::before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  border-left: 32px solid #fff;
  border-top: 39px solid transparent;
  border-bottom: 40px solid transparent;
  top: 50%;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  right: -31px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > img, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > img {
  position: absolute;
  right: 0;
  top: 33px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > div, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > div {
  text-align: left;
  margin-right: 17px;
  height: 60px;
  width: 100%;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > div .label, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > div .label {
  font-size: 10px;
  font-weight: bold;
  color: #253858;
  line-height: 13px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > div .label span, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > div .label span {
  font-size: 14px;
  line-height: 16px;
  display: block;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > div .value, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > div .value {
  font-size: 10px;
  font-weight: bold;
  color: #1DD1A1;
  line-height: 16px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-locked-content > div .value span, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-locked-content > div .value span {
  font-size: 11px;
  display: block;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-location-content, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-location-content {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0px 3px 16px #3469cb29;
          box-shadow: 0px 3px 16px #3469cb29;
  border-radius: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 14px 10px;
  position: relative;
  margin-left: -30px;
  margin-bottom: -15px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-location-content > div, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-location-content > div {
  text-align: right;
  margin-left: 25px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-location-content > div .label, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-location-content > div .label {
  font-size: 10px;
  font-weight: bold;
  color: #253858;
  line-height: 13px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-location-content > div .value, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-location-content > div .value {
  font-size: 12px;
  font-weight: bold;
  color: #F99746;
  line-height: 16px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content {
  background: #CBFFED;
  text-align: right;
  border-radius: 4px;
  padding: 9px 5px;
  position: relative;
  margin-left: 10px;
  margin-bottom: -15px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content::before, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content::before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  border-right: 23px solid #CBFFED;
  border-top: 22px solid transparent;
  border-bottom: 23px solid transparent;
  top: 50%;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  left: -22px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content .label, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content .label {
  font-size: 10px;
  font-weight: bold;
  color: #253858;
  line-height: 13px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content .value, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content .value {
  font-size: 12px;
  font-weight: bold;
  color: #0F8D61;
  line-height: 16px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content-left, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content-left {
  background: #CBFFED;
  text-align: left;
  border-radius: 4px;
  padding: 9px 5px;
  position: relative;
  margin-left: 10px;
  margin-bottom: -15px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content-left::before, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content-left::before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  border-left: 23px solid #CBFFED;
  border-top: 22px solid transparent;
  border-bottom: 23px solid transparent;
  top: 50%;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  right: -22px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content-left .label, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content-left .label {
  font-size: 10px;
  font-weight: bold;
  color: #253858;
  line-height: 13px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .milestones-timeline .milestones-timeline-item .milestone-achieved-content-left .value, .incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .set-goals-timeline .milestones-timeline-item .milestone-achieved-content-left .value {
  font-size: 12px;
  font-weight: bold;
  color: #0F8D61;
  line-height: 16px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 8px;
  padding: 5px;
  text-align: CENTER;
  position: absolute;
  width: 100%;
  bottom: 30px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  border: 2px dashed #ccc;
  padding: 8px;
  border-radius: 8px;
  text-align: left;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div img {
  margin-right: 12px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div p.label {
  font-size: 10px;
  line-height: 16px;
  color: #253858;
  text-transform: uppercase;
  font-weight: bold;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div p.label span {
  text-transform: capitalize;
  font-weight: 400;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div p.description {
  font-size: 12px;
  line-height: 16px;
  color: #253858;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .disclaimer > div p.description span {
  font-size: 14px;
  font-weight: bold;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .note {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 8px;
  padding: 16px 0;
  text-align: CENTER;
  position: absolute;
  width: 100%;
  bottom: 30px;
}

.incentive_cal .card .MuiCardContent-root .incentive-calculator-section .tab-panel .note p {
  font-size: 14px;
  line-height: 19px;
  color: #253858;
  max-width: 200px;
  margin: 0 auto;
}

.MuiTimelineItem-alignAlternate:nth-child(even) .MuiTimelineItem-content .milestone-achieved-content {
  background: #CBFFED;
  text-align: left !important;
  border-radius: 4px;
  padding: 9px 5px;
  position: relative;
  margin-left: 10px;
  margin-bottom: -15px;
}

.MuiTimelineItem-alignAlternate:nth-child(even) .MuiTimelineItem-content .milestone-achieved-content::before {
  position: absolute;
  content: "";
  width: 0px;
  height: 0px;
  border-left: 23px solid #CBFFED !important;
  border-right: unset !important;
  border-top: 22px solid transparent;
  border-bottom: 23px solid transparent;
  top: 50%;
  -webkit-transform: translate(0, -50%);
          transform: translate(0, -50%);
  right: -22px !important;
  left: unset !important;
}

.MuiTimelineItem-alignAlternate:nth-child(even) .MuiTimelineItem-content .milestone-achieved-content .label {
  font-size: 10px;
  font-weight: bold;
  color: #253858;
  line-height: 13px;
}

.MuiTimelineItem-alignAlternate:nth-child(even) .MuiTimelineItem-content .milestone-achieved-content .value {
  font-size: 12px;
  font-weight: bold;
  color: #0F8D61;
  line-height: 16px;
}

.ReactModalPortal {
  z-index: 5100;
  position: relative;
}

@media all and (max-width: 768px) {
  .content-panel .main-slider .slick-slider .slick-slide .card {
    min-height: 244px;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.reward {
    padding: 25px;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.reward img {
    display: none;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.reward button.btn {
    margin-top: 0;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card .carousel-caption p {
    font-size: 13px;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.ceo_msge .MuiCardContent-root {
    margin-top: 0;
  }
  .content-panel .leaderboard .slick-slider {
    min-height: 240px;
  }
  .content-panel .gallery .card .slick-slider .slick-slide .card p {
    font-size: 13px;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image {
    min-height: 270px;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image img {
    width: 80px;
    float: left;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image strong {
    display: -ms-grid;
    display: grid;
    width: auto;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .image strong small {
    clear: both;
    display: table-caption;
    position: relative;
    bottom: inherit;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description {
    padding-right: 0;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description h5 {
    font-size: 14px;
    line-height: 20px;
    margin-bottom: 10px;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .media .description p {
    line-height: inherit;
    font-size: 13px;
  }
  .content-panel .explainer_video .card .slick-slider .slick-slide .card.image .more {
    position: inherit;
    right: inherit;
    bottom: inherit;
    font-size: 12px;
    float: right;
  }
}

@media all and (max-width: 600px) {
  .content-panel .main-slider .slick-slider .slick-slide .banner {
    padding: 15px;
  }
  .content-panel .main-slider .slick-slider .slick-slide .banner.slide1 {
    background-size: contain;
  }
  .content-panel .main-slider .slick-slider .slick-slide .banner.slide2 {
    background-size: contain;
  }
  .content-panel .main-slider .slick-slider .slick-slide .banner.slide3 {
    background: -webkit-gradient(linear, right top, left top, from(#15C78B), to(#0187C4));
    background: linear-gradient(270deg, #15C78B 0%, #0187C4 100%);
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.ceo_msge .MuiCardContent-root .image {
    display: none;
  }
  .content-panel .main-slider .slick-slider .slick-slide .card.ceo_msge .MuiCardContent-root .carousel-caption {
    width: 100%;
  }
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth {
  border-radius: 8px;
  max-width: 672px !important;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content {
  background: transparent linear-gradient(302deg, #424770 0%, #696694 100%) 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0px 3px 16px #86868629;
          box-shadow: 0px 3px 16px #86868629;
  border-radius: 8px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form p {
  color: #FFFFFF;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 24px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth {
  margin-bottom: 24px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth .MuiInputLabel-formControl {
  color: #fff;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth .MuiInputBase-root {
  color: #fff;
  font-size: 24px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth .MuiInputBase-root.MuiInput-underline:before, .set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth .MuiInputBase-root.MuiInput-underline:after {
  border-bottom-color: #fff;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-form .form .form-group .MuiFormControl-fullWidth .MuiInputBase-root.MuiInput-underline:hover:not(.Mui-disabled):before {
  border-bottom-color: #fff;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper {
  background: #2a2c57;
  border-radius: 8px;
  padding: 3px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper .required-run-rate-content {
  border: 0.800000011920929px dashed #5D73E5;
  border-radius: 8px;
  padding: 8px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper .required-run-rate-content > div {
  margin-left: 10px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper .required-run-rate-content > div .heading {
  color: #F9BB46;
  text-transform: uppercase;
  font-size: 10px;
  line-height: 16px;
  font-weight: 900;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper .required-run-rate-content > div .description {
  color: #fff;
  font-size: 12px;
  line-height: 16px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-run-rate-wrapper .required-run-rate-content > div .description span {
  color: #F9BB46;
  font-size: 14px;
  font-weight: bold;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper {
  margin: 23px auto 25px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper > p {
  font-size: 12px;
  line-height: 16px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 16px;
  text-align: center;
  text-transform: uppercase;
  position: relative;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper > p::before {
  content: "";
  background: #fff;
  height: 1px;
  width: 27%;
  position: absolute;
  left: 0;
  top: 50%;
  -webkit-transform: translate(0, 50%);
          transform: translate(0, 50%);
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper > p:after {
  content: "";
  background: #fff;
  height: 1px;
  width: 27%;
  position: absolute;
  right: 0;
  top: 50%;
  -webkit-transform: translate(0, 50%);
          transform: translate(0, 50%);
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper .required-details-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper .required-details-content .details {
  background: #6f709b;
  border-radius: 8px;
  width: 100%;
  padding: 16px 0px 26px 8px;
  max-width: 104px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper .required-details-content .details p.heading {
  font-size: 10px;
  line-height: 13px;
  color: #fff;
  font-weight: bold;
  margin-bottom: 4px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .required-details-wrapper .required-details-content .details .description {
  font-size: 16px;
  line-height: 23px;
  color: #fff;
  font-weight: 500;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .set-goal-as-wrapper {
  background: #2a2c57;
  border-radius: 8px;
  padding: 9px 14px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .set-goal-as-wrapper .label {
  font-size: 14px;
  line-height: 19px;
  color: #fff;
  font-weight: 500;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .set-goal-as-wrapper .goal {
  font-size: 24px;
  line-height: 32px;
  color: #fff;
  font-weight: 400;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-details .set-goal-as-wrapper .goal span {
  font-size: 16px;
  line-height: 21px;
  opacity: 0.6;
  text-decoration: line-through;
  display: inherit;
  text-align: right;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-buttons {
  text-align: center;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-buttons .disclaimer {
  font-size: 14px;
  line-height: 19px;
  color: #fff;
  font-weight: 400;
  margin-bottom: 14px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-buttons .buttons .set-goal {
  background: #F99746 0% 0% no-repeat padding-box;
  border-radius: 8px;
  color: #fff;
  font-size: 14px;
  font-weight: bold;
  max-width: 158px;
  width: 100%;
  margin-right: 8px;
}

.set-goal-popup-wrapper .MuiDialog-paperFullWidth .set-goal-popup-content .set-goal-popup-content-text .set-goal-container .set-goal-buttons .buttons .cancel {
  border: 1px solid #F99746;
  font-weight: bold;
  border-radius: 8px;
  color: #F99746;
  max-width: 158px;
  width: 100%;
  margin-right: 8px;
}

.MuiTabs-indicator {
  display: none !important;
}
/*# sourceMappingURL=main.css.map */