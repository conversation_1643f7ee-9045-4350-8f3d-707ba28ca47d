import React, { useState } from "react";
import Drawer from '@mui/material/Drawer';
import { Tooltip, Zoom } from '@material-ui/core';
import { withStyles } from "@material-ui/core/styles";
import CloseIcon from '@material-ui/icons/Close';
import '../../jag2023/scss/_calculationDetails.scss'
import moment from 'moment';


const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    color: "#0164ff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);

const ToolTipFactory = ({ renderTooltip,color }) => {
  if(color)
  {
    return (<WrapperTooltip
      disableFocusListener
      TransitionComponent={Zoom}
      placement="top"
      arrow
      title={renderTooltip()}
      enterTouchDelay={0}
      
    // classes={{ tooltip: classes.customWidth }}
    >
      <i className="fa fa-info-circle" style={{color:"red"}}></i>
    </WrapperTooltip>
    )
  }
  else{
  return (<WrapperTooltip
    disableFocusListener
    TransitionComponent={Zoom}
    placement="top"
    arrow
    title={renderTooltip()}
    enterTouchDelay={0}
  // classes={{ tooltip: classes.customWidth }}
  >
    <i className="fa fa-info-circle"></i>
  </WrapperTooltip>
  )
  }

}

const renderTooltipTotalAPE = (agentData) => {
  const { ProductId, BUId } = agentData || {};
  return (
    <p>
      {![65, 67].includes(BUId) && <><p>Projected Sourced APE = (Primary + FOS) Sourced APE * Business Factor</p> <br/><p>**Business Factor = (FY 23-24 APE)/(FY 23-24 APE till Date Last Year)</p></>}
      {[65, 67].includes(BUId) && <><p>Projected Sourced BKGS = (Primary + FOS) Sourced BKGS * Business Factor</p> <br/><p>**Business Factor = (FY 23-24 BKGS)/(FY 23-24 BKGS till Date Last Year)</p></>}
    </p>
  )
}

const renderTooltipProjectedApe = (agentData) => {
  const { DaysDiff, ProductId } = agentData || {};
  return (
    <>
      <div>
        <p>Total days in FY 24-25: 365</p><br />
        <p>No. of days from 1st April 2023 till yesterday:  {DaysDiff}</p><br />
        {
          ![117, 217].includes(ProductId) && <>
            <p>Projected Sourced APE (FY 24-25) =  </p><br />
            <p>(Sourced APE till date) x (365/ {DaysDiff})</p>
          </>
        }
        {
          [117, 217].includes(ProductId) && <>
            <p>Projected Sourced Bookings (FY 24-25) =  </p><br />
            <p>(Sourced Bookings till date) x (365/ {DaysDiff})</p>
          </>
        }
      </div>
    </>
  );
}

const renderTooltipIssuance = (agentData) => {
  const { ProductId, BUId } = agentData || {};
  return (
    <>
      {
        ![65, 67].includes(BUId) && <>
          {/* <p> Issued APE From {moment('2024-04-01').format('D MMMM YYYY')} Till {moment(new Date(new Date().setDate(new Date().getDate()-45))).format('D MMMM YYYY')} &nbsp; (I)</p><br />
          <p> Sourced APE From {moment('2024-04-01').format('D MMMM YYYY')} Till {moment(new Date(new Date().setDate(new Date().getDate()-45))).format('D MMMM YYYY')} (S)</p><br /> */}

          <p> Issued APE From {moment('2024-04-01').format('D MMMM YYYY')} Till Date&nbsp;(I)</p><br />
          <p> Sourced APE from 1st April 2024 till 31st March 2025 (S)</p><br />
        </>
      }
      {
        [65, 67].includes(ProductId) && <>
          {/* <p> Issued Bookings From {moment('2024-04-01').format('D MMMM YYYY')} Till {moment(new Date(new Date().setDate(new Date().getDate()-45))).format('D MMMM YYYY')} &nbsp; (I)</p><br />
          <p> Sourced Bookings From {moment('2024-04-01').format('D MMMM YYYY')} Till {moment(new Date(new Date().setDate(new Date().getDate()-45))).format('D MMMM YYYY')} (S)</p><br /> */}
          <p> Issued Bookings From {moment('2024-04-01').format('D MMMM YYYY')} Till Date &nbsp; (I)</p><br />
          <p> Total Sourced Bookings (S)</p><br />
        </>
      }
      <p> %Issuance = 100% x ( I / S )</p>
    </>
  );
}

const renderTooltipEligibleCashReward = (agentData, slabforTarget) => {
  let growthTarget = slabforTarget.Target * 100;
  return (
    growthTarget && <p> Your growth should be above {growthTarget}% to be eligible for cash rewards.</p> || <></>
  );
}

const renderTooltipCashReward = (agentData, slabforTarget) => {
  const {ProductId, BUId } = agentData || {};
  let cashReward;
  if([65, 67].includes(BUId)) {
    cashReward = slabforTarget.CashReward;
  } else {
    cashReward = slabforTarget.CashReward * 100;
  }
  return (
    <>
    { ![65, 67].includes(BUId) && <> <p> Cash Rewards = ({cashReward}%) * (Issued APE FY' 25) </p><br/><p>**Cash Rewards are subject to a limit</p></>}
    { [65, 67].includes(BUId) && <><p> Cash Rewards = ({cashReward}) * (Issued Bookings FY' 25) </p><br/><p>**Cash Rewards are subject to a limit</p></>}
    </>
  );
}

const renderTooltipProjectedIssuedApe = (agentData) => {
  const { ProductId,BUId } = agentData || {};

  return (
    <>
      {
        ![65, 67].includes(BUId) && <p>Total Issued APE = Total Sourced APE * %Issuance</p>
      }
      {
        [65, 67].includes(BUId) && <p>Total Issued BKG = Total Sourced BKG * %Issuance </p>
      }
    </>
  );
}

const renderToolTipTillDate = (agentData) => {
  const { ProductId,BUId } = agentData || {};

  return (
    <>
     {moment('2024-04-01').format('D MMM YYYY')} to {moment('2025-03-31').format('DD MMM YYYY')}
     {/* {moment('2024-04-01').format('D MMM YYYY')} to {moment(new Date(new Date().setDate(new Date().getDate()-1))).format('D MMM YYYY')} */}
    </>
  );
}

const renderTooltipGrowth = (agentData) => {
  const { DaysDiff, ProductId } = agentData || {};
  return (
    <>
      <div>
        <p>To Calculate Growth only, Issued APE from April till Feb will be considered for this year and last year. However to calculate Cashrewards, APE From Apr'23 till Mar'24 will be considered.</p>
    
      </div>
    </>
  );
}

const renderTooltipTarget = (agentData, slabforTarget) => {
  let minApe = slabforTarget && slabforTarget.MinValue || '';
  const { ProductId } = agentData || {};
  
  return (<>
    {
      ![117, 217].includes(ProductId) && <p>Minium APE for Lottery Tickets: ( {parseInt(minApe).toLocaleString('en-IN')} )</p>
    }
    {
      [117, 217].includes(ProductId) && <p>Minium Bookings for Lottery Tickets: ( {parseInt(minApe).toLocaleString('en-IN')} )</p>
    }
  </>
  );
}

const CalculationDetails = (props) => {
  const { agentData, slabforTarget } = props;

  const {
    TotalAPE_CurrYear,
    TotalBKG_CurrYear,
    SecondaryAPE,
    SecondaryBKG,
    SecondarySourceBkgs,
    ULIP,
    TRAD,
    ProjectedIssuedAPE_CurrYear,
    ProjectedIssuedBKG_CurrYear,
    IssuedAPE_PrevYear,
    IssuedBKG_PrevYear,
    ActualG1,
    CashRewards,
    LotteryTicket,
    ProjectedAPE_CurrYear,
    ProjectedBKG_CurrYear,
    Issuance,
    ProductId,
    BUId,
    MemberType

  } = agentData || {};


  const toggleDrawer = (e) => {
    props.HandleOpenCalculation(e);
  }

  const handleGrowthFuction=(agentData)=>{
    if([115].includes(agentData.ProductId) && [52,57].includes(agentData.BUId) && ['gold','silver'].includes(agentData.MemberType.toLowerCase()))
    {
      return(
        <>
        <>{"%Growth w.r.t LY Issued APE"}&nbsp;</>
        <ToolTipFactory renderTooltip={() => renderTooltipGrowth(agentData)} color={true} />
        </>
      )
    }
    else if([65,67].includes(agentData.BUId))
    {
      return(
      <>{"%Growth w.r.t LY Issued BKGS"}&nbsp;</> 
      )
    }
    else{
      return(
        <>{"%Growth w.r.t LY Issued APE"}&nbsp;</>
      )
    }
  }

  return (
    <div>
      <Drawer
        anchor={'right calculation-modal'}
        open={true}
        onClose={(e) => toggleDrawer(e)}
      >
        <div className='CalculationDeatilsPopup'>
          <h5>See how Calculation Works</h5>
          <div className='close'>
            <CloseIcon onClick={toggleDrawer} />
          </div>
          <div className="DetailsBox">
            <h3>Cash Rewards Calculation Details</h3>
            <img src="/images/jag2023/CoinIcon.svg" width="65" />
            <ul>
            <li>
                {![65, 67].includes(BUId) && <>{"Primary Sourced APE (FY 24-25)"}&nbsp;</>}
                {[65, 67].includes(BUId) && <>{"Primary Sourced BKGS (FY 24-25)"}&nbsp;</>}
                <ToolTipFactory renderTooltip={() => renderToolTipTillDate(agentData)} />
              </li>

              <li>
              {![65, 67].includes(BUId) && <> {parseInt(TotalAPE_CurrYear?TotalAPE_CurrYear:0).toLocaleString('en-IN') || '-'}</>}
              {[65, 67].includes(BUId) && <> {parseInt(TotalBKG_CurrYear?TotalBKG_CurrYear:0).toLocaleString('en-IN') || '-'}</>}
              </li>


              <li>
                {![65, 67].includes(BUId) && <>{"FOS Sourced APE (FY 24-25)"}&nbsp;</>}
                {[65, 67].includes(BUId) && <>{"FOS Sourced BKGS (FY 24-25)"}&nbsp;</>}
                <ToolTipFactory renderTooltip={() => renderToolTipTillDate(agentData)} />
              </li>

              <li>
              {![65, 67].includes(BUId) && <> {parseInt( SecondaryAPE?SecondaryAPE:0).toLocaleString('en-IN') || '-'}</>}
              {[65, 67].includes(BUId) && <> {parseInt( SecondarySourceBkgs?SecondarySourceBkgs:0).toLocaleString('en-IN') || '-'}</>}
              </li>

              
              <li>
                {![65, 67].includes(BUId) && <>{"Total Sourced APE (FY 24-25)"}&nbsp;</>}
                {[65, 67].includes(BUId) && <>{"Total Sourced BKGS (FY 24-25)"}&nbsp;</>}
                {/* <ToolTipFactory renderTooltip={() => renderTooltipTotalAPE(agentData)} /> */}
              </li>

              <li>
              {![65, 67].includes(BUId) && <> {parseInt(ProjectedAPE_CurrYear?ProjectedAPE_CurrYear:0).toLocaleString('en-IN') || '-'}</>}
              {[65, 67].includes(BUId) && <> {parseInt(ProjectedBKG_CurrYear?ProjectedBKG_CurrYear:0).toLocaleString('en-IN') || '-'}</>}
              </li>

              {/* <li>
                {![117, 217].includes(ProductId) && <>{"Projected APE FY 24-25"}&nbsp;</>}
                {[117, 217].includes(ProductId) && <>{"Projected BKGS FY 24-25"}&nbsp;</>}
                <ToolTipFactory renderTooltip={() => renderTooltipProjectedApe(agentData)} />
              </li>
              <li>
              {![117, 217].includes(ProductId) && <> {parseInt(ProjectedAPE_CurrYear).toLocaleString('en-IN') || '-'}</>}
              {[117, 217].includes(ProductId) && <> {parseInt(ProjectedBKG_CurrYear).toLocaleString('en-IN') || '-'}</>}
               
              </li> */}
              <li>
                % Issuance &nbsp;
                <ToolTipFactory renderTooltip={() => renderTooltipIssuance(agentData)} />
              </li>
              <li>
                {(Issuance && (Issuance * 100) + '%') || "NA"}
              </li>
              <li>
                {![65, 67].includes(BUId) && <>Total Issued APE <span>(FY 24-25)</span>&nbsp;</>}
                {[65, 67].includes(BUId) && <>Total Issued Bookings<span>(FY 24-25)</span>&nbsp;</>}
                <ToolTipFactory renderTooltip={() => renderTooltipProjectedIssuedApe(agentData)} />

              </li>
              {![65, 67].includes(BUId) && <li>{ProjectedIssuedAPE_CurrYear && parseInt(ProjectedIssuedAPE_CurrYear).toLocaleString('en-IN') || 'NA'}</li>}
              {[65, 67].includes(BUId) && <li>{ProjectedIssuedAPE_CurrYear && parseInt(ProjectedIssuedAPE_CurrYear).toLocaleString('en-IN') || 'NA'}</li>}

              <div className="FinalDetails">
                <ul>

                  <li>
                    {![65, 67].includes(BUId) && <>{"Last Year Issued APE FY'24"}&nbsp;</>}
                    {[65, 67].includes(BUId) && <>{" Last Year Issued BKG FY'24"}&nbsp;</>}
                  </li>

                 
                  {![65, 67].includes(BUId) &&  <li>{IssuedAPE_PrevYear && parseInt(IssuedAPE_PrevYear + ULIP).toLocaleString('en-IN') || 'NA'}</li>}
                  {[65, 67].includes(BUId) &&  <li>{IssuedBKG_PrevYear && parseInt(IssuedBKG_PrevYear + TRAD).toLocaleString('en-IN') || 'NA'}</li>}
                  <li>

                    {/* {![117, 217,115].includes(ProductId) &&  <>{"%Growth w.r.t LY Issued APE"}&nbsp;</>}
                    {[117, 217].includes(ProductId) && ![115].includes(ProductId) &&  <>{"%Growth w.r.t LY Issued BKGS"}&nbsp;</>}
                    {[[115].includes(ProductId)] && ![52,57].includes(BUId) && <>{"%Growth w.r.t LY Issued APE"}&nbsp;</>}
                    {[[115].includes(ProductId)] && [52,57].includes(BUId) && &&  <>{"%Growth w.r.t LY Issued APE"}&nbsp;</>} */}
                    {handleGrowthFuction(agentData)}

                  </li>
                  <li>{ActualG1}%</li>
                  <li>
                    Eligible for Cash Rewards &nbsp;
                    <ToolTipFactory renderTooltip={() => renderTooltipEligibleCashReward(agentData, slabforTarget)} />
                  </li>
                  <li>{CashRewards > 0 ? "Yes" : "No"}</li>
                  <li>Cash rewards &nbsp;
                  <ToolTipFactory renderTooltip={() => renderTooltipCashReward(agentData, slabforTarget)} />
                  </li>
                  <li>₹{CashRewards && parseInt(CashRewards).toLocaleString('en-IN') || '0'}</li>
                </ul>
              </div>
            </ul>
            <p>Last Updated on {moment().format('DD MMM YYYY 00:00:00')}</p>
          </div>
          <div className="DetailsBox">
            <h3>Lottery ticket Calculation</h3>
            <img src="/images/jag2023/TicketIcon.svg" />
            <ul>
              <li>
                {![65, 67].includes(BUId) && <>{"Total Issued APE FY 24-25"}&nbsp;</>}
                {[65, 67].includes(BUId) && <>{"Total Issued BKG FY 24-25"}&nbsp;</>}
                <ToolTipFactory renderTooltip={() => renderTooltipProjectedIssuedApe(agentData)} />
              </li>
             
                  {![65, 67].includes(BUId) &&  <li>₹{ProjectedIssuedAPE_CurrYear && parseInt(ProjectedIssuedAPE_CurrYear).toLocaleString('en-IN') || 'NA'} </li>}
                  {[65, 67].includes(BUId) &&   <li>{ProjectedIssuedAPE_CurrYear && parseInt(ProjectedIssuedAPE_CurrYear).toLocaleString('en-IN') || 'NA'} </li>}
              <li>
                {![65, 67].includes(BUId) && <>{"Minimum Issued APE for Lottery Ticket"}&nbsp;</>}
                {[65, 67].includes(BUId) && <>{"Minimum Issued Bkg for Lottery Ticket"}&nbsp;</>}
                {/* <ToolTipFactory renderTooltip={() => renderTooltipTarget(agentData, slabforTarget)} /> */}
              </li>
             
                {![65, 67].includes(BUId) &&  <li>₹{slabforTarget && parseInt(slabforTarget.MinValue).toLocaleString('en-IN') || 'NA'}</li>}
                {[65, 67].includes(BUId) &&  <li>{slabforTarget && parseInt(slabforTarget.MinValue).toLocaleString('en-IN') || 'NA'}</li>}
              <li>
                Eligible for Ticket
              </li>
              <li>{LotteryTicket > 0 ? "Yes" : "No"}</li>

              <li>LotteryTicket</li>

              <li>
                {LotteryTicket || 0}
              </li>
            </ul>
            <p>Last Updated on {moment().format('DD MMM YYYY')}</p>
          </div>
        </div>
      </Drawer>
    </div>
  );
};

export default CalculationDetails;

