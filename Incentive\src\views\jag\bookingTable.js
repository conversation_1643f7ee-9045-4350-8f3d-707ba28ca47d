import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
//import CssBaseline from "@material-ui/core/CssBaseline";
//import Pagination from "material-ui-flat-pagination";
//import DialogReson from "../../components/Dialogs/NewQuestion";
import Tooltip from '@material-ui/core/Tooltip';
//import Button from '@material-ui/core/Button';
import Zoom from '@material-ui/core/Zoom';
import VisibilityIcon from '@material-ui/icons/Visibility';
import ClickAwayListener from '@material-ui/core/ClickAwayListener';

import Button from '@material-ui/core/Button';
import { Chart } from "react-google-charts";
import moment from "moment";
const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));


const BookingTable = (props) => {

  const classes = useStyles();
  const { userId, date, agentDetail } = props;
  const [AgentsMonthlyData, setAgentsMonthlyData] = useState([]);


  useEffect(() => {
    setAgentsMonthlyData([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`jag/GetAgentsMonthlyData/${userId}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          setAgentsMonthlyData(JSON.parse(response.Response));
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [props]);

  const renderChartdata = (AgentsMonthlyData) => {
    let data = []
    let label = 'FY 19-20';
    if ([131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1) {
      label = 'Monthly base'
    }
    data.push(['Month',
      { label: label, type: "number" },
      { label: 'ISSUED', type: "number" },
      { label: 'PROJECTED', type: "number" }]);


    AgentsMonthlyData.forEach(element => {

      let monthyear = new Date(element.ProjectionYear, element.ProjectionMonth - 1, 1);
      if (['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(agentDetail.BU) == -1) {
        let IssuedAPE_PrevYear = element.ProratedAPE == null ? element.IssuedAPE_PrevYear : element.ProratedAPE;

        data.push([moment(monthyear).format('MMM'), IssuedAPE_PrevYear, element.IssuedAPE_CurrYear, element.ProjectedAPE_CurrYear]);
        //data.push([moment(monthyear).format('MMM YYYY'), element.IssuedAPE_PrevYear, null, element.ProjectedAPE_CurrYear]);
      }
      else {
        let IssuedBKG_PrevYear = element.ProratedBKGS == null ? element.IssuedBKG_PrevYear : element.ProratedBKGS;
        data.push([moment(monthyear).format('MMM'), IssuedBKG_PrevYear, element.IssuedBKG_CurrYear, element.ProjectedBKG_CurrYear]);

      }
    });

    return data;
  }
  let msg = <>
    <span>* Projected Issued bookings is a sum of the issued bookings for the months whose incentives are paid and Projected Bookings for the rest.</span>
    <br />
    <span>* Projections are made using the current bookings growth and issuance in the same month of previous financial year. </span>
  </>;
  if ([131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1) {
    msg = <>
      <span>* Projected Issued APE is a sum of the issued APE for the months whose issuance is mature and Projected APE for the rest.</span>
      <br />
      <span>* Projections are made using the current APE growth vs your base target. </span>
    </>
  }
  else if (['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(agentDetail && agentDetail.BU) == -1) {
    msg = <>
      <span>* Projected Issued APE is a sum of the issued APE for the months whose incentives are paid and Projected APE for the rest.</span>
      <br />
      <span>* Projections are made using the current APE growth and issuance in the same month of previous financial year. </span>
    </>
  }


  return AgentsMonthlyData && AgentsMonthlyData.length > 0 && (
    <Fragment>

      <div className="pading-zero" className={classes.root}>
        <div className="booking-table">

          <Chart
            chartType="ColumnChart"
            data={renderChartdata(AgentsMonthlyData)}
            loader={<div>Loading Chart</div>}
            width="100%"
            height="300px"
            options={{
              is3D: false,
              colors: ['#6457A6', '#51DEA2', '#FF6978'],
              chartArea: { width: '80%' },
              hAxis: {
                minValue: 0,
                textStyle: {
                  color: '#808080',
                  fontName: 'Roboto,Regular',
                  fontSize: '10'
                }
              },
              vAxis: {
                minValue: 0,
                textStyle: {
                  color: '#808080',
                  fontName: 'Roboto,Regular',
                  fontSize: '10'
                }
              },
              bar: { groupWidth: '30%' },
              legend: {
                position: 'top',
                alignment: 'end',
                textStyle: {
                  color: '#808080',
                  fontName: 'Roboto,Regular',
                  fontSize: '10'

                }
              },
              animation: {
                startup: true,
                easing: 'linear',
                duration: 700,
              },
              isStacked: false
            }}
            legendToggle

          />

        </div>
      </div>
      {msg}
    </Fragment>
  );
};

export default BookingTable;