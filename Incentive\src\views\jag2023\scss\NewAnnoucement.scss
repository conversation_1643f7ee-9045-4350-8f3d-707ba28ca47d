.NewAnnoucementPopup {

    .MuiDialog-paper {
        width: 447px;
        text-align: center;
         border-radius: 16px;  
         img{
            margin: 10px;
         } 
        h3 {
            color: #000;
            text-align: center;
            font-family: <PERSON>pins;
            font-size: 24px;
            font-style: normal;
            font-weight: 600;
            line-height: normal;
            margin: 10px 0px;
        }

        ul {
            width: 290px;
            height: 72px;
            margin: auto;
            text-align: left;

            li {
                overflow: hidden;
                color: #253858;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-family: Poppins;
                font-size: 14px;
                font-style: normal;
                font-weight: 400;
                line-height: normal;

                &::before {
                    content: "\2022";
                    /* Use the Unicode bullet character */
                    color: #253858;
                    /* Set the color of the marker */
                    font-size: 1.4em;
                    /* Adjust the size of the marker */
                    margin-right: 8px;
                    /* Add spacing between the marker and content */
                    line-height: 20px;
                }
            }
        }

        button {
            border-radius: 4px;
            background: #0065FF;
            color: #FFF;
            font-family: <PERSON><PERSON>s;
            font-size: 16px;
            font-style: normal;
            font-weight: 500;
            height: 50px;
            width: 190px;
            margin-bottom: 15px;
        }
    }
}
@media screen and (max-width: 750px) {
    .NewAnnoucementPopup {
        .MuiDialog-paper {
            width: 100%;
            text-align: center;
            border-radius: 16px !important;
            margin: 8px !important;
        }
        .MuiDialog-scrollPaper{
            align-items: center !important;
        }
}
}