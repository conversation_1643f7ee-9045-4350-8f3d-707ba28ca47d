const path = require('path');
const hwp = require('html-webpack-plugin');
const webpack = require('webpack');
require('dotenv').config();
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const CleanWebpackPlugin = require('clean-webpack-plugin');
const CopyWebpackPlugin = require('copy-webpack-plugin');

module.exports = {
  mode: 'production',

    entry: path.join(__dirname, '/src/index.js'),
  output: {
    filename: 'js/build.[contenthash].js',
    path: path.join(__dirname, '/dist'),
    publicPath: "/"
  },
  module: {
    rules: [{
      exclude: /node_modules/,
      test: /\.(js|jsx|mjs)$/,
      loader: 'babel-loader'
    },
    {
      test: /\.mjs$/,
      include: /node_modules/,
      type: 'javascript/auto', // This allows Webpack to process .mjs files as JS
    },
    {
      test: /\.(sa|sc|c)ss$/,
      use: [
        {
          loader: MiniCssExtractPlugin.loader,
        },
        'css-loader',
        'postcss-loader',
        'sass-loader',
      ],
    },
    {
      test: /.(ttf|otf|eot|svg|woff(2)?)(\?[a-z0-9]+)?$/,
      use: [{
        loader: 'file-loader',
        options: {
          name: '[name].[ext]',
          outputPath: 'fonts/'
        }
      }]
    }]
  },
  resolve: {
    extensions: ['.js', '.jsx', '.scss', '.css', '.mjs','.json'],
  },

  plugins: [
    new webpack.DefinePlugin({
      "API_BASE_URL": JSON.stringify(process.env.API_BASE_URL),
      "TOKEN_KEY": JSON.stringify(process.env.TOKEN_KEY),
      "COOKIE_LOGIN": JSON.stringify(process.env.COOKIE_LOGIN)
    }),

    new CleanWebpackPlugin(),
    new MiniCssExtractPlugin({
      // Options similar to the same options in webpackOptions.output
      // both options are optional
      path: path.join(__dirname, '/dist'),

      filename: 'css/[name].[contenthash].css',
      chunkFilename: 'css/[id].[contenthash].css',
    }),
    new hwp({ template: path.join(__dirname, '/public/index.html') }),
    new CopyWebpackPlugin([{ from: 'public/images', to: 'images' }
  ]),


  ]
}
