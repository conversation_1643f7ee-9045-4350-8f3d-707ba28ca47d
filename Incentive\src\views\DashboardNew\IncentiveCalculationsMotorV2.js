import React from "react";
import { withStyles, makeStyles } from "@material-ui/styles";

import _ from "lodash";
import {
  Card,
} from "@material-ui/core";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(0),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  content: {
    paddingTop: 150,
    textAlign: "center",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#fff",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    height: "70px",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#303030",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
    "& svg": {
      background: " #c8dcfa 0% 0% no-repeat padding-box",
      borderRadius: "50%",
      padding: "8px",
      width: "35px",
      height: "35px",
      color: "#0052cc",
    },
    "& ul": {
      display: "table",
      width: "100%",
      "& li": {
        display: "table-cell",
        width: "auto",
      },
    },
  },
  expandIcon: {
    background: "#00398e",
    color: "#fff",
    borderRadius: "50%",
  },
  warningBtn: {
    background: "#c8dcfa",
    borderRadius: "17px",
    margin: "5px 0",
    fontSize: "12px",
    color: "#0052cc",
    "&:hover": {
      background: "#c8dcfa",
    },
  },
}));


const IncentiveCalculationsMotorV2 = (props) => {
  const classes = useStyles();
  const { MotorCalculationsV2 } = props;
  const IncentiveData = MotorCalculationsV2 && Array.isArray(MotorCalculationsV2) && MotorCalculationsV2.length > 0 &&
    MotorCalculationsV2[0].Status && MotorCalculationsV2[0].Response != "[]" && JSON.parse(MotorCalculationsV2[0].Response) || [];

  return (
    <div 
    //className={classes.root} 
    className = "incentive"
    >
      <Card className={classes.card}>
        <ul className="incentive-box table-incentive">
          <h6>
            Incentive Calculations
          </h6>
          
          <li>
            <table className="motorIncentive">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>TP</th>
                  <th>FosTP</th>
                  <th>Com</th>
                  <th>FosCom</th>
                  <th>Total Eligible <br/>Bookings</th>
                </tr>
              </thead>
              <tbody>
                  <tr>
                    <td>{'Bookings'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].TP) ? IncentiveData[0].TP:'-' }</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].FosTP) ? IncentiveData[0].FosTP: '-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].Com) ? IncentiveData[0].Com : '-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].FosCom) ? IncentiveData[0].FosCom : '-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].TotalBookings) ? IncentiveData[0].TotalBookings:'-'}</td>
                  </tr>

                <tr>
                  <td>&nbsp;</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>

                <tr>
                  <th>Multipliers Used</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th>Total Incentive before <br/>salary deduction</th>
                </tr>

                <tr>
                    <td>{'Per Booking'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].Slab_TP_FosTP) ? IncentiveData[0].Slab_TP_FosTP:'-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].Slab_TP_FosTP) ? IncentiveData[0].Slab_TP_FosTP:'-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].Slab_Com_FosCom)? IncentiveData[0].Slab_Com_FosCom:'-'}</td>
                    <td>{IncentiveData[0] && !isNaN(IncentiveData[0].Slab_Com_FosCom)? IncentiveData[0].Slab_Com_FosCom: '-'}</td>
                    <td><i className="fa fa-inr"></i> { IncentiveData[0] && IncentiveData[0].IncentivePool && parseFloat(IncentiveData[0].IncentivePool).toLocaleString('en-IN')}</td>
                  </tr>
              </tbody>
            </table>
          </li>
        </ul>
      </Card>
    </div>
    
  );
};

export default IncentiveCalculationsMotorV2;
