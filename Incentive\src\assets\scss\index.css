@import url("https://fonts.googleapis.com/css?family=Merriweather:wght@300;400;700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;1,100;1,300;1,400;1,500;1,700&display=swap");
.table-wrapper {
  -webkit-box-shadow: 0px 0px 3px #00000029;
          box-shadow: 0px 0px 3px #00000029;
  border-radius: 4px;
  display: block;
}

@media screen and (max-width: 960px) {
  .table-wrapper {
    display: none;
  }
}

.table {
  background-color: #ffffff;
}

.table .table-content-heading {
  background-color: transparent;
}

.table .table-content-heading .table-heading {
  text-align: left;
  padding: 16px 40px;
  color: #000;
  font-weight: normal;
  border-bottom: none;
  text-align: center;
  font-size: 12px;
}

.table .table-content-body {
  background: #ffffff;
}

.table .table-content-body tr:nth-of-type(odd) {
  background-color: #FAFAFA;
}

.table .table-content-body .table-cell {
  text-align: left;
  padding: 9px 40px;
  font-weight: 400;
  border-bottom: none;
  text-align: center;
  font-size: 12px;
  color: #808080;
}

.table .table-content-body .table-cell a h6 {
  color: #6DD1D6;
}

.table .table-content-body .table-cell p {
  font-size: 12px;
  color: #808080;
}

.table .table-content-body .table-cell p svg {
  width: 13px;
  height: 13px;
  margin-right: 5px;
}

.main-cards {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding-bottom: 16px;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media (min-width: 992px) {
  .main-cards {
    -webkit-box-pack: start;
        -ms-flex-pack: start;
            justify-content: flex-start;
  }
}

.main-cards .main-card {
  -ms-flex-preferred-size: 51%;
      flex-basis: 51%;
  color: #ffffff;
  padding: 16px 0;
  border-radius: 10px;
  margin-bottom: 16px;
  background: url(/images/cardBg.png) no-repeat 100%/cover;
}

.main-cards .main-card p, .main-cards .main-card h2 {
  color: #ffffff;
}

.main-cards .main-card > div {
  padding: 0 16px;
}

@media (min-width: 600px) {
  .main-cards .main-card {
    -ms-flex-preferred-size: 30%;
        flex-basis: 30%;
    margin-right: 16px;
  }
}

@media (min-width: 992px) {
  .main-cards .main-card {
    max-width: 19%;
  }
}

.main-cards .main-card-footer {
  margin-left: 0;
  padding: 10px 0 0;
}

.data-card {
  -ms-flex-preferred-size: 100%;
      flex-basis: 100%;
  margin: 10px;
}

@media (min-width: 600px) {
  .data-card {
    -ms-flex-preferred-size: 45%;
        flex-basis: 45%;
  }
}

@media (min-width: 992px) {
  .data-card {
    -ms-flex-preferred-size: 30%;
        flex-basis: 30%;
  }
}

.data-card .data-card-content {
  padding: 16px 16px 0;
  counter-reset: serial-number;
}

.data-card .data-card-content .data-card-content-row {
  padding-bottom: 16px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
}

.data-card .data-card-content .data-card-content-row > div {
  -ms-flex-preferred-size: 50%;
      flex-basis: 50%;
}

.data-card .data-card-content .data-card-content-row .s-no h6 {
  display: inline-block;
  background: #e4e4e4;
  padding: 6px 8px;
  border-radius: 50%;
}

.data-card .data-card-content .data-card-content-row .status {
  text-align: right;
}

.data-card .data-card-content .data-card-content-row .labelTitle {
  font-size: 12px;
}

.data-card .data-card-content .data-card-content-row .profile-title {
  font-weight: 400;
}

@media (min-width: 992px) {
  .data-card .data-card-content .data-card-content-row {
    margin-bottom: 16px;
  }
}

.data-card .link-name {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.data-card .link-name a {
  color: #6DD1D6;
  font-weight: bold;
}

.app-bar {
  -webkit-box-shadow: none !important;
          box-shadow: none !important;
  background: transparent !important;
}

.app-bar .tabs {
  padding: 0;
  border-bottom: 1px solid #e4e4e4;
  margin: 0 8px;
  min-height: 37px;
}

.app-bar .tabs > div > span {
  display: none;
}

@media (min-width: 992px) {
  .app-bar .tabs {
    margin: 0;
    width: 670px;
  }
}

.app-bar .tabs div:nth-child(1), .app-bar .tabs div:nth-child(4) {
  color: #495973;
}

.app-bar .tabs .tab {
  text-transform: capitalize;
  min-width: 140px;
  font: 400 16px/20px Lato;
  color: #495973;
  min-height: 37px;
}

.app-bar .tabs .tab.active-tab {
  border-bottom: 2px solid #6DD1D6;
  color: #6DD1D6;
}

.tab-panel {
  padding: 0;
}

.tab-panel > div {
  padding: 8px 0 !important;
}

.search-wrapper {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

@media (min-width: 960px) {
  .search-wrapper {
    display: block;
  }
}

@media (min-width: 960px) {
  .search-wrapper .search-box {
    max-width: auto;
  }
}

.search-wrapper .search-input {
  background: #ffffff;
  border: none;
  border-bottom: 1px solid #ccc;
  border-radius: 0;
}

.search-wrapper .search-input div:hover:before {
  display: none;
}

.expansion-panel {
  background: #FAFAFA !important;
  margin: 0 !important;
}

.expansion-panel > div[aria-expanded="true"] {
  background: #ffffff;
  border-radius: 4px;
}

.expansion-panel > div[aria-expanded="true"] span:first-child svg {
  display: none;
}

.expansion-panel > div[aria-expanded="true"] span:first-child:after {
  content: '\f068';
  font-family: 'FontAwesome';
  width: 24px;
  height: 24px;
  display: inline-block;
  font-size: 14px;
  -webkit-transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  line-height: 24px;
}

.expansion-panel > div[aria-expanded="true"] .heading {
  color: #495973;
}

.expansion-panel > div:nth-child(2) {
  background: #ffffff;
  border-radius: 4px;
}

.expansion-panel > div span:first-child {
  border: 1px solid #6DD1D6;
  border-radius: 50%;
  color: #6DD1D6;
}

.expansion-panel > div .heading {
  color: #6DD1D6;
  font: 500 16px/20px Lato;
}

.drawer > div {
  top: 65px;
  width: 230px;
}

@media screen and (max-width: 660px) {
  .drawer > div {
    top: 57px;
  }
}

.drawer > div .drawer-header {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  padding: 0 5px;
}

.drawer > div .drawer-header svg {
  color: #495973;
}

.drawer > div .drawer-header p {
  font-weight: bold;
  color: #495973;
}

.drawer .filters .form-control {
  padding: 6px 0;
  min-width: 190px;
  background: #ffffff;
}

.drawer .filters .form-control .select {
  line-height: 0.3;
  font-size: 14px;
}

.drawer .filters .form-control label[data-shrink='true'] {
  -webkit-transform: translate(14px, 0px) scale(0.75);
          transform: translate(14px, 0px) scale(0.75);
}

.drawer .filter-buttons {
  padding: 0px 9px 15px;
}

.drawer .filter-buttons .grey-button {
  display: inline-block;
  padding: 5px 10px;
  background: #D1D1D1;
  color: #495973;
  border-radius: 5px;
  text-transform: uppercase;
  margin: 0 10px 0 0;
  min-width: 100px;
}

.drawer .filter-buttons .secondary-button {
  display: inline-block;
  padding: 5px 10px;
  background: #6DD1D6;
  color: #ffffff;
  border-radius: 5px;
  text-transform: uppercase;
  margin: 0;
  min-width: 100px;
}

.modal {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

@media screen and (min-width: 960px) {
  .modal {
    display: none;
  }
}

.modal .modal-content {
  background: -webkit-gradient(linear, left top, left bottom, color-stop(50%, #fcfff4), color-stop(80%, #dfe5d7), to(#b3bead));
  background: linear-gradient(to bottom, #fcfff4 50%, #dfe5d7 80%, #b3bead 100%);
  width: 100%;
  padding: 16px 0 0;
  overflow-y: auto;
  max-height: 100vh;
}

@media screen and (min-width: 600px) {
  .modal .modal-content {
    width: 65%;
  }
}

.menu-button:hover {
  background-color: transparent !important;
}

.menu-button .user-name {
  text-transform: capitalize;
  font-weight: 500;
  margin: 10px;
}

@media screen and (max-width: 660px) {
  .menu-button .user-name span {
    display: none;
  }
}

.menu-button .user-name i {
  margin-left: 10px;
}

@media screen and (max-width: 660px) {
  .menu-button .user-name i {
    margin-left: 2px;
  }
}

.notifications {
  margin: 0 20px;
}

.notification-badge {
  position: relative;
  padding: 2px 6px;
  background-color: #f30707;
  color: #f4f6f8;
  bottom: 15px;
  left: 50px;
  border-radius: 50%;
  font-size: 11px;
  font-weight: bolder;
}

.notification-menu .new-notification {
  background-color: #eafcfd;
  margin: 5px 5px;
  border-radius: 4px;
}

.notification-menu .notification {
  margin: 5px 5px;
  border-radius: 4px;
}

.notification-menu .label {
  font-weight: 600;
}

.notification-menu .description {
  font-size: 12px;
}

.tool-bar {
  padding-left: 0 !important;
}

.MuiList-padding {
  padding: 0 !important;
}

.Component-paper-32 {
  border: 1px solid transparent;
  border-radius: 0px 0px 20px 20px !important;
  background: #ffffff 0% 0% no-repeat padding-box;
  -webkit-box-shadow: 0px 0px 26px #0000001a;
          box-shadow: 0px 0px 26px #0000001a;
  margin: 5px 0 0;
}

.Component-paper-32 li {
  padding: 5px 15px;
  line-height: 10px;
  min-height: 10px;
  background-color: #fff;
}

.Component-paper-32 li:focus {
  background: #cfcfcf;
  color: #00458b;
}

.full-width {
  width: 100%;
}

.no-padding {
  padding: 0;
}

.clear {
  clear: both;
}

.tab-view {
  background-color: #fff;
  margin: 2em 0 0;
}

.tab-view .MuiTabs-flexContainer {
  background-color: #fff;
}

.tab-view .PrivateTabIndicator-colorPrimary-36 {
  background-color: #4669d6 !important;
}

.month-view {
  margin: 15px 5px 10px;
  display: inline-block;
  border: none;
  padding: 8px;
  border-radius: 5px;
  background: transparent;
}

.month-view select {
  border: transparent;
  color: #0065FF;
  font-size: 13px;
  width: 80px;
  background: transparent;
}

.month-view select:focus {
  outline: none;
}

.month-view .option-bg {
  background-color: #fff;
  color: #253858;
}

.month-view i {
  color: #0065FF;
  font-size: 14px;
}

.mt-1 {
  margin-top: 1em;
}

.mt-2 {
  margin-top: 1.5em;
}

.mt-3 {
  margin-top: 2em;
}

.mb-3 {
  margin-bottom: 3em;
}

h6 {
  font-size: 14px;
  letter-spacing: 0;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 19px;
  color: #253858;
  padding: 0 0 0 5px;
}

h6 span {
  font-size: 11px;
  color: #808080;
  display: block;
  font-weight: 400;
}

.ranking-box .MuiExpansionPanelSummary-expandIcon {
  display: none;
}

.ranking-box .MuiExpansionPanelSummary-content {
  display: block;
  padding: 0 0 15px;
}

.ranking-box h5 {
  color: #00458b;
  font-size: 20px;
  font-weight: 500;
  line-height: 12px;
  letter-spacing: 1px;
}

.ranking-box h5 span {
  margin: 3px 0 0;
  display: block;
  letter-spacing: 0;
}

.ranking-box ul {
  width: 100%;
}

.ranking-box ul li {
  width: 84%;
  display: inline-block;
}

.ranking-box ul li strong {
  display: block;
  color: #00458b;
  font-size: 14px;
}

.ranking-box ul li span {
  color: #00458b;
  font-size: 10px;
}

.ranking-box ul li:last-child {
  text-align: right;
  width: 16%;
}

.ranking-box .MuiExpansionPanelDetails-root {
  background-color: #0065ff;
  -webkit-box-shadow: 0 7px 5px 8px #dad4d4e8;
          box-shadow: 0 7px 5px 8px #dad4d4e8;
  padding: 12px 16px 6px;
  border-radius: 20px 20px 0 0;
}

.ranking-box .progress-bar {
  background-color: #b3d4ff;
  position: relative;
  margin: 15px 0 0;
  width: 100%;
  height: 8px;
  border-radius: 15px;
}

.ranking-box .progress-bar .status-bar {
  background-color: #0065ff;
  width: 35%;
  left: 0;
  top: 0px;
  display: block;
  height: 8px;
  border-radius: 15px;
  position: absolute;
}

.ranking-box .progress-bar .status-bar span {
  position: absolute;
  right: 0;
  top: -16px;
  color: #2699fb;
  font-size: 10px;
}

.ranking-box .progress-bar ul {
  padding: 8px 0 0;
}

.ranking-box .progress-bar ul li {
  display: inline-block;
  width: 50%;
}

.ranking-box .progress-bar ul li:last-child {
  text-align: right;
}

.ranking-data {
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
  position: relative;
  margin: 0 0 48px;
  border-radius: 18px;
}

.ranking-data .rank-box {
  position: relative;
  background: #fff url(/images/ranking-block-bg.svg) no-repeat top right;
  border-radius: 18px;
  padding: 15px 0 15px 15px;
  z-index: 99;
  left: 0;
  top: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: start;
      -ms-flex-align: start;
          align-items: flex-start;
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box {
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
  }
}

.ranking-data .rank-box h5 {
  color: #00458b;
  font-size: 20px;
  font-weight: 500;
  line-height: 12px;
  letter-spacing: 1px;
}

.ranking-data .rank-box h5 span {
  margin: 3px 0 0;
  display: block;
  letter-spacing: 0;
}

.ranking-data .rank-box .rank-box-left-section {
  width: 65%;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-left-section {
    width: 100%;
  }
}

.ranking-data .rank-box .rank-box-left-section .name-description {
  margin: 10px 0 20px;
}

.ranking-data .rank-box .rank-box-left-section .name-description strong {
  display: block;
  color: #00458b;
  font-size: 24px;
  font-weight: 700;
  line-height: 31px;
  font-family: "Merriweather", serif;
}

.ranking-data .rank-box .rank-box-left-section .name-description span {
  color: #00458b;
  font-size: 10px;
  font-weight: 600;
}

.ranking-data .rank-box .rank-box-left-section .name-description p {
  color: #00458B;
  font-family: "Roboto", sans-serif;
  font-weight: 600;
  font-size: 12px;
}



.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-slide {
  padding: 0;
}

.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-slide .highlights-heading {
  color: #253858;
  font-size: 14px;
  font-weight: 600;
  line-height: 24px;
}

.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-slide .highlights-description {
  color: #253858;
}

.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-dots {
  text-align: left;
  bottom: -13px;
}

.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-dots li.slick-active button::before {
  background: #0065ff;
}

.ranking-data .rank-box .rank-box-left-section .highlights .slick-slider .slick-dots li button:before {
  height: 3px;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar {
  margin: 47px 0 24px;
  position: relative;
  padding: 20px 0 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -ms-flex-pack: distribute;
      justify-content: space-around;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar::before {
  content: '';
  background: #CCDBFF;
  width: 70%;
  height: 1px;
  position: absolute;
  top: 0;
  left: -15px;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar > div {
  width: 30%;
  text-align: center;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar > div .MuiFab-label {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar > div .MuiFab-label .ape-gained {
  color: #253858;
  font-size: 14px;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar > div .MuiFab-label .ape-left {
  color: #253858;
  font-size: 10px;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar p {
  font-size: 12px;
  line-height: 16px;
  color: #253858;
  width: 80%;
}

.ranking-data .rank-box .rank-box-left-section .total-ape-progress-bar p span {
  color: #1DD1A1;
  font-size: 16px;
}

.ranking-data .rank-box .rank-box-right-section {
  width: 33%;
  height: 202px;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-right-section {
    width: 100%;
  }
}

.ranking-data .rank-box .rank-box-right-section ul {
  margin-top: 40px;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-right-section ul {
    margin-top: 20px;
  }
}

.ranking-data .rank-box .rank-box-right-section ul li {
  background: url(/images/ranking-block-right-detail-bg.svg) no-repeat right;
  margin: 0 0 16px;
  list-style: none;
  padding: 15px 15px 15px 45px;
  position: relative;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-right-section ul li {
    text-align: right;
  }
}

.ranking-data .rank-box .rank-box-right-section ul li::before {
  content: '';
  background: url(/images/icons/icon-rank.svg);
  position: absolute;
  height: 32px;
  width: 32px;
  left: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
          transform: translateY(-50%);
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-right-section ul li::before {
    left: auto;
    right: 55px;
  }
}

.ranking-data .rank-box .rank-box-right-section ul li:last-child::before {
  background: url(/images/icons/icon-slab.svg) no-repeat right;
}

.ranking-data .rank-box .rank-box-right-section ul li .label {
  color: #6C5A42;
  font-size: 12px;
  font-weight: 600;
  line-height: 16px;
  margin-bottom: 2px;
  display: block;
}

.ranking-data .rank-box .rank-box-right-section ul li .value {
  color: #253858;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
}

.ranking-data .rank-box .rank-box-right-section .background-caricrature {
  background: url(/images/ranking-block-right-caricrature.svg) no-repeat right top;
  position: absolute;
  right: 5%;
  top: 21px;
  width: 84px;
  height: 202px;
}

@media screen and (max-width: 768px) {
  .ranking-data .rank-box .rank-box-right-section .background-caricrature {
    display: none;
  }
}

.ranking-data .rank-box .progress-bar {
  background-color: #b3d4ff;
  position: relative;
  margin: 24px 0 12px;
  width: 100%;
  height: 8px;
  border-radius: 15px;
}

.ranking-data .rank-box .progress-bar .status-bar {
  background-color: #0065ff;
  width: 35%;
  left: 0;
  top: 0px;
  display: block;
  height: 8px;
  border-radius: 15px;
  position: absolute;
}

.ranking-data .rank-box .progress-bar .status-bar span {
  position: absolute;
  right: 0;
  top: -16px;
  color: #2699fb;
  font-size: 10px;
  font-weight: 900;
}

.ranking-data .rank-box .progress-bar ul {
  width: 100%;
}

.ranking-data .rank-box .progress-bar ul li {
  display: inline-block;
  width: 50%;
}

.ranking-data .rank-box .progress-bar ul li:last-child {
  text-align: right;
}

.ranking-data .rank-box-inner {
  position: absolute;
  background: #0065ff;
  border-radius: 18px;
  padding: 15px;
  z-index: 98;
  top: 75px;
  left: 0;
  width: 100%;
  padding: 37px 10px 5px;
  margin: 20px 0;
}

.ranking-data .rank-box-inner ul {
  width: 100%;
  display: table;
}

.ranking-data .rank-box-inner ul li {
  position: relative;
  width: 72%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.ranking-data .rank-box-inner ul li span {
  color: #fff;
  font-size: 10px;
}

.ranking-data .rank-box-inner ul li span img {
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
}

.ranking-data .rank-box-inner ul li:nth-child(1) span {
  padding-left: 25px;
}

.ranking-data .rank-box-inner ul li:nth-child(1) span.numbere {
  font-size: 11px;
  padding-left: 0;
  font-weight: 700;
  line-height: 16px;
}

.ranking-data .rank-box-inner ul li:last-child {
  width: 28%;
  text-align: right;
  float: right;
}

.ranking-data .rank-box-inner ul li:last-child span {
  font-size: 12px;
}

.ranking-data .rank-box-inner ul li:last-child span button {
  background-color: transparent;
  border: transparent;
  color: #fff;
  cursor: pointer;
  font-weight: 700;
  line-height: 16px;
}

.ranking-data .rank-box-inner ul li:last-child span button:focus {
  outline: none;
}

.ranking-data .rank-box-inner ul li:last-child span i {
  font-size: 14px;
}

.critiria-popup {
  position: relative;
}

.critiria-popup .close-btn {
  position: absolute;
  top: 3px;
  right: 0;
}

.critiria-popup .close-btn svg {
  font-size: 0.7em;
}

.critiria-popup p {
  color: #253858;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 20px;
}

.critiria-popup li {
  /*list-style: none;
    color: #253858;
    font-size: 13px;
    line-height: 18px;
    margin-bottom: 20px;
    list-style: none;
    padding: 0 8px;
    text-align: left;
    color: #253858;
    font-size: 13px;
    line-height: 18px;
    margin-bottom: 20px;*/
}

.critiria-popup .slabs-box {
  border: 1px solid #ccc;
  width: 300px;
  max-width: 100%;
  -webkit-box-shadow: 0 0rem 0.4rem rgba(0, 0, 0, 0.15);
          box-shadow: 0 0rem 0.4rem rgba(0, 0, 0, 0.15);
  margin: 0 auto;
}

.critiria-popup .slabs-box ul li {
  list-style: none;
  border-bottom: 1px solid #ccc;
  padding: 0 8px;
  text-align: center;
  background: -webkit-gradient(linear, left top, right top, from(#2775ff), to(#7202bb));
  background: linear-gradient(to right, #2775ff, #7202bb);
}

.critiria-popup .slabs-box ul li h5 {
  position: relative;
  color: #fff;
  font-size: 14px;
  padding: 10px 0 5px;
}

.critiria-popup .slabs-box ul li h5:after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  margin: auto;
  top: 100%;
  height: 2px;
  width: 50%;
  background: #275efe;
}

.critiria-popup .slabs-box ul li p {
  font-size: 12px;
  margin: 5px 0;
  color: #fff;
}

.critiria-popup .popup-inner-box {
  width: 90%;
  max-width: 100%;
  margin: 0 auto;
}

.criteriaHtml li {
  color: #253858;
  font-size: 13px;
  line-height: 18px;
  margin-bottom: 20px;
  list-style: none;
}

.highlights h6 {
  margin: 0;
}

.highlights .highlights-card {
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
  border-radius: 18px;
  background: #ffe8c4 url(/images/cureved_bg.svg) no-repeat center right;
  height: 96px;
  margin: 10px 0 0;
}

.highlights .highlights-card ul {
  padding: 7px 0;
}

.highlights .highlights-card ul li {
  display: inline-block;
  vertical-align: middle;
  padding-left: 15px;
  width: 70%;
}

.highlights .highlights-card ul li strong {
  color: #c47800;
  font-size: 20px;
  font-weight: 500;
  padding-right: 5px;
}

.highlights .highlights-card ul li strong span {
  font-size: 12px;
  display: inline;
  padding: 0 3px;
}

.highlights .highlights-card ul li:last-child {
  width: 30%;
  text-align: right;
  padding: 0;
}

.leader-board .lader-ranking {
  padding: 15px 15px 10px;
  min-height: 268px;
}

.leader-board .lader-ranking li {
  display: inline-block;
  width: 50%;
  color: #050505;
}

.leader-board .lader-ranking li strong {
  display: block;
  font-size: 11px;
  font-weight: 900;
  line-height: 13px;
}

.leader-board .lader-ranking li span {
  font-size: 12px;
  font-weight: 500;
}

.leader-board .lader-ranking li:nth-child(even) {
  text-align: right;
}

.leader-board .lader-ranking li:nth-child(odd) strong {
  color: #00458b;
}

.leader-board .lader-ranking div {
  margin: 0 0 0.5em;
  padding: 10px;
}

.leader-board .lader-ranking div.active {
  background: #0065ff;
  border-radius: 8px;
}

.leader-board .lader-ranking div.active li {
  color: #fff;
}

.leader-board .lader-ranking div.active li:nth-child(odd) strong {
  color: #fff;
}

.incentive .incentive-box {
  padding: 20px 30px;
  min-height: 250px;
}

.incentive .incentive-box li {
  display: inline-block;
  width: 100%;
  color: #050505;
  margin: 16px 0;
  letter-spacing: 0px;
}

.incentive .incentive-box li strong {
  display: block;
  font-size: 18px;
  font-weight: 500;
  color: #273a59;
}

.incentive .incentive-box li strong i {
  font-size: 16px;
}

.incentive .incentive-box li span {
  color: #808080;
  font-size: 11px;
  line-height: 26px;
  font-weight: 500;
}

.incentive .incentive-box li:nth-child(even) {
  text-align: right;
}

.booking-table {
  margin: 1em 0;
}

.booking-table h6 {
  display: block;
}

.booking-table ul {
  display: table;
  padding: 10px 15px;
  width: 100%;
}

.booking-table ul li {
  font-size: 12px;
  font-weight: 600;
  display: table-cell;
  text-align: left;
  width: 11.75%;
  color: #808080;
}

.booking-table ul li span {
  display: none;
}

.booking-table ul li strong {
  font-weight: 500;
  position: relative;
  color: #273A59;
  font-size: 12px;
}

.booking-table ul li strong.weighted-price {
  cursor: pointer;
}

.booking-table ul li strong svg {
  font-size: 13px;
  margin: 2px 4px 0;
  position: absolute;
  color: #90bafb;
}

.booking-table ul li.booking-SNo {
  width: 6%;
}

.booking-table ul.booking-table-head {
  background: #ffffff;
  border-radius: 18px;
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
}

.booking-table ul.booking-table-head li {
  font-weight: 900;
  font-size: 12px;
  line-height: 16px;
}

.booking-table ul.booking-table-body {
  background: #ffffff;
  margin: 5px 0 0;
}

.booking-table ul.booking-table-body li {
  font-size: 12px;
  font-weight: 500;
  vertical-align: middle;
}

.booking-table ul.booking-table-body li:nth-child(8) {
  padding-left: 2px;
}

.booking-table ul.booking-table-body li:nth-child(9) {
  padding-left: 6px;
}

.booking-table ul.body-table {
  height: 100px;
}

.booking-table .table-scroll {
  min-height: 260px;
  max-height: 400px;
  overflow-y: auto;
  padding: 0 0 20px;
  margin: 10px 0 0;
}

.booking-table .tooltip-popup {
  background-color: #ffcccc;
  -webkit-transition-duration: 2s;
          transition-duration: 2s;
}

.booking-table .tooltip-popup li {
  background-color: #ffcccc;
  display: block;
  list-style: none;
  position: relative;
  padding-left: 20px;
  width: 100%;
}

.booking-table .tooltip-popup li.yes::before {
  content: "\f00c";
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  color: #28a745;
  font-size: 12px;
  padding-right: 0.5em;
  position: absolute;
  top: 4px;
  left: 0;
}

.booking-table .tooltip-popup li.no::before {
  content: "\f00d";
  font-family: FontAwesome;
  font-style: normal;
  font-weight: normal;
  text-decoration: inherit;
  color: #dc3545;
  font-size: 12px;
  padding-right: 0.5em;
  position: absolute;
  top: 4px;
  left: 0;
}

.MuiAvatar-colorDefault {
  color: #414141 !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  font-size: 13px !important;
  border-radius: 0 !important;
  width: auto !important;
}

@media all and (max-width: 1080px) {
  .incentive .incentive-box {
    padding: 12px 20px;
  }
}

@media all and (max-width: 768px) {
  .criteria-slabs {
    width: 70%;
    border: 1px solid #050505;
    margin: 0px 15% !important;
    text-align: center;
    line-height: 22px !important;
  }
  .booking-table h6 {
    display: block;
  }
  .booking-table ul {
    border-radius: 18px;
  }
  .booking-table ul.booking-table-head {
    display: none;
  }
  .booking-table ul.booking-table-body {
    margin-bottom: 20px;
  }
  .booking-table ul.booking-table-body li.booking-SNo {
    width: 48%;
  }
  .booking-table ul li {
    display: inline-block;
    width: 48%;
  }
  .booking-table ul li strong {
    color: #273a59;
    font-weight: 600;
  }
  .booking-table ul li span {
    display: block;
    font-size: 10px;
  }
  .booking-table ul li:nth-child(even) {
    text-align: right;
    margin: 8px 5px;
  }
  .booking-table .table-scroll {
    height: 260px;
    overflow-y: auto;
    padding: 10px 0 20px;
  }
}

.scrool-toll::-webkit-scrollbar-track {
  -webkit-box-shadow: transparent;
          box-shadow: transparent;
  background-color: #fff;
}

.scrool-toll::-webkit-scrollbar {
  width: 6px;
  background-color: #808080;
  height: 1px;
  border: 3px;
}

.scrool-toll::-webkit-scrollbar-thumb {
  background-color: #808080;
}

.textGrey {
  color: #050505 !important;
}

.criteria-slabs {
  width: 60%;
  border: 1px solid #050505;
  margin: 0px 20% !important;
  text-align: center;
  line-height: 22px !important;
}

/* Criteria HTML Style */
.block_container {
  width: 100%;
  margin: 0 0 0 0;
  padding: 0 0 20px 0;
}

.table_hd {
  background: #0065ff;
  border-radius: 8px 8px 0 0;
  color: #fff;
  display: table;
  width: 100%;
}

.table_hd > div {
  display: table-cell;
  width: 50%;
  padding: 10px 5px;
  font-size: 13px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.table_data {
  border-bottom: 1px solid #0662f6;
}

.table_data > div.loop_box {
  display: table;
  padding: 0;
  margin: 0;
  width: 100%;
  border: 1px solid #0662f6;
  border-bottom: 0;
}

.table_data > div.loop_box > div {
  display: table-cell;
  width: 50%;
  padding: 5px;
  font-size: 13px;
  border-right: 1px solid #0662f6;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
}

.table_data > div.loop_box > div:last-child {
  border-right: 0;
}

.critiria-popup > ul {
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
}

.critiria-popup > ul li {
  margin: 0 0 0 20px;
  padding: 0 0 2px 0;
  list-style: decimal;
  font-size: 13px;
  color: #253858;
  line-height: 18px;
}

.payout .payout-box {
  padding: 15px 15px;
  background: #ffffff;
  border-radius: 18px;
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
}

.payout .payout-box li.firstbox {
  width: 100%;
  background-color: #cbffed;
  color: #55b795;
}

.payout .payout-box li.firstbox strong {
  color: #0F8D61;
  font-size: 12px;
  font-weight: 700;
}

.payout .payout-box li.firstbox span {
  color: #0F8D61;
  font-size: 18px;
  text-align: center;
  padding-left: 0;
  font-weight: 600;
  width: 100%;
}

.payout .payout-box li {
  display: inline-block;
  width: 100%;
  color: #050505;
  padding: 6px;
  border-radius: 8px;
  text-align: center;
  letter-spacing: 0px;
}

.payout .payout-box li strong {
  display: block;
  font-size: 18px;
  font-weight: 500;
  color: #273a59;
}

.payout .payout-box li strong i {
  font-size: 16px;
}

.payout .payout-box li span {
  color: #808080;
  font-size: 12px;
  line-height: 26px;
  font-weight: 500;
  display: inline-block;
  text-align: left;
  width: 50%;
  float: left;
  padding-left: 0px;
}

.payout .payout-box li p {
  color: #273A59;
  font-size: 14px;
  line-height: 26px;
  font-weight: 500;
  text-align: right;
  padding-left: 20px;
  width: 50%;
  float: left;
}

.payout .payout-box li i {
  font-size: 15px;
  margin-left: 5px;
}

.payout .payout-box li em {
  background-color: #0052cc;
  color: #ffffff;
  padding: 2px 4px;
  font-weight: 700;
  font-style: normal;
  font-size: 9px;
}

.common-tooltip-popup {
  -webkit-transition-duration: 2s;
          transition-duration: 2s;
  color: #273A59;
  padding: 3px;
}

@media only screen and (min-width: 360px) and (max-width: 767px) {
  .firstbox {
    width: 100% !important;
  }
  .payout .payout-box li {
    width: 100%;
  }
  .payout .payout-box li span {
    width: 50%;
    float: left;
    text-align: left;
    padding-left: 0px;
  }
  .payout .payout-box li p {
    width: 50%;
    float: left;
    text-align: right;
  }
}

.jag-ranking-data {
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
  position: relative;
  margin: 0 0 48px;
  border-radius: 18px;
}

.jag-ranking-data .rank-box {
  position: relative;
  background: #ffffff;
  border-radius: 18px;
  padding: 15px 15px 8px;
  z-index: 99;
  left: 0;
  top: 0;
}

.jag-ranking-data .rank-box h5 {
  color: #00458b;
  font-size: 20px;
  font-weight: 500;
  line-height: 12px;
  letter-spacing: 1px;
}

.jag-ranking-data .rank-box h5 span {
  margin: 3px 0 0;
  display: block;
  letter-spacing: 0;
}

.jag-ranking-data .rank-box ul {
  margin: 10px 0;
}

.jag-ranking-data .rank-box ul li {
  width: 66%;
  display: inline-block;
  vertical-align: top;
}

.jag-ranking-data .rank-box ul li strong {
  display: block;
  color: #00458b;
  font-size: 16px;
  font-weight: 700;
  line-height: 19px;
}

.jag-ranking-data .rank-box ul li span {
  color: #00458b;
  font-size: 12px;
  font-weight: 600;
}

.jag-ranking-data .rank-box ul li:last-child {
  text-align: right;
  width: 34%;
}

.jag-ranking-data .rank-box .gold {
  width: 100px;
  background: #FFF5E5 0% 0% no-repeat padding-box;
  border: 1px solid #EDC689;
  border-radius: 50px;
  opacity: 1;
  padding: 4px;
  text-align: center;
  text-transform: uppercase;
  color: #C47800;
  font-weight: 600;
  margin-top: 28px;
  font-size: 10px;
}

.jag-ranking-data .rank-box .gold img {
  vertical-align: text-top;
  margin-right: 7px;
}

.jag-ranking-data .rank-box .silver {
  width: 100px;
  background: #F2F2F2 0% 0% no-repeat padding-box;
  border: 1px solid #D1D1D1;
  border-radius: 50px;
  opacity: 1;
  padding: 4px;
  text-align: center;
  text-transform: uppercase;
  color: #9FA5AA;
  font-weight: 600;
  margin-top: 28px;
  font-size: 10px;
}

.jag-ranking-data .rank-box .silver img {
  vertical-align: text-top;
  margin-right: 7px;
}

.jag-ranking-data .rank-box .bottom-shadw {
  background: transparent -webkit-gradient(linear, left top, left bottom, from(#FFFFFF), to(#DDE8FD)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(180deg, #FFFFFF 0%, #DDE8FD 100%) 0% 0% no-repeat padding-box;
  width: 100%;
  padding: 10px 6px 0px 15px;
  border-radius: 8px;
}

.jag-ranking-data .rank-box .bottom-shadw h5 {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 20px;
}

.jag-ranking-data .rank-box .bottom-shadw li {
  width: 33%;
}

.jag-ranking-data .rank-box .bottom-shadw span {
  font-size: 12px;
  color: #808080;
  font-weight: normal;
}

.jag-ranking-data .rank-box .bottom-shadw strong {
  color: #253858;
  font-size: 14px;
  font-weight: 600;
  margin-top: 7px;
}

.jag-ranking-data .rank-box .bottom-shadw img {
  top: 4px;
}

.jag-ranking-data .rank-box-inner {
  position: absolute;
  background: #0065ff;
  border-radius: 18px;
  z-index: 98;
  top: 210px;
  left: 0;
  width: 100%;
  padding: 40px 12px 10px;
  margin: 20px 0;
}

.jag-ranking-data .rank-box-inner ul {
  width: 100%;
  display: table;
}

.jag-ranking-data .rank-box-inner ul li {
  position: relative;
  width: 72%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
}

.jag-ranking-data .rank-box-inner ul li span {
  color: #fff;
  font-size: 11px;
}

.jag-ranking-data .rank-box-inner ul li span img {
  position: absolute;
  left: 0;
  top: 0;
  width: 18px;
  height: 18px;
}

.jag-ranking-data .rank-box-inner ul li:nth-child(1) span {
  padding-left: 20px;
}

.jag-ranking-data .rank-box-inner ul li:nth-child(1) span.numbere {
  font-size: 12px;
  padding-left: 0;
  font-weight: 500;
  line-height: 16px;
}

.jag-ranking-data .rank-box-inner ul li:last-child {
  width: 28%;
  text-align: right;
  float: right;
}

.jag-ranking-data .rank-box-inner ul li:last-child span {
  font-size: 12px;
}

.jag-ranking-data .rank-box-inner ul li:last-child span button {
  background-color: transparent;
  border: transparent;
  color: #fff;
  cursor: pointer;
  font-weight: 700;
  line-height: 16px;
}

.jag-ranking-data .rank-box-inner ul li:last-child span button:focus {
  outline: none;
}

.jag-ranking-data .rank-box-inner ul li:last-child span i {
  font-size: 14px;
}

.jag-leader-board .lader-ranking {
  padding: 15px 15px 10px;
  min-height: 250px;
}

.jag-leader-board .lader-ranking li {
  display: inline-block;
  width: 33%;
  color: #050505;
}

.jag-leader-board .lader-ranking li strong {
  display: block;
  font-size: 12px;
  font-weight: 900;
  line-height: 13px;
}

.jag-leader-board .lader-ranking li span {
  font-size: 12px;
  font-weight: 500;
  color: #050505;
}

.jag-leader-board .lader-ranking li:nth-child(odd) strong {
  color: #00458b;
}

.jag-leader-board .lader-ranking li .gold {
  text-transform: uppercase;
  color: #C47800 !important;
  font-size: 11px !important;
}

.jag-leader-board .lader-ranking li .gold img {
  vertical-align: text-top;
  top: 0px !important;
}

.jag-leader-board .lader-ranking li .silver {
  text-transform: uppercase;
  color: #9FA5AA !important;
  font-size: 11px !important;
}

.jag-leader-board .lader-ranking li .silver img {
  vertical-align: text-top;
  top: 0px !important;
}

.jag-leader-board .lader-ranking li:nth-child(2) {
  text-align: center;
}

.jag-leader-board .lader-ranking li:nth-child(3) {
  text-align: center;
}

.jag-leader-board .lader-ranking div {
  margin: 0 0 0.8em;
  padding: 10px 10px 10px;
}

.jag-leader-board .lader-ranking div.active {
  background: #D8EAFA;
  border-radius: 8px;
}

.jag-leader-board .lader-ranking div.active li {
  color: #fff;
}

.jag-leader-board .lader-ranking div.active li strong {
  color: #00458b;
  margin-bottom: 5px;
}

.jag-leader-board .lader-ranking div.active li strong img {
  position: relative;
  top: 5px;
}

.jag-leader-board .lader-ranking div.active li:last-child strong {
  color: #00458b;
  margin-bottom: 5px;
}

.jag-highlights .ranking-data {
  -webkit-box-shadow: none;
          box-shadow: none;
  position: relative;
  margin: 0 0 48px;
  border-radius: 18px;
}

.jag-highlights .ranking-data h3 {
  color: #0E4E84;
  font-size: 18px;
  margin-top: 20px;
  margin-bottom: 2px;
}

.jag-highlights .ranking-data .rank-box {
  position: relative;
  background: #ffffff;
  border-radius: 18px;
  padding: 15px;
  z-index: 99;
  left: 0;
  top: 0;
}

.jag-highlights .ranking-data .rank-box span {
  color: #0E4E84;
  font-size: 11px;
}

.jag-highlights h6 {
  margin: 0;
  margin-bottom: 15px;
}

.jag-highlights h6 i {
  font-size: 10px;
}

.jag-highlights .highlights-card {
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
  border-radius: 18px;
  background: #ffe8c4;
  height: 250px;
  margin: 10px 0 0;
  padding: 8px;
  /* Slideshow container */
  /* Next & previous buttons */
  /* Position the "next button" to the right */
  /* On hover, add a black background color with a little bit see-through */
  /* Caption text */
  /* Number text (1/3 etc) */
  /* The dots/bullets/indicators */
  /* Fading animation */
  /* On smaller screens, decrease text size */
}

.jag-highlights .highlights-card ul {
  padding: 7px 0;
}

.jag-highlights .highlights-card ul li {
  display: inline-block;
  vertical-align: top;
  padding-left: 15px;
  width: 70%;
  /* scrollbar itself */
}

.jag-highlights .highlights-card ul li strong {
  color: #c47800;
  font-size: 20px;
  font-weight: 500;
  padding-right: 5px;
}

.jag-highlights .highlights-card ul li strong span {
  font-size: 12px;
  display: inline;
  padding: 0 3px;
}

.jag-highlights .highlights-card ul li:last-child {
  width: 30%;
  text-align: right;
  padding: 0;
}

.jag-highlights .highlights-card ul li h3 {
  color: #C47800;
  font-size: 16px;
  position: relative;
  top: 46px;
}

.jag-highlights .highlights-card ul li p {
  color: #C47800;
  font-size: 12px;
  text-align: left;
  margin-top: 56px;
  overflow-y: auto;
  padding-right: 5px;
}

.jag-highlights .highlights-card ul li p::-webkit-scrollbar {
  width: 5px;
}

.jag-highlights .highlights-card ul li p::-webkit-scrollbar-thumb {
  background-color: #C47800;
  border-radius: 16px;
}

.jag-highlights .highlights-card ul li button {
  background-color: #C47903;
  padding: 10px 16px;
  border: none;
  color: #fff;
  margin-top: 20px;
  border-radius: 24px;
  cursor: pointer;
}

.jag-highlights .highlights-card img {
  mix-blend-mode: luminosity;
}

.jag-highlights .highlights-card .mySlides {
  display: none;
}

.jag-highlights .highlights-card .slideshow-container {
  max-width: 1000px;
  position: relative;
  margin: auto;
  height: 130px;
}

.jag-highlights .highlights-card .prev, .jag-highlights .highlights-card .next {
  cursor: pointer;
  position: absolute;
  top: 50%;
  width: auto;
  padding: 16px;
  margin-top: -22px;
  color: white;
  font-weight: bold;
  font-size: 18px;
  -webkit-transition: 0.6s ease;
  transition: 0.6s ease;
  border-radius: 0 3px 3px 0;
  -webkit-user-select: none;
     -moz-user-select: none;
      -ms-user-select: none;
          user-select: none;
}

.jag-highlights .highlights-card .next {
  right: 0;
  border-radius: 3px 0 0 3px;
}

.jag-highlights .highlights-card .prev:hover, .jag-highlights .highlights-card .next:hover {
  background-color: rgba(0, 0, 0, 0.8);
}

.jag-highlights .highlights-card .text {
  color: #f2f2f2;
  font-size: 15px;
  padding: 8px 12px;
  position: absolute;
  bottom: 8px;
  width: 100%;
  text-align: center;
}

.jag-highlights .highlights-card .numbertext {
  color: #f2f2f2;
  font-size: 12px;
  padding: 8px 12px;
  position: absolute;
  top: 0;
}

.jag-highlights .highlights-card .dot {
  cursor: pointer;
  height: 4px;
  width: 34px;
  margin: 0 2px;
  background-color: #C7AB7D;
  border-radius: 5px;
  display: inline-block;
  -webkit-transition: background-color 0.6s ease;
  transition: background-color 0.6s ease;
}

.jag-highlights .highlights-card .active, .jag-highlights .highlights-card .dot:hover {
  background-color: #C47903;
  cursor: pointer;
}

.jag-highlights .highlights-card .fade {
  -webkit-animation-name: fade;
  -webkit-animation-duration: 1.5s;
  animation-name: fade;
  animation-duration: 1.5s;
}

@-webkit-keyframes fade {
  from {
    opacity: .4;
  }
  to {
    opacity: 1;
  }
}

@keyframes fade {
  from {
    opacity: .4;
  }
  to {
    opacity: 1;
  }
}

@media only screen and (max-width: 300px) {
  .jag-highlights .highlights-card .prev, .jag-highlights .highlights-card .next, .jag-highlights .highlights-card .text {
    font-size: 11px;
  }
}

.jag-highlights .highlights-card .show {
  display: block;
}

.jag-highlights .calculator-box {
  padding: 10px 0px;
  background-color: #fff;
  width: 100%;
  border-radius: 18px;
}

.jag-highlights .calculator-box .formatselector-buttons {
  font-size: 14px;
}

.jag-highlights .calculator-box .formatselector-buttons span {
  cursor: pointer;
  margin-left: 5px;
  padding: 5px;
}

.jag-highlights .calculator-box .formatselector-buttons span.active {
  color: #0063FA;
  font-weight: 700;
  border-bottom: 2px solid #0063FA;
}

.jag-highlights .calculator-box .selectoptions {
  margin-top: 25px;
}

.jag-highlights .calculator-box .selectoptions select {
  border-radius: 18px;
  padding: 10px 18px;
  margin-right: 25px;
  font-size: 11px;
  width: 150px;
}

.jag-highlights .calculator-box .selectoptions button {
  padding: 10px 15px;
  background-color: #0063FA;
  border: none;
  border-radius: 18px;
  color: #fff;
  cursor: pointer;
}

.jag-highlights .calculator-box h4 {
  text-transform: uppercase;
  margin-bottom: 25px;
  width: 100%;
}

.jag-highlights .calculator-box .box {
  border-right: 1px solid #BCBCBC;
  margin: 10px 15px;
}

.jag-highlights .calculator-box .noborder {
  border: none;
}

.jag-highlights .calculator-box .rewardbreakup {
  float: right;
  margin-right: 30px;
  font-size: 18px;
  cursor: pointer;
}

.graphbox {
  background-color: #fff;
  border-radius: 16px;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  padding: 2px;
}

.jag-criteriaHtml table {
  font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

.jag-criteriaHtml table td, .jag-criteriaHtml table th {
  border: 1px solid #ddd;
  padding: 8px;
}

.jag-criteriaHtml table {
  font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 100%;
}

.jag-criteriaHtml table th:first-child {
  background-color: transparent;
  border: none;
}

.jag-criteriaHtml table td, .jag-criteriaHtml table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

.jag-criteriaHtml table th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #0063FA;
  color: white;
}

.jag-criteriaList table {
  font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
  border-collapse: collapse;
  width: 50%;
  display: inline-table;
}

.jag-criteriaList table td, .jag-criteriaList table th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: center;
}

.jag-criteriaList table th {
  padding-top: 12px;
  padding-bottom: 12px;
  text-align: left;
  background-color: #0063FA;
  color: white;
}

.wallofFamePopup .MuiDialogContent-root {
  overflow: hidden !important;
}

.wallofFamePopup .MuiPaper-root {
  background: #0762F7 0% 0% no-repeat padding-box;
  border-radius: 20px;
  width: 1200px;
  max-width: 1200px;
  padding: 25px 7px;
}

.wallofFamePopup .wallofFamecontent {
  background: #FAF8FB 0% 0% no-repeat padding-box;
  border-radius: 20px;
  opacity: 1;
  padding: 32px;
}

.wallofFamePopup .wallofFamecontent .jaglogo {
  width: 214px;
  position: absolute;
  left: -3px;
  top: -21px;
}

.wallofFamePopup .wallofFamecontent .wofLogo {
  position: relative;
}

.wallofFamePopup .wallofFamecontent .wofLogo span {
  top: 80px;
  position: absolute;
  left: 85px;
  text-align: left;
  font: normal normal normal 13px/30px Roboto;
  letter-spacing: 0px;
  opacity: 1;
}

.wallofFamePopup .wallofFamecontent .wofLogo span:last-child {
  top: 80px;
  line-height: 15px;
}

.wallofFamePopup .wallofFamecontent .wofLogo img {
  width: 85%;
}

.wallofFamePopup .wallofFamecontent .pblogo {
  margin-top: 40px;
}

.wallofFamePopup .wallofFamecontent .scrollWOF {
  overflow-y: auto;
  height: 280px;
}

.wallofFamePopup .wallofFamecontent #scrollbar::-webkit-scrollbar {
  width: 6px;
  background: rgba(100, 100, 100, 0.1);
  height: 5px;
  border-radius: 50px;
}

.wallofFamePopup .wallofFamecontent #scrollbar::-webkit-scrollbar-thumb {
  background-color: #96A0B5;
  border-radius: 50px;
}

.wallofFamePopup .wallofFamecontent table {
  font-family: roboto;
  width: 100%;
  border-collapse: separate;
  border-spacing: 0 5px;
}

.wallofFamePopup .wallofFamecontent table tbody tr {
  background: transparent -webkit-gradient(linear, right top, left top, from(#FAF8FB), color-stop(73%, #FAEBBF), to(#F3CF62)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(270deg, #FAF8FB 0%, #FAEBBF 73%, #F3CF62 100%) 0% 0% no-repeat padding-box;
  text-align: left;
  font: normal normal 500 16px/30px Roboto;
  letter-spacing: 0px;
  color: #253858;
  opacity: 1;
}

.wallofFamePopup .wallofFamecontent .silver {
  background: transparent -webkit-gradient(linear, right top, left top, from(#FAF8FB), color-stop(73%, #F1F1F1), to(#E2E2E2)) 0% 0% no-repeat padding-box;
  background: transparent linear-gradient(270deg, #FAF8FB 0%, #F1F1F1 73%, #E2E2E2 100%) 0% 0% no-repeat padding-box;
}

.wallofFamePopup .wallofFamecontent table td, .wallofFamePopup .wallofFamecontent table th {
  border: none;
  padding: 3px 8px;
  line-height: 11px;
  text-align: center;
}

.wallofFamePopup .wallofFamecontent table tbody td:first-child {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.wallofFamePopup .wallofFamecontent table th {
  border: none;
  font: normal normal 500 14px/30px Roboto;
  letter-spacing: 0px;
  color: #A2A2A2;
  text-transform: uppercase;
  opacity: 1;
}

.wallofFamePopup .wallofFamecontent .radbg {
  position: relative;
  width: 100%;
  height: 50px;
}

.wallofFamePopup .wallofFamecontent .radbg img {
  left: 0;
  top: -76px;
  right: 0;
  margin: auto;
}

@media only screen and (min-width: 360px) and (max-width: 767px) {
  .firstbox {
    width: 100% !important;
  }
  .payout .payout-box li {
    width: 100%;
  }
  .payout .payout-box li span {
    width: 50%;
    float: left;
    text-align: left;
    padding-left: 0px;
  }
  .payout .payout-box li p {
    width: 50%;
    float: left;
    text-align: right;
  }
  .jag-highlights .ranking-data .rank-box {
    padding: 15px 11px;
  }
  .jag-highlights .calculator-box .box {
    border-right: none;
    border-bottom: 2px solid #BCBCBC;
    padding-bottom: 10px;
  }
  .jag-highlights .calculator-box .noborder {
    border: none;
  }
  .faq-section .MuiInputBase-root {
    width: 86% !important;
  }
}

@media only screen and (max-width: 320px) {
  .faq-section .MuiInputBase-root {
    width: 86% !important;
  }
}

/* FAQ CSS start */
.faq-section h4 {
  color: #253858;
  font-size: 20px;
  margin: 35px 0px 30px;
}

.faq-section .searchbar {
  -webkit-box-shadow: 0px 0px 16px #00000014;
          box-shadow: 0px 0px 16px #00000014;
  background-color: #fff;
  border-radius: 40px;
  margin-bottom: 20px;
  padding: 0px 17px;
}

.faq-section .MuiInputBase-root {
  width: 93%;
}

.faq-section .PrivateTabIndicator-colorSecondary-43 {
  display: none;
}

.faq-section .MuiTab-textColorInherit.Mui-selected {
  opacity: 1;
  border-bottom: 3px solid #0065FF;
  font-size: 18px;
  font-weight: bold;
  border-radius: 3px;
}

.faq-section .MuiAppBar-colorPrimary {
  color: #253858;
  background-color: transparent;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.faq-section .MuiTab-textColorInherit {
  opacity: 0.7;
  padding: 5px 20px;
  min-width: auto;
  font-size: 16px;
  color: #253858;
  text-transform: capitalize;
  margin-right: 20px;
}

.faq-section .faq-tab {
  margin-top: 10px;
}

.faq-section .faq-tab .MuiTab-textColorInherit.Mui-selected {
  opacity: 1;
  border-bottom: none;
  font-size: 12px;
  background-color: #0065FF;
  font-weight: bold;
  color: #fff;
  border-radius: 49px;
  padding: 0px 30px;
}

.faq-section .faq-tab .MuiTab-textColorInherit {
  font-size: 12px;
}

.faq-section .faq-tab .MuiTab-root {
  min-height: 38px;
}

.faq-section .faq-tab .MuiTouchRipple-root {
  display: none;
}

.faq-section .faq-tab .MuiTabs-indicator {
  display: none;
}

.faq-section .MuiBox-root {
  padding: 24px 0px !important;
}

.faq-section .MuiAccordion-rounded:first-child {
  border-top-left-radius: 16px;
  border-top-right-radius: 16px;
}

.faq-section .MuiAccordion-rounded:last-child {
  border-bottom-left-radius: 16px;
  border-bottom-right-radius: 16px;
}

.faq-section .MuiAccordion-rounded {
  border-radius: 16px;
}

.faq-section .MuiAccordion-root {
  position: relative;
  -webkit-transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
  margin-bottom: 10px;
  -webkit-box-shadow: none;
          box-shadow: none;
}

.faq-section .MuiAccordion-root:before {
  display: none;
}

.faq-section .MuiSvgIcon-root {
  color: #0065FF;
}

.faq-section .faqtab-data .MuiTypography-body1 {
  color: #495973;
  font-size: 14px;
  font-family: "Roboto";
}

.faq-section .faqtab-data .faq-ans {
  color: #808080;
}

.faq-section .read-more-link h2 {
  padding: 6px;
  background-color: #0065FF;
  width: 100px;
  color: #fff;
  font-size: 12px;
  margin: auto;
  text-align: center;
  border-radius: 24px;
}

.faq-rightside h6 {
  color: #253858;
  font-size: 16px;
  margin: 45px 0px 0px;
}

.faq-rightside p {
  font-size: 14px;
  padding: 9px 5px;
  color: #808080;
}

.faq-rightside .rightside-box {
  float: left;
  background-color: #fff;
  width: 100%;
  border-radius: 16px;
  padding: 20px;
}

.faq-rightside .rightside-box img {
  width: 35px;
}

.faq-rightside .rightside-box .reward {
  width: 22px;
}

.faq-rightside .rightside-box p {
  padding-left: 0px;
  padding-top: 23px;
  font-size: 12px;
  color: #253858;
  font-weight: bold;
}

.faq-rightside .contact-us {
  padding: 10px;
  width: 100%;
}

.faq-rightside .contact-us h6 {
  color: #253858;
  font-size: 16px;
  margin: 0px;
}

.faq-rightside .contact-us img {
  float: left;
}

.faq-rightside .contact-us a {
  float: left;
  margin-left: 10px;
  margin-top: 7px;
  color: #303030;
  font-size: 14px;
}

.faq-rightside .contact-us .rightside-box {
  margin-bottom: 10px;
  padding: 30px 20px 30px 20px;
}

.breakupheader {
  color: #0063FA;
}

.how-it-works-section {
  background: #D6E2FF;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: COLUMN;
          flex-direction: COLUMN;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  height: 100%;
  padding: 32px 16px;
}

.how-it-works-section h3 {
  font-size: 24px;
  font-weight: bold;
  letter-spacing: 4.08px;
  color: #253858;
  margin-bottom: 18px;
}

.how-it-works-section p {
  letter-spacing: 0px;
  color: #253858;
  font-size: 16px;
  line-height: 24px;
}

.how-it-works-section .slick-slider .slick-arrow {
  display: block !important;
  background: transparent;
  z-index: 1000;
}

.how-it-works-section .slick-slider .slick-arrow.slick-next {
  right: 10px;
}

.how-it-works-section .slick-slider .slick-arrow.slick-next:before {
  color: #606060;
}

.how-it-works-section .slick-slider .slick-arrow.slick-prev {
  left: 10px;
}

.how-it-works-section .slick-slider .slick-arrow.slick-prev:before {
  color: #606060;
}

.how-it-works-section .slick-slider .slick-slide img {
  text-align: center;
  margin: 15px auto 0;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab {
  margin-top: 30px;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab {
  text-align: center;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab p {
  font-size: 12px;
  color: #fff;
  line-height: 16px;
  padding: 13px 0;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab.slab-4 {
  background: url(/images/slab-yellow-bg.svg) no-repeat center;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab.slab-3 {
  background: url(/images/slab-blue-bg.svg) no-repeat center;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab.slab-2 {
  background: url(/images/slab-green-bg.svg) no-repeat center;
}

.how-it-works-section .slick-slider .slick-slide .steps .incentive-slab .slab.slab-1 {
  background: url(/images/slab-red-bg.svg) no-repeat center;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul {
  padding: 10px 30px 0;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li {
  color: #253858;
  font-size: 16px;
  line-height: 24px;
  list-style: none;
  margin-bottom: 34px;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li:last-child {
  margin-bottom: 0;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li p {
  width: 90%;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li span {
  font-size: 12px;
  height: 48px;
  width: 48px;
  display: -webkit-inline-box;
  display: -ms-inline-flexbox;
  display: inline-flex;
  -webkit-box-align: CENTER;
      -ms-flex-align: CENTER;
          align-items: CENTER;
  -webkit-box-pack: CENTER;
      -ms-flex-pack: CENTER;
          justify-content: CENTER;
  border-radius: 50%;
  color: #fff;
  margin-right: 12px;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li span.pink {
  background: #F3A191;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li span.purple {
  background: #868CF1;
}

.how-it-works-section .slick-slider .slick-slide .steps > ul li span.blue {
  background: #4D5C7E;
}

.how-it-works-section .slick-slider .slick-slide .steps div {
  padding: 0 85px;
}

.how-it-works-section .slick-slider .slick-slide .steps div p {
  font-size: 14px;
  margin: 5px 0 0;
}

.how-it-works-section .slick-slider .slick-slide .steps table {
  border-collapse: collapse;
  border-radius: 4px;
  margin-left: 90px;
}

.how-it-works-section .slick-slider .slick-slide .steps table thead tr {
  background: #ACC4FF;
  border-radius: 4px;
}

.how-it-works-section .slick-slider .slick-slide .steps table tbody tr:nth-child(2n+2) {
  background: #ACC4FF;
}

.how-it-works-section .slick-slider .slick-slide .steps table tr th, .how-it-works-section .slick-slider .slick-slide .steps table tr td {
  padding: 4px;
  letter-spacing: 0px;
  color: #253858;
  font-size: 14px;
}

.how-it-works-section .slick-slider .slick-slide .steps.step-3 ul li {
  margin-bottom: 25px;
}

.how-it-works-section .slick-slider .slick-slide .steps.step-3 ul li img {
  margin: 0 12px 0 0;
}

.how-it-works-section .slick-slider .slick-dots li.slick-active button::before {
  background: #0065ff;
}

.how-it-works-section .slick-slider .slick-dots li button:before {
  height: 3px;
}

.booking_details {
  position: relative;
}

.booking_details ul {
  width: 100%;
  position: relative;
  display: table;
  margin: 0;
}

.booking_details ul > span {
  content: "";
  position: absolute;
  top: -17px;
  left: 0;
  background-color: #fff;
  padding: 5px 13px 5px 0;
  display: inline-block;
  font-size: 14px;
}

.booking_details ul li {
  display: table-cell;
  font-size: 14px;
  padding: 6px 0;
}

.booking_details ul li p,
.booking_details ul li span {
  color: #808080;
  font-size: 12px;
}

.booking_details ul li p {
  color: #0052cc;
  font-weight: bold;
}

.booking_details ul li span {
  font-weight: 500;
}

.booking_details ul div {
  margin: 0 0 0 0;
  width: 100%;
  display: table;
}

.booking_details .card-body {
  padding: 0;
}

.booking_details ul.top_section {
  width: 100%;
  display: table;
  margin: 0;
  padding: 0;
  position: absolute;
  top: 0;
  left: 0;
  overflow: hidden;
}

.booking_details ul.top_section li {
  background: #ffffff;
  vertical-align: middle;
  border-radius: 0 18px 0 0;
}

.booking_details ul.top_section li:first-child {
  display: table-cell;
  background-color: #0052cc;
  padding: 0 0 0 25px;
  margin: 0;
  border-radius: 20px 0 0 0;
  text-align: left;
  width: 40%;
  font-size: 12px;
  font-weight: bold;
  vertical-align: middle;
  position: relative;
  height: 40px;
  color: #ffffff;
}

.booking_details ul.top_section li:first-child:after {
  position: absolute;
  right: -21px;
  top: -31px;
  width: 30px;
  height: 80px;
  content: "";
  background: #0052cc;
  -webkit-transform: rotate(40deg);
          transform: rotate(40deg);
}

.booking_details ul.top_section li:last-child {
  color: #0052cc;
  text-align: right;
  font-size: 16px;
  padding: 10px 15px;
  vertical-align: bottom;
  font-weight: bold;
}

.booking_details ul.top_section1 {
  width: 100%;
  display: table;
  margin: 0;
  padding: 0;
  position: absolute;
  left: 0;
  top: 0;
  overflow: hidden;
  display: none;
}

.booking_details ul.top_section1 li {
  background: #ffffff;
  vertical-align: middle;
  border-radius: 0 20px 0 0;
}

.booking_details ul.top_section1 li:first-child {
  display: table-cell;
  background-color: #e25151;
  padding: 2px 0 0 20px;
  margin: 0;
  border-radius: 20px 0 0 0;
  text-align: left;
  width: 40%;
  font-size: 11px;
  font-weight: bold;
  vertical-align: middle;
  position: relative;
  height: 40px;
  color: #ffffff;
}

.booking_details ul.top_section1 li:first-child > span {
  padding: 0;
  display: inline-block;
  font-size: 10px;
  color: #fff;
  text-align: left;
  display: block;
}

.booking_details ul.top_section1 li:first-child:after {
  position: absolute;
  right: -21px;
  top: -31px;
  width: 30px;
  height: 80px;
  content: "";
  background: #e25151;
  -webkit-transform: rotate(40deg);
          transform: rotate(40deg);
}

.booking_details ul.top_section1 li:last-child {
  color: #0052cc;
  text-align: right;
  font-size: 16px;
  padding: 10px 15px;
  font-weight: bold;
  vertical-align: bottom;
}

.booking_details ul.top_section1 li > em {
  font-style: normal;
  vertical-align: middle;
}

.booking_details ul.top_section1 li > em.reject {
  display: block;
  font-style: normal;
  font-size: 12px;
  padding: 5px 0 0 10px;
  margin: 0;
  line-height: normal;
}

.booking_details ul.top_section1 li > img {
  vertical-align: middle;
}

.booking_details:hover ul.top_section1 {
  display: table;
  width: 100%;
  cursor: pointer;
}

.booking_details .icons_block {
  display: table;
  width: 100%;
  padding: 0;
  margin: 50px 0 7px 0;
}

.booking_details .icons_block > div {
  display: table-cell;
  width: 50%;
  margin: 0;
  padding: 0;
  vertical-align: middle;
  position: relative;
}

.booking_details .icons_block > div > em {
  font-style: normal;
  margin: 0 10px 0 0;
  border-radius: 100%;
  width: 30px;
  height: 30px;
  background: #fdd6cc;
  display: inline-block;
  text-align: center;
  vertical-align: middle;
}

.booking_details .icons_block > div > em > img {
  margin: 8px 0 0 0;
  vertical-align: middle;
}

.booking_details .icons_block > div > em > i.fa-ticket-alt {
  -webkit-transform: rotate(40deg);
          transform: rotate(40deg);
}

.booking_details .icons_block > div > span {
  color: #fff;
  margin: 2px 0;
  display: inline-block;
  padding: 0px 8px;
  font-size: 10px;
  border-radius: 10px 0px 10px 11px;
  background-color: #174cff;
  position: absolute;
  right: 0;
  z-index: 0;
  top: -35px;
}

.booking_details .icons_block > div > hr {
  width: 100%;
  border-top: 1px dashed #0152cc;
  border-width: 2px;
}

.text-right {
  text-align: right;
}

.toggle_section {
  text-align: center;
}

.toggle_box {
  background-color: #fff;
  width: 90%;
  padding: 0px;
  font-size: 10px;
  text-align: center;
  color: #935d2d;
  position: relative;
  font-weight: bold;
  border-radius: 0 0 20px 20px;
  -webkit-box-shadow: 0px 27px 16px #0000001a;
          box-shadow: 0px 27px 16px #0000001a;
  margin: 0 auto;
}

.toggle_box .inner-box {
  background: #ffd474;
  color: #9a5c1b;
  padding: 6px 0;
  width: 100%;
  right: 0;
  margin: 0;
  top: 0;
  border-radius: 0 0 20px 20px;
  font-weight: bold;
  font-size: 12px;
}

.toggle_box .topshadow {
  opacity: 0.2;
  width: 100%;
  border-top: 1px solid #0000002b;
}

.txt-area {
  text-align: left;
  padding-left: 10px;
}

.txt-area label {
  color: #050505;
  font-size: 12px;
  margin: 0;
  line-height: 13px;
  font-weight: 500;
}

.txt-area label span {
  display: block;
  color: #ff0000;
}

.txt-area label span.done-txt {
  color: #1ab975;
}

.customer-history .card {
  border-radius: 20px;
  -webkit-box-shadow: 0px 0px 26px #0000001a;
          box-shadow: 0px 0px 26px #0000001a;
}

.customer-history .card .card-body {
  padding: 15px 15px 0;
}

.customer-history .custumer-heading h6 {
  text-align: right;
  font-size: 18px;
  letter-spacing: 0px;
  color: #1f3b95;
  padding: 15px;
}

.customer-history ul {
  width: 100%;
  display: table;
  padding: 10px 0;
  position: relative;
  border-top: 1px dashed #d0c9c9;
}

.customer-history ul > span {
  top: -17px;
  left: 0;
  color: #1f3b95;
  content: "";
  display: inline-block;
  padding: 5px 13px 5px 0;
  position: absolute;
  font-size: 16px;
  letter-spacing: 0px;
  background-color: #fff;
}

.customer-history ul li {
  width: 50%;
  display: inline-table;
  padding: 10px 0;
  font-size: 14px;
}

.customer-history ul li div {
  margin: 10px 0;
}

.customer-history ul li p,
.customer-history ul li span {
  font-size: 12px;
}

.customer-history ul li span {
  font: Regular 12px/15px Roboto;
  color: #1f3b95;
  font-size: 11px;
  font-weight: 500;
}

.customer-history ul li:nth-child(1) p {
  color: #000000;
}

.customer-history ul li:last-child {
  text-align: right;
}

.customer-history ul li:last-child > p {
  color: #1f3b95;
  font-size: 12px;
}

.ticket-all {
  padding: 20px 10px;
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.ticket {
  background-color: #ebebeb;
  padding: 9px;
  border-radius: 50%;
  height: 48px;
  width: 48px;
  display: block;
  line-height: 28px;
  margin: 8px 0 0 11px;
}

.ticket > img {
  margin: 4px;
}

.ticket-view {
  position: relative;
  padding: 0 0 10px;
  font-size: 12px;
}

.ticket-view > span {
  font-size: 12px;
  color: #808080;
}

.ticket-view > span > img {
  margin: 4px;
}

.ticket-view > p {
  font-size: 14px;
  color: #253858;
}

.ticket-view .ticket-date {
  width: 100%;
  display: block;
  padding: 7px 15px 0;
  font-size: 9px !important;
  text-align: right;
}

* {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  height: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.text-left {
  text-align: left;
}

body {
  background-color: #FAFAFA;
  height: 100%;
  font-family: "Roboto", sans-serif;
  -ms-overflow-style: none;
}

body::-webkit-scrollbar {
  display: none;
}

.tool-bar {
  -webkit-box-shadow: 0px 3px 6px #00000029;
          box-shadow: 0px 3px 6px #00000029;
}

.text-center {
  text-align: center;
}

.pading-zero {
  padding: 0;
}

.card {
  background: #ffffff;
  -webkit-box-shadow: 0px 0px 26px #0000001a;
          box-shadow: 0px 0px 26px #0000001a;
  border-radius: 20px;
}

a {
  text-decoration: none;
}

#root {
  height: 100%;
}

.title {
  position: relative;
  margin: 12px 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: baseline;
      -ms-flex-align: baseline;
          align-items: baseline;
  padding: 0 16px !important;
}

.title > div > span {
  font-size: 16px;
  color: #495973;
  position: relative;
  font-weight: 700;
}

@media (min-width: 600px) {
  .title {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
    height: auto;
  }
}

.title .firstbox {
  width: 100%;
}

.inner-mobile {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

@media (min-width: 960px) {
  .inner-mobile {
    display: none;
  }
}

.inner-mobile .firstbox {
  width: 100%;
}

.inner-desktop {
  min-width: 800px;
  width: 100%;
  border-radius: 5px 5px 0 0;
  display: none;
}

@media (min-width: 960px) {
  .inner-desktop {
    display: block;
  }
}

.logo-container {
  -webkit-box-pack: center !important;
      -ms-flex-pack: center !important;
          justify-content: center !important;
  margin-bottom: 25px;
}

.MuiIconButton-root {
  color: #495973 !important;
  background-color: red;
}

.wrapper {
  padding: 24px;
}

@media all and (max-width: 768px) {
  .wrapper,
  .bg-color {
    padding: 15px;
  }
  .wrapper .proceedBtn,
  .bg-color .proceedBtn {
    font-size: 11px;
  }
}
/*# sourceMappingURL=index.css.map */