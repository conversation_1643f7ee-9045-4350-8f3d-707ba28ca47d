<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="318" height="242" viewBox="0 0 318 242">
  <defs>
    <linearGradient id="linear-gradient" x1="0.958" y1="0.434" x2="0" y2="0.434" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffb893"/>
      <stop offset="1" stop-color="#ed7d39"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_2226" data-name="Rectangle 2226" width="318" height="242" rx="16" fill="url(#linear-gradient)"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <ellipse id="Ellipse_481" data-name="Ellipse 481" cx="86" cy="86.5" rx="86" ry="86.5" transform="translate(0)" fill="rgba(255,255,255,0.24)" opacity="0.3"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_2" data-name="Mask Group 2" clip-path="url(#clip-path)">
    <g id="Group_1564" data-name="Group 1564" transform="translate(180 87)">
      <g id="Mask_Group_1" data-name="Mask Group 1" transform="translate(0 0)" clip-path="url(#clip-path-2)">
        <rect id="Rectangle_2200" data-name="Rectangle 2200" width="402" height="234" rx="16" transform="translate(-261 -16)" fill="#fff" opacity="0.09"/>
      </g>
      <g id="top" transform="translate(43.441 30.5)">
        <g id="Group_1525" data-name="Group 1525" transform="translate(19.313 47.078)">
          <path id="Path_1640" data-name="Path 1640" d="M189.169,220.127v57.045a1,1,0,0,0,1.768.735l12.246-15.866a.939.939,0,0,1,1.524,0l12.246,15.866a1,1,0,0,0,1.768-.735V220.127Z" transform="translate(-189.169 -220.127)" fill="#ffa730"/>
        </g>
        <g id="Group_1527" data-name="Group 1527" transform="translate(18.494 47.078)">
          <g id="Group_1526" data-name="Group 1526" transform="translate(0 0)">
            <path id="Path_1641" data-name="Path 1641" d="M198.013,220.127h-8.844v57.045a1,1,0,0,0,1.768.735l7.076-9.167Z" transform="translate(-189.169 -220.127)" fill="#e58d23"/>
          </g>
        </g>
        <g id="Group_1528" data-name="Group 1528" transform="translate(44.04 74.962)">
          <path id="Path_1642" data-name="Path 1642" d="M298.962,363.981a1.327,1.327,0,0,1-1.327-1.327V347.567a1.327,1.327,0,1,1,2.653,0v15.088A1.327,1.327,0,0,1,298.962,363.981Z" transform="translate(-297.635 -346.24)" fill="#ffb74b"/>
        </g>
        <g id="Group_1529" data-name="Group 1529" transform="translate(30.128 68.686)">
          <circle id="Ellipse_478" data-name="Ellipse 478" cx="3.497" cy="3.497" r="3.497" fill="#e58d23"/>
        </g>
        <g id="Group_1530" data-name="Group 1530" transform="translate(0 0)">
          <path id="Path_1643" data-name="Path 1643" d="M145.632,84.463a2.409,2.409,0,0,1-2-1.062,14.574,14.574,0,0,0-12.342-6.5,15.277,15.277,0,0,0-4.651.723,2.512,2.512,0,0,1-.763.119,2.458,2.458,0,0,1-1.688-.668,2.118,2.118,0,0,1-.671-1.594l0-.154c0-.06,0-.114,0-.168a13.578,13.578,0,0,0-10.194-12.92,2.28,2.28,0,0,1-1.574-1.5l-.015-.047a2.108,2.108,0,0,1,.373-1.942,12.9,12.9,0,0,0,2.9-7.594,13.339,13.339,0,0,0-2.789-8.538,1.648,1.648,0,0,1-.248-1.543l.081-.23c.01-.027.1-.269.174-.481a1.8,1.8,0,0,1,1.3-1.121l.27-.063a13.688,13.688,0,0,0,10.9-13.105c0-.065,0-.129,0-.194l-.005-.167a2.161,2.161,0,0,1,1.026-1.884l.176-.109.065-.039a2.5,2.5,0,0,1,2.149-.162l.038.015a15.194,15.194,0,0,0,5.5,1.018,14.82,14.82,0,0,0,11.691-5.594,2.423,2.423,0,0,1,1.912-.913,2.4,2.4,0,0,1,1.888.883,14.673,14.673,0,0,0,11.544,5.4,16.337,16.337,0,0,0,1.643-.084,15.443,15.443,0,0,0,2.182-.4,2.292,2.292,0,0,1,.554-.068,2.242,2.242,0,0,1,1.253.377l.081.055.236.163a1.972,1.972,0,0,1,.883,1.68l-.007.25c0,.077-.005.143-.005.211a13.737,13.737,0,0,0,10.329,12.953,2.307,2.307,0,0,1,1.56,1.393,2.1,2.1,0,0,1-.3,2,12.8,12.8,0,0,0,.192,15.76,2.079,2.079,0,0,1,.359,1.952,2.259,2.259,0,0,1-1.454,1.438,13.646,13.646,0,0,0-9.505,12.642,2.3,2.3,0,0,1-2.359,2.2,2.508,2.508,0,0,1-.762-.118,15.3,15.3,0,0,0-4.645-.719,14.556,14.556,0,0,0-12.782,7.241l-.013.022a2.381,2.381,0,0,1-2.073,1.187l-.286,0Z" transform="translate(-111.649 -18.044)" fill="#ff624b"/>
        </g>
        <g id="Group_1531" data-name="Group 1531">
          <path id="Path_1644" data-name="Path 1644" d="M148.168,76.9a13.469,13.469,0,0,0-4.355.723,2.2,2.2,0,0,1-2.3-.549,2.192,2.192,0,0,1-.628-1.594l0-.154c0-.06,0-.114,0-.168a13.441,13.441,0,0,0-9.545-12.92,2.2,2.2,0,0,1-1.474-1.5l-.014-.047a2.221,2.221,0,0,1,.35-1.942,13.42,13.42,0,0,0,2.713-7.594,13.954,13.954,0,0,0-2.612-8.538,1.74,1.74,0,0,1-.232-1.543l.076-.23c.009-.027.091-.269.163-.481a1.725,1.725,0,0,1,1.218-1.121l.253-.063A13.49,13.49,0,0,0,142,26.071c0-.065,0-.129,0-.194l0-.167a2.2,2.2,0,0,1,.961-1.884l.164-.109.061-.039a2.21,2.21,0,0,1,2.012-.162l.036.015a13.472,13.472,0,0,0,9.122.415,13.412,13.412,0,0,1-7.6-5.022,2.192,2.192,0,0,0-1.767-.883,2.217,2.217,0,0,0-1.79.913,13.564,13.564,0,0,1-10.946,5.594,13.429,13.429,0,0,1-5.151-1.018l-.036-.015a2.21,2.21,0,0,0-2.012.162l-.061.039-.164.109a2.2,2.2,0,0,0-.961,1.884l0,.167c0,.065,0,.129,0,.194a13.49,13.49,0,0,1-10.209,13.105l-.253.063a1.726,1.726,0,0,0-1.218,1.121c-.073.212-.154.454-.163.481l-.076.23a1.74,1.74,0,0,0,.232,1.543,13.951,13.951,0,0,1,2.612,8.538,13.419,13.419,0,0,1-2.713,7.594,2.221,2.221,0,0,0-.35,1.942l.014.047a2.2,2.2,0,0,0,1.474,1.5,13.441,13.441,0,0,1,9.544,12.92c0,.054,0,.107,0,.168l0,.154a2.191,2.191,0,0,0,.628,1.594,2.2,2.2,0,0,0,2.3.549A13.511,13.511,0,0,1,141.594,83.4a2.221,2.221,0,0,0,1.872,1.062l.128,0,.268,0a2.207,2.207,0,0,0,1.941-1.187l.012-.022a13.511,13.511,0,0,1,6.01-5.859A13.66,13.66,0,0,0,148.168,76.9Z" transform="translate(-111.648 -18.043)" fill="#ff4b34"/>
        </g>
        <g id="Group_1532" data-name="Group 1532" transform="translate(8.635 9.91)">
          <ellipse id="Ellipse_479" data-name="Ellipse 479" cx="25.449" cy="23.28" rx="25.449" ry="23.28" transform="translate(0 0)" fill="#fce575"/>
        </g>
        <g id="Group_1533" data-name="Group 1533" transform="translate(15.232 14.332)">
          <circle id="Ellipse_480" data-name="Ellipse 480" cx="18.859" cy="18.859" r="18.859" transform="translate(0 0)" fill="#fce575"/>
        </g>
        <g id="Group_1535" data-name="Group 1535" transform="translate(42.24 42.042) rotate(9)">
          <path id="Path_1646" data-name="Path 1646" d="M279.826,203.612a1.327,1.327,0,0,1-.5-2.556h0c.057-.023,5.766-2.4,7.308-7.141a1.327,1.327,0,0,1,2.523.821c-1.929,5.926-8.553,8.665-8.834,8.779A1.329,1.329,0,0,1,279.826,203.612Z" transform="translate(-278.499 -192.998)" fill="#fff4c1"/>
        </g>
        <g id="Group_1538" data-name="Group 1538" transform="translate(50.179 18.05)">
          <path id="Path_1648" data-name="Path 1648" d="M327.92,113.056a20.234,20.234,0,0,1-.231,3.055l1.753.239a21.94,21.94,0,0,0-6.319-18.937l-.7,1.809A20.112,20.112,0,0,1,327.92,113.056Z" transform="translate(-322.42 -97.414)" fill="#ffd551"/>
        </g>
        <g id="house" transform="translate(16.728 14.542)">
          <rect id="Rectangle_4" data-name="Rectangle 4" width="3.58" height="6.249" transform="translate(23.368 4.491)" fill="#f05540"/>
          <rect id="Rectangle_5" data-name="Rectangle 5" width="6.314" height="3.385" transform="translate(22.001 1.302)" fill="#f3705a"/>
          <path id="Path_97" data-name="Path 97" d="M44.8,79.958V94.343H70.316V79.958L57.558,67.2h0C52.611,72.212,49.812,75.011,44.8,79.958Z" transform="translate(-42.066 -63.099)" fill="#ffc34c"/>
          <path id="Path_98" data-name="Path 98" d="M70.381,79.958,57.623,67.2c-2.539,2.473-4.491,4.426-6.444,6.379L44.8,79.958v2.929L57.558,70.129,70.316,82.887v.391h0v-3.32h.065Z" transform="translate(-42.066 -63.099)" fill="#ffa730"/>
          <path id="Path_99" data-name="Path 99" d="M98.348,336.725H90.667v-7.55a3.847,3.847,0,0,1,3.84-3.84h0a3.847,3.847,0,0,1,3.84,3.84Z" transform="translate(-85.134 -305.481)" fill="#415a6b"/>
          <rect id="Rectangle_6" data-name="Rectangle 6" width="10.024" height="8.787" transform="translate(15.492 19.853)" fill="#344a5e"/>
          <g id="Group_15" data-name="Group 15" transform="translate(16.533 20.894)">
            <rect id="Rectangle_7" data-name="Rectangle 7" width="3.45" height="6.704" fill="#8ad7f8"/>
            <rect id="Rectangle_8" data-name="Rectangle 8" width="3.45" height="6.704" transform="translate(4.491)" fill="#8ad7f8"/>
          </g>
          <path id="Path_100" data-name="Path 100" d="M30.658,14.19,17.054.586a2.124,2.124,0,0,0-2.929,0L.586,14.19a2.124,2.124,0,0,0,0,2.929,2.124,2.124,0,0,0,2.929,0L15.622,5.012,27.729,17.119a2.071,2.071,0,0,0,2.929-2.929Z" fill="#f3705a"/>
        </g>
      </g>
    </g>
  </g>
</svg>
