<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="318" height="242" viewBox="0 0 318 242">
  <defs>
    <linearGradient id="linear-gradient" x1="0.958" y1="0.434" x2="0" y2="0.434" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffb893"/>
      <stop offset="1" stop-color="#ed7d39"/>
    </linearGradient>
    <clipPath id="clip-path">
      <rect id="Rectangle_2227" data-name="Rectangle 2227" width="318" height="242" rx="16" fill="url(#linear-gradient)"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <ellipse id="Ellipse_481" data-name="Ellipse 481" cx="86" cy="86.5" rx="86" ry="86.5" transform="translate(0)" fill="rgba(255,255,255,0.24)" opacity="0.4"/>
    </clipPath>
  </defs>
  <g id="Mask_Group_3" data-name="Mask Group 3" clip-path="url(#clip-path)">
    <g id="Group_1564" data-name="Group 1564" transform="translate(180 87)">
      <g id="Mask_Group_1" data-name="Mask Group 1" transform="translate(0 0)" clip-path="url(#clip-path-2)">
        <rect id="Rectangle_2200" data-name="Rectangle 2200" width="402" height="234" rx="16" transform="translate(-261 -16)" fill="#fff" opacity="0.09"/>
      </g>
      <g id="winner_1_" data-name="winner (1)" transform="translate(18.089 34.567)">
        <path id="Path_1" data-name="Path 1" d="M106.519,44.034H10.6a4.36,4.36,0,0,1-4.36-4.36V4.36A4.36,4.36,0,0,1,10.6,0h95.915a4.36,4.36,0,0,1,4.36,4.36V39.674A4.36,4.36,0,0,1,106.519,44.034Z" fill="#ffe07d"/>
        <path id="Path_2" data-name="Path 2" d="M460.263,0h-6.54a4.36,4.36,0,0,1,4.36,4.36V39.674a4.36,4.36,0,0,1-4.36,4.36h6.54a4.36,4.36,0,0,0,4.36-4.36V4.36A4.36,4.36,0,0,0,460.263,0Z" transform="translate(-353.744 0)" fill="#ffd064"/>
        <g id="Group_1" data-name="Group 1" transform="translate(21.695 30.208)">
          <path id="Path_3" data-name="Path 3" d="M186.881,146.963H116.688a1.4,1.4,0,1,1,0-2.754h70.192a1.4,1.4,0,1,1,0,2.754Z" transform="translate(-115.07 -144.209)" fill="#e28086"/>
        </g>
        <path id="Path_4" data-name="Path 4" d="M360.291,196.571H350.7v-6.54a4.8,4.8,0,0,1,9.591,0v6.54Z" transform="translate(-272.301 -146.434)" fill="#ffcbbe"/>
        <path id="Path_5" data-name="Path 5" d="M115.512,196.571H125.1v-6.54a4.8,4.8,0,0,0-9.591,0v6.54Z" transform="translate(-86.379 -146.434)" fill="#ffddce"/>
        <path id="Path_6" data-name="Path 6" d="M164.777,239.35v17.439H125.1V239.35h-9.592v21.363a6.54,6.54,0,0,0,6.54,6.54h8.72v27.052a2.158,2.158,0,0,0,2.158,2.158h24.022a2.158,2.158,0,0,0,2.158-2.158V267.252h8.72a6.54,6.54,0,0,0,6.54-6.54V239.35Z" transform="translate(-86.379 -189.213)" fill="#a6de90"/>
        <path id="Path_7" data-name="Path 7" d="M318.088,239.35v19.837a3.27,3.27,0,0,1-3.27,3.27h-7.63a4.36,4.36,0,0,0-4.36,4.36v29.646h2.2a2.158,2.158,0,0,0,2.158-2.158V267.252h8.72a6.54,6.54,0,0,0,6.54-6.54V239.35Z" transform="translate(-234.458 -189.213)" fill="#74c887"/>
        <circle id="Ellipse_1" data-name="Ellipse 1" cx="10.9" cy="10.9" r="10.9" transform="translate(47.662 45.777)" fill="#ffddce"/>
        <path id="Path_8" data-name="Path 8" d="M224.3,224.209a10.9,10.9,0,0,1-14.658,14.658A10.9,10.9,0,1,0,224.3,224.209Z" transform="translate(-160.789 -177.243)" fill="#ffcbbe"/>
        <g id="Group_2" data-name="Group 2" transform="translate(21.546 11.461)">
          <path id="Path_10" data-name="Path 10" d="M118.165,54.712h-2.516a1.618,1.618,0,0,0,0,3.237h.9V68.1a1.618,1.618,0,0,0,3.237,0V56.331A1.618,1.618,0,0,0,118.165,54.712Z" transform="translate(-101.246 -54.712)" fill="#ff624b"/>
          <path id="Path_11" data-name="Path 11" d="M164.707,54.712a4.895,4.895,0,0,0-4.889,4.889v5.23a4.889,4.889,0,0,0,9.778,0V59.6A4.895,4.895,0,0,0,164.707,54.712Zm1.652,10.119a1.652,1.652,0,0,1-3.3,0V59.6a1.652,1.652,0,1,1,3.3,0Z" transform="translate(-137.441 -54.712)" fill="#ff624b"/>
          <path id="Path_12" data-name="Path 12" d="M229.228,54.712a4.895,4.895,0,0,0-4.889,4.889v5.23a4.889,4.889,0,1,0,9.778,0V59.6A4.895,4.895,0,0,0,229.228,54.712Zm1.652,10.119a1.652,1.652,0,1,1-3.3,0V59.6a1.652,1.652,0,1,1,3.3,0Z" transform="translate(-188.447 -54.712)" fill="#ff624b"/>
          <path id="Path_13" data-name="Path 13" d="M293.748,54.712a4.895,4.895,0,0,0-4.889,4.889v5.23a4.889,4.889,0,0,0,9.778,0V59.6A4.895,4.895,0,0,0,293.748,54.712ZM295.4,64.831a1.652,1.652,0,0,1-3.3,0V59.6a1.652,1.652,0,1,1,3.3,0Z" transform="translate(-239.452 -54.712)" fill="#ff624b"/>
          <path id="Path_14" data-name="Path 14" d="M358.268,54.712a4.895,4.895,0,0,0-4.889,4.889v5.23a4.889,4.889,0,0,0,9.778,0V59.6A4.894,4.894,0,0,0,358.268,54.712Zm1.652,10.119a1.652,1.652,0,0,1-3.3,0V59.6a1.652,1.652,0,1,1,3.3,0Z" transform="translate(-290.457 -54.712)" fill="#ff624b"/>
          <g id="rupee" transform="translate(0 0.103)">
            <path id="Path_67" data-name="Path 67" d="M241.439,0H236.6L236,1.043l.6,1.26a2.9,2.9,0,0,1,1.294,1.546H236.6l-.6.962.6.962h1.294A2.9,2.9,0,0,1,236.6,7.319L236,8.5l.6.91a4.828,4.828,0,0,0,3.287-3.632h1.556V3.849h-1.556a4.791,4.791,0,0,0-.868-1.924h2.424V0Z" transform="translate(-231.753)" fill="#ff624b"/>
            <path id="Path_68" data-name="Path 68" d="M237.393,397.489l1.361-1.361-2.157-2.157-.6.955.6,1.767Z" transform="translate(-231.753 -382.227)" fill="#ff624b"/>
            <g id="Group_13" data-name="Group 13" transform="translate(0)">
              <path id="Path_69" data-name="Path 69" d="M96.942,245.912H93.526v1.924l4.843,4.843v-2.722l-2.122-2.122h.694a4.8,4.8,0,0,0,1.427-.216v-2.087A2.868,2.868,0,0,1,96.942,245.912Z" transform="translate(-93.526 -238.214)" fill="#ff624b"/>
              <path id="Path_70" data-name="Path 70" d="M93.526,129.119h4.843v1.924H93.526Z" transform="translate(-93.526 -125.27)" fill="#ff624b"/>
              <path id="Path_71" data-name="Path 71" d="M96.942,0H93.526V1.924h3.416a2.868,2.868,0,0,1,1.427.379V0Z" transform="translate(-93.526)" fill="#ff624b"/>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
