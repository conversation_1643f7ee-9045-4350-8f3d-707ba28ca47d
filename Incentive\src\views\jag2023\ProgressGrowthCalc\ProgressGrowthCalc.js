import React, { useState, useEffect } from "react";
import LinearProgress from '@material-ui/core/LinearProgress';
import Typography from '@material-ui/core/Typography';
import '../../../assets/jag2022/scss/_performance.scss';
import '../../../assets/jag2022/scss/common/_common.scss';
import { GROWTH_PERC_MAP, GOLD, SILVER, HUSTLER, WARRIOR} from "../JagConstants2023";

const ProgressGrowthCalc = ({ agentData, slabforTarget }) => {

  const [isBelow100, setIsBelow100] = useState(true);
  const [isUnderGrowth, setIsUnderGrowth] = useState(false);
  const [growth, setGrowth] = useState(0);
  const growthTarget = parseInt(slabforTarget && slabforTarget.Target * 100 || 0);


  useEffect(() => {
    if (agentData.ActualG1) {
      let growth = Math.max(-100, Math.min(200, agentData.ActualG1))
      setGrowth(growth);
      if (agentData.ActualG1 > 100) {
        setIsBelow100(false)
      }

    }

    if (agentData.MemberType) {
      // let memberType = agentData.MemberType;
      let growth = Math.max(-100, Math.min(200, agentData.ActualG1));
      if(growth < growthTarget) {
        setIsUnderGrowth(true)
      }

      // switch (memberType && memberType.toLowerCase()) {
      //   case GOLD:
      //     if(growth < GROWTH_PERC_MAP) setIsUnderGrowth(true);
      //     break;
      //   case SILVER:
      //     if(growth < GROWTH_PERC_MAP[SILVER]) setIsUnderGrowth(true);
      //     break;
      //   case WARRIOR:
      //     if(growth < GROWTH_PERC_MAP[WARRIOR]) setIsUnderGrowth(true);
      //     break;
      //   case HUSTLER:
      //     if(growth < GROWTH_PERC_MAP[HUSTLER]) setIsUnderGrowth(true);
      //     break;

      //   default:
      //     break;
      // }
    }

  }, [agentData, growthTarget]);


  return (
    <div className="CurrentGrowth">
      <h4>Growth</h4>
      <p className="TotalGrowth">Growth target: { slabforTarget && (parseInt(slabforTarget.Target * 100)) || 'NA'}%</p>

      {
        isBelow100 ?
          <div className="Dflex">
            <LinearProgress variant="determinate" value={growth < 0 ? Math.abs(growth) : 100}
             className={growth < 0 ? "Negativebar NegativeGrowth" : (isUnderGrowth ? "Negativebar NegativeUnderGrowth" : 'Negativebar GrowthAchieved')} direction="rtl" />
            <div 
             className={isUnderGrowth ? "UnderGrowth" : "Growthbar"}
            >
              <LinearProgress variant="determinate" value={growth >= 0 ? (growth) : 0} />

              <div className="line-container">
                <div className="vertical-line line-0"></div>
                {/* {MemberType && MemberType.toLowerCase() === GOLD && <div className="vertical-line line-30"></div>}
                {MemberType && MemberType.toLowerCase() === SILVER && <div className="vertical-line line-40"></div>}
                {MemberType && MemberType.toLowerCase() === WARRIOR && <div className="vertical-line line-50"></div>}
                {MemberType && MemberType.toLowerCase() === HUSTLER && <div className="vertical-line line-60"></div>} */}
                {[10,15,20,30,40,50,60,70].map(item => {
                  if(growthTarget === item) {
                    return  <div className={`vertical-line line-${growthTarget}`}></div>
                  }
                })}
              </div>
            </div>
            <Typography className="GrowthPersantage">100%</Typography>
          </div>

          :

          <div className="Dflex">
            <LinearProgress variant="determinate" value={100} className="Negativebar GrowthAchieved" direction="rtl" />
            <div className="Growthbar">
              <LinearProgress variant="determinate" value={growth >= 0 ? (growth / 2) : 0} className="Growthbar" />

              <div className="line-container">
                <div className="vertical-line line-0"></div>
                {/* {MemberType && MemberType.toLowerCase() === 'gold' && <div className="vertical-line lineDouble-30"></div>}
                {MemberType && MemberType.toLowerCase() === 'silver' && <div className="vertical-line lineDouble-40"></div>}
                {MemberType && MemberType.toLowerCase() === 'warrior' && <div className="vertical-line lineDouble-50"></div>}
                {MemberType && MemberType.toLowerCase() === 'hustler' && <div className="vertical-line lineDouble-60"></div>} */}
                 {[10,15,20,30,40,50,60,70].map(item => {
                  if(growthTarget === item) {
                    return  <div className={`vertical-line lineDouble-${growthTarget}`}></div>
                  }
                })}
              </div>
            </div>
            <Typography className="GrowthPersantage">200%</Typography>
          </div>
      }
    </div>
  )

}

export default ProgressGrowthCalc;