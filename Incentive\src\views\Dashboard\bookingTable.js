import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
//import CssBaseline from "@material-ui/core/CssBaseline";
//import Pagination from "material-ui-flat-pagination";
//import DialogReson from "../../components/Dialogs/NewQuestion";
import Tooltip from '@material-ui/core/Tooltip';
//import Button from '@material-ui/core/Button';
import Zoom from '@material-ui/core/Zoom';
import VisibilityIcon from '@material-ui/icons/Visibility';
import ClickAwayListener from '@material-ui/core/ClickAwayListener';

import Button from '@material-ui/core/Button';
const longText = `<ul><li>dkdhkhdk</li></ul>
`;
const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);
const BookingTable = (props) => {

  const classes = useStyles();
  const { userId, date } = props;
  const [bookingList, setBookingList] = useState([]);
  const [open, setOpen] = React.useState(-1);

  /*
  const [offset, setOffset] = useState(0);
  const handleClick = (offset) => {
    setOffset(offset);
  };
  */
  const HtmlTooltip = withStyles((theme) => ({
    // tooltip: {
    //   display: 'block',
    //   backgroundColor: '#fff',
    //   color: 'rgba(0, 0, 0, 0.87)',
    //   maxWidth: 320,
    //   fontSize: theme.typography.pxToRem(12),
    //   "& ul":{
    //     "& li":{
    //       listStyle:"none",
    //     }
    //   },
    // },

  }))(Tooltip);

  const sortBookingByDate = (data) => {
    return _.orderBy(data, ['BookingDate'], ['desc']);
  };

  useEffect(() => {
    setBookingList([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/GetIncentiveBookings/${userId}/${date}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          setBookingList(sortBookingByDate(JSON.parse(response.Response)));
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [props]);

  const renderTooltipWrapper = (item) => {
    if (!item.Remarks || JSON.parse(item.Remarks).length == 0) return 'No Remarks';
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            JSON.parse(item.Remarks).map((breakup, indx) =>
              <li key={indx + 1} className={breakup.IsApplied ? "yes" : "no"}>
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >{breakup.Text}</td>
                    <td style={{ width: "20%", textAlign: "right", color: "#00458b" }} >{breakup.Value}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }
  return bookingList && bookingList.length > 0 && (
    <Fragment>
      <ClickAwayListener onClickAway={() => setOpen(-1)}>
        <div className="pading-zero" className={classes.root}>
          <div className="booking-table">
            <h6>
              Bookings Breakdown
            <span className='mobile-view'>Details of bookings</span>
            </h6>
            <ul className="booking-table-head">
              {/*<h6>Bookings Breakdown</h6>*/}
              <li className="booking-SNo">S.No</li>
              <li>Booking ID</li>
              <li>Booking Date</li>
              <li>Insurer Name</li>
              <li>Booking Status</li>
              {/*<li>Issuance Probability</li>
              <li>Issuance/Rej Date</li>*/}
              <li>APE</li>
              <li>Weighted APE</li>
            </ul>
            <div className="table-scroll scrool-toll">
              {
                bookingList.map((item, index) =>

                  <ul key={index + 1} className="booking-table-body">
                    <li className="booking-SNo">
                      <span>S.No</span>
                      <strong>{index + 1}</strong>
                    </li>
                    <li>
                      <span>Booking ID</span>
                      <strong>{item.LeadId}{item.E2E == 'No' ? '(NON E2E)' : ''}{item.IsAddOn == 1 ? '(AddOn)' : ''}</strong>
                    </li>
                    <li>

                      <span>Booking Date</span>
                      <strong>
                        {item.BookingDate ? (new Intl.DateTimeFormat("en-AU", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        }).format(
                          Date.parse(item.BookingDate || new Date())
                        )) : "-"}
                      </strong>
                    </li>
                    <li>
                      <span>Insurer Name</span>
                      <strong>{item.SupplierName || "-"}</strong>
                    </li>
                    <li>
                      <span>Booking Status</span>
                      <strong>{item.StatusName || "-"}</strong>
                    </li>
                    {/* <li>
                      <span>Issuance Probability</span>
                      <strong>{item.IssuanceProbabilty * 100}%</strong>
                    </li>
                    <li>
                      <span>Issuance/Reg Date</span>
                      <strong>
                        {item.IssuanceORrejDate ? (new Intl.DateTimeFormat("en-AU", {
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        }).format(
                          Date.parse(item.IssuanceORrejDate || new Date())
                        )) : "-"}
                      </strong>
                    </li> */}
                    <li>
                      <span>APE</span>
                      <strong><i className="fa fa-inr"></i> {item.APE ? item.APE.toLocaleString('en-IN') : "0.00"}</strong>
                    </li>
                    <li>
                      <span>Weighted APE</span>
                      <WrapperTooltip
                        disableFocusListener
                        TransitionComponent={Zoom}
                        placement="top"
                        arrow
                        onClose={() => setOpen(-1)}
                        open={index == open ? true : false}
                        title={item.Remarks && renderTooltipWrapper(item)}
                        classes={{ tooltip: classes.customWidth }}
                      >
                        <strong
                          onClick={(e) => { e.stopPropagation(); setOpen(index) }}
                          onMouseEnter={(e) => { e.stopPropagation(); setOpen(index) }}
                          onMouseLeave={(e) => { e.stopPropagation(); setOpen(-1) }}
                          className="weighted-price"><i className="fa fa-inr"></i> {item.ProjectedWeightedAPE ? item.ProjectedWeightedAPE.toLocaleString('en-IN') : "0.00"} <VisibilityIcon /></strong>
                      </WrapperTooltip>
                    </li>
                  </ul>
                )
              }
            </div>

            {/*<CssBaseline />
          <Pagination
            limit={10}
            offset={offset}
            total={100}
            color="primary"
            onClick={(e, offset) => handleClick(offset)}
          /> */}
          </div>
        </div>
      </ClickAwayListener>
    </Fragment>
  );
};

export default BookingTable;