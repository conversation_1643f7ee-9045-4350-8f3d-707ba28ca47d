import React, { useEffect, useState, Fragment } from "react";
import Accordion from "@material-ui/core/Accordion";
import AccordionDetails from "@material-ui/core/AccordionDetails";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import Typography from "@material-ui/core/Typography";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { makeStyles } from "@material-ui/core/styles";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: "absolute",
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: "2px solid #000",
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));

const IncentiveFaqs = (props) => {
  const classes = useStyles();
  const { handleChange, expanded } = props;
  const [readMore, setReadMore] = React.useState(false);

  const linkName = readMore ? 'Show Less' : 'Show More';

  const extraContent = (
    <div>
      <Accordion
        expanded={expanded === "panel1c2"}
        onChange={handleChange("panel1c2")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1c2bh-content"
          id="panel1c2bh-header"
        >
          <Typography className={classes.heading}>
            Quality dampener is applied on APE or Incentive?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>It is applied on earned incentive</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ca"}
        onChange={handleChange("panel1ca")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cabh-content"
          id="panel1cabh-header"
        >
          <Typography className={classes.heading}>
            What if my customer has paid only 1st Premium for his issued policy,
            will I get the incentives?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            No, incentives will be held until he does not make payments for next
            due premiums
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cb"}
        onChange={handleChange("panel1cb")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cbbh-content"
          id="panel1cbbh-header"
        >
          <Typography className={classes.heading}>
            Do you check if 2nd/3rd premium has been paid or not?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Yes, we check and if 2nd premium is paid by the customer we pay
            incentive as arrears in succeeding month/s.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cc"}
        onChange={handleChange("panel1cc")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1ccbh-content"
          id="panel1ccbh-header"
        >
          <Typography className={classes.heading}>
            For how many months we pay arrears for those policies which get
            issued later?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            For the last 3 months we pay arrears. For eg: If your policy gets
            booked in Apr’20, we will consider the issued month till July’20.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cd"}
        onChange={handleChange("panel1cd")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cdbh-content"
          id="panel1cdbh-header"
        >
          <Typography className={classes.heading}>
            Will the entire APE gets nullified if policy gets cancelled in Free
            Look period?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Yes</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ce"}
        onChange={handleChange("panel1ce")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cebh-content"
          id="panel1cebh-header"
        >
          <Typography className={classes.heading}>
            Prior SI was not activated but now it has been activated, will I get
            the remaining credit for my booking?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            If SI is activated post incentives were processed, then
            remaining/pending incentive amount will be paid with next month
            incentives. This is applicable till T+3 months only. For eg: - If
            your booking month is Apr’20 and SI was not activated till May’20
            month basis which incentives were paid in June’20. However, post
            incentive payout if SI was activated in June’20, arrears will be
            paid in July’20.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cf"}
        onChange={handleChange("panel1cf")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cfbh-content"
          id="panel1cfbh-header"
        >
          <Typography className={classes.heading}>
            What if there was a wrong Quality deduction/fatal deduction?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Please raise it to your supervisor/RM. S/he will check with quality
            team and if there is a scope for getting your quality score
            corrected, the pending amount will be paid as Arrears in succeeding
            month{" "}
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cg"}
        onChange={handleChange("panel1cg")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>
            My process alignment was wrongly mapped, will my correct incentive
            get paid in next month?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Please raise it to your supervisor/RM. If your process was
            incorrectly mapped S/he will take approval from Sales head to get it
            changed, subsequently the pending amount will be paid as Arrears in
            succeeding month.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ch"}
        onChange={handleChange("panel1ch")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1chbh-content"
          id="panel1chbh-header"
        >
          <Typography className={classes.heading}>
            Plan was not available on E2E platform when policy got booked, will
            I see any dampener for my booking?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>No</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ci"}
        onChange={handleChange("panel1ci")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cibh-content"
          id="panel1cibh-header"
        >
          <Typography className={classes.heading}>
            What is considered for incentive calculation, monthly in-hand or
            monthly CTC?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>Monthly CTC</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cj"}
        onChange={handleChange("panel1cj")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cjbh-content"
          id="panel1cjbh-header"
        >
          <Typography className={classes.heading}>
            Whether my Issued and Weighted APE will be considered separately for
            NRI bookings?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Yes For Eg: If you have an issued weighted APE of 8 Lakh out of
            which 2 Lakh is NRI, 6 Lakh will be calculated as per your process
            incentive slab and 2 Lakh will be multiplied as per NRI incentive
            slab. Both the Cost justification amount will be added and then
            subtracted by your monthly CTC.
          </Typography>
        </AccordionDetails>
      </Accordion>
    </div>
  );

  return (
    <div>
      <Accordion
        expanded={expanded === "panel1"}
        onChange={handleChange("panel1")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bh-content"
          id="panel1bh-header"
        >
          <Typography className={classes.heading}>
            What is cost justification and how you calculate this?
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography className="faq-ans">
            Cost justification is the amount which comes post multiplying your
            issued weighted APE with the respective incentive slab as per your
            process.
          </Typography>
        </AccordionDetails>
      </Accordion>

      <Accordion
        expanded={expanded === "panel1b"}
        onChange={handleChange("panel1b")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bbh-content"
          id="panel1bbh-header"
        >
          <Typography className={classes.heading}>
            Cost Justification factor is applicable on booking incentives?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            No it’s not applicable as they are Flat incentives. We pay whichever
            is highest (Cost justification or Booking Incentive)
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1bb"}
        onChange={handleChange("panel1bb")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bbbh-content"
          id="panel1bbbh-header"
        >
          <Typography className={classes.heading}>
            If the policy is not credited but issued, will I get the incentives?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            No, every policy should be credited and issued to PB. Even, pending
            credit status will not be considered for Payout.{" "}
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1c"}
        onChange={handleChange("panel1c")}
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cbh-content"
          id="panel1cbh-header"
        >
          <Typography className={classes.heading}>
            What is the quality dampener?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            If your quality is less than 65%, there will be 20% of deduction on
            your overall incentives. For eg: If your quality score is 64.9%, you
            will see 20% deduction on your incentives.
          </Typography>
        </AccordionDetails>
      </Accordion>
      {readMore && extraContent}
      <a
        className="read-more-link"
        onClick={() => {
          setReadMore(!readMore);
        }}
      >
        <h2>{linkName}</h2>
      </a>
    </div>
  );
};

export default IncentiveFaqs;
