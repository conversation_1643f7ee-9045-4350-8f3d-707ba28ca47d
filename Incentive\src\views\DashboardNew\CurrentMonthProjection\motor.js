import React, { useEffect, useState, useRef } from "react";
import { makeStyles } from "@material-ui/core/styles";
import { withSnackbar } from 'notistack';

import {
    TableContainer,
    Table,
    TableRow,
    TableCell,
    TableBody,
    Grid,

} from "@material-ui/core";
import moment from "moment";
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import Accordion from '@material-ui/core/Accordion';
import TopTabs from "./topTabs";
import * as services from "../../../services";

import {
    Box,
    Tabs, Tab
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
    rightPopup: {
        position: "relative",
        zIndex: "999",
    },
    list: {
        width: 600,
    },
    mList: {
        width: '100%',
    },
    currentProjectedIncentive: {
        textAlign: 'left',
        marginLeft: '1rem',
        marginBottom: '0.5rem',
        fontSize: '13px',
        fontWeight: 'normal'
    }
}));

const motor = (props) => {
    //let ProjectionDetailsMotor = props.data;
    let ProjectedData = props.data;
    ////debugger;
    const classes = useStyles();


    const refCurrentTrend = useRef(null);
    const refOpportunity = useRef(null);
    const refDailySourcing = useRef(null);
    const [activeScroll, setActiveScroll] = useState(0);
    const [ProjectionDetailsMotor, setProjectionDetailsMotor] = useState([]);





    useEffect(() => {
        
            try {
                services
                    .API_GET(`Incentive/GetProjectionDetailsMotor/${ProjectedData.UserId}`).then(response => {
                        //////debugger
                        if (response && response.Status) {
                            response = JSON.parse(response.Response);
                            if (response.length > 0) {
                                response = JSON.parse(response[0]);
                                setProjectionDetailsMotor(response);
                            }
                        }
                    })
                    .catch((err) => {
                        console.log("Error", err);
                    });
            }
            catch (e) {

            }
        

    }, [props.data]);

    useEffect(() => {
        const handleScroll = (e) => {

            console.log(refOpportunity.current == null)

            if(refOpportunity.current == null){

                if ( window.scrollY > refCurrentTrend.current.offsetTop -200  && window.scrollY < refDailySourcing.current.offsetTop-200) {
                    setActiveScroll(0)
                }

                else if (window.scrollY > refDailySourcing.current.offsetTop -200){
                    setActiveScroll(1)
                }

            }
            else if(refOpportunity.current != null){

                if ( window.scrollY > refCurrentTrend.current.offsetTop -200  && window.scrollY < refOpportunity.current.offsetTop-200) {
                    setActiveScroll(0)
                } else if ( window.scrollY > refOpportunity.current.offsetTop -200 && window.scrollY < refDailySourcing.current.offsetTop-200 ) {
                    setActiveScroll(1)
                } else if (window.scrollY > refDailySourcing.current.offsetTop -200) {
                    setActiveScroll(2)
                    // Etc...
                }

            }
        }
        document.addEventListener('scroll', handleScroll);
        return () => {
            document.removeEventListener('scroll', handleScroll);
        }
    }, [])

    const executeCurrentTrendScroll = (e) => {
        setActiveScroll(0);
        //refCurrentTrend.current.scrollIntoView()
        window.scrollTo(0, refCurrentTrend.current.offsetTop - 60)
    }
    const executeOpportunityScroll = () => {
        setActiveScroll(1);
        window.scrollTo(0, refOpportunity.current.offsetTop - 60)
        //refOpportunity.current.scrollIntoView()
    }
    const executeDailySourcingScroll = () => {
        setActiveScroll(2);
        window.scrollTo(0, refDailySourcing.current.offsetTop - 60)
        
        //refDailySourcing.current.scrollIntoView()
    }

    const handleTabChange = (event, newValue) => {
        setActiveScroll(newValue);
        switch (newValue) {
            case 0:
                executeCurrentTrendScroll()
                break;
            case 1:
                executeOpportunityScroll()
                break;
            
            case 2:
                executeDailySourcingScroll()
                break;
            default:
                break;
        }
    }
    const sumDRR = () => {
        let CalculationType = 'MinimumRequired';
        let sum_drr = 0;
        if (ProjectionDetailsMotor.length > 0) {
            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
                const element = ProjectionDetailsMotor[index];

                if (element.CalculationType == CalculationType) {

                    sum_drr += Math.ceil(element.DRR);


                }
            }

        }

        return sum_drr;
    }

    const bindDRR = () => {
        let result = [
          <tr>
            <th >Plan Type</th>
            <th >Plan Category</th>
            <th >Minimum Bookings Required (full month)</th>
            <th >Required DRR</th>
          </tr>
        ];
    
    
        let CalculationType = 'MinimumRequired';
        let sum_drr = 0;
        if (ProjectionDetailsMotor.length > 0) {
          for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
            const element = ProjectionDetailsMotor[index];
            if (ProjectedData.ProductId == 117) {
              if (element.CalculationType == CalculationType) {
    
                sum_drr += Math.ceil(element.DRR);
    
                result.push(<tr>
                  <td >{element.PlanType}</td>
                  <td>{element.PlanCategory}</td>
                  <td>#{element.BookingCount}</td>
                  <td>
                    #{Math.ceil(element.DRR).toLocaleString('en-IN')}
                  </td>
    
    
                </tr>)
    
                // console.log(element.DRR);
    
                // console.log("This Works!");
              }
            }
          }
          console.log(sum_drr);
        }
    
        return result;
    
      }
    const bindProjectiondata = (CalculationType) => {
        let result = [];
        let totalIncentiveAmount = 0;
        if (ProjectionDetailsMotor.length > 0) {

            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

                const element = ProjectionDetailsMotor[index];

                
                    if (element.CalculationType == CalculationType) {
                        totalIncentiveAmount += parseInt(element.IncentiveAmount) || 0;
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            <td>{element.PlanCategory}</td>
                            <td>{element.BookingCount} x {element.IncentiveValue}</td>
                            <td><i className="fa fa-inr"></i>{element.IncentiveAmount.toLocaleString('en-IN')}</td>
                        </tr>)
                    }
                

            }
        }

        result.push(<tr>
            <td>{'-'}</td>
            <td>{' '}</td>
            <td>{' '}</td>
            <td>{' '}</td>
        </tr>);

        result.push(<tr>
            <td>Total</td>
            <td>{'-'}</td>
            <td>{'-'}</td>
            <td><i className="fa fa-inr"></i> {totalIncentiveAmount && Math.round(totalIncentiveAmount).toLocaleString('en-IN')}</td>

        </tr>);

        return result;
    }

    const bindTillNowData = (CalculationType, isWeightedAPE) => {
        let result = [];

        if (ProjectionDetailsMotor.length > 0) {

            for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

                const element = ProjectionDetailsMotor[index];

                
                    if (element.CalculationType == CalculationType) {
                        result.push(<tr>
                            <td>{element.PlanType}</td>
                            <td>{element.PlanCategory}</td>
                            <td className={(element.CalculationType == "NextProjection" && element.PlanCategory == "Comprehensive") ? 'highlight' : ''}>#{element.BookingCount}</td>
                        </tr>)
                    }
                

            }
        }
        return result;
    }




    return (
        <>
            <Grid container spacing={2} className='mobile-view'>
                <Box className='items-list'>
                    <Tabs
                        value={activeScroll}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons
                        allowScrollButtonsMobile
                        aria-label="scrollable force tabs example"
                    >
                        <Tab label="Current Trends" />
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                        <Tab label="Opportunity" />}
                        <Tab label="Daily Sourcing Req." />
                    </Tabs>
                </Box>
            </Grid>
            <Grid container spacing={2} className='mobile-view'>
                <Box className='items-list'>
                    <Tabs
                        value={activeScroll}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons
                        allowScrollButtonsMobile
                        aria-label="scrollable force tabs example"
                    >
                        <Tab label="Current Trends" />
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                            <Tab label="Opportunity" />}
                        <Tab label="Daily Sourcing Req." />
                    </Tabs>
                </Box>
            </Grid>
            <Grid item sm={12} md={12} xs={12} className='common-view'>

                <div className="CurrentMonthData" ref={refCurrentTrend}>
                    <Grid container>
                        <Grid item sm={4} md={4} xs={12}>
                            <h2>Current Trends</h2>
                            <img src="/images/incentive/currentTrends.png" className="leftimage" />
                        </Grid>
                        <Grid item sm={8} md={8} xs={12}>
                            <TableContainer>
                                <Table className='web-common' aria-label="simple table">
                                    

                                    {ProjectedData.SuperGroupTypeId == 3 &&
                                        <TableBody>
                                            <TableRow>
                                                <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                                                <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell colSpan={2}>
                                                    <Accordion>
                                                        <AccordionSummary
                                                            expandIcon={<ExpandMoreIcon />}
                                                            aria-controls="panel1a-content"
                                                            id="panel1a-header"
                                                        >
                                                            <table className="motorIncentive-header">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Sourced Bookings (till date)</td>
                                                                        <td>#{ProjectedData.BKGSTillNow}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>

                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <table className="motorIncentive-content">
                                                                <tbody>
                                                                    {bindTillNowData("TillNow")}
                                                                </tbody>
                                                            </table>
                                                        </AccordionDetails>
                                                    </Accordion>
                                                </TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell colSpan={2}>
                                                    <Accordion>
                                                        <AccordionSummary
                                                            expandIcon={<ExpandMoreIcon />}
                                                            aria-controls="panel1a-content"
                                                            id="panel1a-header"
                                                        >
                                                            <table className="motorIncentive-header">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Projected Sourced Bookings (full month)</td>
                                                                        <td>#{ProjectedData.ProjectedBKGS}</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>

                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <table className="motorIncentive-content">
                                                                <tbody>
                                                                    {bindTillNowData("CurrentProjection")}
                                                                </tbody>
                                                            </table>
                                                        </AccordionDetails>
                                                    </Accordion>


                                                </TableCell>

                                            </TableRow>
                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(pre salary adjustment)</i></TableCell>
                                                    <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                            </>
                                            }
                                            {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>
                                                    <TableCell colSpan={2}>
                                                        <Accordion>
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls="panel1a-content"
                                                                id="panel1a-header"
                                                            >
                                                                <table className="motorIncentive-header">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(Post salary adjustment)</i></td>
                                                                            <td><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <table className="motorIncentive-content">
                                                                    <tbody>
                                                                        {bindProjectiondata("CurrentProjection")}
                                                                    </tbody>
                                                                </table>
                                                            </AccordionDetails>
                                                            <p className={classes.currentProjectedIncentive}>Your Current Projection CJ Incentive is calculated as : <b style={{ color: 'red' }}>Total - Salary</b></p>
                                                        </Accordion>
                                                    </TableCell>
                                                </TableRow>
                                            }

                                        </TableBody>
                                    }

                                </Table>
                            </TableContainer>
                            {/* <li>You are qualifying for Slab {ProjectedData.Slab || "-"} basis current projections - {ProjectedData.CurrentSlabPercentage}%</li> */}
                            {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                                <div className="caption error"><img src="/images/incentive/announceIcon.svg" /><b>Basis current projections, you are not able to justify your cost. You need to work harder to make CJ incentives.</b></div>}
                            {/* Issued Bookings/Sourced Bookings for your process in last payout Month */}
                            {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                <div className="caption"><img src="/images/incentive/announceIcon.svg" /><b>Congratulations !!! Basis current projections you are able to justify your cost this month.</b></div>}


                        </Grid>
                    </Grid>

                </div>
                {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                    <div className="CurrentMonthData" ref={refOpportunity}>
                        <Grid container>
                            <Grid item sm={4} md={4} xs={12} className='common-view'>
                                <h2>Opportunity</h2>
                                <img src="/images/incentive/Opportunity.png" className="leftimage" />
                            </Grid>
                            <Grid item sm={8} md={8} xs={12}>
                                <TableContainer>
                                    

                                    {ProjectedData.SuperGroupTypeId == 3 &&
                                        <Table className='web-common opportunity' aria-label="simple table">
                                            <TableBody>
                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell colSpan={2}>
                                                        <Accordion>
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls="panel1a-content"
                                                                id="panel1a-header"
                                                            >
                                                                <table className="motorIncentive-header">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>For Slab {ProjectedData.NextSlab || "-"} Minimum Issued bookings Required</td>
                                                                            <td>#{ProjectedData.NextSlabBKGS}</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <table className="motorIncentive-content">
                                                                    <tbody>
                                                                        {bindTillNowData("NextProjection")}
                                                                    </tbody>
                                                                </table>
                                                            </AccordionDetails>
                                                        </Accordion>
                                                    </TableCell>

                                                </TableRow>}


                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell colSpan={2}>
                                                        <Accordion>
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls="panel1a-content"
                                                                id="panel1a-header"
                                                            >
                                                                <table className="motorIncentive-header">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"}</td>
                                                                            <td><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <table className="motorIncentive-content">
                                                                    <tbody>
                                                                    {bindProjectiondata("NextProjection")}
                                                                    </tbody>
                                                                </table>
                                                            </AccordionDetails>
                                                        </Accordion>
                                                    </TableCell>


                                                </TableRow>}

                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                                                    <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}

                                            </TableBody>
                                        </Table>
                                    }

                                </TableContainer>
                                <div className="caption OpportunityNote"><img src="/images/incentive/note.svg" />

                                    <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                                    <ul>
                                        <li>The above Incentive calculations include only Cost Justification (CJ) Incentive.</li>
                                        {/* <li>Above Calculated CJ Incentive is before net of Salary for e.g. if Projected CJ Incentive: 47K and your salary is 25K then Net In-hand CJ Incentive: (47K-25K) = 22K</li> */}
                                        {/* <li>Actual incentive may differ from this projections.</li> */}
                                        {/* <li>In Case Projected CJ Incentive is less than salary, then Projected CJ Incentive earned is 0 and take home will be Salary.</li> */}
                                    </ul>


                                </div>
                            </Grid>
                        </Grid>

                    </div>}

                <div className="CurrentMonthData" ref={refDailySourcing}>
                    <Grid container>
                        <Grid item sm={4} md={4} xs={12} className='common-view'>
                            <h2>Daily Targets</h2>
                            <img src="/images/incentive/DailySourcingRequired.png" className="leftimage" />
                        </Grid>
                        <Grid item sm={8} md={8} xs={12}>

                            <TableContainer>
                                {ProjectedData.SuperGroupTypeId == 1 &&
                                    <Table className='web-common' aria-label="simple table">
                                        <TableBody>
                                            <TableRow>
                                                <TableCell >Current Sourced APE/Day</TableCell>
                                                <TableCell ><i className="fa fa-inr"></i> {ProjectedData.PerDayAPE && Math.round(ProjectedData.PerDayAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>

                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 && <TableRow>
                                                <TableCell colSpan={2}>
                                                    <Accordion>
                                                        <AccordionSummary
                                                            expandIcon={<ExpandMoreIcon />}
                                                            aria-controls="panel1a-content"
                                                            id="panel1a-header"
                                                        >
                                                            <table className="motorIncentive-header">
                                                                <tbody>
                                                                    <tr>

                                                                        <td>Required Sourced Bookings/day to justify Cost and make CJ Incentive</td>
                                                                        <td><i className="fa fa-inr"></i> {sumDRR("CurrentProjection").toLocaleString('en-IN')}</td>
                                                                    </tr>


                                                                </tbody>
                                                            </table>

                                                        </AccordionSummary>
                                                        <AccordionDetails>
                                                            <table className="motorIncentive-content">
                                                                <tbody>
                                                                    {bindSourcedBooking("CurrentProjection")}
                                                                </tbody>
                                                            </table>
                                                        </AccordionDetails>
                                                    </Accordion>


                                                </TableCell>

                                            </TableRow>}





                                            {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>

                                                    <TableCell colSpan={2}>
                                                        <Accordion>
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls="panel1a-content"
                                                                id="panel1a-header"
                                                            >
                                                                <table className="motorIncentive-header">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>Required Sourced APE/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></td>
                                                                            <td><i className="fa fa-inr"></i> {ProjectedData.NextSlabPerDayAPE && Math.round(ProjectedData.NextSlabPerDayAPE).toLocaleString('en-IN')}</td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>

                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <table className="motorIncentive-content">
                                                                    <tbody>
                                                                        {bindNextProjectionDRR()}
                                                                    </tbody>
                                                                </table>
                                                            </AccordionDetails>
                                                        </Accordion>
                                                    </TableCell>


                                                </TableRow>}

                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                                                <TableRow>
                                                    <TableCell>Required Sourced APE/day to justify Cost and make CJ incentive</TableCell>
                                                    <TableCell><i className="fa fa-inr"></i> {Math.round((ProjectedData.MinAPERequired - ProjectedData.APETillNow) / ProjectedData.NextWorkingDays).toLocaleString('en-IN')}</TableCell>

                                                </TableRow>}

                                        </TableBody>
                                    </Table>}



                                {ProjectedData.SuperGroupTypeId == 3 &&
                                    <Table className='web-common' aria-label="simple table">
                                        <TableBody>
                                            <TableRow>
                                                <TableCell >Current Sourced Bookings/Day</TableCell>
                                                <TableCell >#{ProjectedData.PerDayBKGS}</TableCell>
                                            </TableRow>
                                            {/* MAKE CHANGES HERE */}
                                            {sumDRR() > 0 &&
                                                <TableRow>
                                                    <TableCell colSpan={2}>
                                                        <Accordion>
                                                            <AccordionSummary
                                                                expandIcon={<ExpandMoreIcon />}
                                                                aria-controls="panel1a-content"
                                                                id="panel1a-header"
                                                            >
                                                                <table className="motorIncentive-header">
                                                                    <tbody>
                                                                        <tr>
                                                                            <td>Required Sourced Bookings/day to justify Cost and make CJ Incentive</td>
                                                                            <td>#{sumDRR().toLocaleString('en-IN')}</td>
                                                                        </tr>

                                                                    </tbody>
                                                                </table>
                                                            </AccordionSummary>
                                                            <AccordionDetails>
                                                                <table className="motorIncentive-content">
                                                                    <tbody>
                                                                        {bindDRR("CurrentProjection")}
                                                                    </tbody>
                                                                </table>
                                                            </AccordionDetails>
                                                        </Accordion>


                                                    </TableCell>

                                                </TableRow>
                                            }

                                            {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>
                                                    <TableCell>Required Sourced Bookings/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></TableCell>
                                                    <TableCell >#{Math.ceil(ProjectedData.NextSlabPerDayBKGS)}</TableCell>
                                                </TableRow>}

                                            {/* {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
      <TableRow>
        <TableCell>Required Sourced Bookings/day to justify Cost and make CJ incentive </TableCell>
        <TableCell >#{Math.round((ProjectedData.MinBKGSRequired - ProjectedData.BKGSTillNow) / ProjectedData.NextWorkingDays * 10) / 10}</TableCell>
      </TableRow>} */}

                                        </TableBody>
                                    </Table>}
                            </TableContainer>
                            <div className="caption note"><img src="/images/incentive/note.svg" />
                                <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                                <ul>
                                    <li>Projections does not include booking incentives, quality dampners and multiplier etc.</li>
                                    <li>Actual calculations may differ from projections.</li>

                                </ul>
                            </div>
                        </Grid>
                    </Grid>

                </div>




            </Grid>
        </>
    )

}

export default withSnackbar(motor);

