import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";

import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import Typography from '@material-ui/core/Typography';


export default function Rewards(props) {


  var slider_rewards = {
    autoplay:false,
    dots: true,
    infinite: false,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  return (


    <Grid item md={3} xs={12} className="rewards">
      <h5>
        Rewards {/*  <a className="viewall">View All</a> */}
      </h5>
      <Slider {...slider_rewards}>
        <div className="bg orange">
          <Card  className="card">
            <CardContent>
              <div className="slide">
                <Typography variant="h5" component="h2">
                  Lottery tickets to win a dream house
                </Typography>
              
                <Typography variant="body2" component="p">
                  Lottery tickets to win the Dream house in Delhi/ NCR worth upto Rs 25 lakhs.
                </Typography>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="bg blue">
          <Card  className="card">
            <CardContent>
              <div className="slide">
                <Typography variant="h5" component="h2">
                  Assured cash prices
                </Typography>
              
                <Typography variant="body2" component="p">
                  Assured cash rewards upto Rs 5 lakhs.
                </Typography>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="bg yellow">
          <Card  className="card">
            <CardContent>
              <div className="slide">
                <Typography variant="h5" component="h2">
                  Goodies
                </Typography>
              
                <Typography variant="body2" component="p">
                  Jeeto Apna Ghar Customized Goodies to superstars.
                </Typography>
              </div>
            </CardContent>
          </Card>
        </div>
        <div className="bg green">
          <Card  className="card">
            <CardContent>
              <div className="slide">
                <Typography variant="h5" component="h2">
                Other rewards
                </Typography>
              
                <Typography variant="body2" component="p">
                  Recognition Events for JAG top performing superstars.
                </Typography>
              </div>
            </CardContent>
          </Card>
        </div>

      </Slider>

    </Grid>

  );
}
