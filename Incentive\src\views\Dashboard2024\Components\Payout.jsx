import React, { useState, useContext, useEffect } from "react";
import { Grid, Tooltip } from "@mui/material";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { DashboardContext } from "../Context/Context";
import { GetCommonData } from "../../../common/CommonAction";
import { rupeeConverter, numberToWords, getUserDetails, convertToIndianNumbering, roundOffToTwo } from '../Utility/Utility';

const Payout = () => {

    const popupHandle = useContext(DashboardContext);

    const [toggles, setToggles] = useState([]);
    const [payout, setPayout] = useState(null);

    let rupee = rupeeConverter();
    let convertToIndianNumber= convertToIndianNumbering();

    useEffect(() => {

        // let body={
        //     eCode: "PW00000" || popupHandle.agentDetails.EmpId,
        //     monthYear: "NOV2023" || popupHandle.monthChosen,
        //     userid: 8223
        // }

        let body = {
            eCode: popupHandle.agentDetails && popupHandle.agentDetails.EmpId,
            monthYear: popupHandle.monthChosen,
            userid: getUserDetails('UserId')
        }
        let data = { EndPoint: "payout", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "term", body: JSON.stringify(body) };
        // let data = { EndPoint: "payout", Method: "POST", AgentId: popupHandle.agentDetails.AgentId, RequestTo: "term", body: JSON.stringify(body) };
        GetCommonData(
            "POST", data
            , (errorStatus, data) => {
                if (!errorStatus) {

                    setPayout(data);
                }
                else{
                    setPayout(null);
                }
            });

      
    }, [popupHandle.monthChosen])

    const handleClick = (e) => {
        popupHandle.handlePopupClick(e);
    }

    const handleToggle = (id) => {
        if (toggles.includes(id)) {
            let tempToggles = toggles.filter((key) => key != id)
            setToggles(tempToggles);
        }
        else {
            setToggles([...toggles, id]);
        }
    }

    const getClassName = (value) => {
        if (value > 0) {
            return 'positive'
        }
        else if (value < 0) {
            return 'Negative'
        }
        return ''
    }

    return (
        <>
            <Grid item md={4} sm={4} xs={12}>
                {
                    payout && 
                    <div className="box2">
                        <h4>Payout (Payouts as per performance)</h4>

                        <h5>Total Incentive Payout  {popupHandle.monthChosen && <> (In {popupHandle.monthChosen.slice(0, 3) + '`' + popupHandle.monthChosen.slice(-2)} month) </>}</h5>
                        {payout['totalIncentivePayout'] &&
                            <>
                                <h1><img src="/images/TermDashboard/rupee.svg" />{convertToIndianNumber.format(payout['totalIncentivePayout']['value']) || '-'}
                                </h1>
                                {/* <p>{numberToWords(parseInt(payout['totalIncentivePayout']['value'] || 0))}</p> */}
                            </>
                        }
                        <ul className="incentive-box">
                            {payout['incentive'] &&
                                <li><span> {payout['incentive']['title'] || 'Incentive'}
                                    <Tooltip title="Final Incentive = MAX(Cost Justification Incentive, Booking Incentive)" arrow
                                        placement="bottom"
                                    >
                                        <i className="fa fa-info-circle"></i>
                                    </Tooltip>
                                </span> {rupee.format(payout['incentive']['value']) || '-'}</li>
                            }
                            {payout['netArrearsClawback'] &&

                                <li>
                                    <span>{payout['netArrearsClawback']['title'] || 'Net Arrear/Clawback'} &nbsp;
                                        { payout['arrearsCalcFlag']==1?  (popupHandle.calculationEnabler['arrearClawBack'] && <a id="SeeCal2" onClick={handleClick}>(See Calculation)</a>):null}
                                    </span> {rupee.format(payout['netArrearsClawback']['value']) || '-'}
                                </li>
                            }
                            {payout['otherComponent'] &&
                                <>
                                    <li className={getClassName(payout['otherComponent']['value'])}><span>{payout['otherComponent']['title'] || 'Other Component'} <ArrowDropDownIcon style={{cursor:'pointer'}} onClick={() => handleToggle(1)} />
                                    </span>{rupee.format(payout['otherComponent']['value']) || '-'}
                                    </li>
                                    {toggles.includes(1) && <div className="DropdownToogle">
                                        <ul>
                                            {
                                                payout['fosVisitAllowance'] &&
                                                <li className={payout['fosVisitAllowance']['value']?'positive':''}><span>{payout['fosVisitAllowance']['title'] || 'FOS Visit Allowance'}  &nbsp; {payout['fosVisitAllowance']['value']!=0 && <a id="popup3" onClick={handleClick}>(See Calculation)</a>}</span> {rupee.format(payout['fosVisitAllowance']['value']) || '-'}</li>
                                            }
                                            {
                                                payout['crossSellIncentive'] &&
                                                <li className={payout['crossSellIncentive']['value']?'positive':''}><span> {payout['crossSellIncentive']['title'] || 'CrossSell'}
                                                </span>{rupee.format(payout['crossSellIncentive']['value']) || '-'}</li>
                                            }
                                            {
                                                payout['nriTeamIncentive'] &&
                                                <li className={payout['nriTeamIncentive']['value']?'positive':''}><span>{payout['nriTeamIncentive']['title'] || 'NRI team Incentive'}</span>{rupee.format(payout['nriTeamIncentive']['value']) || '-'}</li>
                                            }
                                            {
                                                payout['upsellIncentive'] &&
                                                <li className={payout['upsellIncentive']['value']?'positive':''}><span>{payout['upsellIncentive']['title'] || 'Upsell Incentive'}
                                                </span>{rupee.format(payout['upsellIncentive']['value']) || '-'}
                                                </li>
                                            }
                                            {
                                                payout['qualityDeduction'] &&
                                                <li className={payout['qualityDeduction']['value']?'Negative':''}><span>{payout['qualityDeduction']['title'] || 'Quality Deduction'} {payout['qualityScore'] && <Tooltip title={(payout['qualityScore']['title'] || 'Quality Score') + ': ' + ((roundOffToTwo(payout['qualityScore']['value'])  + '%') || '-')} arrow
                                                    placement="bottom">

                                                    <i className="fa fa-info-circle"></i>

                                                </Tooltip>}
                                                </span> - {rupee.format(payout['qualityDeduction']['value']) || '-'}
                                                </li>
                                            }
                                            {
                                                payout['warningLetterDeduction'] &&
                                                <li className={payout['warningLetterDeduction']['value']?'Negative':''}><span>{payout['warningLetterDeduction']['title'] || 'Warning Letter Deduction'}
                                                </span> - {rupee.format(payout['warningLetterDeduction']['value']) || '-'}
                                                </li>
                                            }
                                            {
                                                payout['crtDeduction'] &&
                                                <li className={payout['crtDeduction']['value']?'Negative':''}><span>{payout['crtDeduction']['title'] || 'CRT Deduction'}
                                                </span> - {rupee.format(payout['crtDeduction']['value']) || '-'}
                                                </li>
                                            }
                                            {
                                                payout['misSellDeduction'] &&
                                                <li className={payout['misSellDeduction']['value']?'Negative':''}><span>
                                                    {/* {payout['misSellDeduction']['title'] || 'MisSell Deduction'} &nbsp; {popupHandle.calculationEnabler['missell'] && <a id="popup4" onClick={handleClick}>(See Calculation)</a>} */}
                                                    {payout['misSellDeduction']['title'] || 'MisSell Deduction'} &nbsp; {payout['misSellDeduction']['value']!=0 && popupHandle.calculationEnabler['missell'] && <a id="popup4" onClick={handleClick}>(See Calculation)</a>}
                                                </span>
                                                    - {rupee.format(payout['misSellDeduction']['value']) || '-'}
                                                </li>
                                            }
                                            {
                                                payout['abDeduction'] &&
                                                <li className={payout['abDeduction']['value']?'Negative':''}><span>{payout['abDeduction']['title'] || 'AB Deduction'}
                                                </span> - {rupee.format(payout['abDeduction']['value']) || '-'}
                                                </li>
                                            }

                                        </ul>
                                    </div>}
                                </>
                            }

                        </ul>
                    </div>
                }
            </Grid>
        </>
    )
}

export default Payout;