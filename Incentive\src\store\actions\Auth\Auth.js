import * as actionTypes from '../../ActionsTypes';
import * as actions from '../../actions';
import * as services from '../../../services';

export const setAuth = (payload) => {
    return {
        type: actionTypes.AUTH_SET,
        payload: payload
    };
};

export const setAuthSuccess = (payload) => {
    return {
        type: actionTypes.AUTH_SUCCESS,
        payload
    }
}

export const authLogout = () => {
    return {
        type: actionTypes.AUTH_LOGOUT
    };
};