import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import Typography from '@material-ui/core/Typography';


export default function KnowMore(props) {


  
  return (
    <Grid container spacing={3} className="homeSection">
<Grid item md={5} xs={12}>
      <h2>
      Pura hoga har Sapna<br/> Jab <b>Ghar Hoga Apna</b>
      </h2>
      <p className="textMsg">Jeeto Apna Ghar is the biggest and one of its kind contest that PB has launched where agents have an opportunity to win their very own house in Delhi/ NCR and cash reward among other amazing privileges.</p>
      <ul>
        <li><img src="/images/homeicon.svg"/> 5 Houses</li>
        <li><img src="/images/salary.png"/> Guaranteed Cash Rewards</li>
      </ul> 
      <div className="btnSection">
      <button>Know More</button>  
      {/* <button>See Position</button>   */}
      </div>     
        </Grid>

         <Grid item md={7} xs={12}>
           <div className="text-right">
             <img src="/images/home.png"/>
           </div>
         </Grid>

  </Grid>

  );
}
