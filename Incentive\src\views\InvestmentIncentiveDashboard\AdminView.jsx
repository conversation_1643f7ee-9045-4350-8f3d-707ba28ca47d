import React, { useState } from "react";
import Box from '@mui/material/Box';
import TextField from '@mui/material/TextField';
import { Button, Grid } from '@mui/material';
import { Redirect, Link } from 'react-router-dom'


const AdminView = () => {

    const [empId, setEmpId] = useState("");


    const handleSearch = () => {
        if (empId) {
            return <Redirect to='/NewDashboard' />
        }
        else {
            alert("Please Enter EmployeeId");
        }
    }

    return (
        <>
            <Box
                maxHeight="85vh"
                width='auto'
                height="auto"
                overflow="auto"
                padding="20px"
                sx={{
                    marginRight: '20px',
                    borderRadius: '10px',
                    border: '#ADD8E6', bgcolor: 'background.paper',
                    marginTop: '20px',
                    marginLeft: '20px',
                }}
                noValidate
                autoComplete="off"
            >
                <h4>Enter EmployeeId</h4>

                <TextField
                    required
                    id="empId"
                    value={empId}
                    onChange={(e) => setEmpId(e.target.value)}

                />

                <br />

                <br />
                {
                    empId.length > 0 &&
                    <Grid container spacing={1}>
                        <Grid item xs={1} sm={1}>

                            <Link to={{
                                pathname: "/InvestmentDashboard",
                                
                                state: {
                                    empId: empId,
                                },
                            }}
                            
                            > Search </Link>
                            
                            
                        </Grid>
                    </Grid>
                }
            </Box>
        </>
    )
}
export default AdminView;