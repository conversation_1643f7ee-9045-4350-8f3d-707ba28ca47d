import React, { useState } from "react";
import {
  Grid,
} from "@material-ui/core";






export default function AboutTheContest(props) {
  const [images, setImages] = useState(['/images/Gallery/g1.jpg', '/images/Gallery/g3.jpg', '/images/Gallery/g2.jpg', '/images/Gallery/g4.jpg'])
  const [current, setCurrent] = useState(null);
  const [photoIndex, setPhotoIndex] = useState(null);


  const handleImageClick = (image, index) => {
    //alert('image click');
    setCurrent(image);
    setPhotoIndex(index);
  }

  const handleCloseModal = (e) => {
    e && e.preventDefault();
    setCurrent(null);
  }



  var slider_gallery = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 900,
    slidesToShow: 2,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  return (

    <Grid container spacing={3}  className="ContestSection" id="AboutContest">  
    <Grid item md={6} xs={12}>
     <img src="/images/keyimg.png"/>
     
    </Grid>
    <Grid item md={6} xs={12}>
      <h2>
      About the <b>Contest</b> {/*  <a className="viewall">View All</a> */}
      </h2>
   <p className="textMsg">Jeeto Apna Ghar is the biggest and one of its kind contest that PB has launched where agents have an opportunity to win their very own house in Delhi/ NCR and cash reward.
The qualified superstars will be entitled to various privileges that include perks like customized goodies, getting featured on the Policybazaar E-Wall of fame, and monthly newsletters. 
The superstars will also be a part of quarterly recognition events with the management where the talent is appreciated and rewarded.
</p>
   <Grid container spacing={2}>
   <Grid item md={4} xs={12}>
     <div className="contestBox">
       <h3>5 Houses</h3>
       <p>Opportunity to win "Apna Ghar" worth Rs 25 lakhs in Delhi/NCR</p>
     </div></Grid>
     <Grid item md={4} xs={12}>
     <div className="contestBox">
     <h3 className="textgreen">Lottery Tickets</h3>
       <p>Lottery Tickets based on Superstar's performance vs last year to secure the great win.</p>
     </div></Grid>
     <Grid item md={4} xs={12}>
     <div className="contestBox">
     <h3 className="textblue">Cash Reward</h3>
       <p>Performance-based guaranteed Cash reward up to Rs 3Lakh</p>
     </div></Grid>
   </Grid>
  
    </Grid>
</Grid>
  );
}
