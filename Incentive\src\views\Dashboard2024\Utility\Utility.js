import React from 'react';
import {CSVLink} from 'react-csv';
import * as XLSX from 'xlsx';

const translations = new Map([
  [10000000, 'Crore'],
  [100000, 'Lakh'],
  [1000, 'Thousand'],
  [100, 'Hundred'],
  [90, '<PERSON>ty'],
  [80, 'Eighty'],
  [70, '<PERSON>ty'],
  [60, 'Sixty'],
  [50, 'Fifty'],
  [40, 'Forty'],
  [30, 'Thirty'],
  [20, 'Twenty'],
  [19, 'Nineteen'],
  [18, 'Eighteen'],
  [17, 'Seventeen'],
  [16, 'Sixteen'],
  [15, 'Fifteen'],
  [14, 'Fourteen'],
  [13, 'Thirteen'],
  [12, 'Twelve'],
  [11, 'Eleven'],
  [10, 'Ten'],
  [9, 'Nine'],
  [8, 'Eight'],
  [7, 'Seven'],
  [6, 'Six'],
  [5, 'Five'],
  [4, 'Four'],
  [3, 'Three'],
  [2, 'Two'],
  [1, 'One'],
]);

export const handleDownload=(data, name)=>{


  let sheet_Data= []
 
  sheet_Data = XLSX.utils.json_to_sheet(data) 
 
  

  var wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, sheet_Data, 'Sheet')

 
  if(name){
      XLSX.writeFile(wb, `${name}.xlsx`);
  }
  else {
      XLSX.writeFile(wb, "Download.xlsx"); 
  }

}

export const rupeeConverter=()=>{
    let rupee= new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        maximumFractionDigits: 0
      });
    return rupee;
}

export const convertToIndianNumbering=()=>{
  let number = new Intl.NumberFormat('en-IN', {
   
    currency: 'INR',
    maximumFractionDigits: 0
  });
  return number;
}

export const exportTableDataToCSV=()=>{

  return(
    <>
    <CSVLink className="downloadbtn" filename="my-file.csv" data={csvData}>
        Download
      </CSVLink>
    </>
  )
}


export const numberToWords=(num)=>{
  if (num === 0) {
    return 'Zero';
  }
  
  if (num <= 20) {
    return translations.get(num);
  }
  
  let result = [];
  
  for (let [value, translation] of translations) {
    const times = Math.floor(num / value);
    
    if (times === 0) {
      continue;
    }
    
    num -= times * value;
    if (times === 1 && value >= 100) {
      result.push('One', translation);
      continue;
    }
    
    if (times === 1) {
      result.push(translation);
      continue;
    }
    
    result.push(numberToWords(times), translation);
  }
  
  if(result[result.length-2]=='Hundred' || result[result.length-3]=='Hundred')
  {
      let indexOfHundred = result.indexOf('Hundred');
      let array= [
          ...result.slice(0,indexOfHundred+1),
          'And',
          ...result.slice(indexOfHundred)
      ]   ;
      result= array;
  }
  return result.join(' ') + ' Rupees Only' ;
}


export const getCookie=(cname)=> {
  let name = cname + "=";
    let decodedCookie = decodeURIComponent(document.cookie);
    let ca = decodedCookie.split(';');
 
    for (let i = 0; i < ca.length; i++) {
        let c = ca[i];
        while (c.charAt(0) === ' ') {
            c = c.substring(1);
        }
        if (c.indexOf(name) === 0) {
            // console.log("cookie : ",name.length, c.length)
            return c.substring(name.length, c.length);
        }
    }
    return "";
}

export const getUserDetails=(key)=>{

  let userDetailsEncoded =  getCookie('User') || null;
 try{
  let userDetails =  JSON.parse(window.atob(userDetailsEncoded));
  if(userDetails)
    {
      return userDetails[key];
    }
    return null;
 }
 catch(e){
  return null;
 }
}

export const roundOffToTwo=(num)=>{
  let roundedNum = parseFloat(num.toFixed(2));
  return roundedNum
}


export const parseCurrencyString = (currencyString) => {

  let returnvalue=0;

  
  try{
  const numericString = currencyString.replace(/[^\d.-]/g, '');
  
  returnvalue = parseFloat(numericString);

  return returnvalue
  }
  catch(e)
  {
    return returnvalue
  }
  
};
