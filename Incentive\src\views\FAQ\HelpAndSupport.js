import React, { useEffect, useState, Fragment } from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import Accordion from "@material-ui/core/Accordion";
import AccordionDetails from "@material-ui/core/AccordionDetails";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import Typography from "@material-ui/core/Typography";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import InputBase from "@material-ui/core/InputBase";
import SearchIcon from "@material-ui/icons/Search";
import { IconButton } from "@material-ui/core";
import AppBar from "@material-ui/core/AppBar";
import Tabs from "@material-ui/core/Tabs";
import Tab from "@material-ui/core/Tab";
import Box from "@material-ui/core/Box";
import IncentiveFaqs from "./IncentiveFaqs";

function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          {children}
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `simple-tab-${index}`,
    "aria-controls": `simple-tabpanel-${index}`,
  };
}


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: "absolute",
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: "2px solid #000",
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));

const HelpAndSupport = (props) => {
  const classes = useStyles();
  const [expanded, setExpanded] = React.useState(false);
  const [value, setValue] = React.useState(0);
  const [firstTabNewValue, setFirstTabValue] = React.useState(0);

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };

  const handleTabChange = (event, newValue) => {
    setValue(newValue);
  };
  const handleFirstTabChange = (event, firstTabNewValue) => {
    setFirstTabValue(firstTabNewValue);
  };



  return (
    <div className="faq-section">
      <h4>Help & Support</h4>
      {/* <div className="searchbar">
    <InputBase className={classes.input}  placeholder="Describe your issue.."  inputProps={{ 'aria-label': 'Describe your issue' }}
/>
<IconButton type="submit" className={classes.iconButton} aria-label="search">
<SearchIcon />
</IconButton>
</div> */}
      <AppBar position="static">
        <Tabs
          value={firstTabNewValue}
          onChange={handleFirstTabChange}
          aria-label=""
        >
          <Tab label="FAQs" {...a11yProps(0)} />
          {/* <Tab label="Policies" {...a11yProps(1)} /> */}
        </Tabs>
      </AppBar>

      <TabPanel value={firstTabNewValue} index={0}>
        <AppBar position="static" className="faq-tab">
          <Tabs value={value} onChange={handleTabChange}>
            <Tab label="Incentives" {...a11yProps(0)} />
            {/* <Tab label="All" {...a11yProps(1)} />
    <Tab label="Contests" {...a11yProps(2)} /> */}
          </Tabs>
        </AppBar>
        
        <TabPanel value={value} index={0} className="faqtab-data">
            <IncentiveFaqs handleChange={handleChange} expanded={expanded} />
        </TabPanel>

        <TabPanel value={value} index={1}>
          <Accordion
            expanded={expanded === "panel2"}
            onChange={handleChange("panel2")}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel2bh-content"
              id="panel2bh-header"
            >
              <Typography className={classes.heading}>Users</Typography>
              <Typography className={classes.secondaryHeading}>
                You are currently not an owner
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>
                Donec placerat, lectus sed mattis semper, neque lectus feugiat
                lectus, varius pulvinar diam eros in elit. Pellentesque
                convallis laoreet laoreet.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </TabPanel>
        <TabPanel value={value} index={2}>
          <Accordion
            expanded={expanded === "panel3"}
            onChange={handleChange("panel3")}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel3bh-content"
              id="panel3bh-header"
            >
              <Typography className={classes.heading}>
                Advanced settings
              </Typography>
              <Typography className={classes.secondaryHeading}>
                Filtering has been entirely disabled for whole web server
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>
                Nunc vitae orci ultricies, auctor nunc in, volutpat nisl.
                Integer sit amet egestas eros, vitae egestas augue. Duis vel est
                augue.
              </Typography>
            </AccordionDetails>
          </Accordion>
          <Accordion
            expanded={expanded === "panel1b"}
            onChange={handleChange("panel1b")}
          >
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls="panel1bbh-content"
              id="panel1bbh-header"
            >
              <Typography className={classes.heading}>
                General settings
              </Typography>
              <Typography className={classes.secondaryHeading}>
                I am an accordion
              </Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Typography>
                Donec placerat, lectus sed mattis semper, neque lectus feugiat
                lectus, varius pulvinar diam eros in elit. Pellentesque
                convallis laoreet laoreet.
              </Typography>
            </AccordionDetails>
          </Accordion>
        </TabPanel>
      </TabPanel>

      <TabPanel value={firstTabNewValue} index={1}>
        Policies
      </TabPanel>
    </div>
  );
};

export default HelpAndSupport;