.ape-slider{width: 100%;margin:auto;color: #38374B;position: relative;z-index: 1001;max-width: 500px;
    @media (max-width:1024px) and (min-width:320px) {
        padding: 0px 10px;max-width: 100%;
    }
}

.ape-slider .d-flex{justify-content: space-between;}
.ape-slider ul.d-flex li{width: 75%;display: flex;justify-content: flex-end;align-items: center;
    @media (max-width:1024px) and (min-width:320px){
    width: 100%;padding-top: 37px;margin-right: 121px;
    } 
}

.ape-slider .range-slider p {font-weight: 600;font-size: 14px;line-height: 24px;margin-bottom: 25px;
    display: inline-block;
    @media (max-width:1024px) and (min-width:320px) {
        margin-bottom: 60px;margin-left: -10px
    }
}
.ape-slider .value-box p.required-sourcing {border: 0;position: absolute;top: 4px;left: 0;margin: auto;
    @media (max-width:1024px) and (min-width:320px){
    top: 0;
    } 
}
// .ape-slider .value-box ul{background: #fff;border: 1px solid #2E5DFF;border-radius: 4px;
// font-weight: 500;font-size: 18px;line-height: 21px;padding: 6px 16px 6px 8px;width: 150px;
// margin: auto;position: absolute;top: 0;right: 0;
//     @media (max-width:1024px) and (min-width:320px){
//     padding: 6px 8px 6px 20px;top: 30px
//     } 
// }
.ape-slider .value-box input[type="text"]{background: #fff;border: 1px solid #2E5DFF;
border-radius: 4px;font-weight: 500;font-size: 18px;line-height: 21px;padding: 6px 10px;width: 150px;
margin: auto;position: absolute;top: 0;right: 0;text-align: right;
    @media (max-width:1024px) and (min-width:320px) {
        top: 32px;right: 0px
    }
}

.ape-slider .value-box h2 {font-weight: 500;font-size: 20px;z-index: 1;margin: 3px 0px 0px;
    @media (max-width:1024px) and (min-width:320px) {
    margin: 0;
    }     
}
.ape-slider .range-slider {max-width: 100%;width: 100%;margin: 25px auto;
    @media (max-width:1024px) and (min-width:320px) {
        max-width: 100%;display: inline-block;margin: 18px auto;
    }
}

.ape-slider .range-slider span.MuiSlider-sizeMedium {color: #2E5DFF;height: 2px;padding: 0;
    box-sizing: border-box;position: relative;
    @media (max-width:1024px) and (min-width:540px) {
        width: 100%;
    } 
    @media (max-width:539px) and (min-width:320px) {
        width: 100%;
    }
    
}

.ape-slider .range-slider span.MuiSlider-rail {
    opacity: 1;
    left: 0;
    width: 100%;

    // @media (max-width:1024px) and (min-width:540px) {
    //     width: 100%;
    //     left: 0;
    // }
    @media (max-width:539px) and (min-width:320px) {
        width: 108%;left: -10px;
    }
    
}

.ape-slider .range-slider span.MuiSlider-thumb:before {
    position: absolute;
    content: '';
    top: -6px;
    bottom: auto;
    left: -14px;
    right: auto;
    margin: auto;
    background: url(/images/jag/loading.svg);
    background-repeat: no-repeat;
    height: 13px;
    width: 40px;
    background-position: center;
    background-color: #fff;
    border-radius: 0;
    box-shadow: none !important;
    -webkit-box-shadow: none !important;
    display: block;
}

.ape-slider .range-slider .MuiSlider-thumb,
.ape-slider .range-slider .MuiSlider-thumb::after {
    box-shadow: none !important;
    width: 12px;
    height: 12px;
    background: #2E5DFF;
    display: block
}

.ape-slider .range-slider span.MuiSlider-valueLabel {
    background: transparent;
    color: #A6A5BC;
    padding: 0;
    border-radius: 0;
    font-size: 12px;
    line-height: 17px;
    font-weight: 500;
    top: 35px;
    display: block;
    right: -15px;
    @media (max-width:1024px) and (min-width:320px) {
    right: -18px;
    }    
}

.ape-slider .range-slider span.MuiSlider-valueLabel::before {
    display: none;
}

.ape-slider p.range-value {
    margin: 0px 0px 0px -10px;
    display: block;
}

.ape-slider p.range-value span,
.ape-slider .range-slider.percentage-slider .MuiSlider-markLabel {
    color: #A6A5BC;
    font-weight: 500;
    font-size: 12px;
    line-height: 18px;
    display: block;
    padding: 5px 0px;
    margin: 0;
}

.ape-slider .range-slider.percentage-slider .MuiSlider-markLabel {
    padding: 0;

    @media (max-width:1024px) and (min-width:320px) {
        top: 28px;
    }
}

.ape-slider .range-slider.percentage-slider span.MuiSlider-valueLabel {
    right: 0;
}