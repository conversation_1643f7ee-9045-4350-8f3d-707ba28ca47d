import React from "react";

const RewardsSlab = ({  isJagType, isEmergingType, agentData }) => {

  const bu = agentData.BUId || '';
  // console.log("The JagType us ",isJagType, bu)

  return (
    <div className="text-center">
      {
        isJagType && <>
        {bu === 58 && <img style={{height:'1400px'}} src="/images/jag2024/JAGHealth.png" />}
        {bu === 59 && <img style={{height:'1400px'}} src="/images/jag2024/JAGHealthRenewalR2.png" />}
        {bu === 60 && <img style={{height:'1400px'}} src="/images/jag2024/JAGHealthRenewalR1.png" />}
        {bu === 61 && <img style={{height:'1400px'}} src="/images/jag2024/JAGTerm.png" />}
        {bu === 62 && <img style={{height:'1400px'}} src="/images/jag2024/JAGHome.png" />}
        {bu === 63 && <img style={{height:'1400px'}} src="/images/jag2024/JAGInvestmentDom.png" />}
        {bu === 64 && <img style={{height:'1400px'}} src="/images/jag2024/JAGInvestmentNRI.png" />}
        {bu === 65 && <img style={{height:'1400px'}} src="/images/jag2024/JAGMotorFresh.png" />}
        {bu === 67 && <img style={{height:'1400px'}} src="/images/jag2024/JAGMotorRenewal.png" />}
        {bu === 69 && <img style={{height:'1400px'}} src="/images/jag2024/SMEJag.png" />}
        {bu === 70 && <img style={{height:'1400px'}} src="/images/jag2024/JAGInvestmentDom.png" />}
        {bu === 68 && <img style={{height:'1400px'}} src="/images/jag2024/JAGMotorHoM.png"/>}
        {/* {bu === 'Motor NewCar' && <img src="/images/jag2024/JAGMotorNewCar.png"/>} */}
        </>
      } 

      {
        isEmergingType && <>
        {bu === 58 && <img style={{height:'1400px'}} src="/images/jag2024/ESHealth.png" />}
        {bu === 59 && <img style={{height:'1400px'}} src="/images/jag2024/ESHealthRenewalR2.png" />}
        {bu === 60 && <img style={{height:'1400px'}} src="/images/jag2024/ESHealthRenewalR1.png" />}
        {bu === 61 && <img style={{height:'1400px'}} src="/images/jag2024/ESTerm.png" />}
        {bu === 62 && <img style={{height:'1400px'}} src="/images/jag2024/ESHome.png" />}
        {bu === 63 && <img style={{height:'1400px'}} src="/images/jag2024/ESInvestmentDom.png" />}
        {bu === 64 && <img style={{height:'1400px'}} src="/images/jag2024/ESInvestmentNRI.png" />}
        {bu === 65 && <img style={{height:'1400px'}} src="/images/jag2024/ESMotorFresh.png" />}
        {bu === 67 && <img style={{height:'1400px'}}  src="/images/jag2024/ESMotorRenewal.png" />}
        {bu === 69 && <img style={{height:'1400px'}} src="/images/jag2024/ESSME.png" />}
        {bu === 70 && <img style={{height:'1400px'}} src="/images/jag2024/ESInvestmentDom.png" />}
        {bu === 68 && <img style={{height:'1400px'}} src="/images/jag2024/ESMotorHoM.png"/>}
        {bu === 66 && <img  style={{height:'1400px'}}src="/images/jag2024/ESMotorNewCar.png"/>}
        </>
      }
    
  </div>
  );
};

export default RewardsSlab;