import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";

import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import <PERSON><PERSON>ield from '@material-ui/core/TextField';
import Button from '@material-ui/core/Button';
import * as services from "../../services";


const useStyles = makeStyles((theme) => ({
  root: {
  },
  media: {
  },
}));


export default function Feedback(props) {
  const urlParams = useParams();
  const classes = useStyles();

  const onFormSubmit = (e) => {
    e.preventDefault();
    // if (!userId) {
    //   return;
    // }
    let userId = urlParams.Token;
    ////debugger;
    if (isNaN(userId)) {
      userId = atob(userId);
    }

    const _e = e;
    var name = e.target.elements.yourname.value
    var email = e.target.elements.youremail.value
    var feedback = e.target.elements.feedback.value
    console.log(e.target.elements.yourname.value, e.target.elements.youremail.value,
      e.target.elements.feedback.value);
    if (name && email && feedback) {
      var json = {
        "AgentId": userId,
        "Name": name,
        "email": email,
        "feedback": feedback
      }

      

      services
        .API_POST(`Jag/InsertJagFeedback`, json)
        .then(response => {
          if (response && response != "[]") {
            alert("Feedback Submitted");
            document.getElementsByName('feedbackform')[0].reset();
          }
        })
        .catch((err) => {
          console.log("Error", err);
        });
    } else {
      alert("Please enter all fields.");
    }

  }

  return (
    <Grid item md={3} xs={12}>
      <h5>Feedback</h5>
      <Card className={classes.root} className="card">
        <CardContent>
          <form className={classes.root} name="feedbackform" onSubmit={onFormSubmit} noValidate autoComplete="off">
            <div className="form-group">
              <TextField name="yourname" fullWidth className="form-control" placeholder="Your Name" label="Your Name" variant="outlined" />
            </div>
            <div className="form-group">
              <TextField name="youremail" fullWidth className="form-control" placeholder="Your Email" label="Your Email" variant="outlined" />
            </div>
            <div className="form-group">
              <TextField name="feedback" fullWidth className="form-control" multiline rows="2" placeholder="Type Your Feedback Here" label="Type Your Feedback Here" variant="outlined" />
            </div>
            <Button type="submit" fullWidth variant="contained" color="primary">Send Your Message</Button>
          </form>
        </CardContent>
      </Card>
    </Grid>

  );
}
