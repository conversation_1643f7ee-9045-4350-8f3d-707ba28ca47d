import React, { Fragment, useEffect, useState } from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../Loader";
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Tooltip from '@material-ui/core/Tooltip';
import Zoom from '@material-ui/core/Zoom';
import moment from "moment";



const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },

  table: {
    border: "2px solid #4e93ff",
    margin: "10px auto",
  },
  tblCell: {
    "&.MuiTableCell-body": {
      color: "#0164ff"
    },
  },
  bold: {
    fontWeight: "700",
    color: "#000000 !important"
  },
  underline: {
    fontDecoration: "underline"
  },
  blueRow: {
    background: "#e5efff"
  },
  memberType: {
    textTransform: "capitalize"
  },
  redText: {
    color: "red"
  }
});
const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    color: "#0164ff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});


const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

const ProjectedRewardCalculationCriteria = withStyles(styles)((props) => {
  const { show, handleClose, superGroupId, productId, date, classes, AvailableData } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    setIsLoading(false);
  }, [props]);

  const renderTooltipdata = (item) =>{
    let res = []
    
//     if(item.ProductId == 115){
//       res.push([
//         <Fragment>
//         <div>
//         Issued APE will be updating on the daily basis till 31st May 2022 <br />
//         <br />

// Issued APE breakdown as per plan category will be as follows : <br />
// <br />
// CG : {item.CG? item.CG: ""}<br />
// Trad : {item.TRAD? item.TRAD:""}<br />
// Ulip : {item.ULIP? item.ULIP:""}<br />
// <br />
// Note : Annuity APE is considered under Ulip for point calculation
//         </div>
//       </Fragment>
    
//       ])
//     }
//     else{
      res.push([<Fragment>
        <div>
        Issued APE will be updating on the daily basis till 31st May 2022
        </div>
      </Fragment>])
    
      return res


  }


  const showReason = (AvailableData) => {
    let result = null;

    // case 1 : ActualG1 < 20%
    // APE Growth criteria
    // case 2 : ActualG1 > 20% and ActualG2<ActualG1-deduction
    // bkg growth
    // case 3 : ActualG1 > 20% and ActualG2>ActualG1-deduction
    // points 500

    if (AvailableData.CashRewards == 0) {
      if (AvailableData.ActualG1 < AvailableData.MinValue) {
        result = <span className={classes.redText}>According to the current projections, your APE growth criteria is not met</span>;
      }

      if (AvailableData.ActualG1 > AvailableData.MinValue && AvailableData.ActualG2 < (AvailableData.ActualG1 - AvailableData.deduction)) {
        result = <span className={classes.redText}>You are not meeting Bookings Growth Criteria, hence your  Cash Rewards are 0, however, you are eligible to make Lottery tickets since you meet APE Growth Criteria</span>;
      }

      if (AvailableData.ActualG1 > AvailableData.MinValue && AvailableData.ActualG2 > (AvailableData.ActualG1 - AvailableData.deduction) && AvailableData.TotalPoints < 500) {
        result = <span className={classes.redText}>You need minimum 500 points to make rewards</span>;
      }

    }


    return result;
  }

  
  const showMinPoints = (AvailableData) => {
    let minAPE = 0;
    if (AvailableData.CashRewards > 0) {
       return 0;
    }
    //debugger;

    if (AvailableData.ActualG1 > AvailableData.MinValue && AvailableData.ActualG2 < (AvailableData.ActualG1 - AvailableData.deduction)) {
      return 0;
    }


    //if ((AvailableData.ActualG1-AvailableData.deduction) < AvailableData.MinValue) {
      minAPE = (AvailableData.TotalAPE_PrevYear * (1 + (AvailableData.OverAllBUAPEGrowth / 200)) * ((100 + AvailableData.MinValue) / 100)).toFixed(0)
    //}
    // else if ((AvailableData.ActualG1-AvailableData.deduction) > AvailableData.MinValue) {
    //   //minAPE = (AvailableData.TotalAPE_PrevYear * (1 + (AvailableData.OverAllBUAPEGrowth / 200)) + (500 / AvailableData.SlabPoints) * 10).toFixed(0);
    //   minAPE = 0;
    // }
    return minAPE;
  }

  console.log("BU", AvailableData)

  return (
    <div>
      {/* <Button variant="outlined" color="primary" onClick={handleClickOpen}>
        Open dialog
      </Button> */}

      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"md"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
          <strong>Rewards Calculation Details</strong>
          {<span> Last Updated On: {moment().subtract(1,'days').format("DD-MMMM-YYYY")}</span>}
        </DialogTitle>
        <DialogContent className="critiria-popup">
          <div className="popup-inner-box">
            <div className="criteriaHtml">
              <TableContainer >
                <Table className="" size="large">
                  <TableBody>
                    <TableRow>
                      <TableContainer >
                        <Table className={classes.table} size="large">
                          <TableBody>

                          {AvailableData.BU && ["Health Renewal","Motor","Motor Renewal"].indexOf(AvailableData.BU) == -1 &&<TableRow>
                          <TableCell className={classes.tblCell}>Total Sourced (APE/ Booking) FY 21-22<span> </span>
                          
                          {[115, 2].indexOf(AvailableData.ProductId) != -1 && <WrapperTooltip
                          disableFocusListener
                          TransitionComponent={Zoom}
                          placement="top"
                          arrow
                          title= {"Your "+(AvailableData.ProductId == 115? "Combo ": "Addon ") + "booking will be considered for APE sourcing but it will not be counted in Booking Calculation." }
                          classes={{ tooltip: classes.customWidth }}>
                          <i className="fa fa-info-circle"></i>
                        </WrapperTooltip> }</TableCell>


                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {
                                !AvailableData.TotalAPE_CurrYear ? 0 : ((AvailableData.TotalAPE_CurrYear / 1).toLocaleString('en-IN') + "  ")
                              }
                              
                              {AvailableData.TotalBKG_CurrYear? " / "+AvailableData.TotalBKG_CurrYear: ""}</TableCell>
                            </TableRow>}


                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Issued APE FY 21-22 <span> </span>
                              
             <WrapperTooltip
                              disableFocusListener
                              TransitionComponent={Zoom}
                              placement="top"
                              arrow
                              title= {renderTooltipdata(AvailableData)}
                            classes={{ tooltip: classes.customWidth }}>
                  <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
              
              
              
              </TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {
                                !AvailableData.IssuedAPE_CurrYear ? 0 : ((AvailableData.IssuedAPE_CurrYear / 1).toLocaleString('en-IN') + "  ")
                              }</TableCell>
                            </TableRow>
                            <TableRow className={classes.tblCell}>
                              <TableCell className={classes.tblCell}>Issued Bookings FY 21-22 <span> </span>
                             
                        
                        <WrapperTooltip
                          disableFocusListener
                          TransitionComponent={Zoom}
                          placement="top"
                          arrow
                          title= {"Issued Bookings will keep updating on a daily basis till 31st May'2022"}
                          classes={{ tooltip: classes.customWidth }}>
                          <i className="fa fa-info-circle"></i>
                        </WrapperTooltip> 
                        
                              </TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">
                              {
                                !AvailableData.IssuedBKG_CurrYear ? 0 : (AvailableData.IssuedBKG_CurrYear)
                              }</TableCell>
                            </TableRow>
                            {/* <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Total  Issued APE FY 21-22 (This Year) 
                              
                              
                              </TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {
                                !AvailableData.ProjectedAPE_CurrYear ? 0 : ((AvailableData.ProjectedAPE_CurrYear / 100000).toFixed(2) + "  ")
                              }</TableCell>
                            </TableRow> */}
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            <TableRow>
                              <TableCell className={classes.tblCell + ' ' + classes.underline}><em>Overall APE growth of <span className={classes.memberType}>{AvailableData.MemberType}</span> Category agents is {AvailableData.OverAllBUAPEGrowth || 0}% this year, inflation factor becomes</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{
                                !AvailableData.OverAllBUAPEGrowth ? 0 : (AvailableData.OverAllBUAPEGrowth / 2).toFixed(1)
                              }%</TableCell>
                            </TableRow>
                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Issued (APE / Booking) FY 20-21 (Last Year)</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {
                                !AvailableData.TotalAPE_PrevYear ? 0 : ((AvailableData.TotalAPE_PrevYear / 1).toLocaleString('en-IN') + "  ")
                              }
                              {AvailableData.TotalBKG_PrevYear? " / "+AvailableData.TotalBKG_PrevYear: ""}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>Inflated Issued APE FY 20-21 (Last Year)</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {
                                !AvailableData.TotalAPE_PrevYear ? 0 : ((AvailableData.TotalAPE_PrevYear * (1 + (AvailableData.OverAllBUAPEGrowth / 200)) / 1).toLocaleString('en-IN') + "  ")
                              }
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            <TableRow>
                              <TableCell className={classes.tblCell}><em>Annual  Growth (POST INFLATION)</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.ActualG1 || 0}%</TableCell>
                            </TableRow>
                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Annual  Bookings Growth</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.ActualG2 || 0}%</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>Your points per additional lakh of incremental APE
                                {AvailableData && AvailableData.ProductId == 115 && "(CG/Trad/ULIP)"}
                              </TableCell>
                              {AvailableData && AvailableData.ProductId == 115 ?
                                <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.CGSlabPoints || 0}/{AvailableData.TRADSlabPoints || 0}/ {AvailableData.ULIPSlabPoints || 0}</TableCell>
                                :
                                <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.SlabPoints || 0}</TableCell>
                              }


                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            <TableRow>
                              <TableCell className={classes.tblCell}><em> Reward Points</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.TotalPoints || 0}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>{AvailableData.Reason && AvailableData.Reason != "" ? "Reason" : ""}</TableCell>
                              <TableCell className={classes.tblCell} align="right">
                                {
                                  showReason(AvailableData)
                                }

                              </TableCell>
                            </TableRow>
                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}> Tickets
                              <span> </span>
                             
                        <WrapperTooltip
                          disableFocusListener
                          TransitionComponent={Zoom}
                          placement="top"
                          arrow
                          title= {"Your sourcing for FY 21-22 is closed. Your rewards and ticket will keep on updating based on your issuance till May 31, 2022."}
                          classes={{ tooltip: classes.customWidth }}>
                          <i className="fa fa-info-circle"></i>
                        </WrapperTooltip></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.LotteryTicket || 0}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}> Cash Rewards</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {AvailableData.CashRewards || 0}</TableCell>
                            </TableRow>
                            {
                              showMinPoints(AvailableData) > 0 &&
                              <TableRow>
                                <TableCell className={classes.tblCell}>
                                  <span className={classes.redText}>Minimum APE required for complete FY 21-22 to make incentive</span></TableCell>
                                <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">
                                  <span className={classes.redText}>
                                    <i className="fa fa-inr"></i> {(showMinPoints(AvailableData) / 1).toLocaleString('en-IN')}  </span>
                                </TableCell>
                              </TableRow>
                            }
                          </TableBody>
                        </Table>
                      </TableContainer>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </div>

            {/* 
          criteriaList && criteriaList.length > 0 && criteriaList.map((item, index) =>
          <p key={index}>
            {item.Description}
          </p>
          )
          */}

          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});
export default ProjectedRewardCalculationCriteria;