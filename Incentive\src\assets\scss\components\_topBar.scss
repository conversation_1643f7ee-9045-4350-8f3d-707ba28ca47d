.menu-button {
  &:hover {
    background-color: transparent !important;
  }
  .user-name {
    text-transform: capitalize;
    font-weight: 500;
    margin: 10px;
    span {
      @media screen and (max-width: 660px) {
        display: none;
      }
    }
    i {
      margin-left: 10px;
      @media screen and (max-width: 660px) {
        margin-left: 2px;
      }
    }
  }
}

.notifications {
  margin: 0 20px;
}
.notification-badge {
  position: relative;
  padding: 2px 6px;
  background-color: #f30707;
  color: #f4f6f8;
  bottom: 15px;
  left: 50px;
  border-radius: 50%;
  font-size: 11px;
  font-weight: bolder;
}

.notification-menu {
  .new-notification {
    background-color: #eafcfd;
    margin: 5px 5px;
    border-radius: 4px;
  }
  .notification {
    margin: 5px 5px;
    border-radius: 4px;
  }
  .label {
    font-weight: 600;
  }
  .description {
    font-size: 12px;
  }
}

.tool-bar {
  padding-left: 0 !important;
}
.MuiList-padding {
  padding: 0 !important;
}
.Component-paper-32 {
  border: 1px solid transparent;
  border-radius: 0px 0px 20px 20px !important;
  background: #ffffff 0% 0% no-repeat padding-box;
  box-shadow: 0px 0px 26px #0000001a;
  margin: 5px 0 0;
  li {
    padding: 5px 15px;
    line-height: 10px;
    min-height: 10px;
    background-color: #fff;
    &:focus {
      background: #cfcfcf;
      color: #00458b;
    }
  }
}
