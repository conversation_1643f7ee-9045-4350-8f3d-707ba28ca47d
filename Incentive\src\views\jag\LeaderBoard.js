import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import { makeStyles } from "@material-ui/styles";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(0),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  content: {
    paddingTop: 150,
    textAlign: "center",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#fff",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    height: "70px",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#303030",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
    "& svg": {
      background: " #c8dcfa 0% 0% no-repeat padding-box",
      borderRadius: "50%",
      padding: "8px",
      width: "35px",
      height: "35px",
      color: "#0052cc",
    },
    "& ul": {
      display: "table",
      width: "100%",
      "& li": {
        display: "table-cell",
        width: "auto",
      },
    },
  },
  expandIcon: {
    background: "#00398e",
    color: "#fff",
    borderRadius: "50%",
  },
  warningBtn: {
    background: "#c8dcfa",
    borderRadius: "17px",
    margin: "5px 0",
    fontSize: "12px",
    color: "#0052cc",
    "&:hover": {
      background: "#c8dcfa",
    },
  },
}));

const LeaderBoard = (props) => {
  const classes = useStyles();
  const { userId, memberType, BU } = props;
  const [leaderBoardData, setLeaderBoardData] = useState([]);

  useEffect(() => {
    setLeaderBoardData([]);
    if (!userId && !memberType) {
      return;
    }

    services
      .API_GET(`jag/GetLeaderBoard/${userId}/${memberType}`)
      .then(response => {

        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          setLeaderBoardData(JSON.parse(response.Response));
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [props]);

  let growthType = 'APE'
  if (['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(BU) > -1) {
    growthType = 'BKGS'
  }


  return (
    <div className={classes.root} className="jag-leader-board">
      <h6>
        Leader Board {<i className="fa fa-angle-right" aria-hidden="true"></i>}
        {<span>Ranking as per Current {growthType} Growth</span>}
      </h6>
      <Card className={classes.card}>
        <ul className="lader-ranking">
          {
            leaderBoardData && leaderBoardData.length > 0 && leaderBoardData.map((item, index) =>
              <div key={index + 1} className={index == 0 ? "active" : ""}>
                <li>
                  <strong>
                    Rank #{item.AgentRank}
                    {index != 0 || <img src="/images/Trophy.svg" />}
                  </strong>
                  <span>{(item.AgentId != userId) ? item.AgentName : (<strong className={index == 0 ? "" : "textGrey"}>YOU</strong>)}  </span>
                </li>
                <li>
                  {<span>{growthType == 'APE' ? item.G1 : item.G2} %</span>}

                </li>
                <li>
                  <strong className={memberType}>{memberType == 'gold' ? <img src="/images/crown.svg" /> : <img src="/images/silvercrown.svg" />} {memberType}</strong>
                </li>
              </div>
            )}
        </ul>
      </Card>
    </div>
  );
};

export default LeaderBoard;
