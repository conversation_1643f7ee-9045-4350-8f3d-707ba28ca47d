import React, { useEffect, useState } from "react";
import * as services from "../../../services";
import Slider from "react-slick";

import parse from "html-react-parser";

const settings = {
  dots: true,
  infinite: true,
  arrows: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1
};

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});


const CriteriaInvestment = (props) => {
  const { show, handleClose, superGroupId, productId, date } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState(null);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [criteriaTable, setCriteriaTable] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getCriteria = () => {
    setCriteriaList([]);
    setCriteriaListHtml(null);
    setCriteriaSlabs([]);
    services
      .API_GET(`Incentive/GetIncentiveCriteria/${superGroupId}/${productId}/${date}`)
      .then(response => {
        console.log(response && response != "[]");
        if (response && response != "[]") {
          setCriteriaList(response.Insights || null);
          setCriteriaListHtml(response.InsightsHtml || null);
          //debugger;
          let tableContent = [];
          let slabContent = [];
          let SubCategory = _.groupBy(response.Slabs, "SubCategory");
          for (var key of Object.keys(SubCategory)) {
            const eSubCategory = SubCategory[key];
            let PlanCategory = _.groupBy(eSubCategory, "PlanCategory");
            for (var plankey of Object.keys(PlanCategory)) {
              const ePlanCategory = PlanCategory[plankey];

              if (ePlanCategory.length == 1) {
                tableContent.push(ePlanCategory[0])
              }
              else {
                slabContent.push({ SubCategory: key, PlanCategory: plankey, data: ePlanCategory });
              }
            }

          }



          //setCriteriaSlabs(response.Slabs ? _.orderBy(response.Slabs, ['IncentiveLevel'], ['desc']) : null);
          setCriteriaSlabs(slabContent);
          setCriteriaTable(tableContent);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  useEffect(() => {
    if (superGroupId && productId && date) {
      getCriteria();
    }
  }, [superGroupId, productId, date]);

  const renderHtml = () => {

    let html = parse(criteriaListHtml)

    return html;
  }


  return (
    <div className='how-it-works-section'>
      <span>Incentive Criterias</span>
      <h3>HOW IT WORKS?</h3>
      <p className="text-center">Your journey will be divided into levels based on APE/Booking</p>
      {/* {productId != 115 && */}
      <div className='steps step-1'>
        <div className="incentive-slab">
          {
            criteriaSlabs && criteriaSlabs.map((item, index) => {
              return <>
                <p>{item.SubCategory} - {item.PlanCategory}</p>
                {
                  item && item.data && item.data.map((eitem, eindex) => {
                    return <div className={"slab slab-" + (item.data.length - eindex)}>

                      <p>SLAB {eindex + 1}<br /> {Math.round(eitem.MinRangeDisplay).toLocaleString('en-IN')} - {Math.round(eitem.MaxRangeDisplay).toLocaleString('en-IN')} ({eitem.IncentivePercentage}%)</p>
                    </div>
                  })
                }
                <hr></hr>
              </>

            })
          }
        </div>
      </div>
      {/* } */}

      {criteriaTable &&
        <div className='steps step-1'>
          <table class="tableizer-table">
            <thead>
              <tr>
                <th>Sub Category</th>
                <th>Plan Category</th>
                <th>Min APE</th>
                <th>Max APE</th>
                <th>Percentage</th>
              </tr>
            </thead><tbody>

              {
                criteriaTable.map((item, index) => {
                  return <tr>
                    <td>{item.SubCategory}</td>
                    <td>{item.PlanCategory}</td>
                    <td>{item.MinRangeDisplay}</td>
                    <td>{item.MaxRangeDisplay}</td>
                    <td>{item.IncentivePercentage}%</td>
                  </tr>
                })
              }
            </tbody>
          </table>
        </div>
      }


      {criteriaListHtml && renderHtml()}
      {/* <Slider {...settings} >
      <div className='steps step-1'>
        <div className="incentive-slab">
          {
            criteriaSlabs && criteriaSlabs.map((item, index) => {
              return <div className={"slab slab-" + (criteriaSlabs.length - index)}>
                <p>SLAB {item.IncentiveLevel}<br /> {Math.round(item.MinRangeDisplay).toLocaleString('en-IN')} - {Math.round(item.MaxRangeDisplay).toLocaleString('en-IN')} ({item.IncentivePercentage}%)</p>
              </div>
            })
          }
        </div>
      </div>
      {criteriaListHtml && renderHtml()}

    </Slider> */}
    </div >
  );
}
export default CriteriaInvestment;