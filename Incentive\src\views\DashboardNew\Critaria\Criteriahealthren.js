import React, { useEffect, useState } from "react";


const CriteriaHealthRen = (props) => {
    const { show, handleClose, superGroupId, productId, date } = props;

    return (

     

<div className='how-it-works-section latest-list'>
    <h3>HOW IT WORKS?</h3>
    <p className="text-center">Your journey will be divided into 4-levels based on Upsell, Main Booking, Add-on &amp; Target</p>
    <h4>Qualifier Amount</h4>
    <p>On achieving the Main booking qualifier target, you will get <strong>INR 10,000/- &amp; INR 9,000</strong> depending on insurer wise category</p>
    <ul className='main-list'>
        <li>
            Agents who miss the Main booking qualifier targets by NOT MORE THAN 5 issued bookings get only INR 2500/-
            <ul className='inner-list'>
                <li>Applicable for R1, R2, Mix and Add-Ons</li>
                <li>Upsell to be paid as per given slabs to such agents</li>
            </ul>
        </li>
    </ul>
    <h4>Over target achieved booking slab</h4>
    <p>Below are the slab applicable after doing Booking over &amp; above qualifier booking:</p>
    <ul className='main-list slab-list'>
        <li>1. 1-5 Bkgs || INR 250</li>
        <li>2. 6-10 Bkgs || INR 500</li>
        <li>3. 11-15 Bkgs || INR 750</li>
        <li>4. &gt;=16 Bkgs || INR 1000</li>
    </ul>
    <h4>Upsell Incentive structure</h4>
    <ul className='main-list bullet-list'>
        <li>Upsell Incentive to be paid only if main booking target is met</li>
        <li>Upsell to ALSO include any new attachment sold (example - STU, CI, Hospicash, Safeguard Rider etc.)</li>
        <li>Downsell Incentive on SI Decrease Cases Only</li>
    </ul>
    <h4>Incentive on Add-Ons Booking to R1, R2 &amp; Mix agent (Core agent)</h4>
    <ul className='main-list core-list'>
        <li>1. If Add-on Booking &lt;=20, then INR 50/- per issued booking</li>
        <li>2. If Add-on Bookings &gt;20, then
            <ul className='main-list agent-list'>
                <li>INR 1000/- for Meeting Add-on Qualifier (Same as respective Core Qualifier)</li>
                <li>Rs. 50/- per issued booking after meeting the Add-On qualifier</li>
            </ul>
        </li>    
    </ul>
    <h3 className='upsell-incentive'><strong>Upsell Incentive Slab</strong></h3>
    <table cellPadding="2" cellSpacing="0" style={{ width: "100%", border : "1px solid black"}} width="350">
    
        <tbody>
            <tr >
                <td style={{border : "1px solid black"}} colSpan="8" height="7" valign="bottom" width="100%">
                    <p className='amt-text'><strong>UPSELL INCENTIVE as a % of upsell amount (Applicable for Grace Also)</strong></p>
                </td>
            </tr>
            <tr>
            <td style={{border : "1px solid black"}} bgcolor="#e6b8b7" height="8" width="15.140845070422536%">
                <p><strong>INSURER</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="8.450704225352112%">
                <p><strong>0-50k</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="16.549295774647888%">
                <p><strong>0.5L-1 lac</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="14.084507042253522%">
                <p><strong>1-1.5 lac</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="14.084507042253522%">
                <p><strong>1.5-2 lac</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="11.267605633802816%">
                <p><strong>2- 3 lac</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="10.211267605633802%">
                <p><strong>3-5 lac</strong></p>
            </td>
            <td style={{border : "1px solid black"}} bgcolor="#d8e4bc" width="10.211267605633802%">
                <p><strong>&gt;5 lac</strong></p>
            </td>
        </tr>
        <tr >
            <td style={{border : "1px solid black"}} height="8" width="15.140845070422536%">
                <p>Apollo</p>
            </td>
            <td style={{border : "1px solid black"}} width="8.450704225352112%">
                <p>4.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="16.549295774647888%">
                <p>5.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>6.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>7.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="11.267605633802816%">
                <p>8.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>9.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>10.0%</p>
            </td>
        </tr>
        <tr>
            <td style={{border : "1px solid black"}} height="8" width="15.140845070422536%">
                <p>Religare</p>
            </td>
            <td style={{border : "1px solid black"}} width="8.450704225352112%">
                <p>3.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="16.549295774647888%">
                <p>4.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>5.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>6.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="11.267605633802816%">
                <p>7.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>8.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>9.0%</p>
            </td>
        </tr>
        <tr>
            <td style={{border : "1px solid black"}} height="8" width="15.140845070422536%">
                <p>Max</p>
            </td>
            <td style={{border : "1px solid black"}} width="8.450704225352112%">
                <p>3.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="16.549295774647888%">
                <p>4.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>5.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>6.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="11.267605633802816%">
                <p>7.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>8.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>9.0%</p>
            </td>
        </tr>
        <tr>
            <td style={{border : "1px solid black"}} height="8" width="15.140845070422536%">
                <p>Star</p>
            </td>
            <td style={{border : "1px solid black"}} width="8.450704225352112%">
                <p>4.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="16.549295774647888%">
                <p>5.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>6.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="14.084507042253522%">
                <p>7.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="11.267605633802816%">
                <p>8.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>9.0%</p>
            </td>
            <td style={{border : "1px solid black"}} width="10.211267605633802%">
                <p>10.0%</p>
            </td>
        </tr>
        <tr>
            <td style={{border : "1px solid black"}} height="7" valign="bottom" width="11.684782608695652%">
                <p>Other</p>
            </td>
            <td style={{border : "1px solid black"}} colSpan="7" width="88.31521739130434%">
                <p><strong>7%</strong></p>
            </td>
        </tr>
    </tbody>
</table>
<p>&nbsp;</p>
      </div>

    )
}

export default CriteriaHealthRen;
