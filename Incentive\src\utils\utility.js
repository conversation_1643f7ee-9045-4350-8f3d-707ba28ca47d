import * as appConfig from './../appconfig/app.config';
import * as services from "../services"

export const getTotalPages = (count, pageSize) => {
  if (count != undefined && typeof count === 'number') {
    return Math.ceil(count / pageSize);
  } else {
    return 1;
  }

};

export const getQueryParams = (params) => {
  const str = [];
  for (const p in params) {
    if (params.hasOwnProperty(p)) {
      str.push(`${encodeURIComponent(p)}=${encodeURIComponent(params[p])}`);
    }
  }
  return str.join('&');
};
export const debounce = (fn, delay) => {
  let timerId = null;
  return function (...args) {
    if (timerId) {
      clearTimeout(timerId);
    }
    timerId = setTimeout(() => {
      fn(...args);
    }, delay);
  };
};

export function setCookie(cname, cvalue, exdays) {
  var d = new Date();
  d.setTime(d.getTime() + (exdays * 24 * 60 * 60 * 1000));
  var expires = "domain=.policybazaar.com;expires=" + d.toUTCString();
  document.cookie = cname + "=" + cvalue + ";" + expires + ";path=/";
}

export function getCookie(cname) {
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) === ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}

export function getUrlParameter(name, decode) {

  name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
  var regex = new RegExp('[\\?&]' + name + '=([^&#]*)');
  var results = regex.exec(window.location.search);
  if (decode) {
      return results === null ? '' : results[1];
  }
  else
      return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
}