<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="220.793" height="384.858" viewBox="0 0 220.793 384.858">
  <defs>
    <clipPath id="clip-path">
      <rect id="Rectangle_33969" data-name="Rectangle 33969" width="37.439" height="412.86" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-2">
      <rect id="Rectangle_33968" data-name="Rectangle 33968" width="37.44" height="412.86" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-3">
      <rect id="Rectangle_33950" data-name="Rectangle 33950" width="5.951" height="279.076" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-4">
      <rect id="Rectangle_33951" data-name="Rectangle 33951" width="3.39" height="5.184" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-5">
      <rect id="Rectangle_33952" data-name="Rectangle 33952" width="4.96" height="11.51" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-6">
      <rect id="Rectangle_33953" data-name="Rectangle 33953" width="4.36" height="6.631" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-7">
      <rect id="Rectangle_33954" data-name="Rectangle 33954" width="2.113" height="3.136" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-8">
      <rect id="Rectangle_33955" data-name="Rectangle 33955" width="1.315" height="2.122" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-9">
      <rect id="Rectangle_33956" data-name="Rectangle 33956" width="0.379" height="0.419" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-10">
      <rect id="Rectangle_33957" data-name="Rectangle 33957" width="1.851" height="11.841" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-11">
      <rect id="Rectangle_33958" data-name="Rectangle 33958" width="2.481" height="80.461" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-12">
      <rect id="Rectangle_33959" data-name="Rectangle 33959" width="2.128" height="3.252" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-13">
      <rect id="Rectangle_33960" data-name="Rectangle 33960" width="3.113" height="7.223" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-14">
      <rect id="Rectangle_33961" data-name="Rectangle 33961" width="2.736" height="4.162" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-15">
      <rect id="Rectangle_33962" data-name="Rectangle 33962" width="1.326" height="1.97" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-16">
      <rect id="Rectangle_33963" data-name="Rectangle 33963" width="0.825" height="1.332" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-17">
      <rect id="Rectangle_33964" data-name="Rectangle 33964" width="0.238" height="0.263" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-18">
      <rect id="Rectangle_33965" data-name="Rectangle 33965" width="8.914" height="72.885" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-19">
      <rect id="Rectangle_33966" data-name="Rectangle 33966" width="4.772" height="54.234" fill="none"/>
    </clipPath>
    <clipPath id="clip-path-20">
      <rect id="Rectangle_33967" data-name="Rectangle 33967" width="8.795" height="28.324" fill="none"/>
    </clipPath>
  </defs>
  <g id="Group_244735" data-name="Group 244735" transform="matrix(-0.891, 0.454, -0.454, -0.891, 220.793, 367.861)">
    <g id="Group_244734" data-name="Group 244734" transform="translate(0 0)" clip-path="url(#clip-path)">
      <rect id="Rectangle_33949" data-name="Rectangle 33949" width="5.951" height="279.076" transform="translate(15.809 26.036)" fill="#35283f"/>
      <g id="Group_244733" data-name="Group 244733" transform="translate(0 0)">
        <g id="Group_244732" data-name="Group 244732" clip-path="url(#clip-path-2)">
          <g id="Group_244680" data-name="Group 244680" transform="translate(15.81 26.036)" opacity="0.57">
            <g id="Group_244679" data-name="Group 244679">
              <g id="Group_244678" data-name="Group 244678" clip-path="url(#clip-path-3)">
                <path id="Path_228540" data-name="Path 228540" d="M4.711,279.076q-.009-22.592-.12-45.186-.085-21.065-.145-42.13c-.026-13.867.194-27.751-.19-41.615C4.15,146.311,5.071,50.51,0,41.709V0H5.95V279.076Z" transform="translate(0 0)" fill="#170822"/>
              </g>
            </g>
          </g>
          <path id="Path_228541" data-name="Path 228541" d="M37.44,31.62s-13.6-.488-14.789,2.623,5.718,8.987,5.718,8.987-1.381,60.613-9.337,72.623c-.033.043-.065.087-.1.131-.065.083-.128.167-.194.251-.343-.486-.675-.979-.993-1.482C10.376,101.3,9.071,43.229,9.071,43.229s6.91-5.875,5.718-8.987S0,31.62,0,31.62c3.1-4.543,17.053-5.28,17.053-5.28C13.6,19.229,15.265,0,15.265,0h6.909s1.668,19.229-1.786,26.34c0,0,13.955.738,17.052,5.28" transform="translate(0 296.626)" fill="#a3c4d9"/>
          <path id="Path_228542" data-name="Path 228542" d="M.464,17.688a1.346,1.346,0,0,1-.227-.394,3.5,3.5,0,0,1-.222-1.633A10.3,10.3,0,0,1,.438,13.52c.087-.291.184-.58.289-.865.245-.664.539-10.725.877-11.347Q1.858.842,2.145.4c.1-.16.239-.535.5-.342A.639.639,0,0,1,2.84.5c.33,2.316.132,14.143-.734,16.315a2.17,2.17,0,0,1-.538.868.834.834,0,0,1-.953.135.674.674,0,0,1-.15-.128" transform="translate(18.678 304.201)" fill="#d1def1"/>
          <path id="Path_228543" data-name="Path 228543" d="M3.965,5.138c-1.192,3.111,5.718,8.987,5.718,8.987S8.3,74.738.346,86.746l-.1.132-.194.251C.036,87.1,0,5.384,0,5.384,1.32-.1,2.646-.089,3.966.029a27.485,27.485,0,0,1,9.33,2.54c-3.929.191-8.651.793-9.332,2.569" transform="translate(18.686 325.731)" fill="#d1def1"/>
          <path id="Path_228544" data-name="Path 228544" d="M1.706,25.243C-1.1,18.261.4,0,.4,0H1.706Z" transform="translate(14.864 296.625)" fill="#87b2ce"/>
          <path id="Path_228545" data-name="Path 228545" d="M.348,11.712c-.034.047-.067.091-.1.135l-.194.25L0,12.021V0C.436,1.624,1.409,4.9,2.82,5.449A19.7,19.7,0,0,1,.348,11.712" transform="translate(18.686 400.762)" fill="#fff"/>
          <path id="Path_228546" data-name="Path 228546" d="M6.9.178c-.094.893-.578,5.65-.578,5.65C2.82,5.881.572,11.308,0,12.38V0A4.936,4.936,0,0,0,6.9.178" transform="translate(18.686 379.332)" fill="#fff"/>
          <path id="Path_228547" data-name="Path 228547" d="M9.549,4.451C9.44,6.813,9.17,17.933,8.874,22.037c-2.01-.3-5.665,1.75-6.714,4.511C1.576,28.085.664,32.985,0,34.369V0C2.257,2.707,6.119,5.056,9.549,4.451" transform="translate(18.686 338.687)" fill="#fff"/>
          <path id="Path_228548" data-name="Path 228548" d="M12.223,2.184A22.214,22.214,0,0,0,4.11.433a6.74,6.74,0,0,0-3.585.914A4.39,4.39,0,0,0,0,1.759V.064A24.621,24.621,0,0,1,3.966.1a27.565,27.565,0,0,1,8.755,2.281c-.166-.065-.333-.132-.5-.2" transform="translate(18.686 325.662)" fill="#fff"/>
          <path id="Path_228549" data-name="Path 228549" d="M4.041,5.057C2.891,9.863,3.3,39.008,5.817,62.27,1.269,45.464.121,2.933,0,0,.267.006.533.018.8.035q.448.046.9.075C2.525.2,3.349.326,4.149.487c.4.082.828.177,1.26.288A29.834,29.834,0,0,0,4.041,5.057" transform="translate(9.08 340.148)" fill="#79a9c7"/>
          <path id="Path_228550" data-name="Path 228550" d="M18.4,26.069c-.1.145-.775,4.437-1.129,5.553a23.256,23.256,0,0,0-8.084-2.367,15.947,15.947,0,0,0-6.479.735A6.959,6.959,0,0,0,0,31.258c3.571-4.219,16.77-4.917,16.77-4.917C13.316,19.229,14.984,0,14.984,0h.454c-.426,6.1,0,20.107,2.966,26.069" transform="translate(0.281 296.625)" fill="#79a9c7"/>
          <path id="Path_228551" data-name="Path 228551" d="M7.659,25.652s-2.06-11.547-6.3-9.189L0,0S1.683,10.241,3.83,10.807s3.8-3.194,3.8-3.194Z" transform="translate(10.58 364.653)" fill="#79a9c7"/>
          <path id="Path_228552" data-name="Path 228552" d="M8.5,7.853S6.229,5.319,4.521,4.734C1.715,3.774.3,5.306.3,5.306S-.012-.2,0,.006,3.516,3.821,4.88,3.468A12.878,12.878,0,0,0,8.4,1.274c.008.283.108,6.578.108,6.578" transform="translate(9.404 347.023)" fill="#79a9c7"/>
          <path id="Path_228553" data-name="Path 228553" d="M1.122,9S2.141,6.412,3.652,7.119s2.7,7.534,2.7,7.534L6.244,6.27S1.8,3.33,0,0Z" transform="translate(12.222 385.522)" fill="#79a9c7"/>
          <path id="Path_228554" data-name="Path 228554" d="M0,1.489.209.113,2.087,0S14.014.365,18.963,5.665c0,0-5.237-4.315-18.963-4.176" transform="translate(18.477 322.581)" fill="#6aa1c4"/>
          <path id="Path_228555" data-name="Path 228555" d="M12.947,19.757C10.6,21.392,9.525,25.408,9.525,26.11H5.844c0-.7-1.077-4.717-3.421-6.353-.9-.625-2.869-7.86-2.331-8.815,0,0,.717,2.534,1.614.969S3.7,3.748,7.64.086V0c.015.016.03.028.044.044C7.7.028,7.714.016,7.729,0V.086c3.945,3.662,5.045,10.271,5.935,11.825s1.614-.969,1.614-.969c.538.956-1.434,8.19-2.331,8.815" transform="translate(11.031 0)" fill="#2a1c34"/>
          <path id="Path_228556" data-name="Path 228556" d="M7.755,19.757c-2.343,1.635-3.421,5.651-3.421,6.353H.653A8.777,8.777,0,0,0,.04,23.822,6.658,6.658,0,0,0,4.9,21.41c1.487-1.986,1.254-4.623.9-6.939A19.741,19.741,0,0,0,0,3.231,13.543,13.543,0,0,1,2.449.086V0c.015.016.03.028.044.044C2.508.028,2.523.016,2.537,0V.086C6.482,3.748,7.583,10.357,8.472,11.911s1.614-.969,1.614-.969c.538.956-1.434,8.19-2.331,8.815" transform="translate(16.222 0)" fill="#1c0d27"/>
          <path id="Path_228557" data-name="Path 228557" d="M9.935,13.5a16.4,16.4,0,0,0,2.452,5.565,13.269,13.269,0,0,1-3.324-3.4S8.8,4.349,7.954,3.972c0,0-.28,14.615-1.726,17.231v.124c-.011-.017-.023-.042-.034-.061-.011.019-.023.044-.035.061V21.2C4.713,18.587,4.433,3.972,4.433,3.972c-.849.377-1.11,11.7-1.11,11.7A13.271,13.271,0,0,1,0,19.064,16.429,16.429,0,0,0,2.452,13.5C3.019,10.576.943,2.651,1.98,1.279,2.956-.012,5.854,0,6.194,0S9.43-.012,10.406,1.279c1.037,1.372-1.037,9.3-.471,12.22" transform="translate(12.487 24.75)" fill="#5a4f62"/>
          <path id="Path_228558" data-name="Path 228558" d="M2.453,7.829A16.383,16.383,0,0,0,0,2.264a13.248,13.248,0,0,1,3.324,3.4s.262,11.32,1.111,11.7c0,0,.28-14.616,1.725-17.232V0c.011.017.023.043.035.062C6.206.043,6.218.017,6.229,0V.124C7.676,2.739,7.956,17.356,7.956,17.356c.849-.377,1.11-11.7,1.11-11.7a13.251,13.251,0,0,1,3.323-3.4A16.407,16.407,0,0,0,9.935,7.829c-.565,2.924,1.51,10.848.472,12.221-.977,1.29-.4.26-4.212.26s-3.238,1.03-4.212-.26c-1.038-1.373,1.036-9.3.471-12.221" transform="translate(12.486 276.615)" fill="#5a4f62"/>
          <path id="Path_228559" data-name="Path 228559" d="M3.16.113V0s.015.025.036.058L3.231,0V.113c.587.94,4.647,7.706,2.578,11.758A3.943,3.943,0,0,1,3.3,14.161v.033a.892.892,0,0,1-.1-.014.931.931,0,0,1-.1.014v-.033a3.938,3.938,0,0,1-2.514-2.29C-1.486,7.82,2.574,1.053,3.16.113" transform="translate(15.481 7.566)" fill="#13031e"/>
          <path id="Path_228560" data-name="Path 228560" d="M2.836.1V0l.032.049L2.9,0V.1c.525.844,4.168,6.916,2.313,10.55A3.539,3.539,0,0,1,2.957,12.7v.03s-.034,0-.09-.011c-.056.009-.089.011-.089.011V12.7A3.537,3.537,0,0,1,.523,10.65C-1.334,7.015,2.31.944,2.836.1" transform="translate(15.808 8.63)" fill="#ff2890"/>
          <g id="Group_244683" data-name="Group 244683" transform="translate(16.03 15.686)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244682" data-name="Group 244682">
              <g id="Group_244681" data-name="Group 244681" clip-path="url(#clip-path-4)">
                <path id="Path_228561" data-name="Path 228561" d="M1.527.292A3.653,3.653,0,0,1,2.839,2.018,8.184,8.184,0,0,1,3.39,4.882a2.382,2.382,0,0,1-.664.275v.027s-.032,0-.083-.011c-.051.009-.082.011-.082.011V5.157a3.271,3.271,0,0,1-2.081-1.9A5.039,5.039,0,0,1,.005.764C.228.055.633-.284,1.527.292" transform="translate(0 0)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244686" data-name="Group 244686" transform="translate(16.584 8.631)" style="mix-blend-mode: multiply;isolation: isolate">
            <g id="Group_244685" data-name="Group 244685">
              <g id="Group_244684" data-name="Group 244684" clip-path="url(#clip-path-5)">
                <path id="Path_228562" data-name="Path 228562" d="M4.437,10.65a5.2,5.2,0,0,1-.545.86A19.6,19.6,0,0,0,3.8,8.157a4.915,4.915,0,0,0-2-3.218A3.583,3.583,0,0,0,0,4.36,23.711,23.711,0,0,1,2.06.1V0l.033.049L2.124,0V.1C2.65.944,6.294,7.015,4.437,10.65" transform="translate(0 0)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244689" data-name="Group 244689" transform="translate(16.896 9.125)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244688" data-name="Group 244688">
              <g id="Group_244687" data-name="Group 244687" clip-path="url(#clip-path-6)">
                <path id="Path_228563" data-name="Path 228563" d="M3.066,4.949A4.72,4.72,0,0,0,0,3.608,22.146,22.146,0,0,1,1.749.093V0s.012.017.03.047C1.8.017,1.808,0,1.808,0V.093A18.536,18.536,0,0,1,4.36,6.631,6.631,6.631,0,0,0,3.066,4.949" transform="translate(-0.001 0)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244692" data-name="Group 244692" transform="translate(16.17 17.148)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244691" data-name="Group 244691" transform="translate(0 0)">
              <g id="Group_244690" data-name="Group 244690" clip-path="url(#clip-path-7)">
                <path id="Path_228564" data-name="Path 228564" d="M2.078,2.6a.668.668,0,0,1-.6.53.835.835,0,0,1-.645-.309A2.859,2.859,0,0,1,.3,2.057a3.033,3.033,0,0,1-.208-.55C.082,1.481.077,1.449.07,1.421A1.964,1.964,0,0,1,0,.811.9.9,0,0,1,.228.217a.692.692,0,0,1,.643-.2.9.9,0,0,1,.46.321,4.543,4.543,0,0,1,.339.412,2.445,2.445,0,0,1,.354.849A2.322,2.322,0,0,1,2.078,2.6" transform="translate(0 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <g id="Group_244695" data-name="Group 244695" transform="translate(16.101 14.592)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244694" data-name="Group 244694" transform="translate(0 0)">
              <g id="Group_244693" data-name="Group 244693" clip-path="url(#clip-path-8)">
                <path id="Path_228565" data-name="Path 228565" d="M1.183,1.621a1.49,1.49,0,0,1-.119.242.5.5,0,0,1-.222.21.538.538,0,0,1-.191.049.544.544,0,0,1-.294-.073A.9.9,0,0,1,0,1.249,1.954,1.954,0,0,1,.026.818.727.727,0,0,1,.306.307L.224.372A.661.661,0,0,1,.433.184,1.092,1.092,0,0,1,.587.06.4.4,0,0,1,.945.027.425.425,0,0,1,1.19.284a1.808,1.808,0,0,1-.007,1.337" transform="translate(0.001 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <g id="Group_244698" data-name="Group 244698" transform="translate(17.061 13.655)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244697" data-name="Group 244697" transform="translate(0 0)">
              <g id="Group_244696" data-name="Group 244696" clip-path="url(#clip-path-9)">
                <path id="Path_228566" data-name="Path 228566" d="M.368.275A.146.146,0,0,1,.337.342.15.15,0,0,1,.286.39C.271.4.258.4.244.41A.129.129,0,0,1,.173.417.113.113,0,0,1,.125.405.138.138,0,0,1,.064.368L.034.333A.193.193,0,0,1,.006.244L0,.2A.188.188,0,0,1,.023.1.458.458,0,0,0,.051.06.193.193,0,0,1,.133.007.161.161,0,0,1,.207,0,.148.148,0,0,1,.279.022.16.16,0,0,1,.335.068.157.157,0,0,1,.37.133a.178.178,0,0,1,0,.1c0,.016,0,.031-.006.044" transform="translate(-0.001 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <path id="Path_228567" data-name="Path 228567" d="M.89,0A.468.468,0,0,0,.95.028C.655,2.965-.058,6.864.3,8.728a16.4,16.4,0,0,0,2.452,5.566s-.679-.49-1.446-1.181A20.192,20.192,0,0,1,.89,0" transform="translate(22.12 29.521)" fill="#8b8390"/>
          <path id="Path_228568" data-name="Path 228568" d="M8.552.861C6.394.508,3.892.275,3.122,2.849c-.783,2.618-.268,5.73-.284,8.409A10.147,10.147,0,0,1,0,18.792,16.1,16.1,0,0,0,2.268,13.5C2.835,10.576.759,2.651,1.8,1.28,2.773-.013,5.671,0,6.01,0S9.194-.011,10.2,1.247A12.3,12.3,0,0,0,8.552.861" transform="translate(12.671 24.75)" fill="#8b8390"/>
          <path id="Path_228569" data-name="Path 228569" d="M6.194,18.046c-3.816,0-3.236,1.032-4.212-.26-1.037-1.373,1.037-9.3.471-12.221A16.392,16.392,0,0,0,0,0,16.885,16.885,0,0,1,2.619,2.383c.1.3.2.6.3.907a46.668,46.668,0,0,1-.327,9.272c-.143,1.478-.238,3.4,1.285,4.17,2.013,1.024,4.42-.754,6.2,1.117a1.8,1.8,0,0,0,.169.145c-.761.961-.466.051-4.052.051" transform="translate(12.486 278.878)" fill="#9a939f"/>
          <path id="Path_228570" data-name="Path 228570" d="M0,6.753C.109,4.505.152,2.628.152,2.628A10.12,10.12,0,0,1,2.5,0,25.627,25.627,0,0,0,0,6.753" transform="translate(21.399 279.645)" fill="#9a939f"/>
          <path id="Path_228571" data-name="Path 228571" d="M.621,5.3A23.428,23.428,0,0,0,0,2.044,7.344,7.344,0,0,1,.578.125V0C.59.017.6.043.613.062.624.043.635.017.647,0V.125c.884,1.6,1.332,7.677,1.547,12.164A48.836,48.836,0,0,0,.621,5.3" transform="translate(18.068 276.615)" fill="#9a939f"/>
          <path id="Path_228572" data-name="Path 228572" d="M2.181,10.709,1.128,0,0,10.942l1.066,75.6Z" transform="translate(17.557 322.694)" fill="#fff"/>
          <path id="Path_228573" data-name="Path 228573" d="M8.9,3.82c-.11,2.042-.211,60.116-.037,63.671.193,3.954-1.42,5.395-1.42,5.395H1.469S-.143,71.444.05,67.49C.223,63.936.122,5.861.013,3.82-.163.566,1.469,0,1.469,0H7.447S9.079.566,8.9,3.82" transform="translate(14.299 189.56)" fill="#35283f"/>
          <g id="Group_244701" data-name="Group 244701" transform="translate(16.393 264.569)" opacity="0.46" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244700" data-name="Group 244700" transform="translate(0 0)">
              <g id="Group_244699" data-name="Group 244699" clip-path="url(#clip-path-10)">
                <path id="Path_228574" data-name="Path 228574" d="M0,0S.027,12.062,0,11.837,4.177,1.123,0,0" transform="translate(0 0)" fill="#35283f"/>
              </g>
            </g>
          </g>
          <g id="Group_244704" data-name="Group 244704" transform="translate(16.344 107.445)" opacity="0.46" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244703" data-name="Group 244703">
              <g id="Group_244702" data-name="Group 244702" clip-path="url(#clip-path-11)">
                <path id="Path_228575" data-name="Path 228575" d="M.1,80.462S-.131-.263.1,0s1.938,26.433,2.337,45.57A202.827,202.827,0,0,1,.1,80.462" transform="translate(0.001 -0.001)" fill="#35283f"/>
              </g>
            </g>
          </g>
          <path id="Path_228576" data-name="Path 228576" d="M1.78.065V0s.008.014.02.032L1.821,0V.065C2.15.592,4.436,4.4,3.271,6.685A2.213,2.213,0,0,1,1.856,7.973v.019L1.8,7.984l-.057.008V7.973A2.214,2.214,0,0,1,.328,6.685C-.837,4.4,1.449.592,1.78.065" transform="translate(16.881 48.053)" fill="#ff2890"/>
          <g id="Group_244707" data-name="Group 244707" transform="translate(17.02 52.482)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244706" data-name="Group 244706" transform="translate(0 0)">
              <g id="Group_244705" data-name="Group 244705" clip-path="url(#clip-path-12)">
                <path id="Path_228577" data-name="Path 228577" d="M.959.185a2.288,2.288,0,0,1,.823,1.084,5.1,5.1,0,0,1,.346,1.8,1.5,1.5,0,0,1-.416.174v.015a.287.287,0,0,1-.052-.007.271.271,0,0,1-.051.007V3.237A2.053,2.053,0,0,1,.3,2.047,3.148,3.148,0,0,1,0,.48C.143.034.4-.179.959.185" transform="translate(0 0)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244710" data-name="Group 244710" transform="translate(17.367 48.054)" style="mix-blend-mode: multiply;isolation: isolate">
            <g id="Group_244709" data-name="Group 244709" transform="translate(0 0)">
              <g id="Group_244708" data-name="Group 244708" clip-path="url(#clip-path-13)">
                <path id="Path_228578" data-name="Path 228578" d="M2.785,6.685a3.327,3.327,0,0,1-.342.539,12.3,12.3,0,0,0-.057-2.1A3.082,3.082,0,0,0,1.128,3.1,2.214,2.214,0,0,0,0,2.736,14.677,14.677,0,0,1,1.294.065V0l.019.032L1.333,0V.065c.33.528,2.616,4.337,1.451,6.62" transform="translate(0 -0.001)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244713" data-name="Group 244713" transform="translate(17.563 48.364)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244712" data-name="Group 244712" transform="translate(0 0)">
              <g id="Group_244711" data-name="Group 244711" clip-path="url(#clip-path-14)">
                <path id="Path_228579" data-name="Path 228579" d="M1.923,3.1A2.96,2.96,0,0,0,0,2.265,13.859,13.859,0,0,1,1.1.059V0s.008.01.019.028A.249.249,0,0,1,1.134,0V.059a11.6,11.6,0,0,1,1.6,4.1A4.139,4.139,0,0,0,1.923,3.1" transform="translate(0 0)" fill="#ff2890"/>
              </g>
            </g>
          </g>
          <g id="Group_244716" data-name="Group 244716" transform="translate(17.108 53.399)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244715" data-name="Group 244715" transform="translate(0 0)">
              <g id="Group_244714" data-name="Group 244714" clip-path="url(#clip-path-15)">
                <path id="Path_228580" data-name="Path 228580" d="M1.3,1.635a.423.423,0,0,1-.376.335.524.524,0,0,1-.4-.2,1.817,1.817,0,0,1-.338-.481A1.9,1.9,0,0,1,.056.947C.051.93.048.91.044.893A1.216,1.216,0,0,1,0,.511.561.561,0,0,1,.143.139.433.433,0,0,1,.547.01a.581.581,0,0,1,.287.2,2.482,2.482,0,0,1,.213.26A1.567,1.567,0,0,1,1.271,1a1.507,1.507,0,0,1,.033.632" transform="translate(0 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <g id="Group_244719" data-name="Group 244719" transform="translate(17.064 51.796)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244718" data-name="Group 244718" transform="translate(0 0)">
              <g id="Group_244717" data-name="Group 244717" clip-path="url(#clip-path-16)">
                <path id="Path_228581" data-name="Path 228581" d="M.742,1.017a.953.953,0,0,1-.075.153.314.314,0,0,1-.258.161.335.335,0,0,1-.186-.047A.565.565,0,0,1,0,.784,1.262,1.262,0,0,1,.016.514.458.458,0,0,1,.192.192C.175.206.158.219.141.235A.391.391,0,0,1,.271.117a.787.787,0,0,1,.1-.079A.254.254,0,0,1,.593.017.261.261,0,0,1,.747.179a1.125,1.125,0,0,1,0,.838" transform="translate(0 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <g id="Group_244722" data-name="Group 244722" transform="translate(17.667 51.207)" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244721" data-name="Group 244721" transform="translate(0 0)">
              <g id="Group_244720" data-name="Group 244720" clip-path="url(#clip-path-17)">
                <path id="Path_228582" data-name="Path 228582" d="M.232.173a.1.1,0,0,1-.02.042A.074.074,0,0,1,.18.244L.153.257a.1.1,0,0,1-.044,0A.062.062,0,0,1,.079.255.085.085,0,0,1,.041.231L.022.209A.119.119,0,0,1,0,.153L0,.125A.13.13,0,0,1,.015.062L.033.038A.108.108,0,0,1,.084.005.108.108,0,0,1,.131,0,.122.122,0,0,1,.176.013.094.094,0,0,1,.211.045.084.084,0,0,1,.233.083a.117.117,0,0,1,0,.062l0,.028" transform="translate(0 0)" fill="#ffb3d8"/>
              </g>
            </g>
          </g>
          <path id="Path_228583" data-name="Path 228583" d="M.067,0H8.779c.014,1.905.032,3.185.053,3.626s.017.83,0,1.2H.015c-.018-.373-.022-.771,0-1.2S.054,1.905.067,0" transform="translate(14.334 253.425)" fill="#8e283f"/>
          <path id="Path_228584" data-name="Path 228584" d="M8.653,0c0,1.938.007,3.748.011,5.382H0C0,3.748.009,1.938.011,0Z" transform="translate(14.425 243.048)" fill="#8e283f"/>
          <path id="Path_228585" data-name="Path 228585" d="M0,0H8.631c0,1.9,0,3.77,0,5.571H0V0" transform="translate(14.442 232.485)" fill="#8e283f"/>
          <path id="Path_228586" data-name="Path 228586" d="M8.648,0c0,1.665,0,3.337-.006,5H.006C0,3.337,0,1.665,0,0Z" transform="translate(14.432 222.486)" fill="#8e283f"/>
          <path id="Path_228587" data-name="Path 228587" d="M8.752,0c-.007,1.529-.012,3.208-.018,5H.018C.012,3.208.007,1.529,0,0Z" transform="translate(14.381 202.111)" fill="#8e283f"/>
          <path id="Path_228588" data-name="Path 228588" d="M8.9,0a7.708,7.708,0,0,1,0,.889c-.015.281-.03,1.624-.044,3.739H.056C.041,2.512.027,1.17.011.889A7.956,7.956,0,0,1,.014,0Z" transform="translate(14.3 192.491)" fill="#8e283f"/>
          <path id="Path_228589" data-name="Path 228589" d="M8.687,0c0,1.741-.008,3.543-.011,5.382H.011C.008,3.543,0,1.741,0,0Z" transform="translate(14.413 212.11)" fill="#8e283f"/>
          <g id="Group_244725" data-name="Group 244725" transform="translate(14.303 189.56)" opacity="0.57">
            <g id="Group_244724" data-name="Group 244724" transform="translate(0 0)">
              <g id="Group_244723" data-name="Group 244723" clip-path="url(#clip-path-18)">
                <path id="Path_228590" data-name="Path 228590" d="M8.9,3.82c-.11,2.042-.211,60.116-.037,63.671.193,3.954-1.42,5.395-1.42,5.395H6.351C8.076,66.939,8.322,60.5,8.163,54.345,7.986,47.505,7.079,40.721,6.8,33.893c-.157-3.806-.472-7.605-.889-11.39C5.647,20.111,4.461,4.577,0,3.6-.085.547,1.465,0,1.465,0H7.443S9.075.566,8.9,3.82" transform="translate(0 0)" fill="#170822"/>
              </g>
            </g>
          </g>
          <g id="Group_244728" data-name="Group 244728" transform="translate(14.813 207.765)" opacity="0.46" style="mix-blend-mode: screen;isolation: isolate">
            <g id="Group_244727" data-name="Group 244727" transform="translate(0 0)">
              <g id="Group_244726" data-name="Group 244726" clip-path="url(#clip-path-19)">
                <path id="Path_228591" data-name="Path 228591" d="M.065,0C.416,1.3.7,2.62.939,3.934a125.727,125.727,0,0,1,1.744,20c.134,7.017-.271,14.1.38,21.088A54.414,54.414,0,0,0,4.31,52.75a10.85,10.85,0,0,0,.463,1.483H1.4S-.172,52.826.015,48.968C.136,46.518.121,17.52.065,0" transform="translate(0 0)" fill="#35283f"/>
              </g>
            </g>
          </g>
          <g id="Group_244731" data-name="Group 244731" transform="translate(14.421 189.56)" opacity="0.57" style="mix-blend-mode: multiply;isolation: isolate">
            <g id="Group_244730" data-name="Group 244730">
              <g id="Group_244729" data-name="Group 244729" clip-path="url(#clip-path-20)">
                <path id="Path_228592" data-name="Path 228592" d="M8.668,28.324C7.97,21.57,7.284,14.68,5.337,8.243,4.788,6.426,4.27,4.164,2.929,2.754A2.519,2.519,0,0,0,0,2.08C.347.356,1.347,0,1.347,0H7.325S8.957.566,8.782,3.82c-.047.868-.092,11.871-.115,24.5" transform="translate(0 0)" fill="#170822"/>
              </g>
            </g>
          </g>
        </g>
      </g>
    </g>
  </g>
</svg>
