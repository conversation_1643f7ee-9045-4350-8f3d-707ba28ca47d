import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { makeStyles, useTheme } from '@material-ui/styles';
import { useMediaQuery } from '@material-ui/core';

import { Sidebar, Topbar } from './components';
// import Notifier from './../../components/Notifier';
import { connect } from 'react-redux';
import LoaderComponent from "../../components/Loader";
import * as utility from "../../utils/utility";

const useStyles = makeStyles(theme => ({
  root: {
    display: 'flex',
  },
  shiftContent: {
    //paddingLeft: 215
  },
  content: {
    height: '100%',
    flexGrow: 1,
    overflow: 'hidden'
  }
}));

const Main = props => {
  const { children, user, onLogout, notifications } = props;

  const [isLoading, setIsLoading] = useState(true);
  const [userDetail, setUserDetail] = useState(null);
  const [app, setApp] = useState(false);

  if (!userDetail || userDetail == null) {
    setTimeout(() => {
      const userData = JSON.parse(localStorage.getItem('user'));
      if (userData != 'undefined' && userData != null) {
        setUserDetail(userData);
      }
      setIsLoading(false);
    }, 2000);
  }

  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });

  const [openSidebar, setOpenSidebar] = useState(false);

  const handleSidebarOpen = () => {
    setOpenSidebar(true);
  };

  const handleSidebarClose = () => {
    setOpenSidebar(false);
  };

  useEffect(() => {
    
    if (utility.getUrlParameter('app') == "dHJ1ZQ") {
      setApp(true);
    }
  });

  const shouldOpenSidebar = openSidebar;

  return (
    <div
      className={clsx({
        [classes.root]: true,
        [classes.shiftContent]: isDesktop
      })}
    >
      {/* <LinearProgressBar /> */}
      {
        userDetail &&
      <Sidebar
        // onClose={handleSidebarClose}
        open={shouldOpenSidebar}
        variant='persistent'
        user={userDetail}
      />
}
      {/* <Notifier /> */}
      <main className={classes.content}>
        {isLoading ? <LoaderComponent open={true} /> : null}
        {!app && <Topbar user={userDetail}
          history={children.props.history}
          onSidebarOpen={handleSidebarOpen}
          onSidebarClose={handleSidebarClose}
          isSideBarOpen={shouldOpenSidebar}
          onLogout={onLogout}
          notifications={notifications}
        />}

        {children}
        {/* <Footer /> */}


      
      </main>
      {/* <span>
        v.1
      </span> */}
    </div>
  );
};

Main.propTypes = {
  children: PropTypes.node
};

const mapStateToProps = state => {
  return {
    isAuth: !!state.auth.isAuth,
    user: state.auth.user,
    access: !!(state.common.access == 'web'),
    notifications: state.notifications
  };
};

const mapDispatchToProps = dispatch => {
  return {
    onLogout: (historyNav) => console.log(0)
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Main);
