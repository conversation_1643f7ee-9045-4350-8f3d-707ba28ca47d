<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="640.903" height="300.105" viewBox="0 0 640.903 300.105">
  <defs>
    <linearGradient id="linear-gradient" y1="0.5" x2="1" y2="0.5" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#f9795a"/>
      <stop offset="1" stop-color="#eb3668"/>
    </linearGradient>
  </defs>
  <g id="Group_1557" data-name="Group 1557" transform="translate(-269.097 -152)">
    <g id="Ellipse_483" data-name="Ellipse 483" transform="translate(610 152)" fill="#fff" stroke="#707070" stroke-width="1" opacity="0.1">
      <circle cx="150" cy="150" r="150" stroke="none"/>
      <circle cx="150" cy="150" r="149.5" fill="none"/>
    </g>
    <g id="Ellipse_484" data-name="Ellipse 484" transform="translate(662 204)" fill="#fff" stroke="#707070" stroke-width="1" opacity="0.1">
      <circle cx="98.5" cy="98.5" r="98.5" stroke="none"/>
      <circle cx="98.5" cy="98.5" r="98" fill="none"/>
    </g>
    <ellipse id="Ellipse_482" data-name="Ellipse 482" cx="106" cy="8" rx="106" ry="8" transform="translate(643 405)" opacity="0.1"/>
    <g id="trophy" transform="translate(671.914 267.024)">
      <rect id="Rectangle_2188" data-name="Rectangle 2188" width="88.805" height="15.607" rx="5.748" transform="translate(24.192 131.369)" fill="#2d3c6b"/>
      <path id="Path_1608" data-name="Path 1608" d="M97.394,116.948H32.5L47,80.97H82.9Z" transform="translate(3.648 14.421)" fill="#ffc839"/>
      <path id="Path_1609" data-name="Path 1609" d="M45.8,103.229,52.044,88.97h28.23l6.239,14.259Z" transform="translate(2.436 19.495)" fill="#ff9100"/>
      <path id="Path_1610" data-name="Path 1610" d="M33.156,9.029V63.448C33.162,76.28,38.909,88.1,48.164,94.3H87.155c9.255-6.208,15-18.024,15.008-30.856V9.029Z" transform="translate(0.936 0)" fill="#ffc839"/>
      <path id="Path_1611" data-name="Path 1611" d="M56.711,46.146,50.174,35.311l11,1.451L65.64,24.474,70.1,36.762l11-1.451L74.569,46.146l6.537,10.835-11-1.451L65.64,67.818,61.176,55.53l-11,1.451Z" transform="translate(2.955 5.521)" fill="#fcf9f0"/>
      <g id="Group_1509" data-name="Group 1509" transform="translate(1.753 19.134)">
        <path id="Path_1612" data-name="Path 1612" d="M46.572,71.849H85.565V84.23H46.572Z" transform="translate(0.774 3.322)" fill="#ff9100"/>
        <path id="Path_1613" data-name="Path 1613" d="M114.766,16.473H95.3v10.86h19.464c3.705.005,6.708,3.649,6.712,8.145V52.763c.008,3.774-2.128,7.057-5.155,7.924L94.845,66.905a40.02,40.02,0,0,1-3.827,12.271L118.4,71.249c7.062-2.021,12.046-9.681,12.027-18.486V35.477C130.42,24.985,123.412,16.483,114.766,16.473Z" transform="translate(3.257 -16.473)" fill="#ff9100"/>
        <path id="Path_1614" data-name="Path 1614" d="M17.414,16.473H36.879v10.86H17.414c-3.705.005-6.708,3.649-6.712,8.145V52.763c-.008,3.774,2.128,7.057,5.155,7.924l21.478,6.219a39.993,39.993,0,0,0,3.827,12.271L13.775,71.249C6.715,69.226,1.734,61.567,1.753,52.763V35.477c.01-10.491,7.016-18.992,15.661-19Z" transform="translate(-1.753 -16.473)" fill="#ff9100"/>
      </g>
    </g>
    <g id="Group_31" data-name="Group 31" transform="translate(749 233.001)">
      <g id="Group_1" data-name="Group 1" transform="translate(0 -0.001)">
        <path id="Path_1" data-name="Path 1" d="M15.726,0A15.719,15.719,0,0,0,0,15.6,15.824,15.824,0,0,0,15.726,31.331,15.665,15.665,0,0,0,15.726,0Z" transform="translate(0 0.002)" fill="#fedb41"/>
        <path id="Path_2" data-name="Path 2" d="M273.109,15.605A15.975,15.975,0,0,1,257,31.332V0a15.873,15.873,0,0,1,16.109,15.6Z" transform="translate(-241.778 0.001)" fill="#fc3"/>
        <path id="Path_3" data-name="Path 3" d="M71.458,60A11.4,11.4,0,1,0,82.8,71.342,11.4,11.4,0,0,0,71.458,60Z" transform="translate(-55.736 -55.734)" fill="#fea832"/>
        <path id="Path_4" data-name="Path 4" d="M269.419,71.342c0,6.247-4.785,11.46-12.419,11.46V60C264.634,60,269.419,65.095,269.419,71.342Z" transform="translate(-241.778 -55.734)" fill="#fe9923"/>
      </g>
      <text id="M" transform="translate(10 20.999)" fill="#fedb41" font-size="13" font-family="SegoeUI, Segoe UI" letter-spacing="0.01em"><tspan x="0" y="0">M</tspan></text>
    </g>
    <g id="Group_31-2" data-name="Group 31" transform="translate(704.333 214.323)">
      <g id="Group_1-2" data-name="Group 1" transform="translate(-0.333 -0.323)">
        <path id="Path_1-2" data-name="Path 1" d="M12.554,0A12.548,12.548,0,0,0,0,12.456,12.631,12.631,0,0,0,12.554,25.01a12.5,12.5,0,0,0,0-25.01Z" transform="translate(0 0.001)" fill="#fedb41"/>
        <path id="Path_2-2" data-name="Path 2" d="M269.859,12.457A12.752,12.752,0,0,1,257,25.01V0a12.67,12.67,0,0,1,12.859,12.456Z" transform="translate(-244.849 0.001)" fill="#fc3"/>
        <path id="Path_3-2" data-name="Path 3" d="M69.147,60A9.1,9.1,0,1,0,78.2,69.054,9.1,9.1,0,0,0,69.147,60Z" transform="translate(-56.596 -56.594)" fill="#fea832"/>
        <path id="Path_4-2" data-name="Path 4" d="M266.914,69.054c0,4.987-3.82,9.148-9.914,9.148V60C263.094,60,266.914,64.067,266.914,69.054Z" transform="translate(-244.849 -56.594)" fill="#fe9923"/>
      </g>
      <text id="M-2" data-name="M" transform="translate(7.667 15.677)" fill="#fedb41" font-size="9" font-family="SegoeUI, Segoe UI" letter-spacing="0.01em"><tspan x="0" y="0">M</tspan></text>
    </g>
    <g id="Group_31-3" data-name="Group 31" transform="translate(749.333 179.313)">
      <g id="Group_1-3" data-name="Group 1" transform="translate(-0.333 -0.313)">
        <path id="Path_1-3" data-name="Path 1" d="M10.049,0A10.044,10.044,0,0,0,0,9.971,10.111,10.111,0,0,0,10.049,20.02a10.01,10.01,0,0,0,0-20.02Z" transform="translate(0 0.001)" fill="#fedb41"/>
        <path id="Path_2-3" data-name="Path 2" d="M267.293,9.971A10.208,10.208,0,0,1,257,20.02V0a10.142,10.142,0,0,1,10.293,9.971Z" transform="translate(-247.274 0.001)" fill="#fc3"/>
        <path id="Path_3-3" data-name="Path 3" d="M67.322,60a7.285,7.285,0,1,0,7.248,7.247A7.285,7.285,0,0,0,67.322,60Z" transform="translate(-57.275 -57.274)" fill="#fea832"/>
        <path id="Path_4-3" data-name="Path 4" d="M264.936,67.247c0,3.992-3.057,7.323-7.936,7.323V60C261.878,60,264.936,63.255,264.936,67.247Z" transform="translate(-247.274 -57.274)" fill="#fe9923"/>
      </g>
      <text id="M-3" data-name="M" transform="translate(6.667 12.687)" fill="#fedb41" font-size="7" font-family="SegoeUI, Segoe UI" letter-spacing="0.01em"><tspan x="0" y="0">M</tspan></text>
    </g>
    <g id="Group_15" data-name="Group 15" transform="translate(275.002 190)">
      <g id="Group_14" data-name="Group 14" transform="translate(0 0)">
        <g id="Group_9" data-name="Group 9" transform="translate(514.623 -20.775)">
          <path id="Path_75" data-name="Path 75" d="M3094.26,763.051a9.693,9.693,0,0,0-1.72,5.65,8.78,8.78,0,0,0,2.279,4.585l-3.144,1.677s-2.332-4.14-1.546-7.6a8.848,8.848,0,0,1,2.175-3.511,3.023,3.023,0,0,1,1.956-.8Z" transform="translate(-3089.968 -758.931)" fill="#242e6c"/>
          <path id="Path_76" data-name="Path 76" d="M3102.27,744.171l1.991-4.4s-9.292.314-12.371,7.886C3091.89,747.655,3098.39,743.018,3102.27,744.171Z" transform="translate(-3091.552 -739.771)" fill="#f24b46"/>
        </g>
        <g id="Group_10" data-name="Group 10" transform="translate(502.59 166.553)">
          <path id="Path_77" data-name="Path 77" d="M3348.77,1355.06a8.233,8.233,0,0,0,2.786-4.171,7.452,7.452,0,0,0-.727-4.287l2.968-.591s.879,3.938-.61,6.557a7.514,7.514,0,0,1-2.63,2.32,2.564,2.564,0,0,1-1.787.172Z" transform="translate(-3340.921 -1346.011)" fill="#242e6c"/>
          <path id="Path_78" data-name="Path 78" d="M3307.1,1385.61l-2.7,3.089s7.632,2.027,11.994-3.372C3316.39,1385.33,3309.97,1387.5,3307.1,1385.61Z" transform="translate(-3304.4 -1378.367)" fill="#f24b46"/>
        </g>
        <g id="Group_11" data-name="Group 11" transform="translate(160 245.529)">
          <path id="Path_79" data-name="Path 79" d="M1380.15,1457.63s.194-2.9-5.254-4.307c-2.442-.63-7.879.49-4.432,5.839,3.585,5.564,6.782,3.555,6.782,3.555s-7.478-5.853-4.486-8.529c3.3-2.954,4.572,2.9,4.572,2.9a7.2,7.2,0,0,1,2.818.542Z" transform="translate(-1369.351 -1453.165)" fill="#f9795a"/>
          <path id="Path_80" data-name="Path 80" d="M1403.96,1492.5a2.9,2.9,0,0,0,1.23-3.552l-2.929.508s1.126,2.651-1.629,2.972C1400.64,1492.43,1403.29,1493.06,1403.96,1492.5Z" transform="translate(-1395.098 -1482.615)" fill="#242e6c"/>
        </g>
        <g id="Group_12" data-name="Group 12" transform="translate(349.382 35.183)">
          <path id="Path_81" data-name="Path 81" d="M2449.63,608.539a5.441,5.441,0,0,0-7.713-1.28l-2.844-2.719s2.656-2.017,8.784,1.6C2451.87,608.507,2449.63,608.539,2449.63,608.539Z" transform="translate(-2439.073 -603.979)" fill="#efecea"/>
          <path id="Path_82" data-name="Path 82" d="M2479.32,616.022s.6-5.033-7.822-8.173c0,0,3.045,1.476,3.985,6.492Z" transform="translate(-2465.75 -607.164)" fill="url(#linear-gradient)"/>
        </g>
        <path id="Path_83" data-name="Path 83" d="M2298.01,487.1s-2.49-1.572-2.752-3.472c0,0-2.424.131-2.948.917,0,0,1.113,3.145,2.424,3.735C2294.73,488.283,2297.68,488.479,2298.01,487.1Z" transform="translate(-2128.906 -469.755)" fill="#9e30ca"/>
        <path id="Path_1650" data-name="Path 1650" d="M2292.31,487.1s2.49-1.572,2.752-3.472c0,0,2.424.131,2.948.917,0,0-1.113,3.145-2.424,3.735C2295.59,488.283,2292.64,488.479,2292.31,487.1Z" transform="translate(-2153.906 -358.755)" fill="#9e30ca"/>
        <path id="Path_84" data-name="Path 84" d="M2777.03,1499.35s2.656-1.677,2.935-3.7c0,0,2.586.14,3.145.978,0,0-1.188,3.355-2.586,3.984C2780.52,1500.61,2777.38,1500.82,2777.03,1499.35Z" transform="translate(-2526.809 -1328.599)" fill="#9e30ca"/>
        <path id="Path_85" data-name="Path 85" d="M2132.88,765.76s-2.668-1.685-2.948-3.721c0,0-2.6.141-3.159.983,0,0,1.193,3.37,2.6,4C2129.37,767.023,2132.53,767.234,2132.88,765.76Z" transform="translate(-2132.678 -778.873)" fill="#f24b46"/>
        <path id="Path_1649" data-name="Path 1649" d="M2126.773,765.76s2.668-1.685,2.948-3.721c0,0,2.6.141,3.159.983,0,0-1.193,3.37-2.6,4C2130.283,767.023,2127.123,767.234,2126.773,765.76Z" transform="translate(-1727.678 -777.873)" fill="#f24b46"/>
        <g id="Group_1553" data-name="Group 1553" transform="translate(540.059 50.756)">
          <path id="Path_34" data-name="Path 34" d="M2937.5,1765.56l.887-6.389-3.726-3.114-1.174,8.458Z" transform="translate(-2912.398 -1594.621)" fill="#ff9792"/>
          <path id="Path_35" data-name="Path 35" d="M2889.25,1801.821h15.19a8.339,8.339,0,0,0-.427-4.071c-.76-1.686-1.944-2.726-2.052-4.017a4.563,4.563,0,0,1-4.349,1.338,16.654,16.654,0,0,1-2.636,3.416C2893.979,1799.2,2889.549,1798.939,2889.25,1801.821Z" transform="translate(-2876.519 -1625.176)" fill="#1b2251"/>
          <path id="Path_36" data-name="Path 36" d="M2905.272,1314.146c.994,7.991,1.8,15.526,2.468,22.547,3.535,37.413,2.842,60.279,2.842,60.279s.255.677,2.448.688a12.528,12.528,0,0,0,2.455-.264s7.915-24.431,7.3-38.333a21.271,21.271,0,0,0-.618-4.5c-1.9-7.113,7.027-34.315-2.675-44.445S2904.813,1310.474,2905.272,1314.146Z" transform="translate(-2889.493 -1229.477)" fill="#242e6c"/>
          <path id="Path_37" data-name="Path 37" d="M2905.272,1314.146c.994,7.991,1.8,15.526,2.468,22.547l15.045,22.37a21.271,21.271,0,0,0-.618-4.5c-1.9-7.113,7.027-34.315-2.675-44.445S2904.813,1310.474,2905.272,1314.146Z" transform="translate(-2889.493 -1229.477)" fill="#1b2251"/>
          <path id="Path_38" data-name="Path 38" d="M2825.712,910.291s.366-1.825.778-3.219c.183-.617-1.09-4.666-1.969-5.164s-3.171,2.229-2.522,4.323c.333,1.076.879,4.575.879,4.575Z" transform="translate(-2821.884 -901.847)" fill="#ff9792"/>
          <path id="Path_39" data-name="Path 39" d="M2842.14,929.42s.478-1.059.91-1.23.895-1.9,1.213-2.59c0,0-.306-.449-.842.129-.326.352-1.056,1.388-1.056,1.388Z" transform="translate(-2838.312 -920.977)" fill="#ff9792"/>
          <path id="Path_40" data-name="Path 40" d="M3170.659,962.822c.093.192.222.534.337.854v-.26c.005-.64,2.407-4.143,3.395-4.362s2.38,3.059,1.147,4.872c-.634.931-2.181,4.118-2.181,4.118l-2.562-1.322s-.147-1.152-.51-1.443-.3-2.082-.4-2.831C3169.89,962.453,3170.318,962.112,3170.659,962.822Z" transform="translate(-3104.122 -948.237)" fill="#ff9792"/>
          <path id="Path_41" data-name="Path 41" d="M3067.958,1760.121c.214-2.048.268-7.435.268-7.435s-5.49-1.755-5.287-.55a38.782,38.782,0,0,1,.724,8.86l2.221,1.326Z" transform="translate(-3017.378 -1591.1)" fill="#ff9792"/>
          <path id="Path_42" data-name="Path 42" d="M2929.66,1343s21.207,58.131,25.382,83.348a4.229,4.229,0,0,0,2.775.444,3.1,3.1,0,0,0,2.606-1.671s3.832-30.222-3.784-40.715c-5.23-7.209,2.257-38.868-9.927-41.407S2929.66,1343,2929.66,1343Z" transform="translate(-2909.292 -1258.714)" fill="#242e6c"/>
          <path id="Path_43" data-name="Path 43" d="M2945.627,1014.112a6.912,6.912,0,0,0,.32.71,5.763,5.763,0,0,0,.616.949,8.64,8.64,0,0,1-.256-1.1h0q-.051-.29-.091-.625a23.449,23.449,0,0,1-.053-4.175,22.969,22.969,0,0,0,.355,4.143c.032.175.069.352.107.527a9.643,9.643,0,0,0,.524,1.67c.04.1.083.2.129.293a42.716,42.716,0,0,1,4.834,5.421c1.188,1.528,2.39,3.192,3.523,4.87.439.647.868,1.293,1.281,1.935h0c.176.271.347.534.513.8h0c.213.332.413.658.613.985.556.9,1.073,1.778,1.535,2.609.107.189.213.375.308.558,1.434,2.628,2.288,4.713,2.092,5.513l.263-.182.161-.107c.586-.406,1.741-1.2,3.2-2.192l.1-.3.73-2.263a5.254,5.254,0,0,1-.057,2.039c2.292-1.55,5.155-3.46,7.746-5.124h0c1.937-1.241,3.721-2.342,5.01-3.053a32.735,32.735,0,0,0-.812-3.281c.758.335,1.479,1.922,1.821,2.762.086-.044.169-.08.245-.112,0,0,.142-6.481-8.326-12.6-4.435-3.2-7.579-4.726-6.917-7.858s1.236-16.949-8.532-14.765a6.06,6.06,0,0,0-2.062-.878,1.837,1.837,0,0,1,.761.509,7.266,7.266,0,0,0-3.99.166,6.092,6.092,0,0,0-2.423,2.624h0c-.05.09-.107.177-.156.271-.348.608-.687,1.292-1.015,2.028a34.044,34.044,0,0,0-1.542,4.27h0c-.146.5-.281,1-.4,1.515a.394.394,0,0,0-.018.075h0C2944.794,1006.842,2944.485,1011.187,2945.627,1014.112Z" transform="translate(-2921.651 -974.388)" fill="#f24b46"/>
          <path id="Path_44" data-name="Path 44" d="M2959.3,1091.784c-.033.01,2.734,2.012,5.081,1.731,2.208-.263,4-2.807,3.975-2.818-.872-.312-.948-1.585-.14-6.888l-.418.019-7.03.336s-.165,2.173-.461,4.2C2960.058,1090.065,2959.717,1091.667,2959.3,1091.784Z" transform="translate(-2933.331 -1049.422)" fill="#ff9792"/>
          <path id="Path_45" data-name="Path 45" d="M2965.09,1084.251s-.165,2.172-.461,4.2c3.843.461,6.377-2.725,7.492-4.535Z" transform="translate(-2937.655 -1049.506)" fill="#ff7d78"/>
          <path id="Path_46" data-name="Path 46" d="M2962.322,1017.311a20.319,20.319,0,0,0,3.357-5.5c.631-1.881.712-9.189-6.639-9.228a9.883,9.883,0,0,0-4.282.93c-1.72.823-4.314,2.766-2.722,9.394l.342,2.208Z" transform="translate(-2927.039 -983.543)" fill="#f24b46"/>
          <path id="Path_47" data-name="Path 47" d="M2900.71,1157.171a29.784,29.784,0,0,0,20.637,2.642c.135-.033,9.46-1.521,9.46-1.521-.16-.439-1.365-7.641-1.523-8.044-2.038-5.259-5.764-33.8-8.875-35.212-.794.866-3.066,1.458-5.01,1.281-2.242-.2-2.989-1.165-2.8-2.2-2.477,1.174-4.377,2.26-4.377,2.26C2905.952,1119.768,2900.71,1157.171,2900.71,1157.171Z" transform="translate(-2885.813 -1074.002)" fill="#efecea"/>
          <path id="Path_48" data-name="Path 48" d="M2951.242,1030.793s9.021,1.494,9.864-4.137,2.577-9.232-3.147-10.425-7.168.789-7.906,2.6S2948.915,1030.259,2951.242,1030.793Z" transform="translate(-2925.365 -994.317)" fill="#ff9792"/>
          <path id="Path_49" data-name="Path 49" d="M2972.67,1010.144s1.494,6.977,5.147,8.519,5.454.77,5.454.77a10.878,10.878,0,0,1-2.573-6.881S2975.008,1007.283,2972.67,1010.144Z" transform="translate(-2944.174 -988.987)" fill="#f24b46"/>
          <path id="Path_50" data-name="Path 50" d="M2953.287,1011.414a8.072,8.072,0,0,0-2.733,2.567c-.664,1.368-1.305,4.29-2.152,4.056,0,0-1.2-4.769,1.281-7C2952.305,1008.682,2953.287,1011.414,2953.287,1011.414Z" transform="translate(-2924.262 -989.63)" fill="#f24b46"/>
          <path id="Path_51" data-name="Path 51" d="M2826.564,945.032a5.782,5.782,0,0,1,1.613-.5,5.013,5.013,0,0,1,1.416.148s2.545,15.821,2.734,16.659,10.917,12.923,13.809,12.875c2.611-.044,5.706,1.973,6.28,2.36,0,0-6.084,12.861-6.385,15.276-.33,2.628-3.479,43.11-3.479,43.11s.187,1.883-3.332.871-4.4-1.851-4.4-1.851,3.231-31.869,3.909-41.78a19.068,19.068,0,0,1,.618-3.7,3.746,3.746,0,0,0-.6-3.184c-4.449-6.107-11.474-16-11.865-18.1C2826.308,964.124,2826.564,945.032,2826.564,945.032Z" transform="translate(-2825.637 -936.457)" fill="#9e30ca"/>
          <path id="Path_52" data-name="Path 52" d="M3022.55,1801.821h15.19a8.345,8.345,0,0,0-.427-4.071c-.76-1.685-1.944-2.726-2.052-4.017a4.563,4.563,0,0,1-4.349,1.337,16.667,16.667,0,0,1-2.636,3.416C3027.279,1799.2,3022.86,1798.939,3022.55,1801.821Z" transform="translate(-2984.63 -1625.176)" fill="#1b2251"/>
          <path id="Path_53" data-name="Path 53" d="M2967.143,1035.752c.512-3.15,7.045-15.8,7.045-15.8s4.132-1.236,6.852-.251,16.79-6.959,17.242-7.686,7.987-14.775,7.987-14.775a1.748,1.748,0,0,1,1.627.16,1.833,1.833,0,0,1,.995,1.524s-5.817,17.681-7.365,20.417c-.933,1.649-8.779,7.392-14.861,11.692a3.818,3.818,0,0,0-1.581,3.629c.818,6.046,1.026,9.451,1.968,19.826.78,8.607,1.078,25.27,1.078,25.27s-6.806,2.355-11.5,2.669-5.722-4.639-5.722-4.639S2966.62,1038.986,2967.143,1035.752Z" transform="translate(-2939.656 -979.106)" fill="#9e30ca"/>
          <path id="Path_54" data-name="Path 54" d="M2959.52,1076.582a4.028,4.028,0,0,1-2.39.449c-.887-.133-1.088-1.184-1.088-1.184a3.489,3.489,0,0,1,1.6,0,6.8,6.8,0,0,1,1.877.734Z" transform="translate(-2930.695 -1042.895)" fill="#efecea"/>
        </g>
        <g id="Group_1554" data-name="Group 1554" transform="translate(301.998 118.299)">
          <path id="Path_55" data-name="Path 55" d="M1998.282,1015.709s-2.321,3.825,2.043,6.233c0,0-3.354,1.209-4.522.753a2.52,2.52,0,0,0,.51.411s-3.152,1-4.724.454-4.951-15.81,4.349-16.614c0,0,7.338-2.628,8.891,5.562s4.378,7.4,4.378,7.4a4.409,4.409,0,0,1-1.061.685s-.609-.039-.8-.291a1.808,1.808,0,0,0,.417.515s-2.976,1.125-4.8.491S1998.308,1017.486,1998.282,1015.709Z" transform="translate(-1954.257 -992.398)" fill="#242e6c"/>
          <path id="Path_56" data-name="Path 56" d="M2040.016,1800.831h-11.87a6.52,6.52,0,0,1,.335-3.183c.594-1.317,1.519-2.13,1.6-3.139a3.566,3.566,0,0,0,3.4,1.045,13.019,13.019,0,0,0,2.06,2.67C2036.32,1798.789,2039.777,1798.586,2040.016,1800.831Z" transform="translate(-1986.647 -1657.026)" fill="#1b2251"/>
          <path id="Path_57" data-name="Path 57" d="M2022.063,1389.849a10.755,10.755,0,0,0,2.069.422c1.87.166,2.142-.39,2.142-.39s1.857-16.54,7.869-48.156a87.942,87.942,0,0,0,1.523-11.152c.094-1.216.186-2.564.285-4.156.272-4.26-.374-11.432-3.572-13.317-1.454-.858-3.434-.619-6.1,1.475-.209.165-.425.342-.643.53-7.793,6.742-4.6,26.1-4.913,35.206a12.3,12.3,0,0,1-.514,3.577,19.767,19.767,0,0,0-1.461,4.015C2017.12,1369.718,2022.063,1389.849,2022.063,1389.849Z" transform="translate(-1978.502 -1250.578)" fill="#7f22ba"/>
          <path id="Path_58" data-name="Path 58" d="M2031.069,1352.832l14.941-19.742c.094-1.215.185-2.564.286-4.155.273-4.26-.374-11.432-3.572-13.317l-6.1,1.475c-.209.165-.424.342-.643.531C2028.187,1324.373,2031.378,1343.726,2031.069,1352.832Z" transform="translate(-1988.838 -1253.093)" fill="#6615b2"/>
          <path id="Path_59" data-name="Path 59" d="M1763.37,1617.135l4.94-10.793a6.517,6.517,0,0,1,2.753,1.63c.951,1.088,1.64.895,3.8-.607a11.885,11.885,0,0,0-1.429,3.657c-.4.049-2.661,2.239-4.98,2.843C1767.534,1614.1,1765.509,1617.851,1763.37,1617.135Z" transform="translate(-1763.37 -1498.305)" fill="#1b2251"/>
          <path id="Path_60" data-name="Path 60" d="M1860.458,1339.362s-10.637,36.977-14.534,39.358-24.389,5.654-28.557,7.439a3.137,3.137,0,0,1-.209-2.281,13.727,13.727,0,0,1,.654-1.587s12.725-9.566,21.1-10.532c2.079-.24-4.232-31.98,9.814-33.248C1861.564,1337.355,1860.458,1339.362,1860.458,1339.362Z" transform="translate(-1808.665 -1272.095)" fill="#7f22ba"/>
          <path id="Path_61" data-name="Path 61" d="M2187.888,922.746s-.286-1.427-.608-2.515c-.142-.482.851-3.646,1.539-4.036s2.475,1.742,1.972,3.378c-.261.841-.689,3.575-.689,3.575Z" transform="translate(-2120.916 -916.148)" fill="#ff9792"/>
          <path id="Path_62" data-name="Path 62" d="M2182.3,941.517s-.374-.828-.707-.961-.7-1.487-.947-2.023c0,0,.239-.354.657.1.256.275.825,1.082.825,1.082Z" transform="translate(-2115.33 -934.92)" fill="#ff9792"/>
          <path id="Path_63" data-name="Path 63" d="M1855.571,973.066c-.072.15-.177.418-.265.671v-.2c0-.5-1.881-3.238-2.652-3.408s-1.857,2.387-.9,3.807c.5.728,1.7,3.218,1.7,3.218l2-1.033s.116-.9.4-1.127.233-1.627.314-2.21C1856.172,972.776,1855.845,972.511,1855.571,973.066Z" transform="translate(-1837.603 -961.668)" fill="#ff9792"/>
          <path id="Path_64" data-name="Path 64" d="M2042.562,1094.063c.026.007-2.137,1.573-3.971,1.353a5.354,5.354,0,0,1-3.106-2.2c.682-.244.74-1.238.109-5.384l.327.015,5.495.265s.128,1.7.36,3.281C2041.97,1092.719,2042.235,1093.966,2042.562,1094.063Z" transform="translate(-1992.891 -1060.955)" fill="#ff9792"/>
          <path id="Path_65" data-name="Path 65" d="M2043.76,1088.185s.128,1.7.36,3.281c-3,.36-4.983-2.13-5.854-3.543Z" transform="translate(-1995.238 -1061.037)" fill="#ff7d78"/>
          <path id="Path_66" data-name="Path 66" d="M2018.939,1022.7a15.882,15.882,0,0,1-2.623-4.3c-.493-1.469-.556-7.18,5.187-7.211a7.729,7.729,0,0,1,3.346.727c1.344.644,3.37,2.162,2.126,7.338l-.266,1.726Z" transform="translate(-1976.553 -996.313)" fill="#242e6c"/>
          <path id="Path_67" data-name="Path 67" d="M2019.808,1150.919s-5.393,3.4-14.366,1.217c-.106-.026-5.1-.823-5.1-.823.126-.343,5.378-9.018,5.5-9.336,1.591-4.11-2.1-23.731.336-24.831a5.5,5.5,0,0,0,3.914,1c1.749-.155,2.335-.91,2.19-1.723,1.935.915,3.423,1.768,3.423,1.768C2017.474,1120.841,2019.808,1150.919,2019.808,1150.919Z" transform="translate(-1963.248 -1085.074)" fill="#ff9792"/>
          <path id="Path_68" data-name="Path 68" d="M2035.755,1035.381s-7.049,1.168-7.708-3.233-2.014-7.214,2.458-8.146,5.6.619,6.178,2.034S2037.568,1034.966,2035.755,1035.381Z" transform="translate(-1986.011 -1006.884)" fill="#ff9792"/>
          <path id="Path_69" data-name="Path 69" d="M2023.444,1018.192s-1.167,5.451-4.023,6.657-4.262.6-4.262.6a8.5,8.5,0,0,0,2.011-5.377S2021.614,1015.947,2023.444,1018.192Z" transform="translate(-1975.749 -1001.654)" fill="#242e6c"/>
          <path id="Path_70" data-name="Path 70" d="M2065.42,1019.313a6.311,6.311,0,0,1,2.135,2.007c.519,1.068,1.02,3.353,1.68,3.174,0,0,.936-3.727-1-5.469C2066.189,1017.174,2065.42,1019.313,2065.42,1019.313Z" transform="translate(-2018.142 -1002.285)" fill="#242e6c"/>
          <path id="Path_71" data-name="Path 71" d="M1867.61,965.592s4.545,13.817,5.755,15.954c.729,1.289,8.917,6.388,13.67,9.748a.816.816,0,0,1,.182.218.023.023,0,0,0,.005.007c1.187,1.834,4.873,13.188,3.993,15.227-3.707,8.565-6.247,11.878-7.172,14.281a2.283,2.283,0,0,0,1.365,2.972,39.035,39.035,0,0,0,6.414,1.68q.758.131,1.557.233c7.305.927,12.251-.787,14.765-2.054a3.344,3.344,0,0,0,1.83-3.174c-.151-2.71-1.5-5.393-2.122-11.909-.462-4.945,3.8-12.466,3.625-15.1a8.8,8.8,0,0,0-1.333-3.094h0c-.784-1.428-1.215-2.719-.693-3.436,3.477-4.774,10.983-11.351,11.29-13,.448-2.415.246-17.337.246-17.337a4.508,4.508,0,0,0-1.26-.389,3.941,3.941,0,0,0-1.107.115s-1.989,12.364-2.137,13.019-8.53,10.1-10.786,10.06c-2.04-.034-4.918,1.542-5.366,1.845a21.7,21.7,0,0,1,2.289,7.631c.318,3.067-1.73,1.7-3.017-.4a68.224,68.224,0,0,0-5.247-6.892,9.942,9.942,0,0,0-4.977.029c-2.122.769-13.121-5.439-13.474-6.012s-6.242-11.545-6.242-11.545a1.366,1.366,0,0,0-1.27.125,1.435,1.435,0,0,0-.782,1.193Z" transform="translate(-1851.296 -950.112)" fill="#f9795a"/>
          <path id="Path_72" data-name="Path 72" d="M2068.09,1081.247s.3.608.707.619a4.792,4.792,0,0,0,1.5-.192,5.84,5.84,0,0,0,.24-.741,6.852,6.852,0,0,0-2.451.314Z" transform="translate(-2020.391 -1055.139)" fill="#efecea"/>
          <path id="Path_73" data-name="Path 73" d="M1973.141,1213.163a39.036,39.036,0,0,0,6.414,1.68c2.521-3.476,6.786-7.848,5.688-15.889-1.134-8.311-9.307-17.064-10.288-18.268,1.187,1.833,4.873,13.188,3.993,15.227-3.707,8.565-6.247,11.878-7.171,14.281a2.283,2.283,0,0,0,1.364,2.97Z" transform="translate(-1939.03 -1139.278)" fill="#f24b46"/>
          <path id="Path_74" data-name="Path 74" d="M2125.339,1086.126c-.784-1.428-1.216-2.719-.693-3.436,3.476-4.774,10.982-11.351,11.29-13-.744,1.785-9.835,7.119-12.392,9.412C2121.016,1081.378,2125.233,1086.02,2125.339,1086.126Z" transform="translate(-2066.49 -1045.666)" fill="#f24b46"/>
        </g>
      </g>
    </g>
    <g id="giftbox" transform="matrix(0.996, 0.087, -0.087, 0.996, 819.065, 176.615)">
      <path id="Path_117" data-name="Path 117" d="M62.988,48.836H2.852A2.852,2.852,0,0,1,0,45.984V0H65.84V45.984A2.852,2.852,0,0,1,62.988,48.836Zm0,0" transform="translate(5.602 28.157)" fill="#a2dae7"/>
      <path id="Path_118" data-name="Path 118" d="M0,0H65.84V9.569H0ZM0,0" transform="translate(5.602 28.157)" fill="#8dd3e7"/>
      <path id="Path_119" data-name="Path 119" d="M77.044,2.292V14.147a2.293,2.293,0,0,1-2.294,2.292H2.292A2.291,2.291,0,0,1,0,14.147V2.292A2.291,2.291,0,0,1,2.292,0H74.751A2.293,2.293,0,0,1,77.044,2.292Zm0,0" transform="translate(0 15.889)" fill="#a2dae7"/>
      <path id="Path_120" data-name="Path 120" d="M23.31,9.134q-.75.874-1.47,1.77a66.469,66.469,0,0,0-4.9,6.965q-.682,1.114-1.318,2.253H0q1.369-3.107,3-6.092A80.612,80.612,0,0,1,9.717,3.684q.9-1.175,1.84-2.32C11.934.9,12.316.451,12.7,0l4.277,3.684c.221.189.442.381.662.571q2.525,2.174,5.05,4.35c.206.175.411.353.617.53Zm0,0" transform="translate(12.592 12.205)" fill="#8dd3e7"/>
      <path id="Path_121" data-name="Path 121" d="M23.314,20.123H7.687q-.636-1.139-1.318-2.253a66.73,66.73,0,0,0-4.9-6.965Q.751,10.009,0,9.134L.617,8.6q2.527-2.177,5.051-4.35l.663-.571Q8.47,1.842,10.609,0q.582.675,1.147,1.363Q12.7,2.508,13.6,3.684a80.75,80.75,0,0,1,6.72,10.346q1.627,2.981,3,6.092Zm0,0" transform="translate(35.903 12.205)" fill="#efb025"/>
      <g id="Group_19" data-name="Group 19" transform="translate(9.184 12.206)">
        <path id="Path_122" data-name="Path 122" d="M29.337,9.135q-.751.873-1.471,1.77A66.861,66.861,0,0,0,13.852,43.062,67.23,67.23,0,0,1,8.416,34.58,70.613,70.613,0,0,0,0,41.033,80.858,80.858,0,0,1,17.584,1.364C17.96.9,18.342.452,18.73,0l4.94,4.255Q26.2,6.429,28.721,8.6c.206.175.41.353.617.53Zm0,0" fill="#ff4440"/>
        <path id="Path_123" data-name="Path 123" d="M29.338,41.033a70.609,70.609,0,0,0-8.416-6.453,67.228,67.228,0,0,1-5.436,8.481A66.836,66.836,0,0,0,6.368,17.87a66.681,66.681,0,0,0-4.9-6.965Q.751,10.009,0,9.135c.206-.177.41-.354.617-.53q2.527-2.177,5.052-4.35Q8.139,2.125,10.608,0q.582.675,1.147,1.363a80.673,80.673,0,0,1,8.56,12.668,80.875,80.875,0,0,1,9.023,27Zm0,0" transform="translate(29.337 0)" fill="#ff4440"/>
        <path id="Path_124" data-name="Path 124" d="M0,0H12.268V10.987H0ZM0,0" transform="translate(23.204 9.135)" fill="#ff4440"/>
      </g>
      <path id="Path_125" data-name="Path 125" d="M20.315,12.668,6.368,16.507a66.679,66.679,0,0,0-4.9-6.965l-.855-2.3L0,5.583,5.668,2.892,11.755,0a80.675,80.675,0,0,1,8.56,12.668Zm0,0" transform="translate(38.521 13.569)" fill="#ea2f2f"/>
      <path id="Path_126" data-name="Path 126" d="M20.312,5.582,19.7,7.241l-.854,2.3a66.5,66.5,0,0,0-4.9,6.965L0,12.667A80.664,80.664,0,0,1,8.558,0l6.087,2.891Zm0,0" transform="translate(18.209 13.57)" fill="#ea2f2f"/>
      <path id="Path_127" data-name="Path 127" d="M22.258.533,0,11.1,4.276,22.6l23.758-6.539a5.493,5.493,0,0,0,3.691-7.211L29.763,3.58A5.493,5.493,0,0,0,22.258.533Zm0,0" transform="translate(38.522 0)" fill="#ff4440"/>
      <path id="Path_128" data-name="Path 128" d="M9.812.533,32.07,11.1,27.795,22.6,4.037,16.063A5.493,5.493,0,0,1,.346,8.853L2.307,3.58A5.493,5.493,0,0,1,9.812.533Zm0,0" transform="translate(6.451 0)" fill="#ff4440"/>
      <path id="Path_129" data-name="Path 129" d="M1.228,8.691a1.227,1.227,0,0,1-.662-2.26,60.29,60.29,0,0,1,8.78-4.52c6.635-2.684,11.066-2.54,13.168.428a1.227,1.227,0,1,1-2,1.418C17.662-.266,5.814,5.99,1.888,8.5a1.222,1.222,0,0,1-.66.193Zm0,0" transform="translate(42.608 7.034)" fill="#ea2f2f"/>
      <path id="Path_130" data-name="Path 130" d="M21.512,8.691a1.223,1.223,0,0,1-.66-.193C16.926,5.99,5.078-.266,2.228,3.757a1.227,1.227,0,1,1-2-1.418c2.1-2.968,6.532-3.112,13.168-.428a60.227,60.227,0,0,1,8.78,4.52,1.227,1.227,0,0,1-.662,2.26Zm0,0" transform="translate(11.574 7.034)" fill="#ea2f2f"/>
      <path id="Path_131" data-name="Path 131" d="M10.44,0H3.459A3.46,3.46,0,0,0,0,3.46V13.6a3.46,3.46,0,0,0,3.459,3.46H10.44A3.46,3.46,0,0,0,13.9,13.6V3.46A3.46,3.46,0,0,0,10.44,0Zm0,0" transform="translate(31.572 7.151)" fill="#ea2f2f"/>
      <path id="Path_132" data-name="Path 132" d="M0,0H12.268V44.665H0ZM0,0" transform="translate(32.388 32.328)" fill="#ff4440"/>
      <path id="Path_133" data-name="Path 133" d="M0,0H12.268V5.4H0ZM0,0" transform="translate(32.388 32.328)" fill="#ea2f2f"/>
    </g>
  </g>
</svg>
