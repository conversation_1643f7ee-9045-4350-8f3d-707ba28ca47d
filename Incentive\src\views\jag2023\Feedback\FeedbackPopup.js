import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DialogTitle, DialogContent, DialogActions, TextField, Slide } from '@material-ui/core';
import FeedbackIcon from '@material-ui/icons/Feedback';
import '../../jag2023/scss/Feedback.scss'
// Custom slide transition for the popup
const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

function FeedbackPopup() {
  const [open, setOpen] = useState(false);

  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <div>    
      <button  className="feedbackBtn" onClick={handleOpen}> <img src="/images/jag2023/feedbackIcon.png"/>  Share Feedback</button>
  
      <Dialog
        open={open}
        onClose={handleClose}
        TransitionComponent={Transition} 
        className="Feedbackpopup"
        keepMounted
        fullWidth
        maxWidth="xs"       
        PaperProps={{
          style: {
            transform: 'translateY(35%)', // Move the dialog off-screen initially
            transition: 'transform 500ms ease-in-out', 
            position: 'absolute',
            top: 0,
            right: 0,
          },
         
        }}
      >
        <DialogTitle>Feedback</DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            id="feedback"
            //label="Your Feedback"
            type="text"
            className="feedbackInput"
            variant="outlined"
            fullWidth
            multiline
            rows={4}
          /> 
         <p className="caption">Submitting feedback as <strong>Keysang Yonthan PW37624</strong></p>
          
          <Button onClick={handleClose} color="primary" className="Submitbutton">
          Submit Feedback
          </Button>
        </DialogContent>
       
      </Dialog>
    </div>
  );
}

export default FeedbackPopup;
