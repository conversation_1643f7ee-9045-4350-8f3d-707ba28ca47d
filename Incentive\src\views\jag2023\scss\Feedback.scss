.feedbackBtn {
    position: fixed;
    right: 10px;
    bottom: 9px;
    padding: 8px 10px;
    cursor: pointer;
    width: auto;
    border-radius: 48px;
    background: #FD9727;
    box-shadow: 0px 3px 12px 0px #0065ff29 !important;
    text-align: center;
    color: #FFF;
    font-family: Poppins;
    font-size: 12px;
    font-style: normal;
    border: none;
    font-weight: 600;
    line-height: normal;
}

.Feedbackpopup {
    .MuiDialogContent-root {
        text-align: center;
    }

    h2 {
        color: #253858;
        text-align: center;
        font-family: Poppins;
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .feedbackInput {
        .MuiOutlinedInput-multiline {
            border-radius: 8px;
            background: #F5F5F5;
            height: 215px;
        }
    }

    .caption {
        color: rgba(37, 56, 88, 0.60);
        text-align: center;
        font-family: Poppins;
        font-size: 12px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
    }

    .Submitbutton {
        border-radius: 4px !important;
        background-color: #FD9727 !important;
        width: 215px;
        color: #FFF !important;
        font-family: Poppins !important;
        font-size: 16px !important;
        font-style: normal;
        font-weight: 500 !important;
        line-height: normal;
        height: 55px;
        margin: 20px 0px 10px;
    }
}

@media screen and (max-width: 750px) {
    .Feedbackpopup{
        z-index: 9999999999999999 !important;
        .MuiDialog-paperFullWidth{
            bottom: 0px;
            top: 0px !important;
            height: 433px;
            border-radius: 16px !important;
            left: 0;
            margin: auto !important;
            transform: translateY(0%) !important;
            width: calc(100% - 12px) !important;
        }
    }
}
