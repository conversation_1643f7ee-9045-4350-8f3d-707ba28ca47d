import React from "react";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/core/styles";
import LinearProgress from "@material-ui/core/LinearProgress";
import Typography from "@material-ui/core/Typography";
import Box from "@material-ui/core/Box";
import { Grid, Card } from "@material-ui/core";

function LinearProgressWithLabel(props) {
  return (
    <Box display="flex" alignItems="center">
      <Box width="100%" mr={1}>
        <LinearProgress variant="determinate" {...props} />
      </Box>
      <Box minWidth={35}>
        <Typography variant="body2" color="textSecondary">{`${Math.round(
          props.value
        )}%`}</Typography>
      </Box>
    </Box>
  );
}

LinearProgressWithLabel.propTypes = {
  /**
   * The value of the progress indicator for the determinate and buffer variants.
   * Value between 0 and 100.
   */
  value: PropTypes.number.isRequired,
};

const ProcessBar = (props) => {

  const {firstLevel, lastLevel, levelPercentage} = props;
  const useStyles = makeStyles({
    root: {
      width: "100%",
    },
    statusBar: {
      width: `${parseFloat(levelPercentage) > 100 ? 100 : levelPercentage }% !important`,
    }
  });  

  const classes = useStyles();

  return (
    <div className={classes.root}>
      <Grid container spacing={2} className="progress-bar">
        <Grid className={classes.statusBar + " " + "status-bar"}>
          <span>{parseFloat(levelPercentage) > 100 ? "+" + (parseFloat(levelPercentage) - 100) : levelPercentage }%</span>
        </Grid>
        <ul>
          <li>
            <span>Level {firstLevel}</span>
          </li>
          { (lastLevel != -1 || lastLevel != "-1") && (
            <li>
              <span>Level {lastLevel}</span>
            </li>
          )}
        </ul>
      </Grid>
    </div>
  );
}
export default ProcessBar;