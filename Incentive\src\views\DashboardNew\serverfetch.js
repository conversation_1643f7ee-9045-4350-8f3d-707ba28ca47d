import React, {useEffect, useState} from "react";
import * as services from "../../services";

const GetServerData = (AgentId) =>{
    const[ProjectionDetailsMotor, setProjectionDetailsMotor] = useState([]);
    const[ProjectedData, setProjectedData] = useState([]);
    const[productId, setProductId] = useState(null)
    useEffect(() => {
        services
          .API_GET(`Incentive/GetProjectedIncentive/${AgentId}`).then(response => {
            ////debugger
            if (response && response.Status) {
              response = JSON.parse(response.Response);
              //console.log("Dashboard================", response[0]);
              setProjectedData(response[0]);
              setProductId(response[0].ProductId);
              // let key = props.enqueueSnackbar('To make the projections more accurate, we are now showing the incentives post salary adjustment.', {
              //   variant: 'error',
              // })
              //debugger;
              try {
                const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
                let url = `Incentive/InsertAgentIncentivLog/${response[0].UserId}/0/${response[0].ProductId}/${dt}?PageName=ProjectedIncentive&EventName=Click`;
    
                services
                  .API_GET(url).then(response => { })
                  .catch((err) => {
                  });
              }
              catch (e) {
    
              }
              //debugger;
              if ([117, 115].indexOf(response[0].ProductId) > -1) {
                try {
                  services
                    .API_GET(`Incentive/GetProjectionDetailsMotor/${AgentId}`).then(response => {
                      ////debugger
                      if (response && response.Status) {
                        response = JSON.parse(response.Response);
                        if (response.length > 0) {
                          response = JSON.parse(response[0]);
                          setProjectionDetailsMotor(response);
                        }
                      }
                    })
                    .catch((err) => {
                      console.log("Error", err);
                    });
                }
    
                catch (e) {
    
                }
              }
    
    
    
    
            }
          })
          .catch((err) => {
            console.log("Error", err);
          });
      }, [AgentId]);

      

      
      
    }

export default GetServerData;


