import React from "react";
import { makeStyles } from "@material-ui/styles";
import { Grid, Typography, Card, CardContent } from "@material-ui/core";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import { BookingDetails as DialogBookingDetails } from "./../../components/Dialogs";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";

const useStyles = makeStyles((theme) => ({
  content: {
    paddingTop: 120,
    textAlign: "center",
  },

  card: {
    background: "#ffffff",
    boxShadow: "0px 6px 16px #3469CB29",
    borderRadius: "20px 20px 0 0",
  },
  textRight: {
    textAlign: "right",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#0052cc 0% 0%",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#808080",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
  },
  expandBox: {
    textAlign: "center",
    "& svg": {
      position: "absolute",
      left: 0,
      right: 0,
      margin: "auto",
      top: "5px",
    },
  },
}));

const BookingBox = () => {
  const classes = useStyles();
  const [open, setOpen] = React.useState(false);

  return (
    <>
      <div className={classes.card}>
        <div className="booking_details">
          <CardContent
            onClick={() => setOpen(true)}
            className="d-flex flex-column"
          >
            <div className="d-flex py-0">
              <ul className="top_section">
                <li>
                  <img src="/images/health_insurance.svg" /> Health
                </li>
                <li>Meet Kapadia</li>
              </ul>

              <ul className="top_section1">
                <li>
                  Rejected <span>Document Pending</span>
                </li>
                <li>Meet Kapadia</li>
              </ul>

              <div className="icons_block">
                <div>
                  <em>
                    <img src="/images/ticket.jpg" />
                  </em>

                  <em>
                    <img src="/images/rupee.jpg" />
                  </em>
                </div>
                <div>
                  <span>New</span>
                  <hr />
                </div>
              </div>
            </div>
            <ul>
              <div>
                <li>
                  <span>
                    Booking Id <i className="fa fa-angle-right"></i>{" "}
                  </span>
                  <p>153823948123</p>
                </li>
                <li className="text-right">
                  <span>Booking Date</span>
                  <p>10/18/2019 8:58:16 PM</p>
                </li>
              </div>

              <div>
                <li>
                  <span>Sum Assured </span>
                  <p>41580.00</p>
                </li>
                <li className="text-right">
                  <span>Application No. </span>
                  <p>QVMNPB26940681</p>
                </li>
              </div>

              <div>
                <li>
                  <span>Premium(Monthly)</span>
                  <p>1630.00</p>
                </li>
                <li className="text-right">
                  <span>Policy No.</span>
                  <p>VMPB186576000100</p>
                </li>
              </div>

              <div>
                <li>
                  <span>Insurer </span>
                  <p>Royal Sundaram</p>
                </li>
                <li className="text-right">
                  <span>Plan Name </span>
                  <p>Royal Sundaram</p>
                </li>
              </div>
            </ul>
          </CardContent>
        </div>
      </div>

      {open ? (
        <DialogBookingDetails handleClose={() => setOpen(false)} open={open} />
      ) : null}
    </>
  );
};

export default BookingBox;
