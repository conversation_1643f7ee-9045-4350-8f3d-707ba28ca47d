import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import Button from '@material-ui/core/Button';
import * as services from "../../services";

import {
    Grid,
    Typography,
    Card,
    CardContent,
    List,
    ListItem,
    MenuItem,
    TextField,
} from "@material-ui/core";
import Autocomplete from '@material-ui/lab/Autocomplete';

import moment from "moment";

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(0),
    },
    margin: {
        margin: theme.spacing(1),
    },
    extendedIcon: {
        marginRight: theme.spacing(1),
    },
    forBox: {
        width: "100%",
    },
    card: {
        background: "#ffffff",
        boxShadow: "0px 0px 26px #0000001a",
        borderRadius: "20px",
    },

    cardBody: {
        padding: "15px 15px 0 !important",
    },

}));

const LotteryBanner = (props) => {
    const classes = useStyles();
    const { agentDetail } = props;
    const [state, setState] = useState({ right: false });
    const [lotteryTicketData, setLotteryTicketData] = useState([]);
    const [srno, setSrNo] = useState(1);
    const [ticketno, setTicketNo] = useState();
    const [value, setValue] = React.useState();


    //   useEffect(() => {//debugger;
    //     setLotteryTicketData([]);
    //     if (!userId ) {
    //       return;
    //     }

    //     services
    //       .API_GET(`jag/GetTickets/${userId}`)
    //       .then(response => {

    //         if (response.Status && response.Response != "[]" && response.Response.length > 0) {
    //             setLotteryTicketData(JSON.parse(response.Response));
    //         }
    //       })
    //       .catch((err) => {
    //         console.log("Error", err);
    //       });
    //   }, [props]);

    const toggleDrawer = (anchor, open) => (event) => {
        if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
            return;
        }

        if (open == false) {
            setValue('');

        }

        if (open == true) {
            //debugger;
            setLotteryTicketData([]);
            // if (!userId ) {
            //   return;
            // }

            services
                .API_GET(`jag/GetTickets/${agentDetail.AgentId}`)
                .then(response => {

                    if (response.Status && response.Response != "[]" && response.Response.length > 0) {
                        let res = JSON.parse(response.Response);
                        if (res && res.length > 0) {
                            res = JSON.parse(res[0])
                            setLotteryTicketData(res);
                            setTicketNo(res[0].TicketNumber);
                        }
                    }
                })
                .catch((err) => {
                    console.log("Error", err);
                });
        }
        setState({ ...state, [anchor]: open });

    };

    const addClassActive = (ticketno) => {
        var elems = document.querySelectorAll(".ticketBox");

        [].forEach.call(elems, function (el) {
            el.classList.remove("TicketActive");
        });
        var element = document.getElementById('ticketselect_' + ticketno);
        element.classList.add("TicketActive");
        console.log(element);
    }

    const ticketSelect = (e, ticketno, serialno) => {
        console.log(e, ticketno, serialno);
        setSrNo(serialno);
        setTicketNo(ticketno);
        addClassActive(ticketno);

    }

    const ticketSearch = () => {
        if (value) {
            let index = lotteryTicketData.findIndex((item) => {
                return item.TicketNumber == value.TicketNumber
            })

            setSrNo(index + 1);
            setTicketNo(value.TicketNumber);
            addClassActive(value.TicketNumber);
        }
    }

    const list = () => (
        <div className="ticketSearchDesign">
            <Grid container spacing={1}>
                <Grid item sm={6} md={6} xs={6}><h3>Your Tickets</h3>
                    <span>Search the winning ticket number</span>
                </Grid>
                <Grid item sm={6} md={6} xs={6}>
                    <h2 className="ft-r">{(lotteryTicketData && lotteryTicketData.length > 0) ? lotteryTicketData.length : 0}</h2>
                    <span className="ft-r">Lottery <br />
             Tickets
        </span>
                </Grid>
                <Grid item sm={12} md={12} xs={12}>
                    <Autocomplete
                        id="combo-box-demo"
                        options={lotteryTicketData}
                        value={value}
                        onChange={(event, newValue) => {
                            setValue(newValue);
                        }}
                        getOptionLabel={(option) => option.TicketNumber}
                        style={{ width: 230, float: "left" }}
                        className="searchTicketNo"
                        renderInput={(params) => <TextField {...params} label="Enter Ticket No." variant="outlined" />}
                    />
                    {/* <input type="text" placeholder="Enter Ticket No." name="search" className="searchTicketNo" /> */}
                    <button type="search" onClick={() => { ticketSearch() }} className="searchBtn">Search</button>
                </Grid>
                <div className="LotteryTicket"><img src="/images/TicketBG.png" />
                    <span className="ticketcaption">Ticket No.</span>
                    <h2 className="ticketNo">{ticketno}</h2>
                    <p className="srNo">No. {srno}</p>
                    <div className="ticketbox">
                        <p>NO. {srno}</p>
                        <h3>{ticketno}</h3>
                    </div>
                </div>
                <p className="ticketView">Click ticket to view</p>
                <div className="scrollBar">
                    <Grid container spacing={1}>
                        {lotteryTicketData && lotteryTicketData.length > 0 && lotteryTicketData.map((element, index) =>
                            <Grid item sm={4} md={4} xs={4}><div id={"ticketselect_" + (element.TicketNumber)} className="ticketBox" onClick={(e) => { ticketSelect(e, element.TicketNumber, index + 1) }}><img src="/images/Subtraction.svg" /><span className="srNo">{index + 1}</span><p>{element.TicketNumber}</p></div></Grid>
                        )}


                    </Grid>
                </div>
            </Grid>
        </div>
    );

    const showMessage = () => {
        //debugger;
        if (agentDetail == null) {
            return;
        }
        let winners = ["PW16650", "PW16961", "PW04629", "PW00463", "PW14965", "PW15259", "PW10646", "PW08386", "PW19518", "PW18900"]
        if (winners.indexOf(agentDetail.EmpId) > -1 && (agentDetail.CashRewards > 0)) {
            return <p>For your new home!<br />&amp; cash rewards of ₹ {agentDetail && agentDetail.CashRewards && agentDetail.CashRewards.toLocaleString('en-IN')}/-</p>
        }
        else if (winners.indexOf(agentDetail.EmpId) > -1 && (!agentDetail.CashRewards)) {
            return <p className="mr-top">for your new home!</p>
        }
        else if (agentDetail.CashRewards > 0) {
            return <p>on earning cash <br />rewards of ₹ {agentDetail && agentDetail.CashRewards && agentDetail.CashRewards.toLocaleString('en-IN')}/-</p>
        }
    }

    if (agentDetail && agentDetail.CashRewards > 0) {
        return (
            <>

                <div className="banner-lottery">
                    <Grid container spacing={1}>
                        <Grid item sm={6} md={6} xs={6}></Grid>
                        <Grid item sm={3} md={3} xs={3}>
                            <hr width="1" size="50" />
                            <div className="LuckyDrawDate">
                                {showMessage()}

                            </div>
                            {/* {agentDetail && (agentDetail.LotteryTicket + agentDetail.BonusTickets) > 0 &&
                            ['right'].map((anchor) => (
                                <React.Fragment key={anchor}>
                                    <Button onClick={toggleDrawer(anchor, true)}>Check Tickets</Button>
                                    <SwipeableDrawer
                                        anchor={anchor}
                                        open={state[anchor]}
                                        onClose={toggleDrawer(anchor, false)}
                                        onOpen={toggleDrawer(anchor, true)}
                                    >
                                        {list()}
                                    </SwipeableDrawer>
                                </React.Fragment>
                            ))
                        } */}

                        </Grid>
                        <Grid item sm={3} md={3} xs={3}></Grid>
                    </Grid>


                </div>

            </>
        );
    }

    return null;
};

export default LotteryBanner;
