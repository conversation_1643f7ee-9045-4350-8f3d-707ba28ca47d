import {CONFIG} from './../../appconfig';
const appConfig = CONFIG;

const setCookie=(name, value, days) =>{
    name=name || 'token';
    if (document) {
        let expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
}

const getCookie=(name)=> {
    if (document) {
        name=name || 'token';
        let nameEQ = name + "=";
        let ca = document.cookie.split(';');
        for (let i = 0; i < ca.length; i++) {
            let c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
}

const eraseCookie=(name) =>{
    if (document) {
        document.cookie = name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        //document.cookie = name + '=; Max-Age=-99999999;';
    }
}



const STORAGE = {
    setAuthToken: (token) => {
        setCookie(appConfig.AUTH_COOKIE_NAME, token, 10)
        return Promise.resolve(true)
    },
    getAuthToken: () => {
        return Promise.resolve(getCookie(appConfig.AUTH_COOKIE_NAME))   
     },
    checkAuth: () => {
        return !!getCookie(appConfig.AUTH_COOKIE_NAME)
    },
    deleteAuth: () => {
        eraseCookie(appConfig.AUTH_COOKIE_NAME)
        // deleteAllCookies()
        return Promise.resolve(true);
    },
}
export default STORAGE
