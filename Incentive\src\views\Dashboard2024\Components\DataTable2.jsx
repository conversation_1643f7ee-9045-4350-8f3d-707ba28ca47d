import React, { useContext, useEffect, useState } from "react";
import { Box, TableHead, Table, TableBody, TableCell, TableContainer, TablePagination, TableRow, TableSortLabel, Collapse, Experimental_CssVarsProvider, Button } from "@mui/material";
import PropTypes from 'prop-types';
import { DataTableContext } from "../Context/Context";
import _ from "lodash";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import ArrowDropUpIcon from '@mui/icons-material/ArrowDropUp';
import {parseCurrencyString} from '../Utility/Utility';

const EnhancedTableHead = (props) => {


    const { columns, headerSpan, headerColor } =
        props;

    const datatable = useContext(DataTableContext);

    const handleClick = (e, column, order) => {
        datatable.handleSort(column.type || '', column.id, order || 'asc');
    }


    return (
        <TableHead>
            <TableRow style={{ background: headerColor }} key={Math.random()}>

                {columns.map((column, index) => {


                    return (
                        <>
                            <TableCell
                                key={column.id}
                                align={"right"}
                                padding={column.disablePadding ? 'none' : 'normal'}
                                rowSpan={2}
                            >
                                {
                                    column.sort ?
                                        <>
                                           {column.label} 
                                            <div className="SortingIcon">   
                                            <img src="/images/TermDashboard/up-icon.svg"  direction="asc"
                                                    active={true}
                                                    onClick={(e) => handleClick(e, column, 'asc')}/>   

                                            <img src="/images/TermDashboard/down-icon.svg" direction="desc"
                                                    active={true}
                                                    onClick={(e) => handleClick(e, column, 'desc')}/>                                  
                                       </div>
                                          
                                            {/* <TableSortLabel
                                               
                                                direction="asc"
                                                active={true}
                                                onClick={(e) => handleClick(e, column, 'asc')}>
                                                {column.label}</TableSortLabel> */}
                                            {/* <TableSortLabel
                                               
                                                direction="desc"
                                                active={true}
                                                onClick={(e) => handleClick(e, column, 'desc')}
                                            /> */}
                                        </>
                                        :
                                        <>{column.label}</>
                                }


                            </TableCell>
                        </>
                    )
                })}
            </TableRow>

        </TableHead>
    );
}

EnhancedTableHead.propTypes = {
    rowCount: PropTypes.number.isRequired,
};

const DataTable2 = ({ data, columns, cellSpan, headerSpan, extraRow, headerColor, pagination }) => {

    const [page, setPage] = useState(0);
    const [tableData, setTableData] = useState([]);

    const [rowsPerPage, setRowsPerPage] = useState(8);

    let noOfPages = (Array.isArray(data) && data.length > 0) ? (data.length % rowsPerPage == 0 ? Math.floor(data.length / rowsPerPage) : Math.floor(data.length / rowsPerPage) + 1) : 0;

    if (noOfPages > 10) {
        let rows = data.length % 10 == 0 ? Math.floor(data.length / 10) : Math.floor(data.length / 10) + 1;
        setRowsPerPage(rows);
    }

    useEffect(() => {

        if (data.length > 0) {


            let fRow = page * rowsPerPage;
            let lRow = fRow + rowsPerPage - 1;
            if (lRow > data.length - 1) {
                lRow = data.length - 1;
            }
            let tableData = [];
            for (let i = fRow; i <= lRow; i++) {
                tableData.push(data[i]);
            }
            setTableData(tableData);
        }
    }, [data, page])




    const handleChangePage = (event, newPage) => {

        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {

        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handlePageChange = (e) => {
  
        setPage(e.target.value);
    }

    const handleSort = (type, key, order) => {

        
            let tempData = tableData || [];

            if(type=="currency")
            {
                tempData.map((data)=>{
                    data[key+'1']= parseCurrencyString(data[key]);
                });
                let sortedTempData = tempData.length> 0 && 
                _.orderBy(tempData, key+'1', order);
                setTableData(sortedTempData)
            }
            else{
            let sortedTempData = tempData.length > 0 &&
                _.orderBy(tempData, key, order);
            setTableData(sortedTempData);
            }
        
       
    }

    // const handleArrowClick = (e) => {
    //     if (e.target.id == 0) {

    //         console.log("The event is ", 0);
    //     }
    //     else {
    //         console.log("The evtns is ", e.target.id);
    //     }
    // }


    return (
        <DataTableContext.Provider value={{ handleSort: handleSort }}>
            <TableContainer>
                <Table
                    sx={{ minWidth: 750 }}
                >
                    <EnhancedTableHead

                        columns={columns}
                        rowCount={tableData.length}
                        headerSpan={headerSpan ? headerSpan : null}
                        headerColor={headerColor ? headerColor : null}
                    />
                    <TableBody>

                        {
                            tableData.map((row, index) => {

                                return (
                                    <TableRow
                                        hover
                                        className={row.ClassName}
                                        key={index}
                                        sx={{ cursor: 'pointer' }}
                                    >
                                        {
                                            columns.map((key, columnIndex) => {


                                                if (!cellSpan || key.id != cellSpan) {

                                                    if (typeof (row[key.id]) != 'boolean') {


                                                        return (

                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} align={columnIndex == 0 ? "right" : "center"} >{row[key.id] || '-'}</TableCell>
                                                        )


                                                    }
                                                    else {

                                                        let data = row[key.id] ? 'Yes' : 'No';

                                                        return (
                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} align={columnIndex == 0 ? "right" : "center"} >{data || '-'}</TableCell>
                                                        )
                                                    }
                                                }

                                                else {
                                                    
                                                    if(row.hasOwnProperty('span'))
                                                    {

                                                        return (
                                                            <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} rowSpan={row['span']} align={columnIndex == 0 ? "right" : "center"}>
                                                                {row[key.id]}
                                                            </TableCell>
                                                        )
                                                    }
                                                    
                                                }


                                            })
                                        }
                                    </TableRow>

                                )
                            })
                        }

                        {
                            extraRow && extraRow > 0 &&
                            Array(extraRow).fill().map((val, index) => {
                                return (
                                    <TableRow hover key='extra' sx={{ cursor: 'pointer' }}>
                                        {
                                            columns.map((key, columnIndex) => {
                                                if (!key) { return null; }
                                                if (Array.isArray(key.extraRow) && key.extraRow[index]) {
                                                    return (
                                                        <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} className="Blue" align={columnIndex == 0 ? "right" : "center"} >{key.extraRow[index]}</TableCell>
                                                    )
                                                }
                                                else {
                                                    return (
                                                        <TableCell style={{ whiteSpace: 'normal', wordWrap: 'break-word' }} />
                                                    )
                                                }
                                            })
                                        }
                                    </TableRow>
                                )
                            })
                        }


                    </TableBody>
                </Table>
            </TableContainer>

            {/* <TablePagination
                rowsPerPageOptions={[8, 16, 32]}
                count={data.length}
                rowsPerPage={rowsPerPage}
                page={page}
                component="div"
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
            /> */}

            {pagination &&
                <div className="TablePagination">
                    {
                        Array.isArray(data) && data.length > 0 &&
                        <>
                            {/* <ArrowBackIcon id={0} onClick={handleArrowClick} color={page != 0 ? "primary" : "disabled"}  /> */}
                            {


                                Array(noOfPages).fill(0).map((val, index) => {
                                    // Array(10).fill(0).map((val, index) => {
                                    return (
                                        <Button className={page == index ? "activeBtn" : ""} value={index} onClick={handlePageChange}>{index + 1}</Button>
                                    )
                                })

                            }
                            {/* <ArrowForwardIcon id={1} onClick={handleArrowClick} color={page != noOfPages ? "primary" : "disabled"} /> */}
                        </>
                    }

                    {/* {
                    Array.isArray(data) && data.length > 0 &&
                    Array((Math.floor(data.length / 8)) + 1).fill(0).map((val, index) => {
                        // Array(10).fill(0).map((val, index) => {
                        return (
                            <Button className={page==index?"activeBtn":""} value={index} onClick={handlePageChange}>{index + 1}</Button>
                        )
                    })

                } */}



                </div>
            }
        </DataTableContext.Provider>
    )

}

export default DataTable2;