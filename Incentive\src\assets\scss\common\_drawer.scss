@import '../variables/variables';

.drawer{
    > div{
        top: 65px;
        width: 230px;
        @media screen and (max-width: 660px) {
            top: 57px;
        }
        .drawer-header{
            display: flex;
            align-items: center;
            padding: 0 5px;
            svg{
                color: $primary-color;
            }
            p{
                font-weight: bold;
                color: $primary-color;
            }
        }
    }
    .filters{
        .form-control{
            padding: 6px 0;
            min-width: 190px;
            background: $white;
            .select{
                line-height: 0.3;
                font-size: 14px;
            }
            label{
                &[data-shrink='true']{
                    transform: translate(14px, 0px) scale(0.75);
                }
            }
        }
    }
    .filter-buttons{
        padding: 0px 9px 15px;
        .grey-button{
            @include grey-button;
        }
        .secondary-button{
            @include secondary-button;
        }
    }
}