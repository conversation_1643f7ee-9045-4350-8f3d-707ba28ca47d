import React, { useEffect, useState, Fragment } from "react";
import Slider from "react-slick";
import { useParams } from "react-router";
import moment from "moment";

import {
  Grid,
} from "@material-ui/core";

import * as services from "../../services";
import STORAGE from '../../store/storage'
// import { SRLWrapper } from "simple-react-lightbox";

import AboutUs from './AboutUs';
import AgentStories from './AgentStories';
import Gallery from './Gallery';
import Rewards from './Rewards';
import FAQs from './FAQs';
import Feedback from './Feedback';
import Videos from './Videos';
import WallOfFame from './WallOfFame';






export default function Main(props) {


  const urlParams = useParams();

  //const [isLoading, setIsLoading] = useState(false);  
  const [userId, setUserId] = useState(null);
  const [productId, setProductId] = useState(null);

  //const [userDetail, setUserDetail] = useState({});
  useEffect(() => {
    console.log("urlParams", urlParams);
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");

    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);

    services
      .API_GET(`Incentive/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {

          localStorage.setItem('user', JSON.stringify(response));

          setProductId(response.ProductId);

          let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JagWebPage&EventName=Pageload`;
          if (Source) {
            url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JagWebPage&EventName=` + Source
          }

          services
            .API_GET(url).then(response => { })
            .catch((err) => {
            });
        }

      })
      .catch((err) => {
        console.log("Error", err);
      });

  }, []);


  return (

    <div className="wrapper content-panel">
      <Grid container spacing={3}>
        <WallOfFame userId={userId} productId={productId} />
        <AboutUs />
        <AgentStories />
        <Gallery />
        <Rewards />
        <FAQs />
        <Feedback />
        <Videos />
      </Grid>

    </div>
  );
}
