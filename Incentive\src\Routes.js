import React from "react";
import { Switch, Redirect } from "react-router-dom";
import { RouteWithLayout } from "./components";
import { Main as MainLayout, Minimal as MinimalLayout , Jag as JagLayout} from "./layouts";
import { getCookie } from "./utils/utility";
import { CONFIG } from "./appconfig/app.config";

import {
  NotFound as NotFoundView,
  DashboardNew as DashboardView,
  //jag2022 as JAG2022View,
  jag2023 as JAG2023View,
  // jag2021 as JAG2021View,
  // jag as JAGView,
  // jag2020 as JAGView2020,
  Main as MainView,
  FAQ as FAQView,
  Landing_jag as Landing_jagView,
  Landing2021_jag as Landing2021_jagView,
  Gallery2021_jag as Gallery2021_jagView,
  WOF as WOFView,
  Timer as TimerView,
  InfoUseful as InfoUsefulView,
  Dashboard2024 as NewDashboard,
  AdminView as AdminView,
  InvestmentDashboard as InvestmentDashboard,
  AdminViewInvestment as AdminViewInvestment,
} from "./views";
import Landing from "./views/Landing";

let flag = false;
if (CONFIG.CHECK_AUTH) {
  let AgentId = getCookie("AgentId");
  const tokenPath = window.location.pathname.split('/');
  let userToken = null
  if (tokenPath.length >= 3) {
    userToken = tokenPath[2]
    if (isNaN(userToken)) {
      userToken = atob(userToken);
    }

  }

  console.log("AgentId", AgentId)
  console.log("userToken", userToken)
  if (AgentId && AgentId != "" && userToken == AgentId) {
    flag = true;
  }
}
else {
  flag = true;
}



const Routes = (props) => {
  return (
    <Switch>
      <RouteWithLayout
        component={flag ? DashboardView : NotFoundView}
        exact
        layout={MainLayout}
        path="/dashboard/:Token"
      />
      <RouteWithLayout
        component={flag ? DashboardView : NotFoundView}
        exact
        layout={MainLayout}
        path="/dashboard/:Token/:Source"
      />

      <RouteWithLayout

      component={NewDashboard}
      exact
      layout={MainLayout}
      path="/TermDashboard"
      />
      <RouteWithLayout

      component={AdminView}
      exact
      layout={MainLayout}
      path="/AdminView"
      />

      <RouteWithLayout

      component={InvestmentDashboard}
      exact
      layout={MainLayout}
      path="/InvestmentDashboard"
      />
      <RouteWithLayout

      component={AdminViewInvestment}
      exact
      layout={MainLayout}
      path="/InvestmentAdminView"
      />

      <RouteWithLayout

        component={flag ? MainView : NotFoundView}
        exact
        layout={MainLayout}
        path="/landing"
      />
      <RouteWithLayout

        component={flag ? MainView : NotFoundView}
        exact
        layout={MainLayout}
        path="/landing/:Token"
      />
      <RouteWithLayout

        component={flag ? MainView : NotFoundView}
        exact
        layout={MainLayout}
        path="/landing/:Token/:Source"
      />
      <RouteWithLayout

        component={flag ? MainView : NotFoundView}
        exact
        layout={MainLayout}
        path="/landing/:Source"
      />
     
      <RouteWithLayout

        component={flag ? JAG2023View : NotFoundView}
       
        exact
        layout={MainLayout}
        path="/jag/:Token"
      />
      <RouteWithLayout

        component={flag ? JAG2023View : NotFoundView}
        
        exact
        layout={MainLayout}
        path="/jag/:Token/:Source"
      />
     
      <RouteWithLayout

        component={flag ? FAQView : NotFoundView}
        exact
        layout={MainLayout}
        path="/faq/:Token"
      />
      <RouteWithLayout

        component={flag ? FAQView : NotFoundView}
        exact
        layout={MainLayout}
        path="/faq/:Token/:Source"
      />
     
      <RouteWithLayout

        component={flag ? Landing2021_jagView : NotFoundView}
        exact
        layout={JagLayout}
        path="/home/<USER>"
      />
      <RouteWithLayout

        component={flag ? Landing2021_jagView : NotFoundView}
        exact
        layout={JagLayout}
        path="/home/<USER>/:Source"
      />
      <RouteWithLayout

        component={flag ? Gallery2021_jagView : NotFoundView}
        exact
        layout={JagLayout}
        path="/gallery/:Token"
      />
      <RouteWithLayout

        component={flag ? Gallery2021_jagView : NotFoundView}
        exact
        layout={JagLayout}
        path="/gallery/:Token/:Source"
      />
      <RouteWithLayout
        component={flag ? WOFView : NotFoundView}
        exact
        layout={MinimalLayout}
        path="/wof/:Token"
      />
      <RouteWithLayout
        component={flag ? TimerView : NotFoundView}
        exact
        layout={MinimalLayout}
        path="/timer/:Token"
      />
      <Redirect exact from="/" to="/dashboard" />

      <RouteWithLayout

        component={flag ? InfoUsefulView : NotFoundView}
        exact
        layout={MinimalLayout}
        path="/infoUseful/:Token"
      />
      <RouteWithLayout

        component={flag ? InfoUsefulView : NotFoundView}
        exact
        layout={MinimalLayout}
        path="/infoUseful/:Token/:Source"
      />
      <RouteWithLayout
        component={NotFoundView}
        exact
        layout={MinimalLayout}
        path="**"
      />
    </Switch>
  );
};
export default Routes;
