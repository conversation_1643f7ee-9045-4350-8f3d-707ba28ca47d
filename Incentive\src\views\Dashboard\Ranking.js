import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/Criteria";
import moment from "moment";
import Slider from "react-slick";
import { green } from '@material-ui/core/colors';
import _ from "lodash";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
  CircularProgress,
  Fab
} from "@material-ui/core";

import ProcessBar from "./ProcessBar";



const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
  fab: {
    width: "110px",
    height: "110px",
    flexDirection: 'column'
  },
  fabProgress: {
    color: '#1DD1A1',
    position: 'absolute',
    top: 14,
    left: 10,
    zIndex: 1,
  },
}));

const Ranking = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [agentDetail, setAgentDetail] = useState([]);
  const [highlights, setHighlights] = useState([]);
  //const [productId, setProductId] = useState([]);
  const [showCriteria, setShowCriteria] = React.useState(false);
  const [currentMileStone, setCurrentMileStone] = React.useState(null);
  const [agentInfo, setAgentInfo] = useState({});
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [IsCurrentMonth, setIsCurrentMonth] = useState(false);

  const handleClickOpen = (value) => {
    setShowCriteria(value);
    const { userId, date } = props;
    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${userId}/0/${response.ProductId}/${date}?PageName=Incentive&EventName=SeeCriteria`;
    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });

  };

  const getAgentDetail = () => {
    setAgentDetail([]);
    setHighlights([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/GetIncentiveAgentDetails/${userId}/${date}`)
      .then(response => {
        if (response && response != "[]") {
          setAgentDetail(response);
          getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const getHighlights = (agentDetail) => {
    if (!agentDetail.SuperGroupID) {
      return;
    }

    services
    .API_GET(`Incentive/GetIncentiveHighlights/${agentDetail.SuperGroupID}/${date}/${agentDetail.ProductId}/${agentDetail.AgentId}`)
      .then(response => {
        if (response && response != "[]") {
          //debugger;
          setHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  useEffect(() => {
    getAgentDetail();
  }, [props]
  );


  useEffect(() => {
    if (userId, date) {
      //if (date == '01-04-2021') {
        setShowProgressBar(true);
        GetMileStonesData();
      // }
      // else {
      //   setShowProgressBar(false);
      // }
    }
  }, [userId, date]);


  const GetMileStonesData = () => {

    if (!userId) {
      return;
    }

    let dt = moment().startOf('month').format("DD-MM-YYYY");

    if (date == dt) {
      setIsCurrentMonth(true)
    }
    else {
      setIsCurrentMonth(false);
    }


    services
      .API_GET(`Incentive/GetMileStonesData/${userId}/${date}`)
      .then(response => {
        console.log(response)
        if (response && response !== "[]" && response.Status === true) {

          let list = JSON.parse(response.Response)

          let agentInfo = list.agentInfo && list.agentInfo[0];
          let milestone = _.orderBy(list.mileStones, ['TargetAmount'], ['asc'])
          //debugger;
          let CurrentMilestone = _.find(milestone, function (o) { return o.TargetAmount >= agentInfo.ProjectedIncentive; });


          //milestone = _.find(milestone, function (o) { return o.TargetAmount >= agentInfo.ProjectedIncentive; });
//debugger;
          setCurrentMileStone(CurrentMilestone);
          setAgentInfo(agentInfo);

        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  var getRemainingDays = function () {
    var date = new Date();
    var time = new Date(date.getTime());
    time.setMonth(date.getMonth() + 1);
    time.setDate(0);
    return (time.getDate() > date.getDate() ? time.getDate() - (date.getDate()-1) : 0);
  }

  const settings = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1
  };
  return (
    <>
      <div className="pading-zero" className={classes.root}>

        <div className="ranking-data">
          <div className="rank-box">
            <div className='rank-box-left-section'>
              <div className='name-description'>
                <strong>{agentDetail.UserName || "-"}</strong>
                <p> {agentDetail.SuperGroupName || "-"}, {agentDetail.JoiningMonth || "-"} Months</p>

              </div>


              <div className='highlights'>
                <Slider {...settings}>
                  {highlights && highlights.length > 0 && highlights.map((item, index) => {
                    return <div>
                      <p className='highlights-description'><strong>{item.figure}</strong> {item.Description}</p>
                    </div>
                  })}
                </Slider>
              </div>
             
              {showProgressBar && currentMileStone && IsCurrentMonth && <div className='total-ape-progress-bar'>
                <img src="/images/target_incentive.svg" />

                <p>You need to make an APE of <span>₹ {currentMileStone && currentMileStone.RequiredRunRate && Math.round(currentMileStone.RequiredRunRate / 1000).toLocaleString('en-IN')}K</span> for next {getRemainingDays()} days To make an incentive of ₹ {currentMileStone && currentMileStone.TargetAmount && Math.round(currentMileStone.TargetAmount).toLocaleString('en-IN')} this month</p>
              </div>}
            </div>
            <div className='rank-box-right-section'>
              <ul>
                <li>
                  <span className='label'>Rank</span><span className='value'>#{agentDetail.AgentRank || "-"} </span>
                </li>
                {(agentDetail.ProductId != '115')?
                <li>
                  <span className='label'>Slab</span><span className='value'>{agentDetail.Currentlevel || "-"}</span>
                </li>
                : ''}
              </ul>
              <div className='background-caricrature'></div>
            </div>

            {/* <ProcessBar firstLevel={agentDetail.Currentlevel} lastLevel={agentDetail.NextLevel} levelPercentage={agentDetail.LevelPercentage || "0"} /> */}
          </div>

          {/* <div className="rank-box-inner">
            <ul>
              {(agentDetail.Incentivetext) ? (
                <li>
                  <span>
                    <img src="/images/information-circle.svg" />
                    <span className="numbere">{agentDetail.Incentivetext}</span>
                  </span>
                </li>
              ) : ""}
              <li>
                <span>
                  <button onClick={(e) => handleClickOpen(true)}>
                    See Criteria <i className="fa fa-angle-right"></i>
                  </button>
                </span>
              </li>
            </ul>
          </div> */}
        </div>

        {/* <div className="highlights">
          <h6>
            Highlights
          </h6>
          {(highlights && highlights.length > 0) ? (highlights.map((item, index) =>
            <div key={index} className="highlights-card">
              <ul>
                <li>
                  <strong>
                    {item.figure}
                    <span>
                      {item.Description}
                    </span>
                  </strong>
                </li>
                <li>
                  <img src="/images/winner.png" />
                </li>
              </ul>
            </div>
          )) : (
              <div className="highlights-card">
                <ul>
                  <li>
                    <strong>
                      <span>
                        &nbsp;
                  </span>
                    </strong>
                  </li>
                  <li>
                    <img src="/images/winner.png" />
                  </li>
                </ul>
              </div>
            )}
        </div> */}
      </div>
      {showCriteria ? <Criteria superGroupId={agentDetail.SuperGroupID} productId={agentDetail.ProductId || "7"} date={date} show={showCriteria} handleClose={() => handleClickOpen(false)} /> : null}
    </>
  );
};

export default Ranking;
