import React from "react";
import { makeStyles } from "@material-ui/styles";
import { Grid, Typography, Card, CardContent, Button } from "@material-ui/core";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import { BookingDetails as DialogBookingDetails } from "./../../components/Dialogs";
import { isBlock } from "typescript";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";

const useStyles = makeStyles((theme) => ({
  content: {
    paddingTop: 120,
    textAlign: "center",
  },

  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px 20px 0 0",
  },
  textRight: {
    textAlign: "right",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,

    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#0052cc 0% 0%",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#808080",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
  },
  expandBox: {
    boxShadow: "none",
    "& svg": {
      left: 0,
      right: 0,
      margin: "auto",
      top: "5px",
    },
  },
  updateBtn: {
    background: "#DEEBFF",
    borderRadius: "40px",
    margin: "0 5px 0  9px",
    color: "#fff",
    textTransform: "capitalize",
    border: "1px solid #0052CC",
    padding: "2px 5px",
    fontSize: "12px",
    color: "#808080",
    "&:hover": {
      background: "#DEEBFF",
    },
  },
}));

const ToggleBox = () => {
  const classes = useStyles();

  const [open, setOpen] = React.useState(false);

  return (
    <>
      <div className="toggle_box">
        <ExpansionPanel id="mainbox" className={classes.expandBox}>
          <ExpansionPanelSummary
            aria-controls="panel1d-content"
            id="panel1d-header"
          >
            <ExpandMoreIcon />
          </ExpansionPanelSummary>

          <ExpansionPanelDetails>
            <Grid container spacing={2}>
              <Grid item xs={8} className="txt-area">
                <label>
                  Doc Upload<span>Pending</span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  className={classes.updateBtn}
                >
                  Upadate
                </Button>
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>
                  Verification<span className="done-txt">Done</span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <img src="images/refresh.svg" />
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>
                  Credit Pending Pool<span className="done-txt">Yes</span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <img src="images/refresh.svg" />
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>
                  Endorsement<span>Pending</span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  className={classes.updateBtn}
                >
                  Upadate
                </Button>
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>Proposal Form</label>
              </Grid>
              <Grid item xs={4}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  className={classes.updateBtn}
                >
                  Link
                </Button>
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>
                  Continue Journey<span></span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  className={classes.updateBtn}
                >
                  Link
                </Button>
              </Grid>
              <Grid item xs={8} className="txt-area">
                <label>
                  H360 - Yes<span></span>
                </label>
              </Grid>
              <Grid item xs={4}>
                <Button
                  size="small"
                  variant="contained"
                  color="primary"
                  className={classes.updateBtn}
                >
                  Link
                </Button>
              </Grid>
            </Grid>
          </ExpansionPanelDetails>
        </ExpansionPanel>

        <div className="inner-box">H360 booking /Telemedical</div>
      </div>
    </>
  );
};

export default ToggleBox;
