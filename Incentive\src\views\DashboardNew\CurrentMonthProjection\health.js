import React, { useState, useRef, useEffect } from "react";
import { withSnackbar } from 'notistack';

import {
    TableContainer,
    Table,
    TableRow,
    TableCell,
    TableBody,
    Grid,

} from "@material-ui/core";
import moment from "moment";
import TopTabs from "./topTabs";


import {
    Box,
    Tabs, Tab
} from "@material-ui/core";

const health = (props) => {
    let ProjectedData = props.data;
    ////debugger;
    console.log("ProjectedData", ProjectedData);

    const refCurrentTrend = useRef(null);
    const refOpportunity = useRef(null);
    const refDailySourcing = useRef(null);
    const [activeScroll, setActiveScroll] = useState(0);

    useEffect(() => {
        const handleScroll = (e) => {

            console.log(refOpportunity.current == null)

            if(refOpportunity.current == null){

                if ( window.scrollY > refCurrentTrend.current.offsetTop -200  && window.scrollY < refDailySourcing.current.offsetTop-200) {
                    setActiveScroll(0)
                }

                else if (window.scrollY > refDailySourcing.current.offsetTop -200){
                    setActiveScroll(1)
                }

            }
            else if(refOpportunity.current != null){

                if ( window.scrollY > refCurrentTrend.current.offsetTop -200  && window.scrollY < refOpportunity.current.offsetTop-200) {
                    setActiveScroll(0)
                } else if ( window.scrollY > refOpportunity.current.offsetTop -200 && window.scrollY < refDailySourcing.current.offsetTop-200 ) {
                    setActiveScroll(1)
                } else if (window.scrollY > refDailySourcing.current.offsetTop -200) {
                    setActiveScroll(2)
                    // Etc...
                }

            }
        }
        document.addEventListener('scroll', handleScroll);
        return () => {
            document.removeEventListener('scroll', handleScroll);
        }
    }, [])

    const executeCurrentTrendScroll = (e) => {
        setActiveScroll(0);
        //refCurrentTrend.current.scrollIntoView()
        window.scrollTo(0, refCurrentTrend.current.offsetTop - 60)
    }
    const executeOpportunityScroll = () => {
        setActiveScroll(1);
        window.scrollTo(0, refOpportunity.current.offsetTop - 60)
        //refOpportunity.current.scrollIntoView()
    }
    const executeDailySourcingScroll = () => {
        setActiveScroll(2);
        window.scrollTo(0, refDailySourcing.current.offsetTop - 60)
        //refDailySourcing.current.scrollIntoView()
    }

    const handleTabChange = (event, newValue) => {
        setActiveScroll(newValue);
        switch (newValue) {
            case 0:
                executeCurrentTrendScroll()
                break;
            case 1:
                executeOpportunityScroll()
                break;
            case 2:
                executeDailySourcingScroll()
                break;
            default:
                break;
        }


    }




    return (
        <>
            <Grid container spacing={2} className='mobile-view'>
                <Box className='items-list'>
                    <Tabs
                        value={activeScroll}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons
                        allowScrollButtonsMobile
                        aria-label="scrollable force tabs example"
                    >
                        <Tab label="Current Trends" />
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                        <Tab label="Opportunity" />}
                        <Tab label="Daily Sourcing Req." />
                    </Tabs>
                </Box>
            </Grid>
            <Grid container spacing={2} className='mobile-view'>
                <Box className='items-list'>
                    <Tabs
                        value={activeScroll}
                        onChange={handleTabChange}
                        variant="scrollable"
                        scrollButtons
                        allowScrollButtonsMobile
                        aria-label="scrollable force tabs example"
                    >
                        <Tab label="Current Trends" />
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                            <Tab label="Opportunity" />}
                        <Tab label="Daily Sourcing Req." />
                    </Tabs>
                </Box>
            </Grid>
            <Grid item sm={12} md={12} xs={12} className='common-view'>

                <div className="CurrentMonthData" ref={refCurrentTrend}>
                    <Grid container>
                        <Grid item sm={4} md={4} xs={12}>
                            <h2>Current Trends</h2>
                            <img src="/images/incentive/currentTrends.png" className="leftimage" />
                        </Grid>
                        <Grid item sm={8} md={8} xs={12}>
                            <TableContainer>
                                <Table className='web-common' aria-label="simple table">
                                    {ProjectedData.SuperGroupTypeId == 1 &&
                                        <TableBody>
                                            <TableRow>
                                                <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                                                <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell >Sourced APE (till date)</TableCell>
                                                <TableCell ><i className="fa fa-inr"></i> {ProjectedData.APETillNow && Math.round(ProjectedData.APETillNow).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Projected Sourced APE (full month)</TableCell>
                                                <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ProjectedAPE && Math.round(ProjectedData.ProjectedAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Weighted APE/Sourced APE for your process in last payout Month</TableCell>
                                                <TableCell >{ProjectedData.WeightedAPERatio && (ProjectedData.WeightedAPERatio * 100).toFixed(0)}%</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell >Projected Weighted APE</TableCell>
                                                <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.ProjectedWeightedAPE && Math.round(ProjectedData.ProjectedWeightedAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(pre salary adjustment)</i></TableCell>
                                                    <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                                                    <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                            </>
                                            }
                                            {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(Post salary adjustment)</i></TableCell>
                                                    <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                            }

                                        </TableBody>
                                    }
                                    {ProjectedData.SuperGroupTypeId == 3 &&
                                        <TableBody>
                                            <TableRow>
                                                <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                                                <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Sourced Bookings / APE (till date)</TableCell>
                                                <TableCell> #{ProjectedData.BKGSTillNow} / &nbsp;<i className="fa fa-inr"></i> {ProjectedData.APETillNow && Math.round(ProjectedData.APETillNow).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Projected Sourced Bookings / APE (full month)</TableCell>
                                                <TableCell >#{Math.round(ProjectedData.ProjectedBKGS)} / &nbsp; <i className="fa fa-inr"></i> {ProjectedData.ProjectedAPE && Math.round(ProjectedData.ProjectedAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Issued Bookings/Sourced Bookings for your process in last payout Month</TableCell>
                                                <TableCell >{ProjectedData.WeightedBKGSRatio && (ProjectedData.WeightedBKGSRatio * 100).toFixed(0)}%</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell>Weighted APE/Sourced APE for your process in last payout Month</TableCell>
                                                <TableCell >{ProjectedData.WeightedAPERatio && (ProjectedData.WeightedAPERatio * 100).toFixed(0)}%</TableCell>
                                            </TableRow>
                                            <TableRow>
                                                <TableCell >Projected Issued Bookings / Weighted APE</TableCell>
                                                <TableCell className="highlight">
                                                    #{Math.round(ProjectedData.ProjectedIssuedBookings)} / &nbsp; <i className="fa fa-inr"></i> {ProjectedData.ProjectedWeightedAPE && Math.round(ProjectedData.ProjectedWeightedAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>
                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(pre salary adjustment)</i></TableCell>
                                                    <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                            </>
                                            }
                                            {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>
                                                    <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(Post salary adjustment)</i></TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>
                                            }

                                        </TableBody>
                                    }


                                </Table>
                            </TableContainer>
                            {/* <li>You are qualifying for Slab {ProjectedData.Slab || "-"} basis current projections - {ProjectedData.CurrentSlabPercentage}%</li> */}
                            {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                                <div className="caption error"><img src="/images/incentive/announceIcon.svg" /><b>Basis current projections, you are not able to justify your cost. You need to work harder to make CJ incentives.</b></div>}
                            {/* Issued Bookings/Sourced Bookings for your process in last payout Month */}
                            {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                <div className="caption"><img src="/images/incentive/announceIcon.svg" /><b>Congratulations !!! Basis current projections you are able to justify your cost this month.</b></div>}


                        </Grid>
                    </Grid>

                </div>
                {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                    <div className="CurrentMonthData" ref={refOpportunity}>
                        <Grid container>
                            <Grid item sm={4} md={4} xs={12} className='common-view'>
                                <h2>Opportunity</h2>
                                <img src="/images/incentive/Opportunity.png" className="leftimage" />
                            </Grid>
                            <Grid item sm={8} md={8} xs={12}>
                                <TableContainer>
                                    {ProjectedData.SuperGroupTypeId == 1 &&
                                        <Table className='web-common opportunity' aria-label="simple table">
                                            <TableBody>
                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell>For Slab {ProjectedData.NextSlab || "-"} Minimum Weighted APE Required</TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.NextSlabAPE && Math.round(ProjectedData.NextSlabAPE).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}


                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"} i.e. @{ProjectedData.NextSlabPercentage || 0}%</TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}

                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                                                    <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}

                                            </TableBody>
                                        </Table>
                                    }
                                    {ProjectedData.SuperGroupTypeId == 3 &&
                                        <Table className='web-common opportunity' aria-label="simple table">
                                            <TableBody>
                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell>For Slab {ProjectedData.NextSlab || "-"} Minimum Issued bookings Required</TableCell>
                                                    <TableCell >#{ProjectedData.NextSlabBKGS}</TableCell>
                                                </TableRow>}

                                                {/* <TableRow>
      <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}%</TableCell>
      <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
    </TableRow> */}

                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"} i.e. @{ProjectedData.NextSlabPercentage || 0}%</TableCell>
                                                    <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}

                                                {ProjectedData.Slab > 1 && <TableRow>
                                                    <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                                                    <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                                                </TableRow>}

                                            </TableBody>
                                        </Table>
                                    }

                                </TableContainer>
                                <div className="caption OpportunityNote"><img src="/images/incentive/note.svg" />

                                    <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                                    <ul>
                                        <li>The above Incentive calculations include only Cost Justification (CJ) Incentive.</li>
                                        {/* <li>Above Calculated CJ Incentive is before net of Salary for e.g. if Projected CJ Incentive: 47K and your salary is 25K then Net In-hand CJ Incentive: (47K-25K) = 22K</li> */}
                                        {/* <li>Actual incentive may differ from this projections.</li> */}
                                        {/* <li>In Case Projected CJ Incentive is less than salary, then Projected CJ Incentive earned is 0 and take home will be Salary.</li> */}
                                    </ul>


                                </div>
                            </Grid>
                        </Grid>

                    </div>}

                <div className="CurrentMonthData" ref={refDailySourcing}>
                    <Grid container>
                        <Grid item sm={4} md={4} xs={12} className='common-view'>
                            <h2>Daily Targets</h2>
                            <img src="/images/incentive/DailySourcingRequired.png" className="leftimage" />
                        </Grid>
                        <Grid item sm={8} md={8} xs={12}>

                            <TableContainer>
                                {ProjectedData.SuperGroupTypeId == 1 &&
                                    <Table className='web-common' aria-label="simple table">
                                        <TableBody>
                                            <TableRow>
                                                <TableCell >Current Sourced APE/Day</TableCell>
                                                <TableCell ><i className="fa fa-inr"></i> {ProjectedData.PerDayAPE && Math.round(ProjectedData.PerDayAPE).toLocaleString('en-IN')}</TableCell>
                                            </TableRow>

                                            {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>

                                                    <TableCell colSpan={2}>

                                                        <table className="motorIncentive-header">
                                                            <tbody>
                                                                <tr>
                                                                    <td>Required Sourced APE/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></td>
                                                                    <td><i className="fa fa-inr"></i> {ProjectedData.NextSlabPerDayAPE && Math.round(ProjectedData.NextSlabPerDayAPE).toLocaleString('en-IN')}</td>
                                                                </tr>
                                                            </tbody>
                                                        </table>


                                                    </TableCell>


                                                </TableRow>}

                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                                                <TableRow>
                                                    <TableCell>Required Sourced APE/day to justify Cost and make CJ incentive</TableCell>
                                                    <TableCell><i className="fa fa-inr"></i> {Math.round((ProjectedData.MinAPERequired - ProjectedData.APETillNow) / ProjectedData.NextWorkingDays).toLocaleString('en-IN')}</TableCell>

                                                </TableRow>}

                                        </TableBody>
                                    </Table>}
                                {ProjectedData.SuperGroupTypeId == 3 &&
                                    <Table className='web-common' aria-label="simple table">
                                        <TableBody>
                                            <TableRow>
                                                <TableCell >Current Sourced Bookings/Day</TableCell>
                                                <TableCell >#{ProjectedData.PerDayBKGS}</TableCell>
                                            </TableRow>
                                            {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                                                <TableRow>
                                                    <TableCell>Required Sourced Bookings/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></TableCell>
                                                    <TableCell >#{ProjectedData.NextSlabPerDayBKGS}</TableCell>
                                                </TableRow>}

                                            {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                                                <TableRow>
                                                    <TableCell>Required Sourced Bookings/day to justify Cost and make CJ incentive </TableCell>
                                                    <TableCell >#{Math.round((ProjectedData.MinBKGSRequired - ProjectedData.BKGSTillNow) / ProjectedData.NextWorkingDays * 10) / 10}</TableCell>
                                                </TableRow>}

                                        </TableBody>
                                    </Table>}



                            </TableContainer>
                            <div className="caption note"><img src="/images/incentive/note.svg" />
                                <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                                <ul>
                                    <li>Projections does not include booking incentives, quality dampners and multiplier etc.</li>
                                    <li>Actual calculations may differ from projections.</li>
                                    {ProjectedData.ProductId == 115 &&
                                        <li>Required Sourced APE/day  does not include  Annuity, SinglePayUlip & SinglePayCG.</li>
                                    }
                                </ul>
                            </div>
                        </Grid>
                    </Grid>

                </div>




            </Grid>
        </>
    )

}

export default withSnackbar(health);

