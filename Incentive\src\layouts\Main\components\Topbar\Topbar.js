import React from "react";
import clsx from "clsx";
import PropTypes from "prop-types";
import { makeStyles, withStyles, useTheme } from "@material-ui/styles";
import {
  AppBar,
  Toolbar,
  Typography,
  Hidden,
  Avatar,
  Button,
  Menu,
  MenuItem,
  ListItemIcon,
  ListItemText,
} from "@material-ui/core";
import InputIcon from "@material-ui/icons/Input";
import SendIcon from "@material-ui/icons/Send";
import IconButton from "@material-ui/core/IconButton";
import MenuIcon from "@material-ui/icons/Menu";
import NotificationsNoneIcon from "@material-ui/icons/NotificationsNone";
import moment from "moment";
const useStyles = makeStyles((theme) => ({
  root: {
    boxShadow: "none",
    // backgroundColor: "transparent",
    position: "relative",
    zIndex: 1100,
    width: "100%",
    marginLeft: "auto",
    top: 0,
  },
  flexGrow: {
    flexGrow: 1,
  },
  signOutButton: {
    marginLeft: theme.spacing(1),
  },
  logo: {
    maxWidth: "150px",
  },
  menuButton: {
    marginLeft: 8,
    color: theme.palette.primary.main,
  },
  hide: {
    display: "none",
  },
  userImg: {
    maxWidth: "2.5rem",
    border: "1px dashed #13c945",
    borderRadius: "50%",
    display: "inline-block",
  },
  hiUser: {
    textTransform: "capitalize"
  },
  today : {
    color: "#0065ff"
  }
  
  // userName: {
  //   backgroundColor: "#fff !important",
  //   borderRadius: 0,
  //   color: "#414141",
  //   fontSize: "15px",
  //   width: "auto !important",
  //   textTransform: "capitalize",
  // },
}));

const StyledMenu = withStyles({
  paper: {
    border: "1px solid #d3d4d5",
  },
})((props) => (
  <Menu
    elevation={0}
    getContentAnchorEl={null}
    anchorOrigin={{
      vertical: "bottom",
      horizontal: "center",
    }}
    transformOrigin={{
      vertical: "top",
      horizontal: "center",
    }}
    {...props}
  />
));

const StyledMenuItem = withStyles((theme) => ({
  root: {
    "&:focus": {
      backgroundColor: theme.palette.primary.main,
      "& .MuiListItemIcon-root, & .MuiListItemText-primary": {
        color: theme.palette.common.white,
      },
    },
  },
}))(MenuItem);

const Topbar = (props) => {
  const {
    user,
    className,
    onLogout,
    history,
    notifications,
    onSidebarOpen,
    onSidebarClose,
    isSideBarOpen,
    ...rest
  } = props;

  const classes = useStyles();
  const theme = useTheme();
  const unreadNotifications = 0;

  const [anchorEl, setAnchorEl] = React.useState(null);
  const [
    notificationListAnchorEl,
    setNotificationListAnchorEl,
  ] = React.useState(null);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const onNotificationClick = (evt) => {
    setNotificationListAnchorEl(evt.currentTarget);
  };
  return (
    <AppBar {...rest} className={clsx(classes.root, className)} color="custom">
      <Toolbar className="tool-bar">
        <IconButton
          aria-label="open drawer"
          onClick={onSidebarOpen}
          edge="start"
          className={clsx(classes.menuButton, {
            [classes.hide]: isSideBarOpen,
          })}
        >
          <MenuIcon />
        </IconButton>
        <IconButton
          color="inherit"
          aria-label="open drawer"
          onClick={onSidebarClose}
          className={clsx(classes.menuButton, {
            [classes.hide]: !isSideBarOpen,
          })}
        >
          <MenuIcon />
        </IconButton>

        <div className='logo mobile-view text-center'></div>

        <div className={classes.flexGrow} />
        {/* <Hidden mdDown> */}

        {/* <SearchIcon className='search'/> */}
        {unreadNotifications
          ? {
              /* <small className="notification-badge">{unreadNotifications}</small> */
            }
          : null}
        {/* <IconButton
          aria-controls="notifications-menu"
          aria-label="more"
          arie-haspopup="true"
          className={clsx(classes.menuButton)}
          onClick={onNotificationClick}
        >
          <NotificationsNoneIcon className="notifications" />
        </IconButton> */}

        <Button
          aria-controls="customized-menu"
          aria-haspopup="true"
          //onClick={handleClick}
          className="menu-button mobile-view"
          disabled
        >
          <Avatar className={classes.purple}>
            <span className={classes.userName}>
              <span className={classes.hiUser}>
                Hi, SuperStar &nbsp;
              </span>
              {/* {user && user.UserName ? user.UserName : "Guest"}
              <span className={classes.hiUser}>
                {user && user.EmpId ? ` (${user.EmpId})` : ""}
              </span> */}
              <span className={classes.today}>
                <br /><br />{ moment().format("DD-MM-YYYY hh:mm a") }
              </span>
            </span>
          </Avatar>
          {/*<Typography className="user-name">
            <i className="fa fa-chevron-down"></i>
      </Typography>*/}
        </Button>
        
        {/*
        <StyledMenu
          id="customized-menu"
          anchorEl={anchorEl}
          keepMounted
          open={Boolean(anchorEl)}
          onClose={handleClose}
        >
          <StyledMenuItem>
            <ListItemText primary="Profile" />
          </StyledMenuItem>
          <StyledMenuItem>
            <ListItemText primary="Change Password" />
          </StyledMenuItem>
          <StyledMenuItem onClick={() => onLogout(history)}>
            <ListItemText primary="Logout" />
          </StyledMenuItem>
        </StyledMenu>
        */}
        {/* <StyledMenuItem>
              <ListItemIcon>
                <SendIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Sent mail" />
            </StyledMenuItem>
            <StyledMenuItem>
              <ListItemIcon>
                <DraftsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Drafts" />
            </StyledMenuItem>
            <StyledMenuItem>
              <ListItemIcon>
                <InboxIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Inbox" />
            </StyledMenuItem> */}

        {/* </Hidden> */}
        {/* <Hidden lgUp>
        <Button
            aria-controls="customized-menu"
            aria-haspopup="true"
            onClick={handleClick}
            className="menu-button"
          >
            <Typography className="user-name">{(user && user.name) ? user.name : ''} <i className='fa fa-chevron-down'></i></Typography>
          </Button>
          <StyledMenu
            id="customized-menu"
            anchorEl={anchorEl}
            keepMounted
            open={Boolean(anchorEl)}
            onClose={handleClose}
          >
            <StyledMenuItem>
              <ListItemIcon>
                <SendIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Sent mail" />
            </StyledMenuItem>
            <StyledMenuItem>
              <ListItemIcon>
                <DraftsIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Drafts" />
            </StyledMenuItem>
            <StyledMenuItem>
              <ListItemIcon>
                <InboxIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Inbox" />
            </StyledMenuItem>
            <StyledMenuItem onClick={() => onLogout(history)}>
              <ListItemIcon>
                <InputIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Logout" />
            </StyledMenuItem>
          </StyledMenu>
        </Hidden> */}
      </Toolbar>
    </AppBar>
  );
};

Topbar.propTypes = {
  className: PropTypes.string,
  onSidebarOpen: PropTypes.func,
  onLogout: PropTypes.func,
};

export default Topbar;
