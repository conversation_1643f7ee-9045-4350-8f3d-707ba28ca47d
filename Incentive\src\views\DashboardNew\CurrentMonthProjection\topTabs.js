
import React, { useState, useEffect, useRef } from "react";
import { withSnackbar } from 'notistack';


import {
    Grid,

} from "@material-ui/core";
import {
    Box,
    Tabs, Tab
} from "@material-ui/core";

const TopTabs = (props) => {
    const ProjectedData = props.data;
    const refCurrentTrend = props.refCurrentTrend;
    const refOpportunity = props.refOpportunity;
    const refDailySourcing = props.refDailySourcing;
    const [activeScroll, setActiveScroll] = useState(0);

  


    const executeCurrentTrendScroll = (e) => {
        window.scrollTo(0, refCurrentTrend.current.offsetTop - 60)
    }
    const executeOpportunityScroll = () => {
        window.scrollTo(0, refOpportunity.current.offsetTop - 60)
        
    }
    const executeDailySourcingScroll = () => {
        window.scrollTo(0, refDailySourcing.current.offsetTop - 60)
    
    }

    useEffect(() => {
        const handleScroll = (e) => {
            
            if ( window.scrollY > refCurrentTrend.current.offsetTop -200  && window.scrollY < refOpportunity.current.offsetTop-200) {
                setActiveScroll(0)
            } else if ( window.scrollY > refOpportunity.current.offsetTop -200 && window.scrollY < refDailySourcing.current.offsetTop-200 ) {
                setActiveScroll(1)
            } else if (window.scrollY > refDailySourcing.current.offsetTop -200) {
                setActiveScroll(2)
                // Etc...
            }
        }
        document.addEventListener('scroll', handleScroll);
        return () => {
            document.removeEventListener('scroll', handleScroll);
        }
    }, [])

    

    // useEffect(() => {
    //     const handleScroll = (e) => {
    //         if (window.scrollY >= 0 && window.scrollY <= window.innerHeight / 2) {
    //             console.log("Section1")
    //         }
    //         else if (refCurrentTrend.current.offsetTop - window.scrollY < window.innerHeight / 2 && refOpportunity.current.offsetTop - window.scrollY >= window.innerHeight / 2) {
    //             console.log("Section2")
    //         }  
    //     }
    //     document.addEventListener('scroll', handleScroll);
    //     return () => {
    //         document.removeEventListener('scroll', handleScroll);
    //     }
    // }, [])

    const handleTabChange = (newValue) => {
        setActiveScroll(newValue);
        switch (newValue) {
            case 0:
                executeCurrentTrendScroll()
                break;
            case 1:
                executeOpportunityScroll()
                break;
            case 2:
                executeDailySourcingScroll()
                break;
            default:
                break;
        }
    }

    



    return (<>
        <Grid container spacing={2} className='mobile-view'>
            <Box className='items-list'>
                <Tabs
                    value={activeScroll}
                    onChange={handleTabChange}
                    variant="scrollable"
                    scrollButtons
                    allowScrollButtonsMobile
                    aria-label="scrollable force tabs example"
                >
                    <Tab label="Current Trends" value={0} />
                    {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                        <Tab label="Opportunity" value={1} />}
                    <Tab label="Daily Sourcing Req." value={2} />
                </Tabs>
            </Box>
        </Grid>
    </>)
}


export default withSnackbar(TopTabs);