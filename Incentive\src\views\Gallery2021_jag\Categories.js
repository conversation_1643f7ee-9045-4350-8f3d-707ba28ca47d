import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
import Typography from '@material-ui/core/Typography';
import AppBar from '@material-ui/core/AppBar';
import Tabs from '@material-ui/core/Tabs';
import Tab from '@material-ui/core/Tab';
import PropTypes from 'prop-types';
import Box from '@material-ui/core/Box';
import imageCategory from "./ImageCategory";
import ImagesGallery from "./ImagesGallery";


function TabPanel(props) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`scrollable-force-tabpanel-${index}`}
      aria-labelledby={`scrollable-force-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box p={3}>
          <Typography>{children}</Typography>
        </Box>
      )}
    </div>
  );
}

TabPanel.propTypes = {
  children: PropTypes.node,
  index: PropTypes.any.isRequired,
  value: PropTypes.any.isRequired,
};

function a11yProps(index) {
  return {
    id: `scrollable-force-tab-${index}`,
    'aria-controls': `scrollable-force-tabpanel-${index}`,
  };
}

const useStyles = makeStyles((theme) => ({
 
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: "absolute",
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: "2px solid #000",
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));



export default function Categories() {
  const classes = useStyles();
  const [value, setValue] = React.useState(0);


  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  useEffect(() => {

   
  });


  return (
    <div className="GallerySection">
       <Grid item sm={12} md={12} xs={12}>
    <h2>Photo <b>Gallery</b></h2>

      <AppBar position="static" color="default">
        <Tabs
          value={value}
          onChange={handleChange}
          variant="scrollable"
          scrollButtons="on"
          indicatorColor="primary"
          textColor="primary"
          aria-label="scrollable force tabs example"         
        >
        {imageCategory && imageCategory.data && imageCategory.data.map((data, index) =>
        <Tab label={data.CategoryName} {...a11yProps(index)}  className="imgcategories"/>
        )}
        </Tabs>
      </AppBar>
      </Grid>
      {imageCategory && imageCategory.data && imageCategory.data.map((data, index) =>
        <TabPanel value={value} index={index}>
           <Grid container spacing={3}>
         
           <ImagesGallery index={value} Categories={imageCategory} />
       
           </Grid>
         
        </TabPanel>        
      )}
    </div>
  );
}
