import { Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@material-ui/core";
import React, { useState, useEffect, useRef } from "react";
import * as services from "../../../services";

const LeaderBoard = ({ agentDetails, contextId, agentData, version }) => {

  const [leaderBoardData, setLeaderBoardData] = useState(null);
  const [topSevenAgents, setTopSevenAgents] = useState([]);
  const [loggedAgentPosition, setLoggedAgentPosition] = useState(-1);
  const UserId = agentDetails.AgentId || 0;
  const MemberType = agentData.MemberType || '';
  const ProductId = agentData.ProductId || 0;
  const BUId = agentData.BUId || 0;

  const refIsTopSeven = useRef(false);

  useEffect(() => {
    setLeaderBoardData([]);
    if (!UserId || !MemberType) {
      return;
    }

    services
      .API_GET(`jag/GetLeaderBoard/${UserId}/${MemberType}?version=${version}`)
      .then(response => {
        
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          setLeaderBoardData(JSON.parse(response.Response));
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [UserId, MemberType]);

  useEffect(() => {
    if (leaderBoardData) {
      leaderBoardData.sort((a, b) => a.AgentRank < b.AgentRank);
      let topSevenAgents = leaderBoardData.slice(0, 7);
      for (let i = 0; i < leaderBoardData.length; i++) {
        const element = leaderBoardData[i];
        if (element.AgentId === agentDetails.AgentId) {
          setLoggedAgentPosition(i);
          if (i <= 6) {
            refIsTopSeven.current = true;
          }
        }
      }
      setTopSevenAgents(topSevenAgents);
    }
  }, [leaderBoardData]);

  const SuperscriptList = ['th', 'st', 'nd', 'rd', 'th', 'th', 'th', 'th', 'th', 'th'];

  return (
    <Grid item md={8} xs={12}>
      <div className="LeaderBoard">
        <h3 className="Heading">Leader Board</h3>
        <ul>
          <li>
            <img src="/images/jag2023/2nd.png" />
            <h4>{topSevenAgents[1] && topSevenAgents[1].AgentName || '-'}</h4>
            
            
            {![65, 67].includes(BUId) && <>
              <p>Issued APE till date</p>
              <h3>{parseInt(topSevenAgents[1]  && topSevenAgents[1].APE || 0).toLocaleString('en-IN')}</h3>
            </>}
            {[65, 67].includes(BUId) && <>
              <p>Issued Bookings till date</p>
              <h3>{parseInt(topSevenAgents[1]  && topSevenAgents[1].IssuedBKG_CurrYear || 0)}</h3>
            </>
            }
            {/* <p>Cash Rewards/Lottery Ticket</p>
            <h3>{(topSevenAgents[1] && topSevenAgents[1].CashRewards) > 0 ? `₹${parseInt(topSevenAgents[1].CashRewards || 0).toLocaleString('en-IN')}` : '₹0'}/{topSevenAgents[1] && topSevenAgents[1].LotteryTicket || '0'}</h3> */}
          </li>

          <li>
            <img src="/images/jag2023/1st.png" />
            <div>
            <h4>{topSevenAgents[0] && topSevenAgents[0].AgentName || '-'}</h4>
            {![65, 67].includes(BUId) && <>
              <p>Issued APE till date</p>
              <h3>{parseInt(topSevenAgents[0]  && topSevenAgents[0].APE || 0).toLocaleString('en-IN')}</h3>
            </>}
            {[65, 67].includes(BUId) && <>
              <p>Issued Bookings till date</p>
              <h3>{parseInt(topSevenAgents[0]  && topSevenAgents[0].IssuedBKG_CurrYear || 0)}</h3>
            </>
            }
            {/* <p>Cash Rewards/Lottery Ticket</p>
            <h3>{topSevenAgents[0] && topSevenAgents[0].CashRewards > 0 ? `₹${parseInt(topSevenAgents[0] && topSevenAgents[0].CashRewards || 0).toLocaleString('en-IN')}` : '₹0'}/{topSevenAgents[0] && topSevenAgents[0].LotteryTicket || '0'}</h3> */}
            </div>
          </li>
          <li>
            <img src="/images/jag2023/3rd.png" />
            <h4>{topSevenAgents[2] && topSevenAgents[2].AgentName || '-'}</h4>
            {![65, 67].includes(BUId) && <>
              <p>Issued APE till date</p>
              <h3>{parseInt(topSevenAgents[2]  && topSevenAgents[2].APE || 0).toLocaleString('en-IN')}</h3>
            </>}
            {[65, 67].includes(BUId) && <>
              <p>Issued Bookings till date</p>
              <h3>{parseInt(topSevenAgents[2]  && topSevenAgents[2].IssuedBKG_CurrYear || 0)}</h3>
            </>
            }
            {/* <p>Cash Rewards/Lottery Ticket</p>
            <h3>{topSevenAgents[2] && topSevenAgents[2].CashRewards > 0 ? `₹${parseInt(topSevenAgents[2] &&  topSevenAgents[2].CashRewards || 0).toLocaleString('en-IN')}` : '₹0'}/{topSevenAgents[2] && topSevenAgents[2].LotteryTicket || '0'}</h3> */}
          </li>
        </ul>
        <hr />
        <TableContainer>
          <Table aria-label="simple table" className="AgentListing">

            <TableHead>
              <TableRow>
                <TableCell>Rank</TableCell>
                <TableCell>Agent Name</TableCell>
                {![65, 67].includes(BUId) && <TableCell>Issued APE till date</TableCell>}
                {[65, 67].includes(BUId) && <TableCell>Issued Bookings till date</TableCell>}
                
                {/* <TableCell>Cash Rewards/Lottery Ticket</TableCell> */}
              </TableRow>
            </TableHead>

            <TableBody>
              {Array.isArray(topSevenAgents) && topSevenAgents.map((agent, index) => {
                if (index >= 3 && (index < 6 || loggedAgentPosition < 7)) {

                  return (<TableRow className={index === loggedAgentPosition ? "active" : ""}>
                    <TableCell>{index + 1}<span>{SuperscriptList[(index + 1) % 10]}</span></TableCell>
                    <TableCell>{agent && agent.AgentName || '-'}</TableCell>
                    
                    {![65, 67].includes(BUId) && <TableCell>{parseInt(agent&& agent.APE || 0).toLocaleString('en-IN')}</TableCell>}
                    {[65, 67].includes(BUId) && <TableCell>{parseInt(agent&& agent.IssuedBKG_CurrYear || 0)}</TableCell>}
                    {/* <TableCell>{agent && agent.CashRewards > 0 ? `₹${parseInt(agent.CashRewards).toLocaleString('en-IN')}` : '₹0'}/{agent && agent.LotteryTicket || '0'}</TableCell> */}
                  </TableRow>)
                }
              })
              }

              {loggedAgentPosition >= 7 &&
                <TableRow className="active">
                  <TableCell>{loggedAgentPosition + 1}<span>{SuperscriptList[(loggedAgentPosition + 1) % 10]}</span></TableCell>
                  <TableCell>{leaderBoardData[loggedAgentPosition] && leaderBoardData[loggedAgentPosition].AgentName || '-'}</TableCell>
                 
                  {![65, 67].includes(BUId) &&  <TableCell>{parseInt(leaderBoardData[loggedAgentPosition] && leaderBoardData[loggedAgentPosition].APE || 0).toLocaleString('en-IN')}</TableCell>}
                    {[65, 67].includes(BUId) &&  <TableCell>{parseInt(leaderBoardData[loggedAgentPosition] && leaderBoardData[loggedAgentPosition].IssuedBKG_CurrYear || 0)}</TableCell>}
                  {/* <TableCell>{ leaderBoardData[loggedAgentPosition] && leaderBoardData[loggedAgentPosition].CashRewards > 0 ?
                   `₹${parseInt(leaderBoardData[loggedAgentPosition].CashRewards).toLocaleString('en-IN')}` : '₹0'}/
                  {leaderBoardData[loggedAgentPosition] &&leaderBoardData[loggedAgentPosition].LotteryTicket || '0'}</TableCell> */}
                </TableRow>
              }
            </TableBody>
          </Table>
        </TableContainer>

        {/* <div className="text-center"> <button>View More</button></div> */}
      </div>
    </Grid>
  );
};

export default LeaderBoard;