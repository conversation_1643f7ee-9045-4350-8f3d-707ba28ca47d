@import "../scss/variables/variables";
@import "../scss//common/table";
@import "../scss/common/coloredCards";
@import "../scss/common/dataCards";
@import "../scss/common/tabs";
@import "../scss/common/searchInput";
@import "../scss/common/expansionPanel";
@import "../scss/common/drawer";
@import "../scss/common/modal";
@import "../scss/components/topBar";
@import "../scss/components/dashboard";
@import "../scss/components/booking";
@import "../scss/components/customerHistory";
@import "../scss/components/Incentive";
@import url("https://fonts.googleapis.com/css?family=Merriweather:wght@300;400;700&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;1,100;1,300;1,400;1,500;1,700&display=swap");

* {
	box-sizing: border-box;
	margin: 0;
	padding: 0;
}

html {
	height: 100%;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

.text-left {
	text-align: left;
}

body {
	background-color: #fafafa;
	height: 100%;
	font-family: "Roboto", sans-serif;
	-ms-overflow-style: none;

	// padding-top: 49px;
	&::-webkit-scrollbar {
		display: none;
	}
}

.tool-bar {
	box-shadow: 0px 3px 6px #00000029;
}

.text-center {
	text-align: center;
}

.pading-zero {
	padding: 0;
}

.card {
	background: #ffffff;
	box-shadow: 0px 0px 26px #0000001a;
	border-radius: 20px;
}

a {
	text-decoration: none;
}

#root {
	height: 100%;
}

.title {
	position: relative;
	margin: 12px 0;
	display: flex;
	align-items: baseline;
	padding: 0 16px !important;

	&>div>span {
		font-size: 16px;
		color: $primary-color;
		position: relative;
		font-weight: 700;
	}

	@media (min-width: 600px) {
		flex-direction: row;
		height: auto;
	}

	.firstbox {
		width: 100%;
	}
}

.inner-mobile {
	display: flex;
	flex-wrap: wrap;

	@media (min-width: 960px) {
		display: none;
	}

	.firstbox {
		width: 100%;
	}
}

.inner-desktop {
	min-width: 800px;
	width: 100%;
	border-radius: 5px 5px 0 0;
	display: none;

	@media (min-width: 960px) {
		display: block;
	}
}

.logo-container {
	justify-content: center !important;
	margin-bottom: 25px;
}

.MuiIconButton-root {
	color: #495973 !important;
	background-color: red;
}

.wrapper {
	padding: 24px;
	// background-color: #FAFAFA;
}

/*-------------------Pragya-02-Feb-2022---------------------------*/
.mobile-view,
.items-list {
	display: none;
}

.CurrentMonthData .MuiAccordion-rounded {
	border-radius: 0px !important;
	box-shadow: none !important;
}

.motorIncentive-header,
.motorIncentive-content {
	width: 100%;
}

.motorIncentive-content tbody tr th {
	text-align: left;
}

.motorIncentive-content tbody tr th:last-child {
	text-align: right;
}

.CurrentMonthData .MuiAccordionSummary-content {
	display: block !important;
	margin: 0 !important;
}

.CurrentMonthData .MuiAccordionSummary-root {
	padding: 0 !important;
	min-height: auto !important;
	align-items: baseline !important;
}

.CurrentMonthData .MuiAccordionDetails-root {
	padding: 0px 5px 0px 0px !important;
}

.web-common {
	margin-top: 10px;
}

.MuiIconButton-edgeEnd {
	padding: 0px !important;
	margin: auto !important;
}

.payout-box li.modal-popup span {
	width: auto;
	white-space: nowrap;
}

.payout-box li.modal-popup p {
	width: 100%;
}

.payout-box li p.border-text:after {
	position: absolute;
	content: "";
	bottom: 0;
	right: 0;
	margin: auto;
	width: 50px;
	border-bottom: 1px dashed #ed3434;
}

button.tooltip {
	border: 0px !important;
	background: transparent !important;
	outline: none !important;
	box-shadow: none !important;
}

#panel1a-content p {
	margin-left: 3px !important;
}

/* -----------------------Gunjan-12-4-22(health Renewal)---------------*/
.HealthRenewal {
	.payout {
		.payout-box {
			height: 410px;

			h6 {
				text-align: left;
				font: normal normal bold 18px/24px Roboto;
				letter-spacing: 0px;
				color: #3b3e51;
				text-transform: capitalize;
				opacity: 1;
			}

			li {
				border-bottom: 1px solid #6c75a633;
				border-radius: 0;
				color: #3b3e51 !important;
				padding: 10px 20px;

				span {
					font: normal normal 600 14px/26px Roboto;
					color: #3b3e51;
				}

				p {
					font: normal normal 500 14px/26px Roboto;
					color: #3b3e51;
				}
			}

			.firstbox {
				border-radius: 8px !important;
				border-bottom: none !important;
				margin-bottom: 12px;
			}
		}
	}

	.green {
		color: #0F8D61;
		font-weight: 600 !important;
	}

	.lightgreen {
		background: #E6FFF7 0% 0% no-repeat padding-box;
	}

	.HealthRenewalCommanView {
		padding: 15px 15px;
		background: #ffffff;
		border-radius: 18px;
		box-shadow: 0px 0px 16px #00000014;
		height: 410px;

		h6 {
			text-align: left;
			font: normal normal bold 18px/24px Roboto;
			letter-spacing: 0px;
			color: #3b3e51;
			text-transform: capitalize;
			opacity: 1;
		}

		table {
			width: 100%;
			border-collapse: collapse;
			border-spacing: 0;

			thead tr th {
				background: #ebf0f8 0% 0% no-repeat padding-box;
				padding: 6px 20px;
				font: normal normal bold 12px/24px Roboto;
				letter-spacing: 0.17px;
				color: #3b3e51;
				text-transform: capitalize;
				opacity: 1;
				text-align: center;

				&:first-child {
					border-radius: 18px 0px 0px 18px;
					text-align: left;
				}

				&:last-child {
					border-radius: 0px 18px 18px 0px;
					text-align: right;
				}
			}

			tbody tr td {
				text-align: center;
				font: normal normal normal 14px/24px Roboto;
				letter-spacing: 0.2px;
				color: #3b3e51;
				padding: 6px 20px;
				opacity: 1;
				border-bottom: 1px solid #6c75a633;

				&:first-child {
					font: normal normal 600 14px/24px Roboto;
					text-align: left;
				}

				&:last-child {
					text-align: right;
				}
			}

			tbody tr:first-child td {
				padding-top: 10px;
			}
		}
	}

	.height230 {
		height: 230px;
	}
}

/*-----------------------Pragya-02-May-2022-----------------*/
.crossButton {
	right: 20px;
	cursor: pointer;
}

.how-it-works-section.latest-list h3 {
	margin-top: 35px;
}

.how-it-works-section.latest-list p {
	padding-left: 15px;
	padding-bottom: 7px;
}

.how-it-works-section.latest-list ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.how-it-works-section.latest-list ul li:before,
.how-it-works-section.latest-list ul.main-list.core-list ul.main-list.agent-list li:before {
	position: absolute;
	content: "";
	top: 7px;
	left: 0;
	margin: auto;
	border: 3px solid #253858;
	border-radius: 3px;
	display: block;
}

.how-it-works-section.latest-list ul li {
	text-align: left;
	font: normal normal normal 14px/24px Roboto;
	letter-spacing: 0px;
	color: #253858;
	opacity: 1;
	padding-left: 15px;
	padding-bottom: 10px;
	position: relative;
	line-height: 1.4;
}

.how-it-works-section.latest-list ul.main-list {
	padding-left: 30px;
	margin-bottom: 10px;
}

.how-it-works-section.latest-list ul li ul.inner-list li:before {
	border: 1px solid #253858;
	background: #fff;
	width: 6px;
	height: 6px;
	left: 22px;
}

.how-it-works-section.latest-list ul li ul.inner-list li {
	padding-left: 38px;
	padding-bottom: 4px;
}

.how-it-works-section.latest-list ul.main-list.slab-list {
	padding-left: 38px;
}

.how-it-works-section.latest-list ul.main-list.slab-list li:before,
.how-it-works-section.latest-list ul.main-list.core-list li:before {
	display: none;
}

.how-it-works-section.latest-list ul.main-list.bullet-list {
	padding-left: 35px;
}

.how-it-works-section.latest-list ul.main-list.bullet-list li,
.how-it-works-section.latest-list ul.main-list.core-list li {
	padding-bottom: 4px;
}

.how-it-works-section.latest-list ul.main-list.core-list {
	padding-left: 0;
	margin-bottom: 0;
}

.how-it-works-section.latest-list h3.upsell-incentive {
	margin-top: 14px;
	text-align: left;
	color: #0065ff;
	letter-spacing: 2px;
	margin-bottom: 16px;
}

.how-it-works-section.latest-list table tr p.amt-text,
.how-it-works-section.latest-list table td p {
	text-align: center;
	padding: 4px;
}

.how-it-works-section.latest-list table td p {
	padding: 2px 10px;
	line-height: 1.2;
}

.warriors {
	width: 100px;
	background: transparent linear-gradient(91deg, #ddccf580 0%, #f5efff80 100%) 0% 0% no-repeat padding-box !important;
	border-radius: 4px !important;
	font: normal normal normal 16px/21px Roboto !important;
	letter-spacing: 0px !important;
	color: #000000 !important;

	& td:nth-child(3n+1) {
		font-weight: 600 !important;
	}
}





/*------------------------------media-css---------------------*/
@media all and (max-width: 768px) {

	.wrapper,
	.bg-color {
		padding: 15px;

		.proceedBtn {
			font-size: 11px;
		}
	}

	.HealthRenewal {
		.HealthRenewalCommanView {
			height: auto;
		}

		.height230 {
			height: auto;
		}

		.payout {
			.payout-box {
				height: auto;
			}
		}
	}

}

@media (max-width: 767px) {
	::-webkit-scrollbar {
		display: none;
	}

	.tool-bar {
		box-shadow: none;
		padding-right: 0px !important;
	}

	.mobile-view {
		display: block;
	}

	.logo.mobile-view {
		background: url("/images/pblogo.svg") no-repeat center;
		background-size: contain;
		height: 25px;
		width: 100%;
		margin: auto;
	}

	.wrapper {
		padding: 20px 16px;
		overflow-x: hidden;
		margin-top: 50px;
	}

	.ranking-data .rank-box .rank-box-right-section ul li.rank:before {
		background: url(/images/incentive/icon_rank.svg) !important;
	}

	.ranking-data .rank-box .rank-box-right-section ul li.slab:before {
		background: url(/images/incentive/Slab.svg) no-repeat right !important;
	}

	.tabLayout .Mui-selected {
		z-index: 10001;
	}

	.tabLayout .payout {
		max-width: 158px;
	}

	.tabLayout .CMP {
		margin-left: -10px;
		max-width: 180px;
	}

	.items-list {
		display: flex;
		list-style: none;
		overflow-x: scroll;
		white-space: nowrap;
		margin: 0px auto 30px;
		align-items: center;
		box-shadow: 0px 3px 8px #00000066;
		-webkit-box-shadow: 0px 3px 8px #00000066;
		height: 48px;
		background: #fff;
		padding: 0px 2px;
		width: 100%;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1001;
	}

	.items-list .MuiTab-root {
		opacity: 1;
		position: relative;
	}

	.items-list .MuiTab-root.active {
		color: #0065ff;
		font-weight: 600;
	}

	.items-list .MuiTab-root.active:after {
		position: absolute;
		content: "";
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		border-bottom: 2px solid #0065ff;
		width: 90%;
	}

	.common-view .MuiCard-root {
		border-radius: 8px;
		margin: 0px 12px;
	}

	.CurrentMonthProjContainer .currentMonthSlab p.add div {
		height: 38px;
	}

	.currentMonthSlab a {
		font-weight: 600;
		white-space: nowrap;
	}

	.PayoutContainer .bookingTable {
		background: transparent;
		box-shadow: none;
		border-radius: 0;
	}

	.web-common {
		margin-top: 0px;
	}

	.motorIncentive-content {
		margin-top: -10px;
	}

	.motorIncentive-header tr td:last-child {
		white-space: nowrap;
	}

	.CurrentMonthProjContainer .CurrentMonthData table tr td.last-text {
		padding: 13px 8px 13px 0px !important;
	}

	.MuiIconButton-edgeEnd {
		color: #253858 !important;
		margin: auto -6px auto -4px !important;
	}

	.MuiAccordionDetails-root {
		padding: 0px !important;
		width: 100%;
		display: block !important;
	}

	.CurrentMonthProjContainer .CurrentMonthData .MuiAccordionDetails-root table.motorIncentive-content tr {
		position: relative;
		width: 100%;
		display: table-row;
	}

	.CurrentMonthProjContainer .CurrentMonthData .MuiAccordionDetails-root table.motorIncentive-content tr:last-child {
		border-bottom: 0;
	}

	.MuiAccordionDetails-root table.motorIncentive-content tr td {
		opacity: 1 !important;
		padding: 12px 0px !important;
		white-space: nowrap;
		width: 50%;
	}

	.MuiAccordionDetails-root table.motorIncentive-content tr:nth-child(4) td {
		border-bottom: 0;
	}

	.CurrentMonthProjContainer .CurrentMonthData table.motorIncentive-content tr td:last-child {
		text-align: left;
		float: right;
		line-height: 40px;
	}

	.CurrentMonthProjContainer .CurrentMonthData table.motorIncentive-content tr td:nth-child(3) {
		float: none !important;
	}

	.CurrentMonthProjContainer .CurrentMonthData .motorIncentive-content tr td:nth-child(4) {
		float: none !important;
		padding-left: 6px !important;
		text-align: right !important;
	}

	.CurrentMonthProjContainer .CurrentMonthData table tr:nth-child(1) td:last-child,
	.CurrentMonthProjContainer .CurrentMonthData table tr:nth-child(2) td:last-child {
		white-space: nowrap;
	}

	.MuiAccordionDetails-root table.motorIncentive-content tr:nth-child(4) td:nth-child(3) {
		text-align: center;
	}

	.CurrentMonthProjContainer .CurrentMonthData .MuiAccordionDetails-root table.motorIncentive-content tr:not(:last-child):after {
		position: absolute;
		content: "";
		bottom: 0;
		left: 0;
		right: 0;
		margin: auto;
		border-bottom: 1px solid #eaeaea;
	}

	#panel1a-content p {
		margin-left: 5px !important;
	}

	.incentive .incentive-box.table-incentive li {
		margin: 12px auto -22px -15px;
		max-width: 320px;
		width: 100%;
		overflow-x: scroll;
	}

	.incentive-box.table-incentive .motorIncentive th,
	.incentive-box.table-incentive .motorIncentive td {
		width: 100%;
		color: #707070;
		line-height: 22px;
		padding-left: 12px;
		vertical-align: baseline;
	}

	.incentive-box.table-incentive .motorIncentive th {
		padding-bottom: 6px;
	}

	.incentive-box.table-incentive .motorIncentive th:first-child,
	.incentive-box.table-incentive .motorIncentive td:first-child {
		background: #fff;
		box-shadow: 5px 0px 10px #3469cb29;
		-webkit-box-shadow: 5px 0px 10px #3469cb29;
		height: 52px;
		padding-left: 14px;
		padding-top: 12px;
		font-size: 12px !important;
		width: 85px;
		left: 0;
		top: auto;
		position: sticky;
		z-index: 1;
	}

	.incentive .incentive-box.table-incentive .motorIncentive tr {
		box-shadow: 0px -1px 0px #3469cb29;
		-webkit-box-shadow: 0px -1px 0px #3469cb29;
	}

	.incentive .incentive-box.table-incentive .motorIncentive thead tr:first-child {
		box-shadow: none;
		-webkit-box-shadow: none;
	}

	.incentive .incentive-box.table-incentive .motorIncentive td {
		color: #253858;
		font-size: 14px;
		font-weight: 600;
		white-space: nowrap;
		padding: 10px 14px;
		vertical-align: middle;
	}

	.incentive-box.table-incentive .motorIncentive tr th:last-child {
		white-space: nowrap;
	}

	.payout-box li button.tooltip {
		margin-top: -8px;
	}

	.MuiTooltip-popper {
		top: 15px !important;
		left: 24.4px !important;
	}

	// .MuiTooltip-tooltip.MuiTooltip-tooltipArrow{max-width: 400px;}
	.MuiTooltip-popper span.MuiTooltip-arrow {
		left: 150px !important;
	}

	.incentive-box i.fa-info-circle {
		vertical-align: text-bottom;
		color: #253858;
	}

	.MuiBackdrop-root {
		background: #172b4db3 !important;
	}

	.MuiDialog-scrollPaper {
		align-items: flex-end !important;
	}

	.MuiDialog-paperScrollPaper {
		width: 100% !important;
		max-width: 100% !important;
		box-shadow: none !important;
		margin: 0 !important;
		border-radius: 32px 32px 0px 0px !important;
	}

	#customized-dialog-title {
		background: #e6f0ff;
		padding: 17px 20px;
	}

	#customized-dialog-title h6 {
		color: #253858;
	}

	#customized-dialog-title h6 strong {
		font-weight: 600;
		font-size: 16px;
		margin-left: -5px;
	}

	.critiria-popup .close-btn {
		top: 12px;
		right: 8px;
		padding: 5px;
	}

	.critiria-popup .close-btn svg {
		font-size: 0.8em;
	}

	.critiria-popup .payoutClawPopup .topPerformance tr:not(:last-child) {
		border-bottom: 1px solid #dbdbdb;
	}

	.critiria-popup .payoutClawPopup .topPerformance th,
	.critiria-popup .payoutClawPopup .topPerformance td {
		color: #253858;
		opacity: 1;
		font-weight: 500;
		background: #fff;
		font-size: 14px;
		line-height: 22px;
		padding: 2px 15px 6px;
		border: 0;
		width: 100%;
	}

	.critiria-popup .payoutClawPopup .topPerformance th:first-child {
		padding-left: 0px;
	}

	.critiria-popup .payoutClawPopup .topPerformance td {
		padding: 16px 0px;
		white-space: nowrap;
	}

	.critiria-popup .payoutClawPopup .topPerformance td:first-child {
		text-align: left;
	}

	.help-section {
		padding: 5px 12px !important;
	}

	.faq-section .MuiTab-textColorInherit.Mui-selected {
		width: 100%;
		margin: 0;
		max-width: 100%;
	}

	.faq-section .faq-tab {
		margin-top: 4px;
	}

	.faq-section .faq-tab .MuiTab-root {
		font-size: 16px !important;
		min-height: 44px !important;
	}

	.faq-section .MuiBox-root {
		padding: 20px 0px 0px !important;
	}

	.faq-section .MuiAccordion-rounded {
		border-radius: 8px !important;
		margin: 15px 0px !important;
	}

	.faq-section .MuiAccordionSummary-root.Mui-expanded {
		min-height: 48px !important;
	}

	.faq-section .MuiAccordionSummary-content.Mui-expanded {
		margin: 12px 0px !important;
	}

	.faq-section .MuiCollapse-wrapper {
		padding: 0px 16px 15px !important;
	}

	.faq-section .faqtab-data .MuiTypography-body1 {
		letter-spacing: 0;
	}

	.faq-section .read-more-link h2 {
		padding: 10px;
		width: 100%;
		font-size: 14px;
		margin: 20px auto 0px;
	}

	.faq-rightside .MuiGrid-spacing-xs-3>.MuiGrid-item {
		padding: 0px !important;
	}

	.faq-rightside .contact-us {
		padding: 14px;
	}

	.faq-rightside .contact-us h6 {
		font-size: 18px;
	}

	.faq-rightside p {
		padding: 8px 0px 15px;
	}

	.faq-rightside .contact-us .rightside-box {
		padding: 20px 15px;
		border-radius: 8px;
		margin-bottom: 5px;
	}

	.booking-table button.load-more {
		border: 0;
		height: 36px;
		max-width: 120px;
		margin: 0px auto !important;
		background: #0065ff;
		color: #fff;
		width: 100%;
		display: block;
		font-size: 14px;
		font-weight: 500;
		font-family: "Roboto", sans-serif;
		text-transform: capitalize;
	}
}

.CongratulationMsg {
	width: 68%;
	margin: 134px auto 30px;
	text-align: justify;
	background-color: #fff;
	padding: 20px;
	border-radius: 5px;
	box-shadow: 0px 6px 16px #3469cb29;
	font: normal normal 500 15px/24px Roboto;
	letter-spacing: 0.09px;
	color: #253858;
}

.Criteria {
	padding: 20px 25px;

	h3 {
		font-size: 20px;
		margin-bottom: 20px;
	}

	ul {
		list-style-type: none;

		li {
			width: 50%;
			display: inline-flex;
			align-items: center;
			font-size: 15px;
			font-weight: 500;
			font-family: 'Roboto';
			border-bottom: 1px solid #edeaea;
			line-height: 38px;
			padding-bottom: 8px;

			button {
				color: #0065ff;
			}
		}
	}

	.CriteriaTab{
		width: 1070px;
		background-color: #fff;
		color: #253858;
		box-shadow: none;
		border-radius: 0px;
		.MuiTabs-scroller{
		overflow: auto !important;
		button{
			font-size: 11px;
			min-width: 170px;
			padding: 8px 10px;
			text-transform: capitalize;
			width: auto;
			margin: 0px;
			font-weight: 600;
			font-family: roboto;
			border-radius: 0px;
		}
		.Mui-selected {
			color: #0065ff;
			border-bottom: 2px solid #0065ff;
		}
	}
	}
	.Pdfwidth{
		width:1070px;
		.react-pdf__Page__canvas{
			width:100% !important;
		}
	}
}