import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";

import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';

import Lightbox from 'react-image-lightbox';
import 'react-image-lightbox/style.css';




export default function Gallery(props) {
  const [images, setImages] = useState(['/images/Gallery/g1.jpg', '/images/Gallery/g3.jpg', '/images/Gallery/g2.jpg', '/images/Gallery/g4.jpg'])
  const [current, setCurrent] = useState(null);
  const [photoIndex, setPhotoIndex] = useState(null);


  const handleImageClick = (image, index) => {
    //alert('image click');
    setCurrent(image);
    setPhotoIndex(index);
  }

  const handleCloseModal = (e) => {
    e && e.preventDefault();
    setCurrent(null);
  }



  var slider_gallery = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 900,
    slidesToShow: 2,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  return (


    <Grid item md={9} xs={12} className="gallery">
      <h5>
        Gallery {/*  <a className="viewall">View All</a> */}
      </h5>
      <Card className="card">
        <CardContent>

          <Slider {...slider_gallery}>
            {images.map((image, index) => (
              <div>
                <div className="card image">
                  {/* <img src="/images/img-comingsoon.svg" className="soon" alt="" /> */}
                  <div className="media">
                    <img src={image} className="thumb"/>
                    <div className="overlay" onClick={() => handleImageClick(image, index)}>
                      <img src="/images/icon_zoom.svg"  title="" />
                    </div>
                  </div>
                  {/* <p>Sneak peek into PB's rewards and recognition programme ... <a href="" className="link">Read more</a></p> */}
                </div>
              </div>
            ))}
          </Slider>

        </CardContent>
      </Card>

      {current &&
        <Lightbox
          mainSrc={images[photoIndex]}
          nextSrc={images[(photoIndex + 1) % images.length]}
          prevSrc={images[(photoIndex + images.length - 1) % images.length]}
          onCloseRequest={handleCloseModal}
          onMovePrevRequest={() =>
            setPhotoIndex((photoIndex + images.length - 1) % images.length)
          }
          onMoveNextRequest={() =>
            setPhotoIndex((photoIndex + 1) % images.length)
          }
          imageCaption="image caption"
        />
      }
    </Grid>

  );
}
