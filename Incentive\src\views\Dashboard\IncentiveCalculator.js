import React, { useEffect, useState, Fragment } from "react";
import { withStyles, makeStyles } from "@material-ui/styles";
import * as services from "../../services";

import Skeleton from "@material-ui/lab/Skeleton";
import _ from "lodash";
import {
  Grid,
  Typography, TextField,
  Card, CardContent,
  List, ListItem, Tooltip,
  Button, AppBar, Zoom,
  Tabs, Tab, Box, Paper
} from "@material-ui/core";
import moment from "moment";
import Slider from "react-slick";
import PropTypes from 'prop-types';
import { Timeline, TimelineItem, TimelineSeparator, TimelineConnector, TimelineContent, TimelineOppositeContent, TimelineDot } from '@material-ui/lab';
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import FastfoodIcon from '@material-ui/icons/Fastfood';
import LaptopMacIcon from '@material-ui/icons/LaptopMac';
import HotelIcon from '@material-ui/icons/Hotel';
import RepeatIcon from '@material-ui/icons/Repeat';

// Slider Incentive Calculator
var slider_incentive_cal = {
  dots: false,
  infinite: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1,
  initialSlide: 0,
  autoplay: true,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        infinite: true,
        dots: true
      }
    },
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        initialSlide: 0
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }
  ]
};

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
}));

const IncentiveCalculator = (props) => {
  const classes = useStyles();
  const { userId, user, date } = props;
  const [desiredAmount, setDesiredAmount] = useState();
  const [goalName, setGoalName] = useState("");
  const [showValues, setShowValues] = useState(false);
  const [open, setOpen] = useState(false);

  const [showCalculator, setShowCalculator] = useState(true);
  const [amountChanged, setAmountChanged] = useState(false);
  const [showRunRate, setShowRunRate] = useState(false);
  const [alreadyHaveMilestone, setAlreadyHaveMilestone] = useState(false);
  const [goalNameValidation, setGoalNameValidation] = useState(false);

  const [calResult, setCalResult] = useState({});
  const [runRateResult, setRunRateResult] = useState({});
  const [mileStones, setMileStones] = useState([]);
  const [agentInfo, setAgentInfo] = useState({});
  const [currentMileStone, setCurrentMileStone] = React.useState(null);
  const [showTimeLine, setShowTimeLine] = useState(false);
  const [IsCurrentMonth, setIsCurrentMonth] = useState(false);

  useEffect(() => {
    if (user && user.ProductId === 115) {
      setShowCalculator(false);
    }
  }, [user]);

  useEffect(() => {
    if (userId, date) {
      //if (date == '01-04-2021') {
      setShowTimeLine(true);
      GetMileStonesData();
      // }
      // else{
      //   setShowTimeLine(false);
      //}

    }
  }, [userId, date]);

  const handleClose = () => {
    setOpen(false);
  };

  const GetMileStonesData = () => {

    if (!userId) {
      return;
    }
    let dt = moment().startOf('month').format("DD-MM-YYYY");
//debugger;
    if (date == dt) {
      setIsCurrentMonth(true)
    }
    else {
      setIsCurrentMonth(false);
      setValue(0);
    }

    services
      .API_GET(`Incentive/GetMileStonesData/${userId}/${date}`)
      .then(response => {
        console.log(response)
        if (response && response !== "[]" && response.Status === true) {

          let list = JSON.parse(response.Response)
          console.log(list);
          //debugger

          let agentInfo = list.agentInfo && list.agentInfo[0];
          let milestone = list.mileStones;

          if (milestone.length > 0) {
            setShowTimeLine(true)
          }
          else {
            setShowTimeLine(false)
          }

          // let milestone = _.filter(list.mileStones, function (o) {
          //   return o.TargetAmount > 0;
          // })
          milestone = _.orderBy(milestone, ['TargetAmount'], ['asc'])

          let CurrentMilestone = _.find(milestone, function (o) { return o.TargetAmount >= agentInfo.ProjectedIncentive; });
          let timeLine = [];
          //debugger;
          timeLine.push({ TargetAmount: agentInfo.ProjectedIncentive, status: 'here' })
          milestone.forEach((element, index) => {

            if (element.TargetAmount > 0) {
              if (element.TargetAmount <= agentInfo.ProjectedIncentive) {
                timeLine.push({ ...element, status: 'achieved' })
              }
              else {
                let exclamation = false;
                if (CurrentMilestone && (CurrentMilestone.TargetAmount == element.TargetAmount)) {
                  exclamation = true;
                }
                timeLine.push({ ...element, status: 'pending', exclamation })
              }
            }
          });



          timeLine = _.orderBy(timeLine, ['TargetAmount'], ['asc'])
          if (timeLine[timeLine.length - 1]['status'] != 'here') {
            timeLine[timeLine.length - 1]['status'] = 'end';
          }

          setMileStones(timeLine);
          setAgentInfo(list.agentInfo && list.agentInfo[0]);
          setCurrentMileStone(CurrentMilestone);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }


  const calculateIncentive = (e) => {
    e.preventDefault();
    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/IncentiveCalculator/${userId}/${desiredAmount}`)
      .then(response => {
        console.log(response)
        if (response && response !== "[]" && response.Status === true) {

          let list = JSON.parse(response.Response)

          list = list[0];
          setCalResult(list);
          //let res = JSON.parse(list[0])
          setShowValues(true);
          //calculateRunRate(list);
          setShowRunRate(true);
          if (list.DesiredAmount != desiredAmount) {
            setAmountChanged(true);
          }



        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const setGoal = (e) => {
    e.preventDefault();
    if (!userId) {
      return;
    }
    if (!showRunRate) {
      return;
    }
    let found = false;
    for (let index = 0; index < mileStones.length; index++) {
      const element = mileStones[index];
      if (element.TargetAmount == calResult.DesiredAmount) {
        found = true;
        break;
      }
    }

    if (found) {
      setAlreadyHaveMilestone(true);
      return;
    }

    if (!goalName) {
      setGoalNameValidation(true);
      return;
    }
    setShowRunRate(false);

    services
      .API_GET(`Incentive/SetCustomMileStone/${userId}/${goalName}/${calResult.WeightedAPE}/${calResult.DesiredAmount}/${calResult.RunRate}`)
      .then(response => {
        console.log(response)
        if (response && response !== "[]" && response.Status === true) {

          handleClose();
          setTimeout(function () {
            GetMileStonesData();
          }, 2000)

        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }





  function TabPanel(props) {
    const { children, value, index, ...other } = props;

    return (
      <Typography
        component="div"
        role="tabpanel"
        hidden={value !== index}
        id={`simple-tabpanel-${index}`}
        aria-labelledby={`simple-tab-${index}`}
        {...other}
      >
        {value === index && <Box p={3}>{children}</Box>}
      </Typography>
    );
  }

  TabPanel.propTypes = {
    children: PropTypes.node,
    index: PropTypes.any.isRequired,
    value: PropTypes.any.isRequired,
  };

  function a11yProps(index) {
    return {
      id: `simple-tab-${index}`,
      'aria-controls': `simple-tabpanel-${index}`,
    };
  }

  const [value, setValue] = React.useState(0);

  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  const renderTimeline = (item, index, type) => {



    if (item.status == 'achieved') {
      return <TimelineItem className='milestones-timeline-item milestone-achieved'>
        <TimelineSeparator>
          <TimelineDot color='inherit' variant='default' className='timeline-separator-dot'>
            <img src='/images/icon-first-milestone.svg' />
          </TimelineDot>
          <TimelineConnector className='timeline-separator-connector' />
        </TimelineSeparator>
        <TimelineContent className='timeline-item-content'>
          {type == 'milestone' && <div className='milestone-achieved-content'>
            <p className='label'>{item.CustomName}</p>
            <p className='value'>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</p>
          </div>}
          {type == 'setgoal' && item.Type == "CUSTOM" && <div className='milestone-locked-content'>
            <div>
              <p className='label'>{item.CustomName} <span>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</span> </p>
            </div>
          </div>}

        </TimelineContent>
      </TimelineItem>
    }

    if (item.status == 'pending') {
      return <TimelineItem className={'milestones-timeline-item milestone-locked'}>
        <TimelineSeparator>
          <TimelineDot color="primary" variant="outlined" className='timeline-separator-dot'>
            <img src='/images/icon-second-milestone.svg' />
          </TimelineDot>
          <TimelineConnector className='timeline-separator-connector' />
        </TimelineSeparator>
        <TimelineContent className='timeline-item-content'>
          {type == 'milestone' && <div className='milestone-locked-content'>
            {item.exclamation && <img src='/images/icon-exclamation.svg' />}
            <div>
              <p className='label'>{item.CustomName} <span>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</span> </p>
              <p className='value'>Req. Run Rate <span>₹ {item.RequiredRunRate && Math.round(item.RequiredRunRate / 1000).toLocaleString('en-IN')}K APE/day</span></p>
            </div>
          </div>}
          {type == 'setgoal' && item.Type == "CUSTOM" && <div className='milestone-locked-content'>
            <div>
              <p className='label'>{item.CustomName} <span>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</span> </p>
              <p className='value'>Req. Run Rate <span>₹ {item.RequiredRunRate && Math.round(item.RequiredRunRate / 1000).toLocaleString('en-IN')}K APE/day</span></p>
            </div>
          </div>}

        </TimelineContent>
      </TimelineItem>
    }


    if (item.status == 'here') {
      return <TimelineItem className='milestones-timeline-item milestone-location'>
        <TimelineOppositeContent className='timeline-item-content'>
          <div className='milestone-location-content'>
            <img src='/images/icon-location.svg' />
            <div>
              <p className='label'>Incentive</p>
              <p className='value'>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</p>
            </div>
          </div>
        </TimelineOppositeContent>
        <TimelineSeparator>
          <TimelineDot color="primary" className='timeline-separator-dot'>
          </TimelineDot>
          <TimelineConnector className='timeline-separator-connector' />
        </TimelineSeparator>
        <TimelineContent className='timeline-item-content'>
          <div>
            <h4>You're Here</h4>
          </div>
        </TimelineContent>
      </TimelineItem>
    }

    if (item.status == 'end') {
      return <TimelineItem className='milestones-timeline-item milestone-locked'>
        <TimelineSeparator>
          <TimelineDot color="secondary" className='timeline-separator-dot'>
            <img src='/images/icon-third-milestone.svg' />
          </TimelineDot>
        </TimelineSeparator>
        <TimelineContent className='timeline-item-content'>
          {type == 'milestone' && <div className='milestone-locked-content'>
            {item.exclamation && <img src='/images/icon-exclamation.svg' />}
            <div>
              <p className='label'>{item.CustomName} <span>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</span> </p>
              <p className='value'>Req. Run Rate <span>₹ {item.RequiredRunRate && Math.round(item.RequiredRunRate / 1000).toLocaleString('en-IN')}K APE/day</span></p>
            </div>
          </div>}
          {type == 'setgoal' && item.Type == "CUSTOM" && <div className='milestone-locked-content'>
            <div>
              <p className='label'>{item.CustomName} <span>₹ {item.TargetAmount && Math.round(item.TargetAmount).toLocaleString('en-IN')}</span> </p>
              <p className='value'>Req. Run Rate <span>₹ {item.RequiredRunRate && Math.round(item.RequiredRunRate / 1000).toLocaleString('en-IN')}K APE/day</span></p>
            </div>
          </div>}

        </TimelineContent>
      </TimelineItem>
    }



  }

  var getRemainingDays = function () {
    var date = new Date();
    var time = new Date(date.getTime());
    time.setMonth(date.getMonth() + 1);
    time.setDate(0);
    return (time.getDate() > date.getDate() ? time.getDate() - (date.getDate() - 1) : 0);
  }

  return (
    <div className="incentive_cal">
      <h6>
        Your Progress {IsCurrentMonth}
        {/* <span>Calculate your incentives for {moment().format('MMM YYYY')}</span> */}
        <span>See current position/set goals/claim rewards</span>
      </h6>
      <Card className="card"  >
        <CardContent>
          {!showTimeLine && <img src='/images/TimelineClosed.svg' />}

          {showTimeLine && <div className='incentive-calculator-section'>


            {IsCurrentMonth && <AppBar position="static"><Tabs value={value} onChange={handleChange} className='tabs'>
              <Tab label="Milestones" {...a11yProps(0)} className='tab' />
              <Tab label="Set Goals" {...a11yProps(1)} className='tab' />
            </Tabs></AppBar>}

            <TabPanel value={value} index={0} className='tab-panel'>
              <div className='tab-panel-content'>
                <Timeline align="alternate" className='milestones-timeline'>
                  {mileStones.map((item, index) => { return renderTimeline(item, index, "milestone"); })}
                </Timeline>
                {currentMileStone && IsCurrentMonth &&
                  <div className='disclaimer'>
                    <div>
                      <img src='/images/icon-exclamation.svg' />
                      <div>
                        <p className='label'>Run Rate Required <span>(for next Goal)</span> </p>
                        <p className='description'>You need to hit the Run rate of <span>₹ {currentMileStone && currentMileStone.RequiredRunRate && Math.round(currentMileStone.RequiredRunRate / 1000).toLocaleString('en-IN')}K /day</span> for next {getRemainingDays()} days to achieve this target</p>
                      </div>
                    </div>
                  </div>
                }
              </div>
            </TabPanel>
            <TabPanel value={value} index={1} className='tab-panel'>
              <div className='tab-panel-content'>
                <Timeline align="alternate" className='milestones-timeline' onClick={() => setOpen(true)}>
                  {mileStones.map((item, index) => { return renderTimeline(item, index, "setgoal"); })}


                </Timeline>
                <div className='note'>
                  <p>Click anywhere on the timeline to set a custom goal</p>
                </div>
              </div>
            </TabPanel>
          </div>
          }
          <Dialog onClose={handleClose} fullWidth={true}
            open={open} aria-labelledby="max-width-dialog-title" className="set-goal-popup-wrapper">
            <DialogContent className="set-goal-popup-content">
              <DialogContentText className="set-goal-popup-content-text">
                <Grid container spacing={3} className="set-goal-container">
                  <Grid item md={5} xs={12} className="set-goal-form">
                    <p>Set your Goal</p>
                    <div className="form">
                      <div className="form-group">
                        <TextField label="Enter Amount" fullWidth
                          label="Enter Goal Name"
                          className="form-control"
                          value={goalName}
                          onChange={(e) => { setGoalName(e.target.value) }}
                          placeholder="Enter Goal Name"
                          InputLabelProps={{
                            shrink: true,
                          }} />
                      </div>
                      <div className="form-group">
                        <TextField label="Enter Amount" fullWidth
                          label="Incentive Amount"
                          className="form-control"
                          value={desiredAmount}
                          onChange={(e) => { setDesiredAmount(e.target.value) }}
                          placeholder="Enter Amount"
                          InputLabelProps={{
                            shrink: true,
                          }} />
                      </div>
                    </div>
                  </Grid>
                  <Grid item md={7} xs={12} className="set-goal-details">
                    {showRunRate && IsCurrentMonth && <div className="required-run-rate-wrapper">
                      <div className="required-run-rate-content">
                        <img src="/images/icon-growth.svg" />
                        <div>
                          <p className="heading">Run Rate Required</p>
                          <p className="description">You need to hit the Run rate of &nbsp;
                          <span>₹{calResult && calResult.RunRate && Math.round(calResult.RunRate / 1000).toLocaleString('en-IN') || 0}K APE/day</span> &nbsp;
                           for next {getRemainingDays()} days to achieve this target</p>
                        </div>
                      </div>
                    </div>}
                    <div className="required-details-wrapper">
                      <p>You Need To Have</p>
                      <div className="required-details-content">
                        <div className="details">
                          <p className="heading">Weighted APE</p>
                          <p className="description">{calResult && calResult.WeightedAPE && calResult.WeightedAPE.toLocaleString('en-IN')}</p>
                        </div>
                        <div className="details">
                          <p className="heading">APE (Per Booking)</p>
                          <p className="description">{calResult && calResult.AvgWeightedAPEPerBKGS && calResult.AvgWeightedAPEPerBKGS.toLocaleString('en-IN')}</p>
                        </div>
                        <div className="details">
                          <p className="heading">Bookings</p>
                          <p className="description">{calResult && calResult.NoOfBKGS}</p>
                        </div>
                      </div>
                    </div>
                    <div className="set-goal-as-wrapper">
                      <p className="label">Amount</p>
                      <p className="goal">₹ {calResult && calResult.DesiredAmount && calResult.DesiredAmount.toLocaleString('en-IN')}
                        {amountChanged && <span>₹{desiredAmount.toLocaleString('en-IN')}</span>}
                      </p>
                    </div>
                  </Grid>
                  <Grid item md={12} xs={12} className="set-goal-buttons">
                    {amountChanged && <p className="disclaimer">Your incentive amount has been auto adjusted to best possible slab</p>}
                    {alreadyHaveMilestone && <p className="disclaimer">Milestone already exist.</p>}
                    {goalNameValidation && <p className="disclaimer">Give name to your goal.</p>}
                    <div className="buttons">
                      <Button onClick={calculateIncentive} variant="contained" className="set-goal">Calculate</Button>
                      <Button onClick={setGoal} variant={showRunRate ? "contained" : "outline"} className={showRunRate ? "set-goal" : "cancel"}>Set Goal</Button>
                      <Button onClick={handleClose} variant="outline" className="cancel">Close</Button>
                    </div>
                  </Grid>
                </Grid>
              </DialogContentText></DialogContent>
          </Dialog>

        </CardContent>
      </Card>
    </div>
  );
};

export default IncentiveCalculator;
