import React,{useState, useEffect, useContext} from "react";
import { GetCommonData } from "../../../common/CommonAction";
import {rupeeConverter} from '../Utility/Utility';
import { DashboardContext } from "../Context/Context";
import { getUserDetails } from "../Utility/Utility";

const IncentiveCalculationPopup=()=>{


    const dashboardData = useContext(DashboardContext);

  

    const [incentiveCriteria, setIncentiveCriteria]= useState(dashboardData.dashboardData['IncentiveCalculationPopup']);

    let rupee = rupeeConverter();

    useEffect(()=>{
        if(!incentiveCriteria)
        {
   
        let body={
            eCode: dashboardData.agentDetails && dashboardData.agentDetails.EmpId,
            monthYear:  dashboardData.monthChosen,
            userid: getUserDetails('UserId')
        }
        // let body={
        //     eCode: "PW00000" || dashboardData.agentDetails.EmpId,
        //     monthYear: "NOV2023" || dashboardData.monthChosen
        // }
        let data = { EndPoint: "bookingIncentive", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "term", body: JSON.stringify(body) };
        GetCommonData(
            "POST", data
            , (errorStatus, data) => {
                if (!errorStatus) {
                  
                    setIncentiveCriteria(data);
                    dashboardData.setDashboardData(data,'IncentiveCalculationPopup');
                }
                else{
                    setIncentiveCriteria(null);
                    dashboardData.setDashboardData(null, 'IncentiveCalculationPopup');
                }
            });
        }
        
    },[])

    return(
        <>
       { incentiveCriteria &&
            <>
            <h4>Booking Incentive to be paid if CJ justification
                incentive is less than booking incentive.</h4>
            <ul class="incentiveCalculation Caption">
                <li>Rules</li>
                <li>Booking</li>
            </ul>
            { incentiveCriteria['minBookings'] &&
            <div class="white">
                <ul class="incentiveCalculation twoGrid"><li>{incentiveCriteria['minBookings']['title'] || 'Minimum Bookings Qualifier'}
                </li><li>{incentiveCriteria['minBookings']['value'] || '-'}</li></ul>
            </div>
            }
            {incentiveCriteria['incPerBooking'] &&
            <div class="gray">
                <ul class="incentiveCalculation twoGrid"><li>{incentiveCriteria['incPerBooking']['title'] || 'Incentive Per Bookings' }
                </li><li>{rupee.format(incentiveCriteria['incPerBooking']['value']) || '-'}</li></ul>
            </div>
            }
            {incentiveCriteria['totalPayoutForMonth'] &&
            <div className="bookingIncentive">{incentiveCriteria['totalPayoutForMonth']['title'] || 'Booking Incentive'} =  {incentiveCriteria['bookingFormula'] || '-'} = {rupee.format(incentiveCriteria['totalPayoutForMonth']['value'] ) || '-' }</div>
            }
            </>
       }
       </>
      
    )
}
export default IncentiveCalculationPopup;