import React from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
  TextField,
  TextareaAutosize,
  Select,
  FormControl,
  InputLabel,
  MenuItem,
  Dialog,
  DialogActions,
  DialogContent,
  DialogContentText,
  DialogTitle,
} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import FormLabel from "@material-ui/core/FormLabel";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
  popupBox: {
    width: "300px",
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  formControl: {
    margin: theme.spacing(1),
    minWidth: "360px",
    maxWidth: "100%",
    border: "1px solid #ccc",
    borderRadius: "5px",
    padding: "0 8px",
    margin: "10px 0",
    "&:hover": {
      outline: "none",
      boxShadow: "none",
    },
  },
  DialogTitle: {
    padding: "15px 0 10px",
  },

  textRight: {
    alignItems: "center",
    font: "Regular 16px/21px Roboto",
    letterSpacing: "0px",
    color: "#303030",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  updateBtn: {
    color: "#0052CC",
    fontWeight: "600",
  },
  closeBtn: {
    color: "#808080",
  },
  formControl: {
    border: "1px solid #ccc",
    borderRadius: "5px",
    position: "relative",
    margin: "10px 0",
    width: "100%",
    "&:hover": {
      outline: "none",
      boxShadow: "none",
    },
    "& textArea": {
      fontSize: "12px",
      padding: "8px",
      border: "1px solid #ccc",
    },
    "& select": {
      padding: "0 8px",
    },
    "& input": {
      padding: "6px",
    },
  },
  questionTxt: {
    position: "absolute",
    top: "-30px",
    fontSize: "12px",
    padding: "0 8px",
    background: "#fff",
    left: "-1px",
  },
  reasonLabel: {
    position: "absolute",
    top: "-7px",
    fontSize: "14px",
    padding: "0 8px",
    background: "#fff",
  },
}));

const Transition = React.forwardRef(function Transition(props, ref) {
  return <Slide direction="up" ref={ref} {...props} />;
});

export default function AlertDialogSlide() {
  const classes = useStyles();
  const [open, setOpen] = React.useState(false);

  const handleClickOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };
  const handleChange = (event, newValue) => {
    setValue(newValue);
  };

  return (
    <div>
      <Grid container spacing={2}>
        <Grid item xs={12}>
          <Button color="primary" onClick={handleClickOpen}>
            Submit
          </Button>
        </Grid>
      </Grid>
      <Dialog
        open={open}
        onClose={handleClose}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        fullWidth={true}
        maxWidth={"xs"}
      >
        <DialogContent>
          <DialogContentText id="alert-dialog-description">
            <DialogTitle className={classes.textCenter} id="alert-dialog-title">
              {"Add New Question"}
            </DialogTitle>
            <Grid container spacing={3} className="booking-question">
              <Grid item xs={7}>
                <p>
                  <span>BPW11001 (TEST TERM AGENT) 5/24/2020 8:59:24 PM</span>
                </p>
              </Grid>
              <Grid item xs={5} className="text-right">
                <strong>Booking ID</strong>
                <span>153823948</span>
              </Grid>
            </Grid>
            <FormControl fullWidth className={classes.formControl}>
              <InputLabel
                className={classes.questionTxt}
                id="demo-simple-select-label"
              >
                Question
              </InputLabel>
              <TextareaAutosize
                aria-label="minimum height"
                rowsMin={6}
                placeholder="Enter your question here…"
              />
            </FormControl>
            <FormControl fullWidth className={classes.formControl}>
              <InputLabel
                className={classes.reasonLabel}
                id="demo-simple-select-label"
              >
                Source
              </InputLabel>
              <Select
                labelId="demo-simple-select-label"
                id="demo-simple-select"
                onChange={handleChange}
              >
                <MenuItem value={0}>Source</MenuItem>
                <MenuItem value={1}>Source</MenuItem>
                <MenuItem value={2}>Source</MenuItem>
              </Select>
            </FormControl>
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Grid container>
            <Grid item xs={4}></Grid>
            <Grid item md={8} container>
              <Grid item xs container>
                <Grid item xs={6}>
                  <Button color="primary" className={classes.closeBtn}>
                    Update
                  </Button>
                </Grid>
                <Grid item xs={6}>
                  <Button
                    onClick={handleClose}
                    color="primary"
                    className={classes.updateBtn}
                  >
                    Add Question
                  </Button>
                </Grid>
              </Grid>
            </Grid>
          </Grid>
          {/* <Grid container spacing={4}>
            <Grid item xs={6}>
              <FormControl>
                <Button color="primary" className={classes.updateBtn}>
                  Update
                </Button>
              </FormControl>
            </Grid>
            <Grid item xs={6}>
              <FormControl>
                <Button
                  onClick={handleClose}
                  color="primary"
                  className={classes.updateBtn}
                >
                  Add Question
                </Button>
              </FormControl>
            </Grid>
          </Grid> */}
        </DialogActions>
      </Dialog>
    </div>
  );
}
