import React, { useEffect, useState } from "react";
import {
  Dialog
} from "@material-ui/core";


// import '../../../assets/jag2022/scss/_growthslab.scss';
// import '../../../assets/jag2022/scss/common/_common.scss';

const BusinessHealthRatingPopup = (props) => {

  const [IsBusinessRatingPopupOpen, setIsBusinessRatingPopupOpen] = useState(false);

  const handleClose = () => {
    setIsBusinessRatingPopupOpen(false)
  };

  useEffect(() => {
    const ProductId = parseInt(props.agentDetails && props.agentDetails.ProductId) || 0;
    const InforceRating = parseInt(props.agentDetails && props.agentDetails.inforce) || 100;
    if (ProductId == 115 && InforceRating <= 55) {
      setIsBusinessRatingPopupOpen(true);
    }
  }, [props]);

  return (
    <>
      <Dialog onClose={handleClose} className="BuniessHealthRatingPOPup" aria-labelledby="simple-dialog-title" open={IsBusinessRatingPopupOpen}>
        <h2>Importance Notice</h2>
        <h3>
          Dear {props.agentDetails.UserName || 'Advisor'},<br /><br />
          Your <span>Current Business Health Rating is less than 55% </span>, to be eligible for JAG 22-23, you need to be above 55%.
          <br /><br />
          Please improve your rating asap to become part of the esteemed JAG contest.</h3>
      </Dialog>
    </>

  );
};

export default BusinessHealthRatingPopup;