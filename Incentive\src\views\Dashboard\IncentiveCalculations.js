import React, { useEffect, useState, Fragment } from "react";
import { withStyles, makeStyles } from "@material-ui/styles";
import * as services from "../../services";
import Skeleton from "@material-ui/lab/Skeleton";
import _ from "lodash";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
} from "@material-ui/core";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';
import Tooltip from '@material-ui/core/Tooltip';


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(0),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  content: {
    paddingTop: 150,
    textAlign: "center",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#fff",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    height: "70px",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#303030",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
    "& svg": {
      background: " #c8dcfa 0% 0% no-repeat padding-box",
      borderRadius: "50%",
      padding: "8px",
      width: "35px",
      height: "35px",
      color: "#0052cc",
    },
    "& ul": {
      display: "table",
      width: "100%",
      "& li": {
        display: "table-cell",
        width: "auto",
      },
    },
  },
  expandIcon: {
    background: "#00398e",
    color: "#fff",
    borderRadius: "50%",
  },
  warningBtn: {
    background: "#c8dcfa",
    borderRadius: "17px",
    margin: "5px 0",
    fontSize: "12px",
    color: "#0052cc",
    "&:hover": {
      background: "#c8dcfa",
    },
  },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);


const IncentiveCalculations = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [incentiveCalData, setIncentiveCalData] = useState([]);
  const response = JSON.parse(localStorage.getItem('user'));


  useEffect(() => {
    setIncentiveCalData([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/GetAgentIncProjection/${userId}/${date}`).then(response => {
        if (response && response.length > 0 && response != "[]") {
          response = _.orderBy(response, ['SequenceNo'], ['asc'])
          setIncentiveCalData(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [userId,date]);

  const renderPayoutIncentiveTooltip = (data) => {

    return <div className="common-tooltip-popup">
      <span>(As per the Issuance till {moment(data.IncentiveDate).format('Do MMMM')})</span>
    </div>
  }

  return (
    <div className={classes.root} className="incentive">
      <h6>
        Incentive Calculations
        {<span>Projections as per current performance</span>}
      </h6>
      <Card className={classes.card}>
        <ul className="incentive-box">
          {incentiveCalData && incentiveCalData.length > 0 && incentiveCalData.map((data, index) =>
            (data.Key == 'Level Achieved' && response.ProductId == 115) ? null :
            (data.Type && data.Type == "currency") ? (
              <li key={index}>
                <span>{data.Key || "-"}</span>
                <strong><i className="fa fa-inr"></i> {data.Value ? parseFloat(data.Value).toLocaleString('en-IN') : "0.00"}
                  {(data.Key == "Projected Incentive" || data.Key == "Incentive Made") ? (
                    <WrapperTooltip
                      disableFocusListener
                      TransitionComponent={Zoom}
                      placement="top"
                      arrow
                      title={renderPayoutIncentiveTooltip(data)}
                      classes={{ tooltip: classes.customWidth }}
                    >
                      <i className="fa fa-info-circle"></i>
                    </WrapperTooltip>

                  ) : null}
                </strong>
              </li>
            ) : (
                <li key={index}>
                  <span>{data.Key || "-"}</span>
                  <strong> {data.Value || "0"} </strong>
                </li>
              )
          )}
        </ul>
      </Card>
    </div>
  );
};

export default IncentiveCalculations;
