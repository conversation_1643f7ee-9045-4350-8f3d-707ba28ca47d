import React, { useEffect, useState, Fragment } from "react";

import { useParams } from "react-router";
import STORAGE from '../../store/storage'
import * as services from "../../services";
import { getCookie } from "../../utils/utility";
//import * as actions from './store/actions';




export default function Main(props) {

  const urlParams = useParams();

  //const [isLoading, setIsLoading] = useState(false);  
  const [userId, setUserId] = useState(null);
  const [AgentData, setAgentData] = useState(null);

  //const [userDetail, setUserDetail] = useState({});
  useEffect(() => {

    //const tokenPath = window.location.pathname.split('/');
    //if (tokenPath.length >= 3) {
    //Set Token Using URL parameter
    let AgentId = getCookie("AgentId");

    

    let urlToken = urlParams.Token;
    let Source = urlParams.Source;

    if (!urlToken) {      
      if(!AgentId){
        window.location.href = "https://matrix.policybazaar.com/pgv/";
        return;
      }
      else{
        urlToken = window.btoa(AgentId);
      }
    }    

    STORAGE.setAuthToken(urlToken);
    services
      .API_GET(`Jag/GetVisibleMenus/${urlToken}`).then(response => {
        if (response) {

          if (response.Status) {

            let obj = JSON.parse(response.Response)[0];
            localStorage.setItem("menu", JSON.stringify(obj));
            //{Incentive: false, JAG: true}
            if (obj.JAG) {
              window.location.href = "../../jag/" + urlToken + "/" + Source;
            }
            // else if (obj.Incentive) {
            //   window.location.href = "../../dashboard/" + urlToken + "/" + Source;
            // }
            else if (obj.Incentive === true && obj.JAG === false) {
              //window.location.href = "../../jag/" + urlToken + "/" + Source;
              window.location.href = "../../dashboard/" + urlToken + "/" + Source;
            }
            else {
              window.location.href = "../../home/" + urlToken + "/" + Source;
            }

          } else {
            localStorage.setItem("menu", JSON.stringify({}));
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
    //}
  }, []);



  return (
    <div className="wrapper">
      Redirecting to page...
    </div>
  );
}
