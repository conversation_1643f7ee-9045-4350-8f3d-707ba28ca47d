import React, { useState, useContext, useEffect } from "react";
import { Grid, Tooltip } from "@mui/material";
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown';
import { DashboardContext } from "../Context/Context";
import { GetCommonData } from "../../../common/CommonAction";
import {rupeeConverter, getUserDetails, roundOffToTwo} from '../Utility/Utility';

const IncentiveCalculation = () => {

    const popupHandle = useContext(DashboardContext);

    const [toggles, setToggles] = useState([]);
    const [aggrCalculation, setAggrCalculation] = useState(null);

    
    let rupee = rupeeConverter();

    useEffect(() => {
        let body={
            eCode: popupHandle.agentDetails && popupHandle.agentDetails.EmpId,
            monthYear:  popupHandle.monthChosen,
            userid: getUserDetails('UserId')
        }
        // let body={
        //     eCode: "PW00000" || popupHandle.agentDetails.EmpId,
        //     monthYear: "NOV2023" || popupHandle.monthChosen,
        //     userid: 8223
        // }
        let data = { EndPoint: "aggrCalculation", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "term", body: JSON.stringify(body) };
        GetCommonData(
            "POST", data
            , (errorStatus, data) => {
                if (!errorStatus) {
                  
                    setAggrCalculation(data);
                }
                else{
                    setAggrCalculation(null)
                }
            })
       
    }, [popupHandle.monthChosen])


    useEffect(()=>{
        if(aggrCalculation)
        {
            popupHandle.setprocessdetails(aggrCalculation.process, aggrCalculation.tenure)
        }

    },[aggrCalculation])

    const handleClick = (e) => {
        popupHandle.handlePopupClick(e);
    }

    const handleToggle = (id) => {
        if (toggles.includes(id)) {
            let tempToggles = toggles.filter((key) => key != id)
            setToggles(tempToggles);
        }
        else {
            setToggles([...toggles, id]);
        }
    }

    return (
        <>
            <Grid item md={4} sm={4} xs={12}>
                <div className="box1">
                    <h4>Incentive Calculation</h4>
                    <h3 className="title">CJ<hr /></h3>

                    <div className="white">
                        <ul className="incentiveCalculation Caption">
                            <li>Rules
                            </li>
                            <li>Booking</li>
                            <li>APE</li>
                        </ul>
                    </div>
                    {
                        aggrCalculation &&
                        <>

                            {aggrCalculation['totalSourcing'] &&
                                <div className="gray">
                                    <ul className="incentiveCalculation">
                                        <li>{aggrCalculation['totalSourcing']['title'] || 'Total Sourcing'} <ArrowDropDownIcon style={{cursor:'pointer'}} onClick={() => handleToggle(1)} />
                                        </li>
                                        <li>{aggrCalculation['totalSourcing']['booking'] || '-'}</li>
                                        <li>{rupee.format(aggrCalculation['totalSourcing']['ape'] ) || '-'}</li>
                                    </ul>
                                    {toggles.includes(1) && <>
                                        {
                                            aggrCalculation['primarySourcing'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['primarySourcing']['title'] || 'Primary Sourcing'}
                                                </li>
                                                <li>{aggrCalculation['primarySourcing']['booking'] || '-'}</li>
                                                <li>{rupee.format(aggrCalculation['primarySourcing']['ape']) || '-'}</li>
                                            </ul>
                                        }
                                        {
                                            aggrCalculation['secondarySourcing'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['secondarySourcing']['title'] || 'Secondary FOS Sourcing'}
                                                </li>
                                                <li>{aggrCalculation['secondarySourcing']['booking'] || '-'}</li>
                                                <li>{rupee.format(aggrCalculation['secondarySourcing']['ape']) || '-' }</li>
                                            </ul>
                                        }
                                    </>

                                    }

                                </div>
                            }

                            {
                                aggrCalculation['totalIssued'] &&
                                <div className="white">
                                    <ul className="incentiveCalculation">
                                        <li> {aggrCalculation['totalIssued']['title'] || 'Total Issued'}<ArrowDropDownIcon style={{cursor:'pointer'}} onClick={() => handleToggle(2)} />
                                        </li>
                                        <li> {aggrCalculation['totalIssued']['booking'] || '-'}</li>
                                        <li>{rupee.format(aggrCalculation['totalIssued']['ape']) || '-' }</li>
                                    </ul>
                                    {toggles.includes(2) && <>
                                        {aggrCalculation['primaryIssued'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['primaryIssued']['title'] || 'Primary Issued'}
                                                </li>
                                                <li>{aggrCalculation['primaryIssued']['booking'] || '-'}</li>
                                                <li>{rupee.format(aggrCalculation['primaryIssued']['ape'] ) || '-'}</li>
                                            </ul>
                                        }
                                        {aggrCalculation['secondaryIssued'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['secondaryIssued']['title'] || 'Secondary FOS Issued'}
                                                </li>
                                                <li>{aggrCalculation['secondaryIssued']['booking'] || '-'}</li>
                                                <li>{rupee.format(aggrCalculation['secondaryIssued']['ape']) || '-' }</li>
                                            </ul>
                                        }
                                    </>

                                    }
                                </div>
                            }
                            {
                                aggrCalculation['issuancePercentage'] &&
                                <div className="gray">
                                    <ul className="incentiveCalculation">
                                        <li>{aggrCalculation['issuancePercentage']['title'] || '% Issuance'}
                                        </li>

                                        <li>{(roundOffToTwo(aggrCalculation['issuancePercentage']['value'] * 100) + '%') || '-'}</li>
                                    </ul>
                                </div>
                            }
                            {
                                aggrCalculation['totalWeighted'] &&
                                <div className="white twoGrid">
                                    <ul className="incentiveCalculation">
                                        <li>{aggrCalculation['totalWeighted']['title'] || 'Total Weighted APE'}<ArrowDropDownIcon style={{cursor:"pointer"}} id={3} onClick={() => handleToggle(3)} />
                                        </li>
                                        <li>{rupee.format(aggrCalculation['totalWeighted']['value']) || '-'}</li>
                                    </ul>
                                    {toggles.includes(3) && <>
                                        {
                                            aggrCalculation['primaryWeighted'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['primaryWeighted']['title'] || 'Primary Weighted APE'}
                                                </li>
                                                <li>{rupee.format(aggrCalculation['primaryWeighted']['value']) || '-' }</li>
                                            </ul>
                                        }
                                        {
                                            aggrCalculation['secondaryWeighted'] &&
                                            <ul className="DropdownToogle">
                                                <li>{aggrCalculation['secondaryWeighted']['title'] || 'Secondary FOS Weighted APE'}
                                                </li>
                                                <li>{rupee.format(aggrCalculation['secondaryWeighted']['value']) || '-'}</li>
                                            </ul>
                                        }
                                    </>
                                    }
                                </div>
                            }
                            <div className="borderLine"></div>
                            {
                                aggrCalculation['slabPercentage'] &&
                                <div className="white">
                                    <ul className="incentiveCalculation">
                                        <li>{aggrCalculation['slabPercentage']['title'] || '% Slab based on WT.APE'}
                                        </li>
                                        <li>{(roundOffToTwo(aggrCalculation['slabPercentage']['value'] * 100) + '%') || '-'}</li>
                                    </ul>
                                </div>
                            }
                            {aggrCalculation['cjIncentive'] &&
                                <div className="gray">
                                    <ul className="incentiveCalculation twoGrid">
                                        <li>{aggrCalculation['cjIncentive']['title'] || 'Cost Justification Incentive'}  <Tooltip title="[Total Weightage X (%slab)]-salary" arrow
                                            placement="bottom"
                                        >
                                            <i className="fa fa-info-circle"></i>
                                        </Tooltip>
                                        </li>

                                        <li>{aggrCalculation['cjIncentive']['value']>0? rupee.format(aggrCalculation['cjIncentive']['value']) : rupee.format(0)  }</li>
                                    </ul>
                                </div>
                            }
                            {
                                aggrCalculation['bookingIncentive'] &&
                                <>
                                    <h3 className="title">BOOKING  <hr /></h3>

                                    <div className="gray">
                                        <ul className="incentiveCalculation twoGrid">
                                            <li>{aggrCalculation['bookingIncentive']['title'] || 'Booking Incentive'} &nbsp; {aggrCalculation['bookingIncentive']['value']!=0 && <a id="SeeCal1" onClick={handleClick}>(See Calculation)</a>}
                                            </li>
                                            <li>{rupee.format(aggrCalculation['bookingIncentive']['value']) || '-'}</li>
                                            
                                        </ul>
                                    </div>
                                </>
                            }
                        </>
                    }
                </div>
            </Grid>
        </>
    )
}

export default IncentiveCalculation;