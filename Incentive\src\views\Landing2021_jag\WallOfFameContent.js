import React, { useEffect } from "react";
import { makeStyles } from "@material-ui/styles";
import {
    Grid,
} from "@material-ui/core";

import * as services from "../../../services";
import { getCookie, setCookie } from "../../../utils/utility";

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(0),
    },
    margin: {
        margin: theme.spacing(1),
    },
    extendedIcon: {
        marginRight: theme.spacing(1),
    },
    forBox: {
        width: "100%",
    },
    card: {
        background: "#ffffff",
        boxShadow: "0px 0px 26px #0000001a",
        borderRadius: "20px",
    },

    cardBody: {
        padding: "15px 15px 0 !important",
    },
}));

export default function WallOfFameContent(props) {
    const { agentDetails, contextId } = props;
    const [open, setOpen] = React.useState(false);
    const [wallOfFame, setWallOfFame] = React.useState([]);
    const [count, setCount] = React.useState(0);

    const getDetail = () => {

        if (!agentDetails && !contextId) {
            return;
        }

        services
            .API_GET(`Jag/GetWallOfFame/${agentDetails.BUId}/${agentDetails.MemberType}`)
            .then(response => {
                if (response && response != "[]") {

                    if (response.Status) {
                        let data = JSON.parse(response.Response);
                        let wof=JSON.parse(data[1]);
                        wof.sort(function(a, b) { 
                            return b.LotteryTicket-a.LotteryTicket;
                        })
                        if (data.length > 0 && parseInt(data[0]) > 5) {
                            setCount(parseInt(data[0]));
                            setWallOfFame(wof);
                            setOpen(true)
                        }
                    }
                }
            })
            .catch((err) => {
                console.log("Error", err);
            });

    }



    useEffect(() => {
        if (agentDetails, contextId) {
            getDetail();
        }

    }, [agentDetails, contextId]);



    return (
        <div className="wallofFamePopup">
            <div className="wallofFamecontent">
                <Grid container spacing={2}>
                    <Grid item xs={4} sm={4} md={4}>
                        <img src="/images/JAGLogo.png" className="jaglogo" />
                    </Grid>
                    <Grid item xs={4} sm={4} md={4}>
                        <div className="wofLogo">
                            <img src="/images/woflogo.png" />
                            <span> {count} agents are going to make lottery tickets in your BU,
                  stay tuned for updates...</span>
                        </div>
                    </Grid>
                    <Grid item xs={4} sm={4} md={4} className="text-right">
                        {/* <img src="/images/pblogo.svg" className="pblogo" /> */}
                        <img src="/images/Emergingstarlogo.png" className="pblogo" />
                    </Grid>
                </Grid>
                <Grid container spacing={2}>
                    <Grid item xs={2} sm={2} md={2} className="text-center">
                        {agentDetails.MemberType.toLowerCase()=='gold' || agentDetails.MemberType.toLowerCase()=='silver' ?  <img src="/images/Pole.svg" style={{ height: "280px" }} />:<img src="/images/pole3.svg" style={{ height: "280px" }} />}
                        {/* <img src="/images/Pole.svg" style={{ height: "280px" }} />
                        <img src="/images/pole3.svg" style={{ height: "280px" }} /> */}
                        </Grid>
                    <Grid item xs={8} sm={8} md={8} >
                        <div id="scrollbar" className="scrollWOF">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Employee ID</th>
                                        <th>Lottery Ticket</th>
                                        <th>{agentDetails.MemberType.toLowerCase()=='gold' || agentDetails.MemberType.toLowerCase()=='silver' ? 'GOLD/SILVER':'WARRIORS/HUSTLERS'}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        wallOfFame && wallOfFame.length > 0 && wallOfFame.map((item, index) =>
                                            <tr className={item.MemberType.toLowerCase()}>
                                                <td>{item.UserName}</td>
                                                <td>{item.EmployeeId}</td>
                                                <td>{item.LotteryTicket}</td>
                                                <td>
                                                {item.MemberType.toLowerCase() == 'warriors' ?
                                                      <img src="/images/Warrior.svg" /> :  <>{item.MemberType.toLowerCase()=='hustlers' ?<img src="/images/Hustler.svg" />:null }</> }

                                                    {item.MemberType.toLowerCase() == 'silver'?
                                                     <img src="/images/btn_silver.svg" /> : <>{item.MemberType.toLowerCase()=='gold' ?<img src="/images/btn_gold.svg" />:null }</> }
                                                    
                                                </td>
                                            </tr>
                                        )}
                                </tbody>
                            </table>
                        </div>
                    </Grid>
                    <Grid item xs={2} sm={2} md={2} className="text-center">
                    {agentDetails.MemberType.toLowerCase()=='gold' || agentDetails.MemberType.toLowerCase()=='silver' ?  <img src="/images/Pole.svg" style={{ height: "280px" }} />:<img src="/images/pole4.svg" style={{ height: "280px" }} />}
                        {/* <img src="/images/Pole.svg" style={{ height: "280px" }} />
                        <img src="/images/pole4.svg" style={{ height: "280px" }} /> */}
                        </Grid>
                    <Grid item xs={12} sm={12} md={12}><div className="text-center radbg">
                        <img src="/images/bg.svg" />
                    </div></Grid>
                </Grid>
            </div>
        </div>
    );
};


