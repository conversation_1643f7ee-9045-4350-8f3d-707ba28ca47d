import React, { useEffect, useState, useMemo } from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../Loader";
import Tooltip from '@material-ui/core/Tooltip';
import DataTable from 'react-data-table-component';
import 'react-data-table-component-extensions/dist/index.css';
import exportFromJSON from 'export-from-json';

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },

  table: {
    border: "2px solid #4e93ff",
    margin: "10px auto",
  },
  tblCell: {
    "&.MuiTableCell-body": {
      color: "#0164ff"
    },
  },
  bold: {
    fontWeight: "700",
    color: "#000000 !important"
  },
  underline: {
    fontDecoration: "underline"
  },
  blueRow: {
    background: "#e5efff"
  },
  memberType: {
    textTransform: "capitalize"
  },
  redText: {
    color: "red"
  }
});
const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    color: "#0164ff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);
const HEALTH = 2;
const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});


const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);


  const BookingDumpData = withStyles(styles)((props) => {
      const containerStyle = useMemo(() => ({ width: '100%', height: '100%' }), []);
      const gridStyle = useMemo(() => ({ height: '100%', width: '100%' }), []);
      const {show, handleClose, classes, AvailableData,userId, date, productId} = props;
      const [isLoading, setIsLoading] = useState(true);
      const [bookingList, setBookingList] = useState([]);
      


      useEffect(() => {
      setBookingList([]);
      if (!userId) {
        return;
      }
      debugger;

      services
        .API_GET(`JAG/GetUserBookingLevelData/${userId}/${productId}`)
        .then(response => {
          if (response.Status && response.Response != "[]" && response.Response.length > 0) {
            setBookingList(JSON.parse(response.Response));
          }
        })
        .catch((err) => {
          console.log("Error", err);
        });
    }, [userId, productId]);

    

  



    

    

    const ShowEligibleCol = ()=>{    
 
      var d1 = new Date(date);
      var d2 = new Date("01-11-2021");
      if(d1>= d2){
        return true;
      }
      return false;
  
    }


      const columnsSetter = (AvailableData) => {
        
        let columns = []
        // const columns = [
        //   { key: 'id', name: 'ID' },
        //   { key: 'title', name: 'Title' }
        // ];

        switch(AvailableData.BU){
          case "Term Retainer":
            columns = [
              {  selector: 'sno', name: "S.No", width: "60px"  },
              {  selector: 'Booking_Date',  name: "Booking Date", width: "100px"},
              {  selector: 'BookingLeadID'  , name: "Lead ID", width: "100px"},
              {  selector: 'BookingStatus'  , name: "Booking Status"},
              {  selector: 'APE'  , name: "APE", width: "60px"},
              {  selector: 'IsEligible'  , name: "IsEligible" , width: "80px"},
              {  selector: 'Remark', name: "Remark" }
            ];
          break;
          case "Term":
            columns = [
              {  selector: 'sno', name: "S.No", width: "60px"  },
              {  selector: 'Booking_Date',  name: "Booking Date", width: "100px"},
              {  selector: 'BookingLeadID'  , name: "Lead ID", width: "100px"},
              {  selector: 'BookingStatus'  , name: "Booking Status"},
              {  selector: 'APE'  , name: "APE", width: "100px"},
              {  selector: 'IsEligible'  , name: "IsEligible" , width: "80px"},
              {  selector: 'Remark', name: "Remark" }
            ];
          break;
          case "Health Renewal":
            columns = [
              { selector: 'sno', name: 'S.No'  },
              { selector: 'Booking_Date', name: 'Booking Date' },
              { selector: 'BookingLeadID', name: 'BookingLeadID' },
              { selector: 'isAddOn', name: 'IsAddOn' },
              { selector: 'BookingStatus', name: 'Booking Status' },
              {  selector: 'IsNop', name: "BookingCount"},
              { selector: 'APE', name: 'APE', width: "100px" },
              { selector: 'IsEligible', name: 'IsEligible' },
              { selector: 'Remark', name: 'Remark' }
            ];
          break;
          case "Health":
            columns = [
              { selector: 'sno', name: 'S.No'  },
              { selector: 'Booking_Date', name: 'Booking Date' },
              { selector: 'BookingLeadID', name: 'BookingLeadID' },
              { selector: 'isAddOn', name: 'IsAddOn' },
              { selector: 'BookingStatus', name: 'Booking Status' },
              {  selector: 'IsNop', name: "BookingCount"},
              { selector: 'APE', name: 'APE', width: "100px" },
              { selector: 'IsEligible', name: 'IsEligible' },
              { selector: 'Remark', name: 'Remark' }
            ];
          break;
          case "Motor Renewal":
            columns = [
              { selector: 'sno', name: 'S.No' },
              { selector: 'Booking_Date', name: 'Booking Date' },
              { selector: 'BookingLeadID', name: 'BookingLeadID' },
              { selector: 'BookingStatus', name: 'Booking Status' },
              { selector: 'IsEligible', name: 'IsEligible' },
              { selector: 'Remark', name: 'Remark' }
            ];
          break;
          case "Motor":
            columns = [
              { selector: 'sno', name: 'S.No' },
              { selector: 'Booking_Date', name: 'Booking Date' },
              { selector: 'BookingLeadID', name: 'BookingLeadID' },
              { selector: 'BookingStatus', name: 'Booking Status' },
              { selector: 'IsEligible', name: 'IsEligible' },
              { selector: 'Remark', name: 'Remark' }
            ];
          break;
          case  "Investment NRI" :
            columns = [
              { selector: 'sno', name: "S.No" },
              { selector: 'Booking_Date', name: "Booking Date" },
              { selector: 'BookingLeadID', name: "BookingLeadID" },
              { selector: 'BookingStatus', name: "Booking Status" },
              {  selector: 'IsNop', name: "BookingCount"},
              { selector: 'IsCombo', name: "IsCombo" },
              { selector: 'APE', name: "APE", width: "100px" },
              { selector: 'IsEligible', name: "IsEligible" },
              { selector: 'Remark', name: "Remark" }
            ];
          break;
          case  "Investment Dom" :
            columns = [
              { selector: 'sno', name: "S.No" },
              { selector: 'Booking_Date', name: "Booking Date" },
              { selector: 'BookingLeadID', name: "BookingLeadID" },
              { selector: 'BookingStatus', name: "Booking Status" },
              {  selector: 'IsNop', name: "BookingCount"},
              { selector: 'IsCombo', name: "IsCombo" },
              { selector: 'APE', name: "APE", width: "100px" },
              { selector: 'IsEligible', name: "IsEligible" },
              { selector: 'Remark', name: "Remark" }
            ];
          break;
          case  "Investment Trad" :
            columns = [
              { selector: 'sno', name: "S.No" },
              { selector: 'Booking_Date', name: "Booking Date" },
              { selector: 'BookingLeadID', name: "BookingLeadID" },
              { selector: 'BookingStatus', name: "Booking Status" },
              {  selector: 'IsNop', name: "BookingCount"},
              { selector: 'IsCombo', name: "IsCombo" },
              { selector: 'APE', name: "APE", width: "100px" },
              { selector: 'IsEligible', name: "IsEligible" },
              { selector: 'Remark', name: "Remark" }
            ];
          break;
          default: columns = []            
        }
        return columns
      }

      const dataCleaner = (bookingList) =>{
        let res = []
        bookingList.map((item, index) => {
          res.push({
            sno: index+1,
            Booking_Date: item.Booking_Date ? (new Intl.DateTimeFormat("en-AU", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            }).format(
              Date.parse(item.Booking_Date || new Date())
            )) : "-" ,
            BookingLeadID: item.BookingLeadID,

            isAddOn: item.isAddOn == 1? 'Yes': 'No',

            IsCombo: item.IsCombo == 1? 'Yes': 'No',

            IsNop: item.IsNop,

            BookingStatus: item.StatusName || '-',

            APE: item.APE ? Math.round(item.APE).toLocaleString('en-IN') : 0,

            IsEligible: item.IsEligible == 1? 'Yes' : 'No',

            Remark: item.Remark,

            })
          
        })





        return res
      }

     

      const handleBookingsExport = () => {
        const fileName = userId + "_bookings";
       
        const data = bookingList.map((item) => {
          let booking = {
            LeadId : item.BookingLeadID,
            BookingDate : item.Booking_Date ? (new Intl.DateTimeFormat("en-AU", {
              day: "2-digit",
              month: "short",
              year: "numeric",
            }).format(
              Date.parse(item.Booking_Date || new Date())
            )) : "-" ,
            
            BookingStatus : item.StatusName
          }

          if([115,2].indexOf(productId) != -1){
            booking.BookingCount = item.IsNop
          }
          if(productId == 115){
            booking.IsCombo = item.IsCombo == 1? 'Yes': 'No'
          }
      
        
          if(productId !== 117) {
            booking.APE = item.APE ? Math.round(parseFloat(item.APE)) : 0;
            
            if(productId === HEALTH){
              booking.IsAddOn = item.isAddOn == 1 ? 'Yes' : 'No';
              booking.IsEligible = item.IsEligible == 1 ? 'Yes' : 'No';
              
            }
          } else {
            
            booking.IsEligible =  item.IsEligible == 1 ? 'Yes' : 'No';
            
          }
          
          
            booking.IsEligible =  item.IsEligible == 1 ? 'Yes' : 'No';
            booking.Remark = item.Remark;
          
    
          return booking;
        });
        const exportType =  exportFromJSON.types.xls;
        exportFromJSON({ data, fileName, exportType });
      }
      useEffect (() =>{
          setIsLoading(false)
      },[show, handleClose, classes, AvailableData]);

     console.log("book", bookingList[0])
    

      
      
    return(
        


      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"md"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
          <strong> Booking Breakdown - FY 21-22 </strong>
        </DialogTitle>

        <div style={gridStyle} >
        <DataTable noHeader = {true} columns={columnsSetter(AvailableData)} data= {dataCleaner(bookingList)} pagination paginationPerPage = {7}  paginationComponentOptions={{ noRowsPerPage: true }}/>
            </div>

            <Button 
                onClick = {handleBookingsExport}
                
              className="downloadBtn" >
                Download
              </Button>
        
        </Dialog>
        
    )

  });

  export default BookingDumpData;



  
