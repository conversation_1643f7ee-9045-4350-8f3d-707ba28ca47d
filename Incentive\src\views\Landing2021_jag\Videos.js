import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import { makeStyles } from '@material-ui/core/styles';
import {
  Grid,
} from "@material-ui/core";

import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
// import ReactPlayer from 'react-player'


export default function Videos(props) {


  var slider_explainer = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: true,
          dots: true
        }
      },
      {
        breakpoint: 768,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1
        }
      }
    ]
  };


  return (


    <Grid item md={12} className="explainer_video">
      <h5>Explainer Video</h5>

      <Card className="card">
        <CardContent>
          <Slider {...slider_explainer}>
            <div>
              <div className="card image">
                <h4>Health Renewal</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Health+Renewal_12072020.mp4' /> */}


              </div>

            </div>

            <div>
              <div className="card image">
                <h4>Health</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Health.mp4' /> */}


              </div>
            </div>

            <div>
              <div className="card image">
                <h4>Investment Retainer</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Invest+Retainer_22072020.mp4' /> */}


              </div>
            </div>

            <div>
              <div className="card image">
                <h4>Investment</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Invest_22072020.mp4' /> */}


              </div>
            </div>
            <div>
              <div className="card image">
                <h4>Motor Renewal</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Motor+Renewal_12072020.mp4' /> */}


              </div>
            </div>
            <div>
              <div className="card image">
                <h4>Motor</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Motor_12072020.mp4' /> */}


              </div>
            </div>
            <div>
              <div className="card image">
                <h4>Term Retainer</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Term+Retainers_15072020.mp4' /> */}


              </div>
            </div>
            <div>
              <div className="card image">
                <h4>Term</h4>
                {/* <ReactPlayer
                  width='100%'
                  height='100%'
                  controls={true}
                  url='https://policystatic.policybazaar.com/JAG/media/videos/Term_15072020.mp4' /> */}


              </div>
            </div>


          </Slider>
        </CardContent>
      </Card>
    </Grid>

  );
}
