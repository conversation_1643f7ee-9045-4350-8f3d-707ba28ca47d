import React, { useEffect, useState, Fragment } from "react";
import Accordion from "@material-ui/core/Accordion";
import AccordionDetails from "@material-ui/core/AccordionDetails";
import AccordionSummary from "@material-ui/core/AccordionSummary";
import Typography from "@material-ui/core/Typography";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { makeStyles } from "@material-ui/core/styles";
import { Grid } from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },

  iconButton: {
    padding: 8,
  },

  flexGrow: 1,
  backgroundColor: theme.palette.background.paper,

  paper: {
    position: "absolute",
    width: 1059,
    backgroundColor: theme.palette.background.paper,
    border: "2px solid #000",
    boxShadow: theme.shadows[5],
    padding: theme.spacing(2, 4, 3),
  },
}));

const FAQs = (props) => {
  const classes = useStyles();
  const [expanded, setExpanded] = React.useState(false);
  const [readMore, setReadMore] = React.useState(false);

  const linkName = readMore ? 'Show Less' : 'Show More';

  const handleChange = (panel) => (event, isExpanded) => {
    setExpanded(isExpanded ? panel : false);
  };
  const extraContent = (
    <div >
      <Accordion
        expanded={expanded === "panel1c2"}
        onChange={handleChange("panel1c2")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1c2bh-content"
          id="panel1c2bh-header"
        >
          <Typography className={classes.heading}>
          What are my chances to win a flat? What can I do to increase my chances of winning?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>he chances for you to win a flat will be directly proportional to your performance. <br/>Greater APE More Lottery Tickets  greater probability to win</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ca"}
        onChange={handleChange("panel1ca")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cabh-content"
          id="panel1cabh-header"
        >
          <Typography className={classes.heading}>
          How will the final winners for the flat be selected? How transparent will the selection process be?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          Winners will be selected by a lucky draw on lottery tickets LIVE
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cb"}
        onChange={handleChange("panel1cb")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cbbh-content"
          id="panel1cbbh-header"
        >
          <Typography className={classes.heading}>
          If I am a silver Agent, what will be the minimum percentage of growth in order to be eligible for the house?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          You need to grow at least by the lowest slab shared in your individual communication. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cc"}
        onChange={handleChange("panel1cc")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1ccbh-content"
          id="panel1ccbh-header"
        >
          <Typography className={classes.heading}>
          Will there be a lucky draw for Cash rewards also? Will I get to choose between the Cash prize or lottery Tickets for Flat?

          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            There will be no lucky draw for cash rewards (it is a definite payout depending on your eligibility). You will be entitled for both cash rewards as well as lottery tickets for the flat based on your performance.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cd"}
        onChange={handleChange("panel1cd")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cdbh-content"
          id="panel1cdbh-header"
        >
          <Typography className={classes.heading}>
            Will the entire APE gets nullified if policy gets cancelled in Free
            Look period?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>No, you cannot take cash instead of flat.</Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ce"}
        onChange={handleChange("panel1ce")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cebh-content"
          id="panel1cebh-header"
        >
          <Typography className={classes.heading}>           
 What about monthly incentives that were paid as per our performance?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            You will be eligible for all the other performance based incentives/contests.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cf"}
        onChange={handleChange("panel1cf")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cfbh-content"
          id="panel1cfbh-header"
        >
          <Typography className={classes.heading}>
          How can I keep a track of my monthly progress in the program? How transparent will the complete process be over the year?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          You can track your progress in Matrix, wherein you will be able to see your current year’s progress vs last year’s performance. {" "}
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cg"}
        onChange={handleChange("panel1cg")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>
            My process alignment was wrongly mapped, will my correct incentive
            get paid in next month?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
            Please raise it to your supervisor/RM. If your process was
            incorrectly mapped S/he will take approval from Sales head to get it
            changed, subsequently the pending amount will be paid as Arrears in
            succeeding month.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ch"}
        onChange={handleChange("panel1ch")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>
          What if I do not perform well for some unknown reasons? Will I be replaced by somebody?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          No, you will not be replaced. <br/>
You will still be a privileged agent and will be entitled to various perks throughout the year based on the category (Gold/Silver) you fall in. <br/>
But you will not be eligible for the cash prize or the lottery for the house. 

          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ci"}
        onChange={handleChange("panel1ci")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>
          Will an agent get the updates over the performance in the matrix such that he/she can keep a track of his/her growth?

          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          Yes, the star agents will soon have a section in the matrix. This will cover details related to their performance as well as insights on the competition and possibilities of receiving the rewards to push their performance further. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cj"}
        onChange={handleChange("panel1cj")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
        What if a star agent gets promoted during the FY 20-21? Will he/she be eligible for the rewards? 
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          In case the agent gets promoted, he/she will have an option to take the promotion and keep working as an agent. If the agent moves to a promoted role and is not able to do APE as per growth targets, he/she will not be eligible for rewards.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ck"}
        onChange={handleChange("panel1ck")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          When will the winners be declared?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The winners will be declared in June.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cl"}
        onChange={handleChange("panel1cl")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          What is the duration of the contest?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The contest is from April’21 to March’22.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cm"}
        onChange={handleChange("panel1cm")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Will the Flat and the cash rewards be taxable or exempt?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          Both the flat and the cash reward will come under taxable income. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cn"}
        onChange={handleChange("panel1cn")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          If the APE is higher but the booking is not growing as per the criteria. Will I be eligible for the contest?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          No, you have to grow in terms of APE as well as bookings to be eligible for the house and the cash rewards. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1co"}
        onChange={handleChange("panel1co")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Are the calculations based on Issued or Sourced business?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          All the calculations (growth in APE and bookings) will be based on Issued and not sourced business. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cp"}
        onChange={handleChange("panel1cp")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Is there any difference in cash rewards for gold and silver agents?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The prize money based on points that you gather as per the growth in performance. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cq"}
        onChange={handleChange("panel1cq")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Will the Silver agent get anything or the rewards are only for the Gold category?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The silver agents will also be eligible for the Flat and the cash rewards as per their growth. And are also eligible for a few other privileges.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cr"}
        onChange={handleChange("panel1cr")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          If any agent is eligible for lottery tickets for a flat, will he be also eligible for cash rewards?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          Yes, the qualified agents are eligible for not only the lottery tickets for flat but also the cash rewards.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cs"}
        onChange={handleChange("panel1cs")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Is there any chance to win 2 flats also?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          No, one agent can win just one flat. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1ct"}
        onChange={handleChange("panel1ct")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Is the cash reward for all the qualifiers? 
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The cash reward is for those star agents who grow both in terms of APE as well as bookings as per their set targets in the FY 20-21 vs the FY 21-22. 
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cu"}
        onChange={handleChange("panel1cu")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          For an issued booking, will the complete APE be considered under the performance of FY 20-21 or any deductions will be applicable (like SI dampener, Quality score deductions, etc.)?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          Issued bookings will be considered with no deductions an APE for calculating the final performance of FY 20-21.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cv"}
        onChange={handleChange("panel1cv")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          Does the winning agent have to pay the cost of registration for the flat or will it be given by the company?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The winner will have to bear the cost of registration for the flat.
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1cw"}
        onChange={handleChange("panel1cw")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cgbh-content"
          id="panel1cgbh-header"
        >
          <Typography className={classes.heading}>          
          How will the cash reward be calculated?
                   </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          The cash reward will be based on the growth slab that you hit. 
You can refer to the growth and points table shared for your BU to understand it further.

          </Typography>
        </AccordionDetails>
      </Accordion>
    
    </div>
  );

  return (


    <Grid container spacing={3}  className="FaqSection" id="faqSection">
    <Grid item md={6} xs={12}>
     <img src="/images/faqbanner.png"/>
     
    </Grid>
    <Grid item md={6} xs={12}>
      <h2>
      Frequently Asked  <b>Questions</b> {/*  <a className="viewall">View All</a> */}
      </h2>
   {/* <p className="textMsg">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore</p> */}
   <div className="scrollBar">
      <Accordion
        expanded={expanded === "panel1"}
        onChange={handleChange("panel1")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bh-content"
          id="panel1bh-header"
        >
          <Typography className={classes.heading}>
          How does one qualify in the contest?
          </Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography className="faq-ans">
          The qualifiers will be selected on the basis of their last year's performance. Moreover, the agent has to be with us for at least 8 months. <br/>
We have used the prorated data for FY 20-21 performance and matched it with the set criteria for gold/silver. If the agent has achieved the target slab last year, he/she will qualify for the contest.

          </Typography>
        </AccordionDetails>
      </Accordion>

      <Accordion
        expanded={expanded === "panel1b"}
        onChange={handleChange("panel1b")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bbh-content"
          id="panel1bbh-header"
        >
          <Typography className={classes.heading}>
          I am a gold agent, if my APE (in FY 20-21) grows by 15% vs last year, will I qualify for the house and other perks?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          You will get all the privileges around the year but you will not be eligible for the annual cash reward or the lottery tickets for the house as you do not meet the criteria of minimum growth (%).
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1bb"}
        onChange={handleChange("panel1bb")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1bbbh-content"
          id="panel1bbbh-header"
        >
          <Typography className={classes.heading}>
          What will I get if I am not lucky enough to win a house with my lottery tickets?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          If you are a Gold/ Silver Agent and you don't win a house, you will still be entitled for cash rewards based on your performance along with other privileges.{" "}
          </Typography>
        </AccordionDetails>
      </Accordion>
      <Accordion
        expanded={expanded === "panel1c"}
        onChange={handleChange("panel1c")}
        className="faqTab"
      >
        <AccordionSummary
          expandIcon={<ExpandMoreIcon />}
          aria-controls="panel1cbh-content"
          id="panel1cbh-header"
         
        >
          <Typography className={classes.heading}>
          What are the privileges the gold/silver agents are eligible for?
          </Typography>
          <Typography className={classes.secondaryHeading}></Typography>
        </AccordionSummary>
        <AccordionDetails>
          <Typography>
          1. Annual Bonus<br/>
          2. Lottery for Flat<br/>
          3. Customized Goodies<br/>
          4.  PB Sathi (SPOC)<br/>
          5. Feature on Pb Wall of Fame & Monthly Newsletter


          </Typography>
        </AccordionDetails>
      </Accordion>
      {readMore && extraContent}
      <a
        className="read-more-link"
        onClick={() => {
          setReadMore(!readMore);
        }}
      >
       <div className="text-center"><button>{linkName}</button></div> 
      </a>
    </div>

  
    </Grid>
</Grid>

    
  );
};

export default FAQs;
