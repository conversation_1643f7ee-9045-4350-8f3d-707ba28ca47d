header {
    box-shadow: 0px 3px 6px #00000029;
    background-color: #fff;

    @media only screen and (min-width: 360px) and (max-width:767px) {
        background: #F4F6F8 !important;
        box-shadow: none !important;

    }
}

.tabLayout {
    position: absolute;
    z-index: 999;
    left: 0;
    right: 0;
    max-width: 405px;
    width: 100%;
    margin: auto;

    @media screen and(max-width:767px) {
        max-width: 100%;
    }

    .MuiTab-textColorInherit {
        background: #edf0f4 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        font: normal normal normal 14px/19px Roboto;
        letter-spacing: 0px;
        color: #778397;
        text-transform: capitalize;
        padding: 0px 20px;
        overflow: unset;
        margin-top: 10px;

        &:last-child {
            &::after {
                content: "";
                background: url(/images/incentive/star.svg) no-repeat right;
                width: 30px;
                height: 30px;
                animation: blink 1s linear infinite;
                position: absolute;
                top: -17px;
                right: 15px;
            }

            @keyframes blink {
                0% {
                    opacity: 0.3;
                }

                50% {
                    opacity: 0.5;
                }

                100% {
                    opacity: 1;
                }
            }
        }

        @media(max-width:767px) {
            background: #D8D8D8;
            font-size: 12.5px;
            color: #25385899;
            padding: 0px 12px 0px 20px;
            max-width: 175px;
            width: 100%
        }
    }

    .MuiTabs-fixed {
        overflow: unset;
    }

    .Mui-selected {
        background: #ffffff;
        box-shadow: 0px 6px 16px #3469cb29;
        border-radius: 8px;
        opacity: 1;
        color: #0065ff;

        &:last-child {
            &::after {
                display: none;
            }
        }

        @media(max-width:767px) {
            max-width: 158px;
            width: 100%;
            font-size: 12.5px;
            padding: 0;
            margin-left: 1px
        }
    }

    .MuiTabs-flexContainer {
        margin-bottom: 20px;

        @media(max-width:767px) {
            justify-content: center;

        }
    }
}

.incentiveCriteraPopup {
    float: right;
    text-align: center;
    font: normal normal 600 14px/19px Roboto;
    letter-spacing: 0px;
    color: #0065ff;
    opacity: 1;
    margin-bottom: 20px;
    margin-right: 0px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;

    @media screen and(max-width:767px) {
        float: none !important;
    }
}

.CriteraPopup {
    z-index: 99999 !important;
}
.CriteraSidebar{
    .MuiDrawer-paper{
        width:89%;
    }
}
.alignLeft {
    float: left;

    @media screen and(max-width:767px) {
        margin-bottom: 10px;
    }
}

/* ranking-box */
.ranking-data {
    box-shadow: none;
    position: relative;
    top: -12px;

    @media(max-width: 767px) {
        top: -18px
    }

    .rank-box {
        position: relative;
        background: transparent url(/images/incentive/ranking-block-bg.svg) no-repeat top right;
        border-radius: 18px;
        padding: 90px 0 0 3px;
        z-index: 99;
        left: 12px;
        top: 0px;
        display: flex;
        align-items: flex-start;
        justify-content: space-between;

        @media(max-width: 767px) {
            padding: 0;
            top: 0px;
            background: transparent !important;
            margin: 90px auto 0px;
        }

        .rank-box-left-section {
            width: 63%;

            @media(max-width: 767px) {
                width: 100%;
            }

            .name-description {
                @media(max-width: 767px) {
                    margin: 13px 0px 20px;
                    position: relative;
                }

                strong {
                    text-align: left;
                    font: normal normal bold 32px/40px Merriweather;
                    letter-spacing: 0px;
                    color: #00458b;
                    text-transform: capitalize;
                    opacity: 1;

                    @media(max-width: 767px) {
                        font: normal normal bold 21px/25px Merriweather;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        width: 220px;
                    }
                }

                p {
                    text-align: left;
                    font: normal normal 600 12px/19px Roboto;
                    letter-spacing: 0px;
                    color: #00458b;
                    opacity: 1;
                    margin-top: 8px;

                    @media(max-width: 767px) {
                        margin-top: 5px;
                        font-size: 14px;
                        font-weight: 500;
                    }
                }

                .month-view {
                    padding: 8px;
                    width: 120px;
                    background: #ffffff 0% 0% no-repeat padding-box;
                    box-shadow: 0px 6px 16px #3469cb29;
                    border-radius: 4px;
                    opacity: 1;
                    height: 32px;
                    margin: 22px 0px;
                    display: inline-block;
                    text-align: center;

                    @media(max-width: 767px) {
                        position: absolute;
                        right: 24px;
                        top: 0;
                        margin: auto;
                        display: block;
                        height: 34px;
                        padding: 10px 0px 10px 6px;
                        width: 104px;
                        height: 34px;
                        text-align: left;
                    }

                    select {
                        border: transparent;
                        opacity: 1;
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #253858;

                        @media(max-width: 767px) {
                            width: 95%;
                        }
                    }
                }
            }

            .highlights {
                h4 {
                    text-align: left;
                    font: normal normal bold 16px/24px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    margin-bottom: 5px;
                }

                .slick-slider {
                    .slick-slide {
                        .highlights-description {
                            font: normal normal normal 12px/24px Roboto;
                            letter-spacing: 0px;
                            color: #253858;
                        }
                    }
                }

                .slick-dots {
                    li {
                        margin: 0 10px 0px 0px !important;
                    }
                }
            }
        }

        .rank-box-right-section {
            width: 37%;
            height: auto;

            @media(max-width: 767px) {
                width: 100%;
            }

            ul {
                margin-top: 78px;

                @media(max-width: 767px) {
                    margin: 0px
                }

                li {
                    width: 215px;
                    background: #ffffff 0% 0% no-repeat padding-box;
                    box-shadow: 0px 6px 16px #3469cb29;
                    border-radius: 8px;
                    list-style: none;
                    position: relative;
                    padding: 10px 10px 10px 65px;
                    margin-bottom: 17px;
                    right: 24px;

                    @media(max-width: 767px) {
                        width: 93% !important;
                        right: 0 !important;
                        top: 0 !important;
                        text-align: left;
                        display: flex;
                        align-items: center;
                        padding: 10px 11px 10px 66px;
                        justify-content: space-between;
                        height: 66px
                    }

                    &::before {
                        content: "";
                        background: url(/images/incentive/icon_rank.svg);
                        position: absolute;
                        height: 42px;
                        width: 42px;
                        left: 15px;
                        top: 50%;
                        transform: translateY(-50%);
                    }

                    &:first-child {
                        right: 71px;
                        top: -5px;
                    }

                    &:last-child {
                        @media(max-width: 767px) {
                            margin-bottom: 5px;
                        }

                        // &::before {
                        //     background: url(/images/incentive/Slab.svg) no-repeat right;
                        //     // @media(max-width: 767px) {
                        //     //   background: url(/images/incentive/Slab.svg) no-repeat right!important;
                        //     // }                             
                        // }
                    }

                    @media(max-width: 767px) {
                        span.label {
                            font-size: 14px;
                            color: #253858;
                            line-height: 1.9;
                            font-weight: 600;
                            margin-bottom: 0;
                        }

                        .label h4 {
                            font-size: 10.4px;
                            font-weight: 400;
                            line-height: 18px;

                        }
                    }
                }
            }

            .background-caricrature {
                background: url(/images/incentive/ranking-block-right-caricrature.svg) no-repeat right top;
                position: absolute;
                right: 137px;
                top: 20px;
                width: 100px;
                height: 240px;
            }
        }
    }
}

/*End ranking-box */

/*Help Icon  css*/
.UserHelp {
    position: fixed;
    bottom: 15px;
    z-index: 99;
    right: 40px;
}

/*End */

.PayoutSection {
    padding: 0px !important;
    position: relative;
    overflow-x: hidden;

    .mainBox {
        width: 100%;
        justify-content: space-between;
        position: relative;

        .bgImage {
            background: url(/images/incentive/bg2.svg) no-repeat right top;
            position: absolute;
            left: -19px;
            top: -137px;
            width: 600px;
            height: 586px;
        }

        /*leader-board */
        .leader-board {
            margin: 0px 10px;
            position: relative;

            .lader-ranking {
                min-height: 328px;

                h6 {
                    font: normal normal 600 16px/21px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;

                    span {
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #808080;
                        opacity: 1;
                    }
                }

                div {
                    &.active {
                        background: #030303 0% 0% no-repeat padding-box;
                        border-radius: 8px;
                        opacity: 1;
                        color: #ffffff;

                        strong {
                            color: #fff !important;
                        }
                    }
                }

                li {
                    display: inline-block;
                    width: 50%;
                    font: normal normal normal 12px/16px Roboto;
                    letter-spacing: 0px;
                    opacity: 1;

                    strong {
                        font: normal normal 600 12px/16px Roboto;
                    }

                    span {
                        display: block !important;
                    }

                    &:nth-child(odd) strong {
                        color: #253858;
                    }
                }
            }
        }

        /* end leader-boader css*/
        /* incentivebox css*/
        .incentive {
            margin: 0px 10px;
            position: relative;

            .incentive-box {
                min-height: 328px;
                padding: 15px 15px 10px;

                h6 {
                    font: normal normal 600 16px/21px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    padding-left: 0px;

                    span {
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #808080;
                        opacity: 1;
                    }
                }

                li {
                    display: flex;
                    margin: 25px 0 10px;
                    letter-spacing: 0px;
                    justify-content: space-between;
                    width: 100%;

                    span {
                        text-align: left;
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #253858;
                        opacity: 1;
                    }

                    strong {
                        text-align: left;
                        font: normal normal 600 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #253858;
                        opacity: 1;

                        .fa-inr {
                            color: #253858;
                            font-size: 12px;
                        }

                        .fa-info-circle {
                            color: #808080;
                            font-size: 16px;
                            left: 3px;
                            position: relative;
                            top: 1px;
                        }
                    }
                }
            }

        }

        /* end incentive css*/
        /* payout css*/
        .payout {
            margin: 0px 10px;

            .payout-box {
                min-height: 328px;
                padding: 15px 15px 10px;

                h6 {
                    font: normal normal 600 16px/21px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    padding-left: 0px;

                    span {
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #808080;
                        opacity: 1;
                    }
                }

                li {
                    &.firstbox {
                        margin-bottom: 15px;
                        padding: 6px;

                        strong {
                            text-align: center;
                            font: normal normal bold 12px/16px Roboto;
                            letter-spacing: 0px;
                            color: #0f8d61;
                            opacity: 1;
                        }

                        span {
                            font: normal normal bold 18px/24px Roboto;
                            letter-spacing: 0px;
                            color: #0f8d61;
                            text-align: center;
                        }
                    }

                    span {
                        text-align: left;
                        font: normal normal normal 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #253858;
                        opacity: 1;

                        .warningStatus {
                            text-align: center;
                            padding-left: 0px;
                            background: #620bd9 0% 0% no-repeat padding-box;
                            border-radius: 4px;
                            font: normal normal bold 10px/13px Roboto;
                            letter-spacing: 0px;
                            color: #ffffff;
                            text-transform: uppercase;
                            opacity: 1;
                            margin-top: 2px;
                        }
                    }

                    p {
                        font: normal normal bold 12px/16px Roboto;
                        letter-spacing: 0px;
                        color: #253858;
                    }
                }
            }
        }

        /* end Payout css*/
    }

    /*booking-table */
    .booking-table {
        margin: 20px 12px;

        @media screen and(max-width:767px) {
            margin: 12px 0px;
        }

        h6 {
            display: block;
            text-align: left;
            font: normal normal 600 16px/21px Roboto;
            letter-spacing: 0px;
            color: #253858;
            padding-left: 0px;
            opacity: 1;
            margin-bottom: 2px;
        }

        span {
            text-align: left;
            font: normal normal normal 12px/16px Roboto;
            letter-spacing: 0px;
            color: #808080;
            opacity: 1;
            margin-top: 3px;
        }

        ul.booking-table-head {
            background: #ffffff;
            box-shadow: 0px 3px 16px #80808014;
            border-radius: 8px;
            margin-top: 15px;

            @media screen and(max-width:767px) {
                display: none;
            }

            li {
                font: normal normal bold 12px/16px Roboto;
                letter-spacing: 0px;
                color: #808080;
                opacity: 1;
            }
        }

        ul {
            display: table;
            padding: 13px 15px;
            width: 100%;

            li {
                color: #273a59;
            }
        }
    }

    /*END booking-table css */

    .currentMonthSlab {
        display: none;
    }
}

/*start current month projection tab UI css */
//Current Trends, Opportunity, Daily Sourcing Required CSS
.errorfont {
    color: #f41c1c !important;
}

.CurrentMonthProjContainer {
    padding: 0 !important;

    .CurrentMonthData {
        background: #ffffff 0% 0% no-repeat padding-box;
        box-shadow: 0px 6px 16px #3469cb29;
        border-radius: 8px;
        opacity: 1;
        width: 100%;
        padding: 5px 20px 0px;
        margin-bottom: 3em;
        position: relative;
        min-height: 302px;

        @media screen and(max-width:767px) {
            width: 93%;
            min-height: auto;
            margin: 0px auto 20px;
            padding: 12px 15px 0px
        }

        h2 {
            text-align: left;
            font: normal normal 200 24px/36px Roboto;
            letter-spacing: 0px;
            color: #253858;
            padding: 12px 0px;

            @media screen and(max-width:767px) {
                font-size: 20px;
                padding: 5px 0px 0px;
                line-height: 1.4;
            }
        }

        .leftimage {
            position: absolute;
            bottom: 0;

            @media screen and(max-width:767px) {
                display: none;
            }
        }

        .caption {
            padding: 3px 20px 3px 15px;
            background: #f5f8ff 0% 0% no-repeat padding-box;
            border-radius: 8px;
            opacity: 1;
            margin: 15px 0px;
            font: italic normal normal 12px/48px Roboto;
            letter-spacing: 0px;
            color: #253858;
            display: flex;

            @media screen and(max-width:767px) {
                line-height: 17px;
                padding: 10px 9px;
                margin: 16px 0px 18px;

                b {
                    font-weight: 500;
                }
            }

            img {
                margin-right: 10px;
            }

            ul {
                margin: auto;

                li {
                    line-height: 20px;

                    @media screen and(max-width:767px) {
                        margin-bottom: 4px;
                        display: block;
                        width: 100%;
                        position: relative;
                        margin-top: 2px;

                        &:last-child {
                            margin: 0;
                        }

                        &:nth-child(3) {
                            padding: 0px 0px 0px 46px;
                        }

                        &::before {
                            position: absolute;
                            content: '';
                            top: 8px;
                            left: 38px;
                            margin: auto;
                            border: 2px solid #253858;
                            border-radius: 5px;
                        }
                    }
                }
            }
        }

        .note {
            background: #fffcf0 !important;

            @media screen and(max-width:767px) {
                display: block;
                padding: 9px 9px 6px;
            }

            img {
                @media screen and(max-width:767px) {
                    float: left;
                    margin: 0px 14px 80px 0px;
                }
            }
        }

        .error {
            background: #fbd6d6 !important;
        }

        .OpportunityNote {
            background: #f0fff7 !important;

            @media screen and(max-width:767px) {
                display: block;
                padding: 8px 9px 4px;
            }

            img {
                @media screen and(max-width:767px) {
                    float: left;
                    margin: 0px 14px 10px 0px;
                }
            }
        }

        table {
            tr {
                td {
                    text-align: left;
                    font: normal normal normal 12px/16px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    overflow-x: hidden;
                    padding: 6px 0px !important;

                    @media screen and(max-width:767px) {
                        opacity: .7;
                        line-height: 17px;
                        padding: 10px 5px 10px 0 !important;
                        width: 100%;
                        white-space: normal;
                        // span{display: block;}
                    }

                    &:last-child {
                        text-align: right;
                        font: normal normal 600 14px/24px Roboto;
                        letter-spacing: 0px;
                        color: #253858;

                        @media screen and(max-width:767px) {
                            opacity: 1;
                            padding: 0px !important;
                            //    line-height:36px;
                        }
                    }

                    // i.fa{display: inline-block!important;}

                    @media screen and(max-width:767px) {
                        i {
                            font-style: normal;
                        }
                    }
                }

                .highlight {
                    color: #00af51 !important;
                    font-weight: 600 !important;
                }
            }
        }
    }

    // END CSS
    /*feedback box css*/
    .feedbackBox {
        background: #e8effd 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        text-align: center;
        padding: 40px;

        @media screen and(max-width:767px) {
            padding: 30px;
            width: 100%;
            max-width: 93%;
            margin: auto;
        }

        p {
            color: #4d5c76;
            font: normal normal 600 18px/36px Roboto;
            margin-top: 20px;
        }

        .thumbIcon {
            background: #ffffff 0% 0% no-repeat padding-box;
            box-shadow: 0px 6px 16px #3469cb29;
            border-radius: 4px;
            opacity: 1;
            display: inline-flex;
            padding: 3px 15px;
            justify-content: space-evenly;
            width: 92px;
            align-items: center;
            margin: 15px 10px;
            cursor: pointer;
            color: #219117;
            font: normal normal 600 18px/36px Roboto;

            &:last-child {
                color: #ff4343;
            }

            svg {
                font-size: 16px;
            }
        }

        .feedbackIcon {
            color: #b9ceff;
            font-size: 52px;
        }
    }

    /*end css */
    /*verified css */
    .verifiedBox {
        background: #c3ffd0 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        text-align: center;
        padding: 40px;

        p {
            color: #4d5c76;
            font: normal normal 600 18px/36px Roboto;
            margin-top: 20px;
        }
    }

    /*end css */
    /*start dislike css */
    .dislike {
        background: #ffdbdb 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        text-align: center;
        padding: 40px;

        p {
            color: #4d5c76;
            font: normal normal 600 18px/36px Roboto;
            margin-top: 20px;
        }

        .thumbIcon {
            background: #ffffff 0% 0% no-repeat padding-box;
            box-shadow: 0px 6px 16px #3469cb29;
            border-radius: 4px;
            opacity: 1;
            display: inline-flex;
            padding: 3px 15px;
            justify-content: space-evenly;
            width: 92px;
            align-items: center;
            margin: 15px 10px;
            cursor: pointer;
            color: #219117;
            font: normal normal 600 18px/36px Roboto;

            &:nth-child(even) {
                color: #fff;
                background-color: #ff4343;
            }

            svg {
                font-size: 16px;
            }
        }

        .feedbackIcon {
            color: #f41c1c;
            font-size: 52px;
        }

        .radioBtn {
            flex-direction: row;
            margin-top: 20px;

            .MuiIconButton-colorSecondary {
                color: #5e6c84 !important;
            }

            .Mui-checked {
                color: #0065ff !important;
            }

            .MuiFormControlLabel-label {
                text-align: left;
                font: normal normal normal 16px/22px Roboto;
                letter-spacing: 0px;
                color: #253858;
                opacity: 1;
            }
        }
    }

    .currentMonthSlab {
        background: #fffdea;
        box-shadow: 0px 0px 16px #3469cb29;
        border-radius: 8px;
        opacity: 1;
        width: 390px;
        padding: 15px;

        @media(max-width:767px) {
            width: 93%;
            margin-bottom: 20px
        }

        h3 {
            text-align: left;
            font: normal normal 600 18px/24px roboto;
            letter-spacing: 0px;
            color: #ea9959;
            opacity: 1;
        }

        p {
            text-align: left;
            font: normal normal normal 12px/18px Roboto;
            letter-spacing: 0px;
            color: #253858;
            margin: 15px 0px 10px 0px;

            div {
                float: left;
                width: 30px;
                height: 20px;
                padding-top: 2px;

                @media screen and(max-width:767px) {
                    height: 32px;
                    width: 25px;
                }


            }
        }
    }

    .highlights {
        display: none;
    }

    .month-view {
        display: none !important;
    }
}

.how-it-works-section {
    padding: 15px 16px;
    background: transparent;
    align-items: inherit;

    @media screen and(max-width:767px) {
        align-items: center;
        padding: 15px 0px;
    }

    .text-center {
        text-align: center;
    }

    h3 {
        text-align: center;
        font: normal normal bold 24px/16px Roboto;
        letter-spacing: 4.08px;
        color: #253858;
        opacity: 1;
        margin-top: 50px;
        margin-bottom: 15px;

        @media screen and(max-width:767px) {
            margin: 18px 0px 15px
        }
    }

    p {
        text-align: left;
        font: normal normal normal 14px/24px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
        padding-left: 32px;
        padding-bottom: 10px;

        @media screen and(max-width:767px) {
            padding-bottom: 0px;
            padding-left: 0
        }
    }

    h4 {
        color: #0065ff;
        padding-left: 14px;
        font: normal normal 600 16px/24px Roboto;
        margin-bottom: 5px;
    }

    span {
        text-align: left;
        font: normal normal 600 18px/18px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 1;
    }

    img {
        text-align: center;
        margin: 0px 15px 0 0px;
    }

    .steps {
        padding: 0px 20px 20px;

        @media screen and(max-width:767px) {
            padding: 10px 0px;
            width: 100%;
        }

        .incentive-slab {
            // background: url(/images/incentive_slabs_background.svg) no-repeat center;
            // height: 183px;
            margin-top: 0px;

            .slab {
                text-align: center;

                p {
                    font-size: 10px;
                    color: #303030;
                    line-height: 16px;
                    padding: 13px 0;
                    text-align: center;
                    margin: 0;
                }

                &.slab-4 {
                    background: url(/images/slab-yellow-bg.svg) no-repeat center;
                }

                &.slab-3 {
                    background: url(/images/slab-blue-bg.svg) no-repeat center;
                }

                &.slab-2 {
                    background: url(/images/slab-green-bg.svg) no-repeat center;
                }

                &.slab-1 {
                    background: url(/images/slab-red-bg.svg) no-repeat center;
                }
            }
        }

        >ul {
            padding: 10px 0px 0;

            @media screen and(max-width:767px) {
                padding: 10px 10px 0;
            }

            .icon-warning {
                align-items: center;
            }

            .icon-files {
                align-items: center;
            }

            li {
                color: $primary-dark;
                font-size: 16px;
                line-height: 24px;
                list-style: none;
                margin-bottom: 15px;
                display: flex;
                align-items: baseline;
                justify-content: flex-start;

                &:last-child {
                    margin-bottom: 10px;
                }

                p {
                    width: 100%;
                    text-align: left;
                    font: normal normal normal 16px/24px Roboto;
                    letter-spacing: 0px;
                    color: #253858;
                    opacity: 1;
                    display: contents;

                }

                span {
                    font-size: 12px;
                    height: 48px;
                    padding: 20px;
                    width: 48px;
                    display: inline-flex;
                    align-items: CENTER;
                    justify-content: CENTER;
                    border-radius: 50%;
                    color: #fff;
                    margin-right: 12px;

                    &.pink {
                        background: #f3a191;
                    }

                    &.purple {
                        background: #868cf1;
                    }

                    &.blue {
                        background: #4d5c7e;
                    }
                }

                h5 {
                    padding-left: 59px;
                }

                .pera {
                    padding-left: 59px;
                    display: block;
                }
            }
        }

        div {
            padding: 0 0px;

            p {
                font-size: 14px;
                margin: 5px 0 0 27px;
                text-align: center;

            }
        }

        table {
            border-collapse: collapse;
            border-radius: 4px;
            margin-left: 90px;

            thead {
                tr {
                    background: #acc4ff;
                    border-radius: 0px;
                }
            }

            tbody {
                tr {
                    &:nth-child(2n + 2) {
                        background: #ededed;
                    }
                }
            }

            tr {

                th,
                td {
                    padding: 4px;
                    letter-spacing: 0px;
                    color: #253858;
                    font-size: 14px;
                    border-radius: 0;
                    border: 1px solid #dddddd;

                    &:last-child {
                        border-radius: 0;
                    }
                }
            }
        }

        &.step-3 {
            ul li {
                margin-bottom: 25px;

                img {
                    margin: 0 12px 0 0;
                }
            }
        }
    }
}

.IncentiveHighLight {
    color: rgb(0, 175, 81) !important;
    display: inline-block;
    font-weight: 700;
    // text-decoration: underline;
    font-size: 15px;
}

.motorTableCal {
    width: 100%;
    border-collapse: collapse;
  
    td {
        border: none;
        text-align: left;
        padding-left: 10px;
        
        &:last-child{
            text-align: right;
            width:14%;
        }
    }

    th {
        border: none;
        text-align: left;
        padding-left: 10px;
        padding: 7px;
        &:last-child {
            text-align: right;
            width:14%;
        }      
    }
    .areaWidth{
        width:195px;
    }
}
.AchievementBox{
    min-height: 328px;
    padding: 15px 15px 10px;
    .motorIncentive {
        width: 100%;
        margin-top:43px;
        td {
            text-align: right;
            width: 15%;
        }
    
        th {
            text-align: left;
            width: 15%;
        }
    }
    
}
.motorIncentive {
    width: 100%;   
    td,th {
        text-align: left;
        width: 15%;
    }
  
}

.crossButton {
    position: fixed;
    top: 10px;
    right: 10px;
    font-size: 22px;
}