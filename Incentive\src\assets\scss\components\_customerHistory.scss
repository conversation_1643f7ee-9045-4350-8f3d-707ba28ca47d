.customer-history {
  .card {
    border-radius: 20px;
    box-shadow: 0px 0px 26px #0000001a;
    .card-body {
      padding: 15px 15px 0;
    }
  }
  .custumer-heading {
    h6 {
      text-align: right;
      font-size: 18px;
      letter-spacing: 0px;
      color: #1f3b95;
      padding: 15px;
    }
  }
  ul {
    width: 100%;
    display: table;
    padding: 10px 0;
    position: relative;
    border-top: 1px dashed #d0c9c9;
    > span {
      top: -17px;
      left: 0;
      color: #1f3b95;
      content: "";
      display: inline-block;
      padding: 5px 13px 5px 0;
      position: absolute;
      font-size: 16px;
      letter-spacing: 0px;
      background-color: #fff;
    }
    li {
      width: 50%;
      display: inline-table;
      padding: 10px 0;
      font-size: 14px;
      div {
        margin: 10px 0;
      }
      p,
      span {
        font-size: 12px;
      }
      span {
        font: Regular 12px/15px Roboto;
        color: #1f3b95;
        font-size: 11px;
        font-weight: 500;
      }
      &:nth-child(1) p {
        color: #000000;
      }
      &:last-child {
        text-align: right;
        & > p {
          color: #1f3b95;
          font-size: 12px;
        }
      }
    }
  }
}

// Ticket View
.ticket-all {
  padding: 20px 10px;
  align-items: center;
}
.ticket {
  background-color: #ebebeb;
  padding: 9px;
  border-radius: 50%;
  height: 48px;
  width: 48px;
  display: block;
  line-height: 28px;
  margin: 8px 0 0 11px;
  & > img {
    margin: 4px;
  }
}
.ticket-view {
  position: relative;
  padding: 0 0 10px;
  font-size: 12px;
  & > span {
    font-size: 12px;
    color: #808080;
    & > img {
      margin: 4px;
    }
  }
  & > p {
    font-size: 14px;
    color: #253858;
  }
  .ticket-date {
    width: 100%;
    display: block;
    padding: 7px 15px 0;
    font-size: 9px !important;
    text-align: right;
  }
}
