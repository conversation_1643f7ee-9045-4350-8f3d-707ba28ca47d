import React, { useEffect } from "react";
import { makeStyles } from "@material-ui/styles";
import {
  Grid,
} from "@material-ui/core";

import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import * as services from "../../services";
import { getCookie, setCookie } from "../../utils/utility";
import WallOfFameContent from "./WallOfFameContent";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const WallOfFame = (props) => {
  const { userId, productId } = props;
  const [open, setOpen] = React.useState(false);
  const [wallOfFame, setWallOfFame] = React.useState([]);
  const [count, setCount] = React.useState(0);

  const getDetail = () => {
    //setMessageData([]);
    ////debugger
    if (!userId && !productId) {
      return;
    }

    services
      .API_GET(`Jag/GetWallOfFame/${productId}`)
      .then(response => {
        ////debugger;
        if (response && response != "[]") {

          if (response.Status) {
            let data = JSON.parse(response.Response);
            if (data.length > 0 && parseInt(data[0]) > 5) {
              setCount(parseInt(data[0]));
              setWallOfFame(JSON.parse(data[1]));
              setOpen(true)
            }

            // let c = getCookie('walloffame');
            // if (c) {
            //   setOpen(false)
            // }
          }


          //getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

  }



  useEffect(() => {
    if (userId, productId) {
      getDetail();
    }

  }, [userId, productId]
  );

  const handleClose = () => {
    ////debugger
    setOpen(false);
    //setCookie("walloffame", 1, 1);
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={() => handleClose()}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        className="wallofFamePopup"
      >
        {/* <DialogTitle id="alert-dialog-title">wall of fame</DialogTitle> */}
        <DialogContent>
          <WallOfFameContent userId={userId} productId={productId}></WallOfFameContent>
        </DialogContent>

      </Dialog>

    </>
  );
};

export default WallOfFame;
