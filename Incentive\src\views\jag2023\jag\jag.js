import React, { useState, useEffect } from "react";
import { useParams } from "react-router";
import AgentDetails from "../AgentDetails/AgentDetails";
import ProgressGrowthCalc from "../ProgressGrowthCalc/ProgressGrowthCalc";
 import '../../jag2023/scss/jag.scss'
import { Container, Grid } from "@material-ui/core";
import LeaderBoard from "../LeaderBoard/LeaderBoard";
import MessageAndCriteria from "../MessageAndCriteria/MessageAndCriteria";
import * as services from "../../../services";
import moment from 'moment';
import STORAGE from '../../../store/storage';
import { EMERGING_TYPE, JAG_TYPE, JAG_VERSION, GOLD, SILVER, HUSTLER, WARRIOR } from "../JagConstants2023";
import ApeBarGraph from "../ApeBarGraph/ApeBarGraph";
import NewAnnoucementpopup from "../NewAnnoucement/NewAnnoucementpopup";
import FeedbackFrom from "../Feedback/FeedbackPopup";
import { useRef } from "react";
import RewardsSlab from "../RewardsSlab/RewardsSlab";


export default function Jag(props) {

  const urlParams = useParams();
  const version = JAG_VERSION;
  const [agentData, setAgentData] = useState({});
  const [agentDetails, setAgentDetails] = useState({});
  const [userId, setUserId] = useState(null);

  const [productId, setProductId] = useState(null);
  const [open, setOpen] = useState(true);
  const [isJagType, setIsJagType] = useState(false);
  const [isEmergingType, setIsEmergingType] = useState(false);

  useEffect(() => {
    const dt = moment().subtract(3, 'months').startOf('month').format("DD-MM-YYYY");
    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);

    services
      .API_GET(`Incentive/GetAgentDetails/${urlToken}/${dt}`).then(response => {
        if (response) {
          
          if (response.status && response.status === 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            setUserId(response.AgentId);

            localStorage.setItem('user', JSON.stringify(response));
            setAgentDetails(response);
            let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JAG&EventName=Pageload`;
            if (Source) {
              url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=JAG&EventName=` + Source
            }

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

    services
      .API_GET(`Jag/GetAgentDetails/${urlToken}/${dt}?version=${version}`).then(response => {
        if (response) {
          if (response && response.status == 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            let memberType = response && response.MemberType && response.MemberType.toLowerCase() || '';
            let isJagType = JAG_TYPE.includes(memberType) || false;
            let isEmergingType = EMERGING_TYPE.includes(memberType) || false;
            setIsJagType(isJagType);
            setIsEmergingType(isEmergingType);
            setAgentData(response);
            setProductId(response.ProductId || null);
            
            localStorage.setItem('jaguser', JSON.stringify(response));
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

  }, []);


  return (
    <div className="Jag2023">
      {/* <div className="AgentDetailsLayout EmergingLayout"> */}
        <div className={`${'AgentDetailsLayout '}${ isJagType ? 'JagBgImage' : (isEmergingType ? 'EmergingLayout' : '')}`}
          >
        <Container>
          {/* Header */}
          <div className="Header">
            <Grid item md={7} xs={12}>
              <div className="leftSide">
                <img src="/images/jag2023/logowhite.svg" />
                <hr />
                {isJagType && <img src="/images/jag2023/jaglogo.svg" />}
                {isEmergingType && <img src="/images/jag2023/emergingLogo.svg" />}
              </div>
            </Grid>
            <Grid item md={5} xs={12}>
              <div className="rightSide">
                {isJagType &&
                  <>
                    <div className="RetainerBtn">
                      <img src="/images/jag2023/termicon.png" /> {agentData && agentData.BU} | &nbsp;{agentData && moment().diff(agentData.DOJ, "months") || '-'} months</div>
                   {agentData.MemberType && agentData.MemberType.toLowerCase() === GOLD && 
                    <img src="/images/jag2023/goldSuperstar.svg" className="goldStar" />
                   } 
                    {agentData.MemberType && agentData.MemberType.toLowerCase() === SILVER && 
                    <img src="/images/jag2023/silver.svg" className="goldStar" />
                   } 
                  </>
                }
                {isEmergingType &&
                  <>
                    <div className="RetainerBtn">
                      <img src="/images/jag2023/termicon.png" /> {agentData && agentData.BU} | &nbsp;{agentData && moment().diff(agentData.DOJ, "months") || '-'} months</div>
                      {agentData.MemberType && agentData.MemberType.toLowerCase() === HUSTLER && 
                        <img src="/images/jag2023/hustler.svg" className="goldStar" />
                      } 
                    {agentData.MemberType && agentData.MemberType.toLowerCase() === WARRIOR && 
                        <img src="/images/jag2023/warrior.svg" className="goldStar" />
                      } 
                  </>
                }

              </div>
            </Grid>
            
          </div>

          <AgentDetails
            agentDetails={agentDetails}
            agentData={agentData}
            isJagType={isJagType}
            isEmergingType={isEmergingType}
          />

          <Grid container spacing={3}>
            <MessageAndCriteria
             agentDetails={agentDetails}
             agentData={agentData}
             />
            <LeaderBoard
              agentDetails={agentDetails}
              agentData={agentData}
              version={version}
            />
          </Grid>
        </Container>

        {/* <NewAnnoucementpopup/> */}

      </div>

      <div className="AppBarSection">
        <ApeBarGraph
         agentDetails={agentDetails}
         agentData={agentData}
         version={version}
        />
      </div>
      
      <RewardsSlab
        agentData={agentData}
       isJagType={isJagType}
       isEmergingType={isEmergingType}
      />

      {/* <FeedbackFrom/> */}
    </div>
  
  );
};
