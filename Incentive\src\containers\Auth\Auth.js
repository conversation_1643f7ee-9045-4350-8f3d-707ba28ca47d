import React, { Component } from 'react';
import { withRouter } from 'react-router-dom';
import { connect } from 'react-redux';
import * as actions from '../../store/actions';
import ErrorBoundary from './../../hoc/ErrorBoundary';
import {
    SignIn as SignInView,
    SignUp as SignUpView,
} from './../../views';


class AuthContainer extends Component {
    constructor(props) {
        super(props);
        if (this.props.location.search) {
            let searchParams = new URLSearchParams(this.props.location.search);
            if (searchParams.has("access")) {
                console.log(searchParams.get("access"))
                this.props.onAccess(searchParams.get("access"))
            }
        }
    }

    render() {
        const { match: { path } } = this.props;
        return "Auth"
        // if (path === '/auth/register') {
        //     return <ErrorBoundary><SignUpView {...this.props} /></ErrorBoundary>
        // }
        // else {
        //     return <ErrorBoundary><SignInView {...this.props} /></ErrorBoundary>
        // }
    }
}

const mapStateToProps = state => {
    return {
        errors: state.auth.errors,
        loading: !(!state.common.loading),
    }
}
const mapDispatchToProps = dispatch => {
    return {
        onAccess: (name) => dispatch(actions.access(name)),
        onSignIn: (userInfo, history) => dispatch(actions.login(userInfo, history)),
        onRegisterPartner: (partnerInfo, history, partnertype) => dispatch(actions.register(partnerInfo, history, partnertype)),
        onChangePassword: (userInfo, history) => dispatch(actions.changePassword(userInfo, history)),
    }
}

export default withRouter(connect(mapStateToProps, mapDispatchToProps)(AuthContainer));
