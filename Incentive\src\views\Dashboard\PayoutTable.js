import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';
import Tooltip from '@material-ui/core/Tooltip';


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);
const PayoutTable = (props) => {

  const classes = useStyles();
  const { userId, date } = props;
  const [payoutList, setPayoutList] = useState(null);
  
  const filterAdjustments = (data, type) => {
    if (!data)
      return [];
    let result = [];
    data.forEach(element => {
      if (element.AdjustmentType === type)
        result.push(element)
    });
    return result;
  };


  useEffect(() => {
    setPayoutList(null);
    if (!userId) {
      return;
    }
    if(date == "01-07-2021"){
      return;
    }
    services
      .API_GET(`Incentive/GetAgentPayout/${userId}/${date}`)
      .then(response => {
        //
        // if (response.Status && response.Response !== "[]" && response.Response.length > 0) {
        //   setPayoutList(JSON.parse(response.Response));
        // }
        setPayoutList(response);
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [props]);

  const renderTooltipWrapper = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                      {
                        moment(breakup.AmountMonth).format('MMM YYYY')
                      }

                    </td>

                    <td style={breakup.Amount < 0 ? { width: "20%", textAlign: "right", color: "red" } : { width: "20%", textAlign: "right", color: "#00458b" }} >{breakup.Amount}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderTooltipArray = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                    {breakup.Name}

                    </td>

                    <td style={{ width: "20%", textAlign: "right", color: "red" }} >{breakup.Value}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderTooltipOtherArray = (item) => {
    ////debugger
    if (item === null || item.length === 0) return <div style={{ border: "1px solid #546e7a4a", width: "100%", color: 'red', textAlign: 'center' }}>No Adjustments</div>;
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            item.map((breakup, indx) =>

              <li key={indx + 1} >
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >

                    {breakup.Name}

                    </td>

                    <td style={{ width: "20%", textAlign: "right", color: "red" }} >{breakup.Amount}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const renderIncentiveTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>( As per the issuance till {moment(payoutList.IncentiveCycle).format('Do MMMM YYYY')}. Incentive for the remaining policies will be paid as arrears with the next month incentive if the policy gets issued)</span>
    </div>
  }
  const renderWarningsTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>{payoutList.WarningCount} WARNINGS</span>
    </div>
  }

  const renderQualityScoreTooltip = (payoutList) => {
    return <div className="common-tooltip-popup">

      <span>Quality Score : {payoutList.QScore} %</span>
    </div>
  }



  //

  if ( payoutList == null || payoutList.length === 0 || payoutList.IsDisplay === false ||payoutList.ProductId===-7 || payoutList.ProductId===-115)
    return <></>;    
  else
    return payoutList && (


      <div className="pading-zero" className={classes.root}>
        <div className="payout">
          <h6>
            Payouts
            <span>Payouts as per performance</span>
          </h6>
          <ul className="payout-box">

            <li className="firstbox">
              <div>
                <strong>Actual Payout (In {moment(payoutList.IncentiveCycle).format('MMMM')}) </strong>
                <span>
                  <i className="fa fa-inr"></i> {payoutList.TotalPayout.toLocaleString('en-IN')}

                </span>
              </div>
            </li>
            <li>
              <span>Incentive
              <WrapperTooltip
                  disableFocusListener
                  TransitionComponent={Zoom}
                  placement="top"
                  arrow
                  title={renderIncentiveTooltip(payoutList)}
                  classes={{ tooltip: classes.customWidth }}
                >
                  <i className="fa fa-info-circle"></i>
                </WrapperTooltip>
              </span>
              <p>
                {payoutList.Incentive.toLocaleString('en-IN')}
              </p>
            </li>
            <li><span>Arrears
            <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderTooltipWrapper(filterAdjustments(payoutList.Adjustments, "ARREARS"))}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>



            </span>
              <p style={payoutList.TotalArrears < 0 ? { color: "red", borderBottom: "dashed", borderWidth: "thin" } : {}}>
                {payoutList.TotalArrears.toLocaleString('en-IN')}
              </p></li>
            <li><span>Clawbacks
            <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderTooltipWrapper(filterAdjustments(payoutList.Adjustments, "Clawback"))}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>


            </span>
              <p style={payoutList.TotalClawbacks < 0 ? { color: "red", borderBottom: "dashed", borderWidth: "thin" } : {}}>
                { payoutList.TotalClawbacks && payoutList.TotalClawbacks.toLocaleString('en-IN')}
              </p></li>
              <li><span>Other Components
            <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderTooltipOtherArray(filterAdjustments(payoutList.Adjustments, "Other"))}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
            </span>
              <p style={payoutList.OtherTotalArrears < 0 ? { color: "red", borderBottom: "dashed", borderWidth: "thin" } : {}}>
                {payoutList.OtherTotalArrears && payoutList.OtherTotalArrears.toLocaleString('en-IN')}
              </p></li>         
            <li><span>Warning Deduction
            <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderWarningsTooltip(payoutList)}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
            </span>
              {/* <em>{payoutList.WarningCount} WARNINGS</em> */}
              <p>
                {payoutList.WarningDeduction && payoutList.WarningDeduction.toLocaleString('en-IN')}
              </p></li>
            <li><span>Quality Deduction
            <WrapperTooltip
                disableFocusListener
                TransitionComponent={Zoom}
                placement="top"
                arrow
                title={renderTooltipArray(payoutList.Deductions)}
                classes={{ tooltip: classes.customWidth }}
              >
                <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
            </span>
              <p style={payoutList.QScoreDeduction < 0 ? { color: "red", borderBottom: "dashed", borderWidth: "thin" } : {}}>
                {payoutList.QScoreDeduction && payoutList.QScoreDeduction.toLocaleString('en-IN')}
              </p>
              </li>

          </ul>



        </div>
      </div>


    );
};

export default PayoutTable;