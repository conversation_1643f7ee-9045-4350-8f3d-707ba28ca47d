@import '../variables/variables';
@import '../common/mixins';

.search-wrapper {
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    @media (min-width: 960px) {
        display: block;
    }
    .search-box {
        //max-width: 180px;
        @media (min-width: 960px) {
            max-width: auto;
        }
    }
    .search-input {
        background: $white;
        border: none;
        border-bottom: 1px solid #ccc;
        border-radius: 0;
        div:hover{
            &:before{
                display: none;
            }
        }
    }
}


