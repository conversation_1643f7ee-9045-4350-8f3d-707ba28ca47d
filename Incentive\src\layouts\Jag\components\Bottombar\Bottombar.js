import React from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { makeStyles } from '@material-ui/styles';
import { Typography, Link, Container } from '@material-ui/core';

const useStyles = makeStyles(theme => ({
  root: {
    padding: theme.spacing(4)
  }
}));

const Bottombar = props => {
  const { className, ...rest } = props;

  const classes = useStyles();

  return (
   
 
          <footer>
              <div className="topSection">                
              <Container className="text-center">
                <a href="#"><img src="/images/fbIcon.svg"/></a>
                <a href="#"><img src="/images/twIcon.svg"/></a>
                <a href="#"> <img src="/images/lnIcon.svg"/></a>
              </Container>
              </div>
            <div className="bottomSection"> 
            <Container>
                 <p>Copyright 2021 Policybazaar.com | Jeeto Apna Ghar</p>
              </Container></div>
          
           

          </footer>
          

  );
};

Bottombar.propTypes = {
  className: PropTypes.string
};

export default Bottombar;
