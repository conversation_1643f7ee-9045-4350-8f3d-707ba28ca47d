import React, { useEffect, useState } from "react";
import * as services from "../../../services";
import Slider from "react-slick";

import parse from "html-react-parser";

const settings = {
  dots: true,
  infinite: true,
  arrows: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1
};

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});


const CriteriaTerm = (props) => {
  const { show, handleClose, superGroupId, productId, date } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState(null);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getCriteria = () => {
    setCriteriaList([]);
    setCriteriaListHtml(null);
    setCriteriaSlabs([]);
    services
      .API_GET(`Incentive/GetIncentiveCriteria/${superGroupId}/${productId}/${date}`)
      .then(response => {
        console.log(response && response != "[]");
        if (response && response != "[]") {
          setCriteriaList(response.Insights || null);
          setCriteriaListHtml(response.InsightsHtml || null);
          setCriteriaSlabs(response.Slabs ? _.orderBy(response.Slabs, ['IncentiveLevel'], ['desc']) : null);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  useEffect(() => {
    if (superGroupId && productId && date) {
      getCriteria();
    }
  }, [superGroupId, productId, date]);

  const renderHtml = () => {

    let html = parse(criteriaListHtml)

    return html;
  }


  return (
    <div className='how-it-works-section'>
      <span>Incentive Criterias</span>
      <h3>HOW IT WORKS?</h3>
      <p className="text-center">Your journey will be divided into levels based on APE/Booking</p>
      {/* {productId != 115 && */}
        <div className='steps step-1'>
          <div className="incentive-slab">
            {
              criteriaSlabs && criteriaSlabs.map((item, index) => {
                return <div className={"slab slab-" + (criteriaSlabs.length - index)}>
                  <p>SLAB {index + 1}<br /> {Math.round(item.MinRangeDisplay).toLocaleString('en-IN')} - {Math.round(item.MaxRangeDisplay).toLocaleString('en-IN')} ({item.IncentivePercentage}%)</p>
                </div>
              })
            }
          </div>
        </div>
        {/* } */}
      {criteriaListHtml && renderHtml()}
      {/* <Slider {...settings} >
      <div className='steps step-1'>
        <div className="incentive-slab">
          {
            criteriaSlabs && criteriaSlabs.map((item, index) => {
              return <div className={"slab slab-" + (criteriaSlabs.length - index)}>
                <p>SLAB {item.IncentiveLevel}<br /> {Math.round(item.MinRangeDisplay).toLocaleString('en-IN')} - {Math.round(item.MaxRangeDisplay).toLocaleString('en-IN')} ({item.IncentivePercentage}%)</p>
              </div>
            })
          }
        </div>
      </div>
      {criteriaListHtml && renderHtml()}

    </Slider> */}
    </div >
  );
}
export default CriteriaTerm;