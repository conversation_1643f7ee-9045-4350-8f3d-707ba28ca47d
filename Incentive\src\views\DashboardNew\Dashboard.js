import React, { useEffect, useState } from "react";
import { useParams } from "react-router";
import moment from "moment";
import { makeStyles, createTheme } from "@material-ui/core/styles";
import {
  Grid, Tab, Box
} from "@material-ui/core";
import {
  Tab<PERSON><PERSON>x<PERSON>, <PERSON>b<PERSON><PERSON>, TabPanel
} from "@material-ui/lab";

import Payout from "./Payout";
//import GetServerData from "./serverfetch";
import CurrentMonthProjections from "./CurrentMonthProjection/main";
import * as services from "../../services";
//import STORAGE from './../../store/storage'
//import * as services from "./../../services";
import * as utility from "../../utils/utility";
import RaiseTicket from "./RaiseTicket";

//import "style/dashboard.css";
//import * as actions from './store/actions';
import '../../assets/scss/index.scss'

const useStyles = makeStyles((theme) => ({
  root: {
    flexGrow: 1,
    width: "100%",
    backgroundColor: theme.palette.background.paper,
  },
}));

/*
const getLastMonths = (months) => {
  let monthNames = moment.monthsShort()
  let today = new Date();
  let d; let mth;
  let monthsList = [];
  for (let i = months; i >= 3; i -= 1) {
    d = new Date(today.getFullYear(), today.getMonth() - i, 1);
    mth = d.getMonth() + 1
    mth = mth < 10 ? "0" + mth : mth;
    monthsList.push({
      value: "01-" + mth + "-" + d.getFullYear(),
      name: monthNames[d.getMonth()] + " " + d.getFullYear()
    });
  }
  return monthsList;
};
*/

export default function Dashboard(props) {

  const classes = useStyles();
  const urlParams = useParams();

  const [date, setDate] = useState(moment().subtract(4, 'months').startOf('month').format("DD-MM-YYYY"));

  const [user, setUser] = useState(null);
  const [projection, setProjection] = useState(false);

  const [TabValue, setTabValue] = useState('1');
  const [agentId, setAgentId] = useState(null);

  const handleTabChange = (event, newValue) => {
    // setTabValue(newValue);
    // //debugger;
    // props && props.history && props.history.push({      
    //   search: '?projection=0'
    // })
  };

  const setUserDetails = (user) => {
    setUser(user);
  }

  useEffect(() => {
    if (user) {
      //debugger;
      if(user.ProductId == 7){
        window.location.href = "../TermDashboard"
      }
      else{
      services
        .API_GET(`Incentive/GetProjectedIncentive/${user.AgentId}`).then(response => {
          ////debugger
          if (response && response.Status) {
            response = JSON.parse(response.Response);
            if (response.length > 0) {
              setAgentId(response[0].UserId)
              if (response[0].disable) {
                setProjection(true)
                if(utility.getUrlParameter('projection') == "1"){
                  setTabValue('2');
                }
              }
            }
          }
        })
        .catch((err) => {
          console.log("Error", err);
        });
      }
    }
  }, [user]);

  return (
    <>

    
   
    
      
      <div className={classes.root} className="wrapper">

        <Grid container spacing={3}>
          <Box sx={{ width: '100%', typography: 'body1' }}>
            <TabContext value={TabValue}>
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }} className="tabLayout">
                {/* {projection ?
                  <TabList onChange={handleTabChange} aria-label="lab API tabs example" >
                    <Tab className='payout' label="Payouts" value="1" />
                    <Tab className='CMP' label="Current Month Projections" value="2" />
                  </TabList> */}
                  {/* :  */}
                  <TabList onChange={handleTabChange} aria-label="lab API tabs example" >
                    <Tab className='payout' label="Payouts" value="1" />
                  </TabList>
                  {/* } */}
              </Box>

              <TabPanel value="1" className="PayoutSection">
                <Payout setUserDetails={setUserDetails} />
              </TabPanel>

              <TabPanel value="2" className="CurrentMonthProjContainer">
                <CurrentMonthProjections agentDetail={user} />
              </TabPanel>
            </TabContext>
          </Box>


        </Grid>
      </div>
      <RaiseTicket onHover={"Raise a Query"}/>

    </>
  );
}
