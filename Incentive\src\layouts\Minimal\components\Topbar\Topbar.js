import React from "react";
import { Link as RouterLink } from "react-router-dom";
import clsx from "clsx";
import PropTypes from "prop-types";
import { makeStyles } from "@material-ui/styles";
import { AppBar, Toolbar } from "@material-ui/core";

const useStyles = makeStyles(() => ({
  root: {
    boxShadow: "0 0rem 0.4rem rgba(0, 0, 0, 0.15)",
  },
  logo: {
    maxWidth: "150px",
  },
}));

const Topbar = (props) => {
  const { className, ...rest } = props;

  const classes = useStyles();

  return (
    <AppBar
      {...rest}
      className={clsx(classes.root, className)}
      color="custom"
      position="fixed"
    >
      <Toolbar className={classes.logoWeb}>
        <RouterLink to="/">
          <img alt="Logo" src="/images/logo.png" className={classes.logo} />
        </RouterLink>
      </Toolbar>
    </AppBar>
  );
};

Topbar.propTypes = {
  className: PropTypes.string,
};

export default Topbar;
