import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/Criteria";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
} from "@material-ui/core";

import ProcessBar from "./ProcessBar";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const MessagingCenter = (props) => {
  const classes = useStyles();
  const { userId, productId } = props;
  const [messageData, setMessageData] = useState([]);
  const [slideIndex, setSlideIndex] = useState(1);

  const getDetail = () => {
    setMessageData([]);
    if (!userId && !productId) {
      return;
    }

    services
      .API_GET(`jag/GetJagMessageCenterData/${userId}/${productId}`)
      .then(response => {
        if (response.Status && response.Response != "[]" && response.Response.length > 0) {
          setMessageData(JSON.parse(response.Response));
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }



  useEffect(() => {
    getDetail();
  }, [props]
  );

  return (
    <>
      <div className="pading-zero" className={classes.root}>



        <div className="jag-highlights">
          <h6>
            Messaging Center
            <span>Shows latest updates</span>
          </h6>

          {(messageData && messageData.length > 0) ? (messageData.map((item, index) =>
            <div key={1} className="highlights-card">

              <ul>
                <li>

                  <div className="slideshow-container">
                    <div className={slideIndex === 1 ? "mySlides show" : "mySlides fade"}>

                      <p><strong>{item.TotalLottery + (item.BonusTickets ? item.BonusTickets : 0)}</strong> lottery tickets are issued in total across all BUs and <strong>{item.MaxLottery}</strong> is the highest number of tickets being issued to a superstar.</p>

                    </div>

                    <div className={slideIndex === 2 ? "mySlides show" : "mySlides fade"}>

                      <p>Greater APE/Bookings &gt; More Lottery Tickets &gt; Greater probability to win the Flat</p>

                    </div>

                    <div className={slideIndex === 3 ? "mySlides show" : "mySlides fade"}>

                      <p>For any feedback or query related to the contest, please write back to <NAME_EMAIL></p>

                    </div>
                  </div>
                  <br />

                  <div >
                    <span className={slideIndex === 1 ? "dot active" : "dot"} onClick={(e) => setSlideIndex(1)}></span>
                    <span className={slideIndex === 2 ? "dot active" : "dot"} onClick={(e) => setSlideIndex(2)}></span>
                    <span className={slideIndex === 3 ? "dot active" : "dot"} onClick={(e) => setSlideIndex(3)}></span>
                  </div>
                </li>
                <li>
                  <img src="/images/message-center.svg" />
                </li>
              </ul>
            </div>
          )) : (
            <></>
          )}
        </div>
      </div>

    </>
  );
};

export default MessagingCenter;
