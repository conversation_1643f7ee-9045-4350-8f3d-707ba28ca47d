import React, { useEffect, useState, useRef, useCallback  } from "react";
import moment, { relativeTimeRounding } from "moment";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import {
  Grid
} from "@material-ui/core";

import Ranking from "./Ranking";
import LeaderBoard from "./LeaderBoard";
import IncentiveCalculations from "./IncentiveCalculations";
import IncentiveCalculationsMotor from "./IncentiveCalculationsMotor";
import IncentiveCalculationsMotorV2 from "./IncentiveCalculationsMotorV2";
import IncentiveCalculationsMotorRenewal from "./IncentiveCalculationsMotorRenewal";
import AchievementMotorRenewal from "./AchievementMotorRenewal";
import LeaderBoardMotorRenewal from "./LeaderBoardMotorRenewal";
import CriteriaMotorRenewal from "./Critaria/CriteriaMotorRenewal"
import BookingTableMotorRenewal from "./BookingTableMotorRenewal"
import SourcingDataMotor from "./SourcingDataMotor";
// import IncentiveCalculator from "./IncentiveCalculator";
import BookingTable from "./bookingTable";
import PayoutTable from "./PayoutTable";
import { useParams } from "react-router";
import STORAGE from '../../store/storage'
import * as services from "../../services";
import CriteriaTerm from "./Critaria/CriteriaTerm";
import CriteriaMotor from "./Critaria/CriteriaMotor";
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
// import PayoutHealthRen from "./healthrenewal/payouthealth";
import MainIncentive from "./healthrenewal/mainincentive";
import CriteriaHealthRen from "./Critaria/Criteriahealthren";
const ActiveMonths = require('../../../ActiveMonths.json')
//import RaiseTicket from "./RaiseTicket";

//import "style/dashboard.css";
//import * as actions from './store/actions';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import InfoUseful from './../InfoUseful';
import { useMediaQuery } from '@material-ui/core';
import CriteriaInvestment from "./Critaria/CriteriaInvestment";
import QualifierHealthRen from "./healthrenewal/qualifier";
import UpsellHealth from "./healthrenewal/upsell";
import LoaderComponent from "../../components/Loader";
import { CONFIG } from "../../appconfig";

import {
  Box,
  Tabs, Tab
} from "@material-ui/core";

import PDFViewer from "./PDFViewer";
import IncentiveCriteria from "./IncentiveCriteria";

const useStyles = makeStyles((theme) => ({
  rightPopup: {
    position: "relative",
    zIndex: "0",
  },
  list: {
    width: 600,
  },
  mList: {
    width: '100%',
  }
}));

const getLastMonths = (months) => {
  let monthNames = moment.monthsShort()
  let today = new Date();
  let d; let mth;
  let monthsList = [];
  for (let i = months; i >= 3; i -= 1) {
    d = new Date(today.getFullYear(), today.getMonth() - i, 1);
    mth = d.getMonth() + 1
    mth = mth < 10 ? "0" + mth : mth;
    monthsList.push({
      value: "01-" + mth + "-" + d.getFullYear(),
      name: monthNames[d.getMonth()] + " " + d.getFullYear()
    });
  }
  console.log("")
  return monthsList;
};

export default function Dashboard(props) {
  const { setUserDetails } = props;
  const urlParams = useParams();
  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });

  const [isLoading, setIsLoading] = useState(false);
  const [date, setDate] = useState(ActiveMonths[Object.keys(ActiveMonths).find(k => ActiveMonths[k].active === "true" )].value);
  const [userId, setUserId] = useState(null);
  const [productId, setProductId] = useState(null);
  const [SuperGroupID, setSuperGroupID] = useState(null);
  const [user, setUser] = useState({});
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  //health renewal
  const [mainIncentive, setMainIncentive] = useState("");
  
  const [qualiUpsell, setQualiUpsell] = useState("");


  const [monthList, setMonthList] = useState([moment().format('MM'),
  moment().subtract(1, 'months').format('MM'),
  moment().subtract(2, 'months').format('MM')]);
  
  const [displayMonth, setDisplayMonth] = useState(moment().subtract(3, 'months').format('MM'));
  //const [TabValue, setTabValue] = useState('1');
  const [RightPopup, setRightPopup] = useState(false);
  const [TotalPayout, setTotalPayout] = useState(0);
  const [MotorRenewal, setMotorRenewal] = useState();
  const [MotorCalculationsV2, setMotorCalculationsV2] = useState({});
  const [EmployeeId, setEmployeeId] = useState();
  const [userName, setUserName] = useState();
  const refSourcedbookings = useRef(null);
  const refLeaderBoard = useRef(null);
  const refIncCalc = useRef(null);
  const refIncCalcMotor = useRef(null);
  const refPayout = useRef(null);
  const refPayoutHealthR = useRef(null);
  const refBookings = useRef(null);
  const refMainInc = useRef(null);
  const refQuali = useRef(null);
  const refUpsell = useRef(null);
  const refMotorNewUIStart = useRef(CONFIG.MOTOR_NEW_UI_START_FROM || '');
  const [activeScroll, setActiveScroll] = useState(0);
  const [sections, setSection] = useState(0);
  const [BUid, setBUid] = useState();
  


  useEffect(() => {
    const handleScroll = (e) => {
      if(BUid == 2){
        if ( window.scrollY > refPayoutHealthR.current.offsetTop -200  && window.scrollY < refMainInc.current.offsetTop-200) {
                setActiveScroll(0)
            } else if ( window.scrollY > refMainInc.current.offsetTop -200 && window.scrollY < refQuali.current.offsetTop-200 ) {
                setActiveScroll(1)
            } else if ( window.scrollY > refQuali.current.offsetTop -200 && window.scrollY < refUpsell.current.offsetTop-200 ) {
              setActiveScroll(2)
            }
            else if ( window.scrollY > refUpsell.current.offsetTop -200  ) { //&& window.scrollY < refBookings.current.offsetTop-200
              setActiveScroll(3)
            }
            // else if (window.scrollY > refBookings.current.offsetTop -200) {
            //     setActiveScroll(4)
            // }
            else {
              setActiveScroll(0)
            }
          }

      else if(productId != 117 && BUid != 2 ){
        if ( window.scrollY > refLeaderBoard.current.offsetTop -200  && window.scrollY < refIncCalc.current.offsetTop-200) {
                setActiveScroll(0)
            } else if ( window.scrollY > refIncCalc.current.offsetTop -200 && window.scrollY < refPayout.current.offsetTop-200 ) {
                setActiveScroll(1)
            } else if ( window.scrollY > refPayout.current.offsetTop -200 && window.scrollY < refBookings.current.offsetTop-200 ) {
              setActiveScroll(2)
            }
            else if (window.scrollY > refBookings.current.offsetTop -200) {
                setActiveScroll(3)
            }
          }
          else if(productId == 117){
            if ( window.scrollY > refIncCalcMotor.current.offsetTop -10 && window.scrollY < refSourcedbookings.current.offsetTop-10) {
                    setActiveScroll(0)
                
                } else if ( window.scrollY > refSourcedbookings.current.offsetTop -10 && window.scrollY < refLeaderBoard.current.offsetTop-10 ) {
                  setActiveScroll(1)
                }
                else if ( window.scrollY > refLeaderBoard.current.offsetTop -10 && window.scrollY < refPayout.current.offsetTop-10 ) {
                  setActiveScroll(2)
                }
                else if ( window.scrollY > refPayout.current.offsetTop -10 && window.scrollY < refBookings.current.offsetTop-10 ) {
                  setActiveScroll(3)
                }
                else if (window.scrollY > refBookings.current.offsetTop -10) {
                    setActiveScroll(4)
                }
              }
              

        
    }
    document.addEventListener('scroll', handleScroll);
    return () => {
        document.removeEventListener('scroll', handleScroll);
    }
    
}, [productId, BUid])

  

  const toggleDrawer = (open) => (event) => {
    /*if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }*/

    setRightPopup(open);
  };

  // const handleTabChange = (event, newValue) => {
  //   setTabValue(newValue);
  // };

  //const [userDetail, setUserDetail] = useState({});

  useEffect(() => {
    
    //debugger;

    if(userId){
    services
      .API_GET(`Incentive/GetHealthRenewalPayout/${userId}/${date}`).then(response => {
        ////debugger
        if (response && response.Status) {
          response = JSON.parse(response.Response);
          
          if (response.length > 0) {
            console.log("response", response[1])

              setMainIncentive(response[0])
              setQualiUpsell(response[1])
              


        }
      }
      })
      .catch((err) => {
        console.log("Error", err);
      });
    }
  
}, [userId, date]);





  useEffect(() => {
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
    //const tokenPath = window.location.pathname.split('/');
    //if (tokenPath.length >= 3) {
    //Set Token Using URL parameter
    let urlToken = urlParams.Token;
    let Source = urlParams.Source;
    STORAGE.setAuthToken(urlToken);

    services
      .API_GET(`Incentive/GetAgentDetails/${urlToken}/${date}`).then(response => {
        if (response) {
          if (response.status && response.status == 401) {
            alert("Your session with Matrix is expired, Please login again");
            window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
          } else {
            //debugger;
            //setUserDetail(response);
            //props.login(response);
            //console.log("Dashboard================", userDetail);
            setUserId(response.AgentId);
            setUserName(response.UserName)
            //debugger
            setEmployeeId(response.EmpId)
            setSuperGroupID(response.SuperGroupID);
            setProductId(response.ProductId);
            setUser(response);
            setBUid(response.BUId)
            setUserDetails(response);
            
            localStorage.setItem('user', JSON.stringify(response));
            // console.log(user);
            //getCriteria(response)

            let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=Pageload`;
            if (Source) {
              url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=` + Source
            }

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });

  }, [urlParams, date]);


  useEffect(() => {
    if(productId && productId === 217){
      let url =`Incentive/GetIncentiveMotorRenewalAgent/${userId}/${date}`;
      services
        .API_GET(url).then(response => {
          setMotorRenewal(response)
        })
        .catch((err) => {
        });
    } else if(productId === 117 && date >= refMotorNewUIStart.current) {
      let url =`Incentive/GetMotorPolicyTypeData/${userId}/${date}`;
      services
        .API_GET(url).then(response => {
          setMotorCalculationsV2(response)
        })
        .catch((err) => {
        });
    }

  },[productId,date])
  
  


  const handleChange = (event, newValue) => {
    setIsLoading(true);
    let timeout = setTimeout(() => {
      setIsLoading(false);
    }, 2000);
    
    setDate(event.target.value || moment().startOf('month').format("DD-MM-YYYY"));

    const dt = event.target.value;
    let urlToken = urlParams.Token;
    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=MonthChange`;

    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });
    return () => clearTimeout(timeout);
  };

  const settings = {
    dots: true,
    infinite: true,
    arrows: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1
  };
  
  // useEffect(() => {
    
  //   window.addEventListener("scroll", onScroll);

  //   return () => window.removeEventListener("scroll", onScroll);
  // },[scrollTop,scrolling]);

  const loadCriteria = (productId, BUid) => {
    console.log(BUid)

    return  <IncentiveCriteria Date={moment(date).format('DD-MM-YYYY')} ProductId={productId}/>

    // return <PDFViewer/>

    // <embed 
    //     src={"https://policystatic.policybazaar.com/UploadApi/2025-01-03/Health-IncentiveCriteria-1735630792061-1735631107421 (1)-1735905692680.pdf"}
    //     type="application/pdf" 
    //     width="100%" 
    //     height="100%" 
    //   />

    // return <iframe src={} width="100%" height="600px"/>

    // if(BUid == 2){
    //   return <CriteriaHealthRen productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaHealthRen>

    // }
    // if (productId == 7) {
    //   return <CriteriaTerm productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaTerm>
    // }
    // if (productId == 117) {
    //   return <CriteriaMotor productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaMotor>
    // }
    // if (productId == 115) {
    //   return <CriteriaInvestment productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaInvestment>
    // }
    // if (productId == 217) {
    //   return <CriteriaMotorRenewal productId={productId} date={date} superGroupId={SuperGroupID} MotorRenewal = {MotorRenewal} userName = {userName}/> 
    // }
    // return <CriteriaTerm productId={productId} date={date} superGroupId={SuperGroupID}></CriteriaTerm>



  }

  
  const handleTotalPayout = (TotalPayout) => {
    setTotalPayout(TotalPayout);
  }
  const executeLeaderBoardScroll = (e) => {
    if(productId != 117){setActiveScroll(0);}
    else{setActiveScroll(2);}
    //refLeaderBoard.current.scrollIntoView();
    window.scrollTo(0, refLeaderBoard.current.offsetTop + 10)
  }
  const executeIncCalcScroll = () => {
    setActiveScroll(1);
    
    
    //refIncCalc.current.scrollIntoView();
    window.scrollTo(0, refIncCalc.current.offsetTop + 10)
  }
  const executeIncCalcScrollMotor = () => {
    
    setActiveScroll(0);
    
    //refIncCalc.current.scrollIntoView();
    window.scrollTo(0, refIncCalcMotor.current.offsetTop + 10)
  }
  const executePayoutScroll = () => {
    if(productId != 117){setActiveScroll(2);}
    else{setActiveScroll(3);}
    //refPayout.current.scrollIntoView();
    window.scrollTo(0, refPayout.current.offsetTop + 10)
  }
  const executebookingsScroll = () => {
    if(productId != 117 && BUid != 2){setActiveScroll(3);}
    // else if(BUid == 2){setActiveScroll(4)}
    else{setActiveScroll(4);}
    
    //refBookings.current.scrollIntoView();
    window.scrollTo(0, refBookings.current.offsetTop + 14)
  }
  const executeSourcingscroll =() => {
    setActiveScroll(1)
    window.scrollTo(0, refSourcedbookings.current.offsetTop + 14)
  }
  const executePayouthealthScroll =() => {
    setActiveScroll(0)
    window.scrollTo(0, refPayoutHealthR.current.offsetTop + 14)
  }
  const executeMainIncScroll =() => {
    setActiveScroll(1)
    window.scrollTo(0, refMainInc.current.offsetTop + 14)
  }
  const executeQualiScroll =() => {
    setActiveScroll(2)
    window.scrollTo(0, refQuali.current.offsetTop + 14)
  }
  const executeUpsellScroll =() => {
    setActiveScroll(3)
    window.scrollTo(0, refUpsell.current.offsetTop + 14)
  }
  
  
  const handleTabChange = (event, newValue) => {

    if(BUid == 2){

      setActiveScroll(newValue);
      switch (newValue) {
        case 0:
          executePayouthealthScroll()
          break;
        case 1: 
          executeMainIncScroll()
          break;
        case 2:
          executeQualiScroll()
          break;
        case 3:
          executeUpsellScroll()
          break;
        // case 4:
        //   executebookingsScroll()
        //   break;
        default:
          break;

        
      }

    }
    
    else if(productId != 117 && BUid != 2){
      setActiveScroll(newValue);
    switch (newValue) {
      case 0:
        executeLeaderBoardScroll()
        break;
      case 1:
        executeIncCalcScroll()
        break;
      case 2:
        executePayoutScroll()
        break;
      case 3:
        executebookingsScroll()
        break;
      default:
        break;
    }
    }
    else if(productId == 117){
      setActiveScroll(newValue);
      switch (newValue) {
        case 0:
          executeIncCalcScrollMotor()
          break;
        case 1:
          executeSourcingscroll()
          break;
        case 2:
          executeLeaderBoardScroll()
          break;
        case 3:
          executePayoutScroll()
          break;
        case 4:
          executebookingsScroll()
          break;
        default:
          break;
      }
      }
  }
  
  return (
    <>
      <div className="PayoutContainer">
      {isLoading ? <LoaderComponent open={true} /> : null}
        <Grid container item xs={12} md={12}>
          <Grid item sm={12} md={12} xs={12}>
            <Ranking userId={userId} date={date} handleChange={handleChange} TotalPayout={TotalPayout} MotorRenewal = {MotorRenewal} />
          </Grid>
        </Grid>
        <Grid container spacing={2}>
          
          {/* <Grid item sm={12} md={12} xs={12} className={classes.rightPopup}>
            <a href="javascript:void(0)" onClick={toggleDrawer(true)} className="incentiveCriteraPopup">View Payout Month Incentive Criteria <ChevronRightIcon /> </a>
            {
              <SwipeableDrawer
                anchor="right"
                open={RightPopup}
                onClose={toggleDrawer(false)}
                onOpen={toggleDrawer(true)}
                className="CriteraPopup CriteraSidebar"
              >
                <div
                  className={isDesktop ? classes.list : classes.mList}
                  role="presentation"
                //onClick={toggleDrawer(false)}
                //onKeyDown={toggleDrawer(false)}
                >
                  <span className="crossButton" onClick={toggleDrawer(false)}>X</span>
                  {loadCriteria(productId, BUid)}
                </div>
              </SwipeableDrawer>
            }
          </Grid> */}
        </Grid>

        <Grid container spacing={2} className='mobile-view'>
          <Box className='items-list'>
          {BUid== 2 && <Tabs
              value={activeScroll}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons
              allowScrollButtonsMobile
              aria-label="scrollable force tabs example"
            >
              
              <Tab label="Payout" />   
              <Tab label="Main Incentive" />          
              <Tab label="Qualifier Bookings" />
              <Tab label="Upsell Incentive" />
              {/* <Tab label="Bookings" /> */}
            </Tabs>}
            {productId == 117 && <Tabs
              value={activeScroll}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons
              allowScrollButtonsMobile
              aria-label="scrollable force tabs example"
            >
              
              <Tab label="Incentive Calculations" />   
              <Tab label="Sourcing Details" />          
              <Tab label="LeaderBoard" />
              <Tab label="Payouts" />
              <Tab label="Bookings" />
            </Tabs>}
            {productId != 117 && BUid != 2 && <Tabs
              value={activeScroll}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons
              allowScrollButtonsMobile
              aria-label="scrollable force tabs example"
            >
              <Tab label="LeaderBoard" />
              <Tab label="Incentive Calculations" />
              <Tab label="Payouts" />
              <Tab label="Bookings" />
            </Tabs>}
            </Box>
         </Grid>


         {BUid == 2 && <Grid container spacing={2} className="HealthRenewal">
         <Grid item md={5} xs={12} className='common-view' ref={refPayoutHealthR}>
            <PayoutTable userId={userId} date={date} handleTotalPayout={handleTotalPayout} />
          </Grid>
         <Grid item md={7} xs={12} className='common-view' ref={refMainInc}>
           {mainIncentive && <MainIncentive userId={userId} date={date} mainIncentive = {mainIncentive} />}
            
          </Grid>
          <Grid item md={5} xs={12} className='common-view' ref={refQuali}>
            {qualiUpsell && <QualifierHealthRen qualiUpsell = {qualiUpsell} userId={userId} date={date}  />}
            
          </Grid>
          <Grid item md={7} xs={12} className='common-view' ref={refUpsell}>
            {qualiUpsell && <UpsellHealth userId={userId} date={date}   qualiUpsell = {qualiUpsell}/>}
            
          </Grid>
        </Grid>}

        {productId != 117 && productId != 217 && <Grid container spacing={2} >
          {BUid != 2 && <Grid item sm={4} md={4} xs={12} className='common-view'ref={refLeaderBoard} >
            <LeaderBoard userId={userId} date={date} productId={productId}  />
          </Grid>}

          {BUid != 2 &&<Grid item sm={4} md={4} xs={12} className='common-view' ref={refIncCalc}>
            <IncentiveCalculations userId={userId} date={date} />
          </Grid>}

          {BUid != 2 && <Grid item md={4} xs={12} className='common-view' ref={refPayout}>
            <PayoutTable userId={userId} date={date} handleTotalPayout={handleTotalPayout} />
          </Grid>}
        </Grid>}
        


        {productId == 117 && <Grid container spacing={2}>

          <Grid item sm={12} md={12} xs={12} className='common-view' ref={refIncCalcMotor}>
          {
            date <  refMotorNewUIStart.current ? 
            <IncentiveCalculationsMotor  userId={userId} date={date} /> 
            :  
            <IncentiveCalculationsMotorV2 MotorCalculationsV2 = {MotorCalculationsV2} userId={userId} date={date} />
           }
          </Grid>

          <Grid item sm={4} md={4} xs={12}  className='common-view' ref={refSourcedbookings}>
            <SourcingDataMotor userId={userId} date={date} />
          </Grid>
          <Grid item md={4} xs={12} ref={refLeaderBoard} className='common-view'>
            <LeaderBoard userId={userId} date={date} productId={productId} />
          </Grid>
          <Grid item md={4} xs={12} ref={refPayout} className='common-view'>
            <PayoutTable userId={userId} date={date} />
          </Grid>

        </Grid>}

        {productId == 217 && <Grid container spacing={2}>

          <Grid item sm={12} md={12} xs={12} className='common-view' ref={refIncCalcMotor}>
            <IncentiveCalculationsMotorRenewal MotorRenewal = {MotorRenewal} userId={userId} date={date} />
          </Grid>

          <Grid item sm={4} md={4} xs={12}  className='common-view' ref={refSourcedbookings}>
            <AchievementMotorRenewal MotorRenewal = {MotorRenewal} userId={userId} date={date} />
          </Grid>
          <Grid item md={4} xs={12} ref={refLeaderBoard} className='common-view'>
            <LeaderBoardMotorRenewal MotorRenewal = {MotorRenewal} userId={userId} date={date} productId={productId} EmployeeId = {EmployeeId}/>
          </Grid>
          <Grid item md={4} xs={12} ref={refPayout} className='common-view'>
            <PayoutTable userId={userId} date={date} />
          </Grid>

        </Grid>}


      
      {BUid != 2 && <Grid container spacing={2} ref={refBookings}>
        <Grid item md={12} xs={12}  className='common-view' >
          {productId != 217 && <BookingTable userId={userId} date={date} productId={productId} />}
          {productId == 217 && <BookingTableMotorRenewal userId={userId} date={date} productId={productId} MotorRenewal = {MotorRenewal}/>}
        </Grid>
      </Grid>}
         
      <InfoUseful agentDetail={{ "Token": userId, "Source": "Payout" }} />
    </div>
  </>
  );
}


