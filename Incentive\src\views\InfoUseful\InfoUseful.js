import React, { useEffect, useState } from "react";
import { makeStyles, createTheme } from "@material-ui/core/styles";
import * as services from "../../services";
import { useParams } from "react-router";
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Grid,
  FormControl,

} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import moment from "moment";
import ThumbDownIcon from '@material-ui/icons/ThumbDown';
import ThumbUpIcon from '@material-ui/icons/ThumbUp';
import FeedbackIcon from '@material-ui/icons/Feedback';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';

const useStyles = makeStyles((theme) => ({
  rightPopup: {
    position: "relative",
    zIndex: "9999",
  },
  list: {
    width: 600,
  }
}));

const InfoUseful = (props) => {
  const { agentDetail } = props;
  const classes = useStyles();
  const params = useParams();
  const urlParams = agentDetail === undefined ? params : agentDetail;
  //const [userId, setUserId] = useState(null);

  const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));
  const [date, setDate] = useState(moment().subtract(0, 'months').startOf('month').format("DD-MM-YYYY"));
  const [ProjectedData, setProjectedData] = useState([]);
  const [TakeFeedback, setTakeFeedback] = useState(true);
  const [ShowThankYou, setShowThankYou] = useState(false);
  const [NegativeFeedbackReason, setNegativeFeedbackReason] = useState(false);
  const [RightPopup, setRightPopup] = useState(false);
  const [productId, setProductId] = useState(null);

  const toggleDrawer = (open) => (event) => {
    /*if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }*/

    setRightPopup(open);
  };

  //const [userDetail, setUserDetail] = useState({});


  const handleFeedback = (event, value) => {
    setTakeFeedback(false);
    setShowThankYou(false);
    setNegativeFeedbackReason(false);
    if (value == "Yes") {
      setShowThankYou(true);
      submitFeedbackReason()
    } else {
      setNegativeFeedbackReason(true);
    }
  };

  const submitFeedbackReason = (value = "") => {//debugger;
    let Source = urlParams.Source;

    var jsonParam = {
      "AgentId": user.AgentId,
      "Response": ShowThankYou || value === "" ? 1 : 0,
      "Reason": value,
      "Source": Source,
    }
    console.log(jsonParam);
    services
      .API_POST(`Incentive/InsertProjectedIncentiveFeedback`, jsonParam)
      .then(response => {
        ////debugger
        if (response && response.Status) {
          setShowThankYou(true);
          setNegativeFeedbackReason(false);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }
  useEffect(() => {
   
  }, []);


  return (
    <>

      <Grid container item xs={12} md={12}>

        <Grid item sm={12} md={12} xs={12}>

          

          {TakeFeedback && (
            <div className="CurrentMonthProjContainer">

            <div className="feedbackBox">
              <FeedbackIcon className="feedbackIcon" />
              <p>Did you find this information useful?</p>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
            </div>
            </div>
          )
          }

          {NegativeFeedbackReason && (
            <div className="CurrentMonthProjContainer">

            <div className="dislike">
              <FeedbackIcon className="feedbackIcon" />
              <p>Did you find this information useful?</p>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
              <div>
                <p>Tell us why</p>
                <FormControl component="fieldset">
                  <RadioGroup
                    aria-label="status"
                    name="status"
                    className="radioBtn"
                  >
                    {" "}

                    <FormControlLabel
                      value="Too much information"
                      control={<Radio onClick={() => submitFeedbackReason("Too much information")} />}
                      label="Too much information"
                      small
                    />
                    <FormControlLabel
                      value="Not able to understand"
                      control={<Radio onClick={() => submitFeedbackReason("Not able to understand")} />}
                      label="Not able to understand"
                      small
                    />
                    <FormControlLabel
                      value="Data inaccuracy"
                      control={<Radio onClick={() => submitFeedbackReason("Data inaccuracy")} />}
                      label="Data Inaccuracy"
                      small
                    />
                  </RadioGroup>
                </FormControl>
              </div>
            </div>
            </div>
          )}

          {ShowThankYou && (
          <div className="CurrentMonthProjContainer">
            <div className="verifiedBox">
              <img src="/images/incentive/verified.svg" />
              <p>Thank you for your response</p>
            </div>
            </div>
          )}


        </Grid>
      </Grid>

    </>
  );
};

export default InfoUseful;
