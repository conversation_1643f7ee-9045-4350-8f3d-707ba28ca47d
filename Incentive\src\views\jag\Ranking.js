import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/JagCriteria";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
} from "@material-ui/core";

import moment from "moment";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const Ranking = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [agentDetail, setAgentDetail] = useState([]);
  //const [highlights, setHighlights] = useState([]);
  //const [productId, setProductId] = useState([]);
  const [showCriteria, setShowCriteria] = React.useState(false);

  const handleClickOpen = (value) => {
    setShowCriteria(value);
    ////debugger;
    const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${userId}/0/${response.ProductId}/${dt}?PageName=JAG&EventName=SeeCriteria`;

    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });

  };

  const getAgentDetail = () => {
    setAgentDetail([]);
    //setHighlights([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Jag/GetAgentDetails/${userId}/${date}`)
      .then(response => {
        if (response && response != "[]") {
          setAgentDetail(response);
          //getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  // const getHighlights = (agentDetail) => {
  //   if (!agentDetail.SuperGroupID) {
  //     return;
  //   } 

  //   services
  //   .API_GET(`Incentive/GetIncentiveHighlights/${agentDetail.SuperGroupID}/${date}`)
  //   .then(response => {
  //     if (response && response != "[]") {
  //         setHighlights(response);
  //       }
  //     })
  //     .catch((err) => {
  //       console.log("Error", err);
  //     });
  // }

  const showCashRewards = (agentDetail) => {

    if (agentDetail == null) {
      return '0';
    }
    else {
      if (agentDetail && agentDetail.CashRewards)
        return agentDetail.CashRewards.toLocaleString('en-IN')
      else
        return "0"
    }
  }
  useEffect(() => {
    getAgentDetail();
  }, [props]
  );

  return (
    <>
      <div className="pading-zero" className={classes.root}>
        <div className="jag-ranking-data">
          <div className="rank-box">
            <ul>
              <li>
                <strong>{agentDetail.UserName || "-"}</strong>
                <span>{agentDetail.Process || "-"}, {
                  Math.round(moment().diff(moment(agentDetail.DOJ), 'months', true)) || "-"} Months</span>
                <p className={agentDetail.MemberType}>{agentDetail.MemberType == 'gold' ? <img src="/images/crown.svg" /> : <img src="/images/silvercrown.svg" />}{agentDetail.MemberType || "-"}</p>
              </li>
              <li>
                {/* <img src="/images/house.svg " /> */}
              </li>
            </ul>
            <div className="bottom-shadw"><h5>Rewards</h5>
              <ul>
                <li><span>Cash <br/>Rewards</span>
                  <strong><i className="fa fa-inr"></i> {showCashRewards(agentDetail)}</strong></li>
                <li><span>Lottery <br/>Tickets</span>
                  <strong>{agentDetail.LotteryTicket}</strong></li>
                <li><span>Bonanza <br/>Tickets</span>
                <strong>{agentDetail.BonusTickets}</strong></li>

                <li><img src="/images/rewards.svg" /></li>
              </ul>
            </div>
          </div>

          <div className="rank-box-inner">
            <ul>
              {(agentDetail.Incentivetext) ? (
                <li>
                  <span>
                    <img src="/images/information-circle.svg" />
                    <span className="numbere">{agentDetail.Incentivetext}</span>
                  </span>
                </li>
              ) : ""}
              <li>
                <span>
                  <button onClick={(e) => handleClickOpen(true)}>
                    See Criteria <i className="fa fa-angle-right"></i>
                  </button>
                </span>
              </li>
            </ul>
          </div>
        </div>


      </div>
      {showCriteria ? <Criteria BU={agentDetail.BU} productId={agentDetail.ProductId || "7"} show={showCriteria} handleClose={() => handleClickOpen(false)} /> : null}
    </>
  );
};

export default Ranking;
