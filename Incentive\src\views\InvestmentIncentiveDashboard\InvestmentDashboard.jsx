import React, { useEffect, useState, useRef } from "react";
import "./InvestmentDashboard.scss";
import Container from '@mui/material/Container';
import Grid from "@mui/material/Grid";
import { Drawer, Button, Tooltip } from '@mui/material'
import ClawbackPopup from "./ClawbackPopup";
import IncentiveCalculation from "./Components/IncentiveCalculation";
import { DashboardContext } from "./Context/Context";
import Payout from "./Components/Payout";
import Leaderboard from "./Components/Leaderboard";
import Bookings from "./Components/Bookings";
import BookingMonth from "./Components/BookingMonth";
import ViewIncentiveCriteria from "./Components/ViewIncentiveCriteria";
import { getUserDetails } from "../Dashboard2024/Utility/Utility";
import { GetAgentDetails } from "../../services";
import {Link, useLocation } from "react-router-dom";
import UnderMaintenance from "./Components/UnderMaintenance";

const InvestmentDashboard = () => {

    const [open, setOpen] = useState(false);
    const [popup, setPopup] = useState(0);
    const [rank, setRank] = useState(null);

    const [monthChosen, setMonthChosen] = useState(null);
    const [agentDetails, setAgentDetails]= useState(null);


    const [calculationEnabler, setCalculationEnabler]= useState({
        missell: true,
        arrearClawBack: true,
        fosAllowance: true,
        incentiveCalculator: true,
        incentiveCriteria : true
    });

    const [showUnavailable,setShowUnavailable] = useState(true);
    
    
    const location = useLocation()
    const empId= (location.state && location.state.empId) || '';
    


    const DashboardData = useRef({
        IncentiveCalculationPopup: null,
        MissellDeduction: null,
        FosAllowance: null,
        ArrearClawback: null,
        IncentiveCriteria: null
    })

    const closeFunc = (popup) => {

        if(popup)
        {
            let obj = calculationEnabler;
            obj[popup]= false;
            setCalculationEnabler(obj);
        }
        setOpen(false);

       
    }

    const handlePopupClick = (e) => {

      
        if (e.target.id == "SeeCal1") {
            setPopup(1)
        }
        else if (e.target.id == "SeeCal2") {
            setPopup(2);
        }
        else if (e.target.id == "popup3") {
            setPopup(3);
        }
        else if (e.target.id == "popup4") {
            setPopup(4);
        }
        else if (e.target.id == "ViewIncentiveCriteria") {
            setPopup(5);
        }

        setOpen(!open);
    }

    const getRank = (rank) => {
        if (rank) {
            setRank(rank);
        }
    }

    const setDashboardData = (data, key) => {
     

        DashboardData.current[key] = data;
    }

    const changeMonthChosen = (month) => {
     

        setMonthChosen(month);
        DashboardData.current={
            IncentiveCalculationPopup: null,
            MissellDeduction: null,
            FosAllowance: null,
            ArrearClawback: null,
            IncentiveCriteria: null
        };

    }

    const setprocessdetails=(processName, tenure)=>{
        setAgentDetails({
          ...agentDetails,
          tenure: tenure,
          processName: processName  
        })
    }


    useEffect(()=>{

            const fetchAgentData=async()=>{
                let agentData= await GetAgentDetails(empId);
                if(agentData 
                    && agentData.errorStatus!=1)
                {
                  
                    setAgentDetails(agentData.data);
                }
             }
             fetchAgentData()
    },[])

//     useEffect(() => {
      
//     }, [popup])
//     useEffect(() => {
//   ;

//     }, [open])


    return (
    
            <DashboardContext.Provider value={{ 
                open: open, 
                popup: popup, 
                handlePopupClick: handlePopupClick,
                closeFunction: closeFunc,
                setRank: getRank, 
                dashboardData: DashboardData.current, 
                setDashboardData: setDashboardData, 
                monthChosen: monthChosen, 
                setMonthChosen: changeMonthChosen, 
                agentDetails: agentDetails, 
                calculationEnabler: calculationEnabler, 
                setprocessdetails: setprocessdetails }}>
                    
               {/* {!showUnavailable ? */}
                <div className="Dashboard2024">
                    <ClawbackPopup />

                    <header>
                        <Container>
                            <Grid container spacing={2}>
                                <Grid item md={4}>
                                    <img src="/images/TermDashboard/pblogo.svg" />
                                    <div className="AgentMsg">
                                        <img src="/images/TermDashboard/hand.png" />
                                        <div>
                                            {agentDetails && <h3><b>Hi {agentDetails.UserName || ''}</b></h3>}
                                            <br/>
                                            {agentDetails && <p>{agentDetails.processName? agentDetails.processName+', ':''}{agentDetails.tenure? agentDetails.tenure+' Months':''} </p>}
                                    
                                        </div>
                                    </div>
                                </Grid>
                                <Grid item md={4} xs={12}>
                                <BookingMonth />
                                </Grid>
                                <Grid item md={4} xs={12}>
                                    <div className="Banner">
                                        <img src="/images/TermDashboard/banner.svg" />
                                        {
                                            rank &&
                                            <div className="Rank">
                                                <p>Rank  <Tooltip title="Rank Based On Your Process" arrow
                                                    placement="bottom"
                                                >
                                                    <i className="fa fa-info-circle"></i>
                                                </Tooltip>

                                                </p>
                                                <h2 style={{fontSize:"19px"}}>#{rank}</h2>
                                            </div>
                                        }
                                    </div>
                                </Grid>

                            </Grid>
                        </Container>

                    </header>
                    {/* {agentDetails && monthChosen && console.log("I think the rendering is happening ", new Date().getTime()) } */}
                    {agentDetails &&
                        monthChosen
                        &&
                        <>

                            <Container className="midLayout">

                                <Grid container spacing={2}>

                                    <IncentiveCalculation />
                                    <Payout />
                                    <Leaderboard />


                                </Grid>
                                <Grid container spacing={2}>
                                    <Bookings />
                                </Grid>


                            </Container>

                            <div  className="ViewIncetiveCriteriaBtn">
                                <Button id="ViewIncentiveCriteria"  onClick={handlePopupClick}> <img id="ViewIncentiveCriteria" src="/images/TermDashboard/incentiveIcon.gif" /> View Incentive Criteria </Button>
                                {popup == 5 &&
                                    <Drawer
                                        anchor="right"
                                        open={open}
                                        onClose={closeFunc}
                                    >
                                         <ViewIncentiveCriteria />
                                    </Drawer>
                                }
                            </div>
                        </>

                    }

                </div> 
             {/* : <UnderMaintenance/>} */}

            </DashboardContext.Provider>
   
    )
}

export default InvestmentDashboard;

