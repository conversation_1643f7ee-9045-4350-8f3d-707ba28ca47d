import React, { useEffect, useState, Fragment } from "react";
import * as services from "../../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
//import CssBaseline from "@material-ui/core/CssBaseline";
//import Pagination from "material-ui-flat-pagination";
//import DialogReson from "../../components/Dialogs/NewQuestion";
import Tooltip from '@material-ui/core/Tooltip';
//import Button from '@material-ui/core/Button';
import Zoom from '@material-ui/core/Zoom';
import VisibilityIcon from '@material-ui/icons/Visibility';
import ClickAwayListener from '@material-ui/core/ClickAwayListener';
import exportFromJSON from 'export-from-json';

import Button from '@material-ui/core/Button';
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem
} from "@material-ui/core";

const HEALTH = 2;



const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },

  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));



const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 6px 16px #3469CB29",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);
const BookingTable = (props) => {

  const classes = useStyles();
  const [showMore, setShowMore] = useState(null);
  const { userId, date, productId } = props;
  const [bookingList, setBookingList] = useState([]);
  const [open, setOpen] = React.useState(-1);

  let bookingData = props.MotorRenewal && props.MotorRenewal[4] && 
      props.MotorRenewal[4].Status && props.MotorRenewal[4].Response != "[]" && JSON.parse(props.MotorRenewal[4].Response);

  let bookingSortData = []
  /*
  const [offset, setOffset] = useState(0);
  const handleClick = (offset) => {
    setOffset(offset);
  };
  */

  const ShowEligibleCol = ()=>{    
 
    var d1 = new Date(date);
    var d2 = new Date("01-11-2021");
    if(d1>= d2){
      return true;
    }
    return false;

  }

  const HtmlTooltip = withStyles((theme) => ({
    // tooltip: {
    //   display: 'block',
    //   backgroundColor: '#fff',
    //   color: 'rgba(0, 0, 0, 0.87)',
    //   maxWidth: 320,
    //   fontSize: theme.typography.pxToRem(12),
    //   "& ul":{
    //     "& li":{
    //       listStyle:"none",
    //     }
    //   },
    // },

  }))(Tooltip);

  const sortBookingByDate = (data) => {
    return _.orderBy(data, ['BookingDate'], ['desc']);
  };

 

  const handleBookingsExport = () => {
    const fileName = userId + "_bookings";
   
    const data = bookingSortData.map((item) => {
      let booking = {
        LeadId : item.LeadId,
        BookingDate : item.BookingDate,
        InsurerName : item.SupplierName,
        BookingStatus : item.StatusName
      }

      if(productId == 115){
        booking.InsurerCategory = item.InsurerCategory ? item.InsurerCategory : "-"
      }
  
      if(productId == 217) {
        booking.PaymentPeriodicity =  item.PaymentPeriodicity;
        booking.PolicyType =  item.PolicyType;
        booking.Category =  item.Category;
        booking.Eligible =  item.Eligible;
        booking.Remarks = item.Remarks;
      } 
      else if(productId !== 117) {
        booking.APE = item.APE ? Math.round(parseFloat(item.APE)) : 0;
        booking.WeightedAPE = item.ProjectedWeightedAPE ? Math.round(parseFloat(item.ProjectedWeightedAPE)) : 0;
        if(productId === HEALTH){
          booking.IsAddOn = item.IsAddOn == 1 ? 'Yes' : 'No';
          booking.EligibleforInc = item.EligibleforInc == 1 ? 'Yes' : 'No';
          booking.MisCommitment = item.MisCommitment == 1 ? 'Yes' : 'No';
          booking.IsFLC = item.IsFLC == 1 ? 'Yes' : 'No';
        }
      } 
      else {
        booking.PlanType =  item.PlanType;
        booking.Eligible =  item.EligibleforInc == 1 ? 'Yes' : 'No';
        booking.InsurerCategory =  item.InsurerCategory;
      }
      
    //   if(ShowEligibleCol())
    //   {
    //     booking.EligibleforInc =  item.EligibleforInc == 1 ? 'Yes' : 'No';
    //     booking.Remarks = item.Remarks;
    //   }

      return booking;
    });
    const exportType =  exportFromJSON.types.xls;
    exportFromJSON({ data, fileName, exportType });
  }

//   useEffect(() => {
//     setBookingList([]);
//     if (!userId) {
//       return;
//     }

//     services
//       .API_GET(`Incentive/GetIncentiveBookings/${userId}/${date}`)
//       .then(response => {
//         if (response.Status && response.Response != "[]" && response.Response.length > 0) {
//           setBookingList(sortBookingByDate(JSON.parse(response.Response)));
//         }
//       })
//       .catch((err) => {
//         console.log("Error", err);
//       });
//   }, [userId, date]);

  



  const renderTooltipWrapper = (item) => {
    if (!item.Remarks || JSON.parse(item.Remarks).length == 0) return 'No Remarks';
    return <Fragment>
      <div>
        <ul className="tooltip-popup">
          {
            JSON.parse(item.Remarks).map((breakup, indx) =>
              <li key={indx + 1} className={breakup.IsApplied ? "yes" : "no"}>
                <table style={{ border: "1px solid #546e7a4a", width: "100%" }}>
                  <tr style={{ padding: "15px" }}>
                    <td style={{ width: "80%", borderRight: "1px solid #ccc", color: "#303030" }} >{breakup.Text}</td>
                    <td style={{ width: "20%", textAlign: "right", color: "#00458b" }} >{breakup.Value}</td>
                  </tr>
                </table>
              </li>
            )
          }
        </ul>
      </div>
    </Fragment>
  }

  const [width, setWidth] = useState(window.innerWidth);

function handleWindowSizeChange() {
    setWidth(window.innerWidth);
    
}
useEffect(() => {
    window.addEventListener('resize', handleWindowSizeChange);
    return () => {
        window.removeEventListener('resize', handleWindowSizeChange);
    }
}, []);

  
  const [counter, setCounter] = useState(5);



  const incrementCounter = () => {
    
    if(width < 768){
      
    setCounter(counter+5)
    }
    else{
      counter = bookingSortData.length
    }
  }

  
  
  function ButtonIncrement(props){
    let result = [];
    
    if(width < 768){
      result.push(<button onClick = {props.onClickFunc} className='load-more'> Load more </button>)
      }
    
    return result

    
  }

  if(bookingData && bookingData.length>0){
    let item = bookingData;
    bookingSortData = sortBookingByDate(item)
    
  }

  return bookingSortData && bookingSortData.length > 0 && (
    <Fragment>
       
      <ClickAwayListener onClickAway={() => setOpen(-1)}>
        <Card className={classes.card +" bookingTable"}>
          <div className="pading-zero" className={classes.root}>
            <div className="booking-table">
              
              <h6>
                Bookings Breakdown
                <span className='mobile-view'>Details of bookings</span>
              </h6>
              <Button 
                variant="outlined"
                onClick = {handleBookingsExport}
                style={{float:'right', marginTop:'-25px'}}
              >
                Export
              </Button>

              <ul className="booking-table-head">
                {/*<h6>Bookings Breakdown</h6>*/}
                <li className="booking-SNo">S.No</li>
                <li>Booking ID</li>
                <li>Booking Date</li>
                <li>Insurer Name</li>
                <li>Booking Status</li>
                {productId == 115  && <li>InsurerCategory</li>}
                {productId != 117 && productId != 217 && productId != 117 && <li>APE</li>}
                {productId != 117 && productId != 217 && <li>Weighted APE</li>}
                {productId == 117 && productId == 217 && <li>Plan Type</li>}
                {productId == 117 && productId == 217 && <li>Insurer Category</li>}               
                {productId == HEALTH && <li>AddOn</li>}
                {productId == -2 && <li>MisCommitment</li>}
                {productId == -2 && <li>FLC</li>}
                {/* {(ShowEligibleCol() || productId == HEALTH) && <li>Eligible for Incentive</li>} */}
                {<li>PaymentPeriodicity</li>}
                {<li>PolicyType</li>}
                {<li>Category</li>}
                {<li>Eligible</li>}
                {<li>Remarks</li>}
              </ul>

              
              {width < 768 && <div className="table-scroll scrool-toll">
                {
                  bookingSortData.slice(0,counter)
                  
                  .map((item, index) =>

                    <ul key={index + 1} className="booking-table-body">
                      <li className="booking-SNo">
                        <span>S.No</span>
                        <strong>{index + 1}</strong>
                      </li>
                      <li>
                        <span>Booking ID</span>
                        {item.LeadId}{item.E2E == 'No' ? '(NON E2E)' : ''}{item.IsAddOn == 1 ? '(AddOn)' : ''}
                      </li>
                      <li>

                        <span>Booking Date</span>
                        <strong>
                          {/* {item.BookingDate ? (new Intl.DateTimeFormat("en-AU", {
                            day: "2-digit",
                            month: "short",
                            year: "numeric",
                          }).format(
                            Date.parse(item.BookingDate || new Date())
                          )) : "-"} */}
                          {item.BookingDate ? item.BookingDate : "-"}
                        </strong>
                      </li>
                      <li>
                        <span>Insurer Name</span>
                        <strong>{item.SupplierName || "-"}</strong>
                      </li>
                      <li>
                        <span>Booking Status</span>
                        <strong>{item.StatusName || "-"}</strong>
                      </li>
                      {productId == 115 &&<li>
                        <span>Insurer Category</span>
                        <strong>{item.InsurerCategory || "-"}</strong>
                      </li>}

                      {productId != 117 && productId != 217 &&
                        <li>
                          <span>APE</span>
                          <strong><i className="fa fa-inr"></i> {item.APE ? Math.round(item.APE).toLocaleString('en-IN') : 0}</strong>
                        </li>}
                      {productId != 117 && productId != 217 &&
                        <li>
                          <span>Weighted APE</span>
                          {/* <WrapperTooltip
                            disableFocusListener
                            TransitionComponent={Zoom}
                            placement="top"
                            arrow
                            onClose={() => setOpen(-1)}
                            open={index == open ? true : false}
                            title={item.Remarks && renderTooltipWrapper(item)}
                            classes={{ tooltip: classes.customWidth }}
                          > */}
                            <strong
                              // onClick={(e) => { e.stopPropagation(); setOpen(index) }}
                              // onMouseEnter={(e) => { e.stopPropagation(); setOpen(index) }}
                              // onMouseLeave={(e) => { e.stopPropagation(); setOpen(-1) }}
                              className="weighted-price"><i className="fa fa-inr"></i> {item.ProjectedWeightedAPE ? Math.round(item.ProjectedWeightedAPE).toLocaleString('en-IN') : 0} 
                              {/* <VisibilityIcon /> */}
                              </strong>
                          {/* </WrapperTooltip> */}
                        </li>
                      }
                      {productId == 117 && productId == 217 && <li>
                        {item.PlanType}
                      </li>}
                      {productId == 117 && productId == 217 && <li>
                        {item.InsurerCategory}
                      </li>}
                      
                      
                      {<li>
                        {item.PaymentPeriodicity}
                      </li>}
                      {<li>
                        {item.PolicyType}
                      </li>}
                      {<li>
                        {item.Category}
                      </li>}
                      {<li>
                        {item.Eligible}
                      </li>}
                      {<li>
                        {item.Remarks != "" ? <span>Remarks</span>: <></>}
                        {item.Remarks}
                      </li>}
                      
                    </ul>
                  )
                }
                {counter <= bookingSortData.length ? <ButtonIncrement onClickFunc={incrementCounter}/> : ""}
              </div>
              
              }

              {width > 768 && <div className="table-scroll scrool-toll">
                {
                  bookingSortData
                  
                  .map((item, index) =>

                    <ul key={index + 1} className="booking-table-body">
                      <li className="booking-SNo">
                        <span>S.No</span>
                        <strong>{index + 1}</strong>
                      </li>
                      <li>
                        <span>Booking ID</span>
                        {item.LeadId}{item.E2E == 'No' ? '(NON E2E)' : ''}{item.IsAddOn == 1 ? '(AddOn)' : ''}
                      </li>
                      <li>

                        <span>Booking Date</span>
                        <strong>
                          {/* {item.BookingDate ? (new Intl.DateTimeFormat("en-AU", {
                            day: "2-digit",
                            month: "short",
                            year: "numeric",
                          }).format(
                            Date.parse(item.BookingDate || new Date())
                          )) : "-"} */}
                          {item.BookingDate ? item.BookingDate : "-"}
                        </strong>
                      </li>
                      <li>
                        <span>Insurer Name</span>
                        <strong>{item.SupplierName || "-"}</strong>
                      </li>
                      <li>
                        <span>Booking Status</span>
                        <strong>{item.StatusName || "-"}</strong>
                      </li>
                      {productId == 115 &&<li>
                        <span>Insurer Category</span>
                        <strong>{item.InsurerCategory || "-"}</strong>
                      </li>}

                      {productId != 117 && productId != 217 &&
                        <li>
                          <span>APE</span>
                          <strong><i className="fa fa-inr"></i> {item.APE ? Math.round(item.APE).toLocaleString('en-IN') : 0}</strong>
                        </li>}
                      {productId != 117 && productId != 217 &&
                        <li>
                          <span>Weighted APE</span>
                          {/* <WrapperTooltip
                            disableFocusListener
                            TransitionComponent={Zoom}
                            placement="top"
                            arrow
                            onClose={() => setOpen(-1)}
                            open={index == open ? true : false}
                            title={item.Remarks && renderTooltipWrapper(item)}
                            classes={{ tooltip: classes.customWidth }}
                          > */}
                            <strong
                              // onClick={(e) => { e.stopPropagation(); setOpen(index) }}
                              // onMouseEnter={(e) => { e.stopPropagation(); setOpen(index) }}
                              // onMouseLeave={(e) => { e.stopPropagation(); setOpen(-1) }}
                              className="weighted-price"><i className="fa fa-inr"></i> {item.ProjectedWeightedAPE ? Math.round(item.ProjectedWeightedAPE).toLocaleString('en-IN') : 0} 
                              {/* <VisibilityIcon /> */}
                              </strong>
                          {/* </WrapperTooltip> */}
                        </li>
                      }
                      {productId == 117 && productId == 217 && <li>
                        {item.PlanType}
                      </li>}
                      {productId == 117 && productId == 217 && <li>
                        {item.InsurerCategory}
                      </li>}
                      
                      
                      {<li>
                        {item.PaymentPeriodicity}
                      </li>}
                      {<li>
                        {item.PolicyType}
                      </li>}
                      {<li>
                        {item.Category}
                      </li>}
                      {<li>
                        {item.Eligible}
                      </li>}
                      {<li>
                        {item.Remarks != "" ? <span>Remarks</span>: <></>}
                        {item.Remarks}
                      </li>}
                      
                    </ul>
                  )
                }
                
              </div>}
              
          

            </div>

          </div>
          
        </Card>
      </ClickAwayListener>
      
    </Fragment>
  );
};

export default BookingTable;