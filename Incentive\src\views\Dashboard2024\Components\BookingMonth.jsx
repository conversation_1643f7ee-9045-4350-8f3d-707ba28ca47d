
import React,{useState, useEffect, useContext, useRef} from 'react';
import Grid  from '@mui/material/Grid';
import Select from '@mui/material/Select';
import { MenuItem } from '@mui/material';
import { DashboardContext } from '../Context/Context';


const getLast9formattedMonthNames=(noMonths)=>{
  const currentDate = new Date();
  const monthsNames = [];
  let formattedMonthDisplay=[];
  
  let currYear= currentDate.getFullYear();
  let  currYearLastTwoDigits =  currYear.toString().slice(-2);
  
  for (let i = 0; i < noMonths; i++) {
    const previousMonth = new Date(currentDate);
    previousMonth.setMonth(currentDate.getMonth() - i);
    
    const monthName = new Intl.DateTimeFormat('en', { month: 'long' }).format(previousMonth);
    let lastAddedMonth = ""+monthsNames[monthsNames.length-1];
    if(monthName.toLowerCase()=='december' && lastAddedMonth.toLowerCase()=='january')
    {
        if(currYear>2024)
        {
        currYear= currYear-1;
        currYearLastTwoDigits = currYear.toString().slice(-2);
        }
        else{
            break;
        }

    }
    monthsNames.push(monthName);
    let monthObj = {
        display: monthName+'`'+currYearLastTwoDigits,
        value: monthName.substring(0,3).toUpperCase() + currYear.toString()
    }
    formattedMonthDisplay.push(monthObj);
  }
 return formattedMonthDisplay;
}


const BookingMonth=()=>{


const options = useRef([]);
const [optionChosen, setOptionChosen] = useState("");

const dashboardData= useContext(DashboardContext);

useEffect(()=>{   
    let months = getLast9formattedMonthNames(12);

    options.current=months.slice(3, months.length) ;
  
    // options.current= months;
    // console.log("The monmths are ", months);
    setOptionChosen(options.current[0].value);
    
},[])



useEffect(()=>{
    if(optionChosen)
    {
        dashboardData.setMonthChosen(optionChosen);
    }

},[optionChosen])

const handleMonthChange=(e)=>{
  
    setOptionChosen(e.target.value);

}



return(
    <div className="text-center">
    <h2>My Incentive Dashboard</h2>                                
        <span className="month-view">
            Booking Month:
            <Select 
            labelId="bookingMonth"
            id="bookingMMonth"
            value={optionChosen}
            label="Select"
            onChange={handleMonthChange}
            className="BookingMonth"
            >
            {
                options.current.length>0 &&

                options.current.map((option)=>{

                    return(
                        <MenuItem value={option.value} className="MonthName">
                        {option.display}
                        </MenuItem>
                    )
                    
                })
                
            }

            </Select>
        </span>
    
    </div>
)
}
export default BookingMonth;