import React, { useEffect, useState, Fragment } from "react";
import moment from "moment";
import * as services from "../../services";

const RaiseTicket = (props) => {
  const [feedbackurl, setfeedbackurl] = useState(null);
  const [userId, setUserId] = useState(null);

  useEffect(() => {
    const tokenPath = window.location.pathname.split("/");
    if (tokenPath.length >= 3) {
      let urlToken = tokenPath[2];
      setUserId(urlToken);
      setfeedbackurl(
        "https://matrixticket.policybazaar.com/Landing.html#/matrix/LandingPage/" +
          urlToken +
          "/ticket"
      );
    }
  }, []);

  const handlefeedbackbutton = (event) => {
    const dt = moment()
      .subtract(1, "months")
      .startOf("month")
      .format("DD-MM-YYYY");
    services
      .API_GET(
        `Incentive/InsertAgentIncentivLog/${userId}/0/7/${dt}?PageName=FAQ&EventName=Raise a Ticket`
      )
      .then((response) => {})
      .catch((err) => {});
  
    window.open(feedbackurl);
  };
  

  return (
    <div className="rightside-box">
      <img src="/images/tickets.svg" />
      {/* <a href={feedbackurl} target="_blank" onClick={handlefeedbackbutton}>Raise a Ticket</a> */}
      <a onClick={handlefeedbackbutton}>Raise a Ticket</a>
      {/*<button type="button" onClick={handleOpen}>
    Raise a Ticket
    </button> */}
    </div>
  );
};

export default RaiseTicket;
