.MuiDrawer-root {
    z-index: 10000000000 !important;
}

.CalculationDeatilsPopup {
    color: #000;
    width: 700px;
    margin: 0;
    padding: 20px 25px;
    position: relative;
    background: #fff;

    .close {
        position: absolute;
        right: 16px;
        top: 25px;
        cursor: pointer;
        display: block;

        img {
            filter: contrast(0.2);
            -webkit-filter: contrast(0.2)
        }
    }

    h5 {
        color: #253858;
        font-family: Poppins;
        font-size: 22px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .DetailsBox {
        border-radius: 24px;
        border: 1px solid rgba(0, 0, 0, 0.12);
        padding: 15px 15px 10px;
        margin-top: 20px;
        position: relative;

        img {
            top: 0px;
            position: absolute;
            right: 0px;

        }

        h3 {
            color: #253858;
            font-family: Poppins;
            font-size: 18px;
            font-style: normal;
            font-weight: 700;
            line-height: normal;

        }

        ul {
            display: flex;
            flex-wrap: wrap;
            margin-top: 1.22rem;

            li {
                color: #253858;
                font-family: Poppins;
                font-size: 15px;
                line-height: 45px;
                font-style: normal;
                font-weight: 600;
                border-bottom: 1px solid #0000001f;
                width: 55%;

                &:nth-child(2n + 2) {
                    text-align: right;
                    font-weight: 500;
                    width:45%;
                }

                &:last-child {
                    border-bottom: none;
                }

                &:nth-last-child(2) {
                    border-bottom: none;
                }


            }

            .FinalDetails {
                width: 100%;
                background: rgba(0, 101, 255, 0.05);
                padding: 5px 16px 5px 16px;

                ul {
                    margin-top: 0px;
                }

            }
          
        }
        p {
            color: rgba(37, 56, 88, 0.6);
            font-family: Poppins;
            font-size: 12px;
            font-style: normal;
            font-weight: 600;
            text-align: right;
            line-height: normal;
            margin-top: 10px;
        }
    }
}

@media screen and (max-width: 750px) {
    .CalculationDeatilsPopup{
        width:100%;
        padding: 20px 15px;
        h5{
            font-size: 20px;
        }
        .DetailsBox{
            img{
                width: 55px !important;
            }
        li{
            width: 100% !important;
            &:nth-child(2n + 2) {
                text-align: left !important;
            }
        }
        }
    }
}