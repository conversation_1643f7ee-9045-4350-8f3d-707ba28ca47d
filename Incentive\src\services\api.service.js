import Axios from 'axios'
import axios from "axios";

import STORAG<PERSON> from '../store/storage'
import { CONFIG } from './../appconfig'
import { getCookie, getUserDetails } from '../views/Dashboard2024/Utility/Utility';
export const base_url = CONFIG.API_BASE_URL;
// export const base_url = 'http://localhost:53481/api';
let axiosInstance = Axios.create({
    baseURL: base_url,
    //    withCredentials: true
});
// console.log("CONFIG.COOKIE_LOGIN" , CONFIG.COOKIE_LOGIN)
// if (CONFIG.COOKIE_LOGIN) {
axios.defaults.withCredentials = true;
//     axiosInstance.defaults.headers['Content-Type']='application/json';
axios.defaults.headers['Access-Control-Allow-Credentials'] = true
// }

export const API_GET = (url, headerParam) => {

    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {}
            if (headerParam) {
                headers = { ...headerParam }
            }
            if (token) {
                headers['Token'] = `${token}`
                
                //headers['cookie'] = document.cookie
            }
            headers['token'] ='4c26c9e9-64e9-4aac-b981-c167b2cb983a'; 
            const config = {
                headers: headers
            };

            axios.get(base_url + "/" + url, config).then((res) => {

                if (res.status && res.status == 401) {
                    resolve({ status: 401, message: "Unauthorised access" });
                } else {
                    resolve(res.data)
                }
            }, (err) => {
                console.log(err);
                reject(err.data)
            })
        })
    })


}
export const API_POST = (url, data) => {
    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {}
            if (token) {
                headers['Token'] = `${token}`
            }
            axios({
                method: 'post',
                url: base_url + "/" + url,
                data: data,
                headers
            }).then((res) => {
                resolve(res.data)
            }, (err) => {
                reject(err.data)
            })
        })
    })


}

export const API_PUT = (url, data) => {
    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {}
            if (token) {
                headers['x-access-token'] = `${token}`
            }
            axios({
                method: 'put',
                url: base_url + "/" + url,
                data: data,
                headers
            }).then((res) => {
                resolve(res.data)
            }, (err) => {
                reject(err.data)

            })
        })
    })


}

export const API_DELETE = (url) => {
    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {}
            if (token) {
                headers['x-access-token'] = `${token}`
            }
            axios({
                method: 'delete',
                url: base_url + "/" + url,
                headers
            }).then((res) => {
                resolve(res.data)
            }, (err) => {
                reject(err.data)

            })
        })
    })

}

export const API_BASE_URL_GET = (baseUrl, url) => {
    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {}
            if (token) {
                headers['x-access-token'] = `${token}`
            }
            axios({
                method: 'get',
                url: base_url + "/" + url,
                headers
            }).then((res) => {
                resolve(res.data)
            }, (err) => {
                reject(err.data)

            })
        })
    })


}
export const API_BASE_URL_POST = (baseUrl, url, data) => {
    return STORAGE.getAuthToken().then((token) => {
        return new Promise((resolve, reject) => {
            let headers = {
                'Content-Type': 'application/json',
            }
            headers['Content-Type'] = 'application/json';
            if (token) {
                headers['x-access-token'] = `${token}`
            }
            axios({
                method: 'post',
                url: base_url + "/" + url,
                data: data,
                headers
            }).then((res) => {
                resolve(res.data)
            }, (err) => {
                reject(err.data)
            })
        })
    })
}



export const GETAPI_INCENTIVE =async (method)=>{
    try {
    let headers ={
        'Content-Type':'application/json',
        'method' : 'Dummy'
    }
    let response= await axios.get(url, {headers: headers});

    if(response.status%100==2)
    {
            return {
                errorStatus: 0,
                data:  response && response.data && response.data.data || ""
            }
    }
    else {
        return {
          errorStatus: 1,
          data: 'Error executing'
        }
    }
    }
    catch(e)
    {
        return {
            errorStatus:1,
            data: e
        }
    }    
}


export const POSTAPI_INCENTIVE = async (method, body)=>{
    try{
        let headers={
            'Content-type':'application/json',
            "MatrixToken":"eyJVc2VySWQiOiI5MDYyOSIsIkVtcGxveWVlSWQiOiJQVDAxODQzIiwiQXN0ZXJpc2tUb2tlbiI6IjdhMDk5MWU3LTg3NjktNDk2MS1hNDlhLTlkOTRlZDYxMDQxMy0xMjM0MzMiLCJHcm91cElkIjoyNjY4LCJSb2xlSWQiOiIyIn0=",
            "token":"4c26c9e9-64e9-4aac-b981-c167b2cb983a",
            "AgentId":getUserDetails('UserId')
        }        
        // console.log("The method is ", body);
        let url = "https://incentiveapi.policybazaar.com/api/Incentive/CallAPI";
        let response = await axios.post(url,body,{headers: headers});
        // console.log("The response status is ", response.status/100);
        if(response.status/100==2)
        {
            if(response.data && response.data.errorStatus)
            {
                return {
                    errorStatus:1,
                    data: 'Error Occured'
                }
            }
            return{
                errorStatus: 0,
                data: response && response.data && response.data.data || ""
            }
        }
        else {
            return {
              errorStatus: 1,
              data: 'Error executing'
            }
        }
    }
    catch(e)
    {
        return {
            errorStatus: 1,
            data: e
        }
    }
}


export const GetAgentDetails = async(EmployeeId)=>{
    try{
        
        let headers={
            'Content-type':'application/json',
            // "MatrixToken": getCookie('MatrixToken'),
            "token":"4c26c9e9-64e9-4aac-b981-c167b2cb983a",
            // "token" : getUserDetails('UserId')
        }
        let employeeId= EmployeeId || getUserDetails('EmployeeId');
        let url = 'https://incentiveapi.policybazaar.com/api/Incentive/GetAgentDataByEmpId/'+employeeId+'/';
       
        let today = new Date();
        let dd = String(today.getDate()).padStart(2, '0');
        let mm = String(today.getMonth() + 1).padStart(2, '0');
        let yyyy = today.getFullYear();

        let currentDate = `${dd}-${mm}-${yyyy}`;
        
        // url = url+date.getDate() + '-'+date.getMonth()+ '-'+ date.getFullYear();
        url= url+ currentDate;
        let response= await axios.get(url,{headers: headers});
        
        if(response.status/100==2)
        {
            if(response.data && !response.data.AgentId)
            {
                return{
                errorStatus: 1,
                msg: "No such agent present in the system"
                }
            }
            return {
                errorStatus: 0,
                data: response.data || ""
            }
        }
        else{
            return{
                errorStatus: 1,
              data: 'Error executing'
            }
        }
        
    }
    catch(e)
    {
        console.log("e is ",e);
        return{
            errorStatus: 1,
            data: e
        }
    }
}


export const GetCriterialURL = async(ProductId, CriteriaMonth)=>{
    try{
        
        let headers={
            'Content-type':'application/json',
            // "MatrixToken": getCookie('MatrixToken'),
            // "token":"4c26c9e9-64e9-4aac-b981-c167b2cb983a",
            // "token" : getUserDetails('UserId')
        }

        

        // let url = 'https://incentiveapi.policybazaar.com/api/Incentive/GetCriteriaURL/'+2+'/2025-01-01';
        
        let url = 'https://incentiveapi.policybazaar.com/api/Incentive/GetCriteriaURL/'+ProductId+'/'+CriteriaMonth;
       
        let response= await axios.get(url,{headers: headers});
        
        
        return {
            errorStatus: 0,
            data: response.data
        }

    }
    catch(e)
    {
        console.log("e is ",e);
        return{
            errorStatus: 1,
            data: e
        }
    }
}
