import React, { useEffect, useState } from "react";
import CalculationDetails from "../CalculationDetails/CalculationDetails";
import ProgressGrowthCalc from "../ProgressGrowthCalc/ProgressGrowthCalc";
import { GROWTH_PERC_MAP, CUTOFF_DATE } from "../JagConstants2023";
import moment from "moment";
import * as services from "../../../services";
import { Tooltip, Zoom } from '@material-ui/core';
import { withStyles } from "@material-ui/core/styles";

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    color: "#0164ff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);

const ToolTipFactory = ({ renderTooltip }) => {
  return (<WrapperTooltip
    disableFocusListener
    TransitionComponent={Zoom}
    placement="top"
    arrow
    title={renderTooltip()}
    enterTouchDelay={0}
  // classes={{ tooltip: classes.customWidth }}
  >
    <i className="fa fa-info-circle"></i>
  </WrapperTooltip>
  )
}

const renderTooltipTotalAPE = (BUId) => {
  return (<>
  {
    ![65, 67].includes(BUId) &&   <p>Advisors with Date of Joining after 1st Apr 2023,
    their last year issued ape has been prorated. </p>
  } 
  {
    [65, 67].includes(BUId) &&   <p>Advisors with Date of Joining after 1st Apr 2023,
    their last year issued bookings has been prorated. </p>
  } 
  </>
  )
}

const containerStyle = {
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center',
  color: '#253858',
  fontFamily: 'Poppins',
  fontSize: '16px',
  fontStyle: 'normal',
  fontWeight: 600,
  lineHeight: 'normal',
  marginBottom: '20px',
};

const leftSideStyle = {
  
};

const rightSideStyle = {
  textAlign: 'right',
  color: 'red',
  fontFamily: 'Poppins',
  fontSize: '12px',
  fontStyle: 'normal',
  fontWeight: 600,
  height: '24px'
};


let rupee = new Intl.NumberFormat('en-IN', {
  style: 'currency',
  currency: 'INR',
  maximumFractionDigits: 0
});

const numberOfDays = year => (((year % 4 === 0) && (year % 100 !== 0)) || (year % 400 === 0)) ? 366 : 365;

const daysThisYear= numberOfDays(new Date().getFullYear());



const AgentDetails = ({ agentDetails, agentData }) => {

  const [calculationDrawerOpen, setCalculationDrawerOpen] = useState(false);
  const [slabforTarget, setSlabForTarget] = useState({});
  const [reqForRewards, setReqForRewards] = useState({
    ReqMonthlyIssuedApeForCash: '',
    ReqMonthlyIssuedBookingsForCash: '',
    YearlyBookingsForCash: '',
    YearlyApeForCash: ''
  });

  const {
    UserName,
    EmpId,
    TotalAPE_CurrYear,
    TotalBKG_CurrYear,
    SecondaryAPE,
    SecondaryBKG,
    SecondarySourceBkgs,
    ULIP,
    TRAD,
    ProjectedIssuedAPE_CurrYear,
    // ProjectedIssuedBKG_CurrYear,
    IssuedAPE_PrevYear,
    IssuedBKG_PrevYear,
    ActualG1,
    CashRewards,
    CurrentMonthSourcing,
    LotteryTicket,
    MemberType,
    Issuance,
    ProductId,
    BUId,
    RequiredAPE,
    DaysDiff

  } = agentData || {};

  // const GrowthRequiredForCash = GROWTH_PERC_MAP[MemberType && MemberType.toLowerCase()];

  const growthTarget = slabforTarget && slabforTarget.Target || 0;
  console.log('gg', growthTarget)
  // console.log("currentmonthsourcing", agentData)

  useEffect(() => {
    if (BUId && MemberType) {
      services
        .API_GET(`JAG/GetJAGGrowthSlabMaster/${BUId}/${MemberType}`).then(result => {
          if (result) {
            if (result.Status && !result.Status) {
              alert("Your session is expired, Please login again");
              window.location.href = "http://mobilematrix.policybazaar.com/PGV/";
            } else {
              if (result.Response) {
                let slabs = JSON.parse(result.Response) || [];
                let minSlab = slabs[0] || {};
                setSlabForTarget(minSlab);
              }
            }
          }
        })
        .catch((err) => {
          console.log("Error", err);
        });
    }

    if (MemberType) {
      const DaysDiffFromCutOff = moment().diff(CUTOFF_DATE, 'days');

      const YearlyApeForCash = (IssuedAPE_PrevYear + ULIP) * (1 +growthTarget) ;
      // const ReqSourcedApeForCash = YearlyApeForCash - (TotalAPE_CurrYear + SecondaryAPE);
      const ReqMonthlyIssuedApeForCash = (YearlyApeForCash ) / 12;

      const YearlyBookingsForCash = (IssuedBKG_PrevYear + TRAD) * (1 + growthTarget);
      // const ReqSourceBookingsForCash = YearlyBookingsForCash - (TotalBKG_CurrYear + SecondaryBKG);
      const ReqMonthlyIssuedBookingsForCash = (YearlyBookingsForCash ) / 12;
      setReqForRewards(prevState => ({
        ...prevState,
        ReqMonthlyIssuedApeForCash,
        ReqMonthlyIssuedBookingsForCash,
        YearlyApeForCash,
        YearlyBookingsForCash
      }))
    }
  }, [BUId, MemberType, growthTarget]);

  const HandleOpenCalculation = (e) => {
    e.preventDefault()
    setCalculationDrawerOpen(!calculationDrawerOpen);
  }

  const HandleViewCriteria = (e) => {
    e.preventDefault()
    window.scrollTo(0, 1900);
  }

  const getDate=()=>{
  try{
  const today = new Date();
  const targetDate = new Date(today.getFullYear(), 5, 1); 

  const diffTime = targetDate - today;
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  }
  catch{
    return '-'
  }
  }


  let cashRewardsInvestment= `You have ${daysThisYear-31-DaysDiff} days left to make Jag Rewards. Push your limit and maximize your rewards`;
  let noCashRewardsInvestment=`You have ${daysThisYear-31-DaysDiff} days left to make Jag Rewards. You need to do a minimum of ${rupee.format(RequiredAPE)} APE per day to make rewards`;
  let cashRewards=`You have ${daysThisYear-DaysDiff} days left to make Jag Rewards. Push your limit and maximize your rewards`;
  let noCashRewards=`You have ${daysThisYear-DaysDiff} days left to make Jag Rewards. You need to do a minimum of ${rupee.format(RequiredAPE)} APE per day to make rewards`;
  let noCashRewardsMotor=`You have ${daysThisYear-DaysDiff} days left to make Jag Rewards. You need to do a minimum of ${RequiredAPE} Bookings per day to make rewards`;

  let msgToDisplay= `You have ${getDate()} days left to make Jag Rewards. Push your limit and maximize your Cash Rewards and Lottery Tickets.`;

  return (
    <>
      <h2 className="AgentDetails"><p>Superstar</p>
        {UserName || '-'} <span>({EmpId || '-'})</span></h2>
    
    <div className="DeadlineReminder">
      <span>
        {msgToDisplay}

        {/* <a href='https://forms.gle/Tmj2QKAcwGCFiEN69'>JAG Survey</a>  */}
      </span>
    </div>
    {/* <div className="DeadlineReminder">
    <span>JAG 5.0 Calculations has been Closed. JAG Lottery Event is Schedule on 25th June. JAG Cash Rewards will be credited in the June Payout</span> */}
    {/* {   agentData  &&
        BUId!== 55 &&

       ([52,57].includes(BUId)
       (['gold','silver'].includes(MemberType.toLowerCase())?
         (CashRewards>0 ? 

         <span>{cashRewardsInvestment}</span> 
         :
          <span>{noCashRewardsInvestment}</span>
         ) 
         :
         (CashRewards>0?

          <span>{cashRewards}</span>
          :
          <span>{noCashRewards}</span>
          )
      )
        :

      (BUId!==54?
        (CashRewards>0 ?
          <span>{cashRewards}</span>
          :
          <span>{noCashRewards}</span>
        )
        :
        (CashRewards>0?
          <span>{cashRewards}</span>
          :
          <span>{noCashRewardsMotor}</span>
        )
        )
       )

    } */}
    
     {/* </div> */}
    
      {/* Progress Score */}
      <div className="ProgressScore">
        <div style={containerStyle}>
        <div style={leftSideStyle}>
          <p>Progress Score</p>
        </div>
        {agentData && ProductId===115 && [52,57].includes(BUId) && ['gold','silver'].includes(MemberType.toLowerCase()) && 
        <div style={rightSideStyle}>
        <p className="GrowthComment">To Calculate Growth only, Issued APE from April till Feb will be considered for this year and last year.</p>
        <p>However to calculate Cashrewards, APE From Apr'24 till Mar'25 will be considered.</p>
        </div>
        }
        </div>

        <ul>
          <li>
            <img src="/images/jag2023/icon1.svg" />
            {/* {console.log(BUId)} */}
            {![65, 67].includes(BUId) && <>Sourced APE <span>(FY 2024-25)</span> <h4>₹{((TotalAPE_CurrYear + SecondaryAPE) / 1e5).toFixed(0).toLocaleString('en-IN')} Lakhs</h4></>}
            {[65, 67].includes(BUId) && <>Sourced Bookings <span>(FY 2024-25)</span> <h4>{(TotalBKG_CurrYear?(TotalBKG_CurrYear+SecondarySourceBkgs).toLocaleString('en-IN'):0)}</h4></>}
            
          </li>
          <li>
            <img src="/images/jag2023/icon2.svg" />

            {[65, 67].includes(BUId) && <>Issued Bookings Till Date<span>(FY 2024-25)</span></>}
            {![65, 67].includes(BUId) &&  <>Issued APE Till Date<span>(FY 2024-25)</span></>}

            {![65, 67].includes(BUId) && <>
              {
                ProjectedIssuedAPE_CurrYear ?
                  <h4>₹{((ProjectedIssuedAPE_CurrYear) / 1e5).toFixed(0).toLocaleString('en-IN')} Lakhs</h4> :
                  <h4>NA</h4>
              }
            </>}

            {[65, 67].includes(BUId) && <h4>{parseInt(ProjectedIssuedAPE_CurrYear?ProjectedIssuedAPE_CurrYear:0).toLocaleString('en-IN')}</h4>}

          </li>
          <li>
            <img src="/images/jag2023/icon3.svg" />
            {[65, 67].includes(BUId) && 'Last Year Issued Bookings'}
            {![65, 67].includes(BUId) && 'Last Year Issued APE'}
            <ToolTipFactory renderTooltip={() => renderTooltipTotalAPE(BUId)} />
         

            {![65, 67].includes(BUId) && <>
              {
              (IssuedAPE_PrevYear + ULIP) ?
                <h4>₹{((IssuedAPE_PrevYear + ULIP) / 1e5).toFixed(0).toLocaleString('en-IN')} Lakhs</h4> :
                <h4>NA</h4>
            }
            </>}

            {[65, 67].includes(BUId) && <h4>{(IssuedBKG_PrevYear + TRAD).toLocaleString('en-IN')}</h4>}

          </li>
          <li>
            <img src="/images/jag2023/icon4.svg" />
            {[65, 67].includes(BUId) && 'Growth w.r.t Last Year Issued Bookings'}
            {![65, 67].includes(BUId) && 'Growth w.r.t Last Year Issued APE'}
            <h4>{ActualG1 || 0}%</h4>

          </li>
          <li>
            <img src="/images/jag2023/icon5.svg" />
            Cash Reward
            {
              !CashRewards ?
                <h4>₹0</h4>
                :
                <h4>₹{((CashRewards < 1e5) ?
                  ((CashRewards) / 1e3).toFixed(1) : ((CashRewards) / 1e5).toFixed(1))}
                  {(CashRewards < 1e5) ? 'K' : 'Lakhs'}


                </h4>
            }
          </li>
        </ul>
        <ProgressGrowthCalc agentData={agentData}  slabforTarget={slabforTarget} />
        <div className="lotteryTicekt"><p> <img src="/images/jag2023/lotteryIcon.svg" />Lottery Tickets  &nbsp; <strong> {LotteryTicket || 0}</strong></p>
          <p>
            <a href="#" onClick={(e) => HandleOpenCalculation(e)}>How is this all calculated?</a>
            <a href="#" onClick={(e) => HandleViewCriteria(e)}>JAG 5.0 Structure</a>
          </p>
        </div>
        <div className="TotalScore">
            {/* {[65, 67].includes(BUId) && <p>Avg Monthly Sourcing : <strong>{parseInt(CurrentMonthSourcing || 0).toLocaleString('en-IN') || 'NA'}</strong></p>}
            {![65, 67].includes(BUId) && <p>Avg Monthly Sourcing : <strong>₹{parseInt(CurrentMonthSourcing || 0).toLocaleString('en-IN') || 'NA'}</strong></p>} */}
          
          {/* <hr /> */}
          {![65, 67].includes(BUId) ? <>
            {ActualG1 > (growthTarget * 100) ?
              <p>You are eligible for cash rewards</p> :
              <p>Target Issued APE for Cash Rewards : &nbsp;
                <p> Yearly  <strong> ₹{parseInt(reqForRewards.YearlyApeForCash || 0).toLocaleString('en-IN') || 'NA'}</strong> Monthly <strong> ₹{parseInt(reqForRewards.ReqMonthlyIssuedApeForCash || 0).toLocaleString('en-IN') || 'NA'}</strong></p> </p>

            }
          </> :
            <>
              {ActualG1 > (growthTarget * 100) ?
                <p>You are eligible for cash rewards</p> :
                <p>Target Issued BKG for Cash rewards : &nbsp;
                  <p> Yearly  <strong> {parseInt(reqForRewards.YearlyBookingsForCash || 0).toLocaleString('en-IN') || 'NA'}</strong> Monthly <strong> {parseInt(reqForRewards.ReqMonthlyIssuedBookingsForCash || 0).toLocaleString('en-IN') || 'NA'}</strong></p> </p>

              }
            </>
          }

        </div>
      </div>

      {/* Calculation Drawer */}
      {calculationDrawerOpen &&
        <CalculationDetails
          agentDetails={agentDetails}
          agentData={agentData}
          HandleOpenCalculation={HandleOpenCalculation}
          slabforTarget={slabforTarget}
        />
      }
    </>
  );
};

export default AgentDetails;