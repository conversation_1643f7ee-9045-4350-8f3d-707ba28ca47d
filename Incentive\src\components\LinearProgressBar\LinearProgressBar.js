import React from "react";
import { makeStyles, withStyles } from "@material-ui/core/styles";
import LinearProgress from "@material-ui/core/LinearProgress";
import { connect } from 'react-redux';

const ColorLinearProgress = withStyles({
  colorPrimary: {
    backgroundColor: "#eef3fd"
  },
  barColorPrimary: {
    backgroundColor: "#6798e5"
  }
})(LinearProgress);

const useStyles = makeStyles(theme => ({
  root: {
    flexGrow: 1
  },
  margin: {
    margin: theme.spacing(1)
  }
}));

const linearProgress = (props) => {
  const { isLoading } = props;
  const classes = useStyles();
  return (isLoading ? <div className={classes.root}>
    <ColorLinearProgress className={classes.margin} />
  </div> : null
  );
};


const mapStateToProps = state => {
  return {
    // loading: !(!state.common.loading)
  };
};

const mapDispatchToProps = dispatch => {
  return {
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(linearProgress);
