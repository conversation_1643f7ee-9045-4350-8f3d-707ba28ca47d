// *https://www.registers.service.gov.uk/registers/country/use-the-api*
import 'isomorphic-fetch';
import React, { useEffect, useState } from 'react';
import TextField from '@material-ui/core/TextField';
import { makeStyles } from '@material-ui/styles';
import Autocomplete from '@material-ui/lab/Autocomplete';
import CircularProgress from '@material-ui/core/CircularProgress';
import * as services from './../../services';

const sleep = (delay = 0) => {
  return new Promise(resolve => {
    setTimeout(resolve, delay);
  });
}
const useStyles = makeStyles(theme => ({
  autoComplete: {
    padding: '6px 0',
    '& label': {
      lineHeight: '0.3',
    },
    '& > div': {
      maxWidth: '190px',
      '& > div': {
        padding: '6px !important',
        '& > input': {
          padding: '6px !important',
          fontSize: '14px',
        }
      }
    }

  }
}));

export default (props) => {
  const { url, label, onChange, inputValue, clearvalue } = props;
  const classes = useStyles();
  const [open, setOpen] = useState(false);
  const [currentValue, setCurrentValue] = useState(null);


  const [options, setOptions] = useState([]);
  const loading = open && options.length === 0;
  useEffect(() => {
    let active = true;
    // if (!loading) {
    //   return undefined;
    // }
    !(async () => {
      let urlName = '/agent/web/allAgents'
      if (currentValue && currentValue != null) {
        urlName = `${urlName}?search_keyword=${currentValue}`
      }
      const response = await services.API_GET(urlName);
      await sleep(1e3);
      let data = [];
      if (response && response['result'] && response['result']['list'])
        data = response['result']['list'];
      if (active) {
        setOptions(data.map(item => { return { id: item.id, name: item.name } }));
      }
    })();

    return () => {
      active = false;
    };
  }, [loading, currentValue]);

  useEffect(() => {
    if (!open) {
      setOptions([]);
    }
  }, [open]);

  return (
    <Autocomplete
      open={open}
      onOpen={() => {
        setOpen(true);
      }}
      onClose={() => {
        setOpen(false);
      }}
      value={clearvalue}
      onChange={onChange}
      getOptionSelected={(option, value) => option.name === value.name}
      getOptionLabel={option => option.name}
      options={options}
      loading={loading}
      className={classes.autoComplete}
      onInputChange={(e, val, reason) => setCurrentValue(val)}
      renderInput={params => (
        <TextField
          {...params}
          label={label}
          autoComplete="false"
          fullWidth
          variant="outlined"
          InputProps={{
            ...params.InputProps,
            endAdornment: (
              <React.Fragment>
                {loading ? <CircularProgress color="inherit" size={18} /> : null}
                {params.InputProps.endAdornment}
              </React.Fragment>
            ),
          }}
        />
      )}
    />
  );
}
