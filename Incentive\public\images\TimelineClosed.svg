<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="376" height="641" viewBox="0 0 376 641">
  <defs>
    <filter id="Rectangle_7014" x="0" y="0" width="376" height="641" filterUnits="userSpaceOnUse">
      <feOffset input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="8" result="blur"/>
      <feFlood flood-color="#3469cb" flood-opacity="0.161"/>
      <feComposite operator="in" in2="blur"/>
      <feComposite in="SourceGraphic"/>
    </filter>
  </defs>
  <g id="Timeline_Closed" data-name="Timeline Closed" transform="translate(-7341 3155)">
    <g transform="matrix(1, 0, 0, 1, 7341, -3155)" filter="url(#Rectangle_7014)">
      <rect id="Rectangle_7014-2" data-name="Rectangle 7014" width="328" height="593" rx="8" transform="translate(24 24)" fill="#001458"/>
    </g>
    <g id="Group_89228" data-name="Group 89228">
      <path id="Path_100071" data-name="Path 100071" d="M0,0H501.71" transform="translate(7524.533 -3091) rotate(90)" fill="none" stroke="rgba(255,255,255,0.4)" stroke-width="6" stroke-dasharray="15" opacity="0.4"/>
      <circle id="Ellipse_983" data-name="Ellipse 983" cx="9" cy="9" r="9" transform="translate(7516 -2974)" fill="#293a73"/>
      <g id="Group_89208" data-name="Group 89208" transform="translate(0 6)" opacity="0.4">
        <g id="Group_89207" data-name="Group 89207" transform="translate(-256.299 -95)">
          <rect id="Rectangle_7025" data-name="Rectangle 7025" width="71.243" height="2.013" transform="translate(7803.261 -2852.11)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7026" data-name="Rectangle 7026" width="71.243" height="2.013" transform="translate(7803.261 -2838.021)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7027" data-name="Rectangle 7027" width="71.243" height="2.013" transform="translate(7803.261 -2823.933)" fill="rgba(255,255,255,0.4)"/>
        </g>
        <g id="Group_89206" data-name="Group 89206" transform="translate(-248.279 -28.556)">
          <circle id="Ellipse_986" data-name="Ellipse 986" cx="9.74" cy="9.74" r="9.74" transform="translate(7874.217 -2913.004)" fill="rgba(255,255,255,0.2)"/>
          <path id="Path_100227" data-name="Path 100227" d="M271.755,229.5a10.776,10.776,0,1,0,10.776,10.776A10.776,10.776,0,0,0,271.755,229.5ZM269.9,245.1l-4.635-4.635,1.3-1.3L269.9,242.5l7.048-7.048,1.3,1.3Z" transform="translate(7612.194 -3143.736)" fill="rgba(255,255,255,0.2)"/>
        </g>
        <rect id="Rectangle_7038" data-name="Rectangle 7038" width="25.395" height="20.634" transform="translate(7550.762 -2978)" fill="rgba(208,205,225,0.4)"/>
        <path id="Path_100240" data-name="Path 100240" d="M555.609,254.472H529.156V232.78h26.453Zm-25.4-1.058h24.337V233.838H530.214Z" transform="translate(7016.844 -3207.605)" fill="rgba(255,255,255,0.4)"/>
      </g>
      <g id="Group_89209" data-name="Group 89209" transform="translate(7405.554 -3086)" opacity="0.4">
        <g id="Group_89207-2" data-name="Group 89207" transform="translate(28.241 30.89)">
          <rect id="Rectangle_7025-2" data-name="Rectangle 7025" width="71.243" height="2.013" transform="translate(0)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7026-2" data-name="Rectangle 7026" width="71.243" height="2.013" transform="translate(0 14.089)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7027-2" data-name="Rectangle 7027" width="71.243" height="2.013" transform="translate(0 28.177)" fill="rgba(255,255,255,0.4)"/>
        </g>
        <g id="Group_89206-2" data-name="Group 89206" transform="translate(0 35.209)">
          <ellipse id="Ellipse_986-2" data-name="Ellipse 986" cx="9.74" cy="9.74" rx="9.74" ry="9.74" transform="translate(1.044 1.231)" fill="rgba(255,255,255,0.2)"/>
          <path id="Path_100227-2" data-name="Path 100227" d="M271.755,229.5a10.776,10.776,0,1,0,10.776,10.776A10.776,10.776,0,0,0,271.755,229.5ZM269.9,245.1l-4.635-4.635,1.3-1.3L269.9,242.5l7.048-7.048,1.3,1.3Z" transform="translate(-260.98 -229.501)" fill="rgba(255,255,255,0.2)"/>
        </g>
        <rect id="Rectangle_7038-2" data-name="Rectangle 7038" width="25.395" height="20.634" transform="translate(70.289)" fill="rgba(208,205,225,0.4)"/>
        <path id="Path_100240-2" data-name="Path 100240" d="M529.156,254.472h26.453V232.78H529.156Zm25.4-1.058H530.214V233.838h24.337Z" transform="translate(-455.163 -229.605)" fill="rgba(255,255,255,0.4)"/>
      </g>
      <circle id="Ellipse_993" data-name="Ellipse 993" cx="9" cy="9" r="9" transform="translate(7516 -3091)" fill="#293a73"/>
      <g id="Group_89210" data-name="Group 89210" transform="translate(7405.554 -2673)" opacity="0.4">
        <g id="Group_89207-3" data-name="Group 89207" transform="translate(28.241 30.89)">
          <rect id="Rectangle_7025-3" data-name="Rectangle 7025" width="71.243" height="2.013" transform="translate(0)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7026-3" data-name="Rectangle 7026" width="71.243" height="2.013" transform="translate(0 14.089)" fill="rgba(255,255,255,0.4)"/>
          <rect id="Rectangle_7027-3" data-name="Rectangle 7027" width="71.243" height="2.013" transform="translate(0 28.177)" fill="rgba(255,255,255,0.4)"/>
        </g>
        <g id="Group_89206-3" data-name="Group 89206" transform="translate(0 35.209)">
          <ellipse id="Ellipse_986-3" data-name="Ellipse 986" cx="9.74" cy="9.74" rx="9.74" ry="9.74" transform="translate(1.044 1.231)" fill="rgba(255,255,255,0.2)"/>
          <path id="Path_100227-3" data-name="Path 100227" d="M271.755,229.5a10.776,10.776,0,1,0,10.776,10.776A10.776,10.776,0,0,0,271.755,229.5ZM269.9,245.1l-4.635-4.635,1.3-1.3L269.9,242.5l7.048-7.048,1.3,1.3Z" transform="translate(-260.98 -229.501)" fill="rgba(255,255,255,0.2)"/>
        </g>
        <rect id="Rectangle_7038-3" data-name="Rectangle 7038" width="25.395" height="20.634" transform="translate(70.289)" fill="rgba(208,205,225,0.4)"/>
        <path id="Path_100240-3" data-name="Path 100240" d="M529.156,254.472h26.453V232.78H529.156Zm25.4-1.058H530.214V233.838h24.337Z" transform="translate(-455.163 -229.605)" fill="rgba(255,255,255,0.4)"/>
      </g>
      <circle id="Ellipse_994" data-name="Ellipse 994" cx="9" cy="9" r="9" transform="translate(7516 -2673)" fill="#293a73"/>
    </g>
    <rect id="Rectangle_7039" data-name="Rectangle 7039" width="277" height="182" transform="translate(7388 -2894)" fill="#021458"/>
    <text id="Timeline_Closed-2" data-name="Timeline Closed" transform="translate(7529 -2761)" fill="#fff" font-size="14" font-family="Roboto-Bold, Roboto" font-weight="700"><tspan x="-50.911" y="0">Timeline Closed</tspan></text>
    <text id="Booking_timeline_has_already_closed_for_the_selected_month._Please_check_the_payout_section" data-name="Booking timeline has already closed for the 
selected month. Please check the payout section" transform="translate(7529 -2740)" fill="#fff" font-size="12" font-family="Roboto-Regular, Roboto"><tspan x="-116.136" y="0">Booking timeline has already closed for the </tspan><tspan x="-130.242" y="16">selected month. Please check the payout section</tspan></text>
    <g id="Group_89231" data-name="Group 89231" transform="translate(-355.173 94.444)">
      <circle id="Ellipse_986-4" data-name="Ellipse 986" cx="9.74" cy="9.74" r="9.74" transform="translate(7874.217 -2913.004)" fill="#fff"/>
      <path id="Path_100227-4" data-name="Path 100227" d="M271.755,229.5a10.776,10.776,0,1,0,10.776,10.776A10.776,10.776,0,0,0,271.755,229.5ZM269.9,245.1l-4.635-4.635,1.3-1.3L269.9,242.5l7.048-7.048,1.3,1.3Z" transform="translate(7612.194 -3143.736)" fill="#0cc713"/>
    </g>
    <g id="Group_89246" data-name="Group 89246" transform="translate(7069.922 -3498.174)">
      <path id="Path_100312" data-name="Path 100312" d="M408.168,873.266s-21.253-50.249-2.736-70.455c25.683-28.026,41.86,7.96,55.918,1.814,7.345-3.212,18.7-11.664,32.4-18.407,16-7.876,57.345,10.726,34.371,40.2-21.952,28.167,20.524,29.286-4.022,47.279Z" transform="translate(-8.839 -163.528)" fill="#a9d2d8" opacity="0.3"/>
      <g id="Group_89232" data-name="Group 89232" transform="translate(441.975 607.713)">
        <rect id="Rectangle_7080" data-name="Rectangle 7080" width="1.682" height="2.546" transform="translate(1.682 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7081" data-name="Rectangle 7081" width="1.682" height="2.546" transform="translate(4.564 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7082" data-name="Rectangle 7082" width="1.682" height="2.546" transform="translate(7.447 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7083" data-name="Rectangle 7083" width="1.682" height="2.546" transform="translate(10.33 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7084" data-name="Rectangle 7084" width="1.682" height="2.546" transform="translate(13.213 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7085" data-name="Rectangle 7085" width="1.682" height="2.546" transform="translate(16.096 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7086" data-name="Rectangle 7086" width="1.682" height="2.546" transform="translate(18.979 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7087" data-name="Rectangle 7087" width="1.682" height="2.546" transform="translate(21.861 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7088" data-name="Rectangle 7088" width="1.682" height="2.546" transform="translate(24.744 2.546) rotate(180)" fill="#2b478b"/>
        <rect id="Rectangle_7089" data-name="Rectangle 7089" width="1.682" height="2.546" transform="translate(27.627 2.546) rotate(180)" fill="#2b478b"/>
      </g>
      <rect id="Rectangle_7090" data-name="Rectangle 7090" width="52.168" height="96.985" transform="translate(429.71 612.695)" fill="#77a4e5"/>
      <rect id="Rectangle_7091" data-name="Rectangle 7091" width="47.319" height="91.458" transform="translate(432.133 615.381)" fill="#9fc1f4"/>
      <g id="Group_89233" data-name="Group 89233" transform="translate(456.818 618.01)">
        <rect id="Rectangle_7092" data-name="Rectangle 7092" width="22.453" height="85.996" transform="translate(0 0)" fill="#bdd8ff"/>
      </g>
      <g id="Group_89234" data-name="Group 89234" transform="translate(432.234 618.373)">
        <rect id="Rectangle_7093" data-name="Rectangle 7093" width="22.453" height="85.996" transform="translate(0 0)" fill="#bdd8ff"/>
      </g>
      <g id="Group_89235" data-name="Group 89235" transform="translate(421.176 684.153)">
        <path id="Path_100313" data-name="Path 100313" d="M809.224,1460.983l.679.679s-3.534.111-9.534-.276c-2.089-.134-4.476-.329-7.117-.609-3.932-.418-8.425-1.027-13.334-1.911q-2.665-.479-5.487-1.072-1-.211-1.986-.431c-3.478-.776-6.733-1.616-9.733-2.47h0c-2.379-.677-4.6-1.363-6.638-2.033h0c-3.861-1.27-7.085-2.487-9.558-3.485-3.646-1.473-5.655-2.472-5.655-2.472l1.613-.9-.493-1.26,1.156-.308.039-1.565,1.261.521s2.134,1.067,6.162,2.618c2.491.958,5.705,2.1,9.587,3.294,1.939.595,4.042,1.2,6.306,1.8,3.178.845,6.671,1.678,10.457,2.452q3.336.682,6.974,1.3c4.04.677,8.352,1.269,12.916,1.735q3.3.337,6.781.581h0q3.124.216,6.384.345l-.8,1.055.724.236-.453.453.63.839Z" transform="translate(-740.86 -1442.874)" fill="#ffa412"/>
        <path id="Path_100314" data-name="Path 100314" d="M1296.443,1586.2l-3.25,4.214c-2.089-.134-4.476-.329-7.117-.609l3.586-4.186Q1292.965,1585.961,1296.443,1586.2Z" transform="translate(-1233.684 -1571.906)" fill="#b64c41"/>
        <path id="Path_100315" data-name="Path 100315" d="M1081.033,1555.386l-4,4.009q-2.665-.479-5.487-1.072-1-.211-1.986-.431l4.5-3.8Q1077.4,1554.773,1081.033,1555.386Z" transform="translate(-1037.971 -1543.404)" fill="#b64c41"/>
        <path id="Path_100316" data-name="Path 100316" d="M909.6,1511.622l-3.778,3.785h0c-2.379-.677-4.6-1.363-6.638-2.033.976-.887,2.6-2.274,4.111-3.553C905.228,1510.415,907.331,1511.022,909.6,1511.622Z" transform="translate(-883.965 -1503.387)" fill="#b64c41"/>
        <path id="Path_100317" data-name="Path 100317" d="M750.6,1446.013l-4.083,3.362c-3.646-1.473-5.655-2.472-5.655-2.472l1.613-.9-.493-1.26,1.156-.308.039-1.565,1.261.521S746.569,1444.462,750.6,1446.013Z" transform="translate(-740.86 -1442.874)" fill="#b64c41"/>
      </g>
      <g id="Group_89236" data-name="Group 89236" transform="translate(416.008 604.174)">
        <path id="Path_100318" data-name="Path 100318" d="M746.116,607.868l.968.277a78.131,78.131,0,0,1-7.189,4.861c-1.626,1-3.513,2.094-5.646,3.247-3.175,1.717-6.895,3.556-11.11,5.357q-2.287.979-4.769,1.934-.883.34-1.755.66c-3.09,1.134-6.06,2.09-8.853,2.894h0c-2.214.638-4.317,1.182-6.279,1.645h0c-3.713.874-6.921,1.456-9.426,1.841-3.7.566-5.86.7-5.86.7l.564-1.713-1.233-.926.636-.912-1.054-1.5,1.286-.185a63.464,63.464,0,0,0,6.334-.836c2.491-.433,5.642-1.077,9.316-2.038,1.834-.479,3.8-1.035,5.875-1.685,2.917-.909,6.057-2,9.372-3.3q2.921-1.147,6.016-2.522c3.433-1.531,7.008-3.287,10.681-5.3q2.658-1.456,5.38-3.093h0q2.442-1.474,4.924-3.1l.144,1.425.695-.165-.019.671,1.043.455Z" transform="translate(-685.107 -604.174)" fill="#ffa412"/>
        <path id="Path_100319" data-name="Path 100319" d="M1199.149,636.478l.529,5.728c-1.626,1-3.513,2.094-5.646,3.247l-.262-5.882Q1196.427,638.116,1199.149,636.478Z" transform="translate(-1144.89 -633.374)" fill="#b64c41"/>
        <path id="Path_100320" data-name="Path 100320" d="M1019.681,723.815l-.167,5.94q-2.287.979-4.769,1.935-.883.34-1.755.66l.675-6.012Q1016.587,725.19,1019.681,723.815Z" transform="translate(-981.482 -712.318)" fill="#b64c41"/>
        <path id="Path_100321" data-name="Path 100321" d="M861.947,784.406l-.156,5.606h0c-2.214.638-4.317,1.182-6.279,1.645.1-1.362.334-3.544.561-5.566C857.906,785.611,859.87,785.056,861.947,784.406Z" transform="translate(-839.136 -767.087)" fill="#b64c41"/>
        <path id="Path_100322" data-name="Path 100322" d="M692.727,823.143l-.672,5.37c-3.7.566-5.86.7-5.86.7l.564-1.713-1.233-.926.636-.912-1.054-1.5,1.286-.185A63.479,63.479,0,0,0,692.727,823.143Z" transform="translate(-685.107 -802.102)" fill="#b64c41"/>
      </g>
      <g id="Group_89243" data-name="Group 89243" transform="translate(388.077 674.959)">
        <g id="Group_89238" data-name="Group 89238" transform="translate(2.888 13.779)">
          <path id="Path_100323" data-name="Path 100323" d="M431.479,1496.425s2.415-2.817.149-4.606a6.428,6.428,0,0,0-3-.92s1.912,3.206,1.293,3.525-1.873-3.726-1.873-3.726-3.766-.365-4.978.217c0,0,3.22,2.635,2.542,2.791s-4.156-2.429-4.156-2.429-3.5.6-3.318,2.735,1.395,2.8,1.925,2.741,3.636-1.242,3.7-.807-1.8,1.856-3.11,1.923c0,0,2.471,2.725,4.113,1.939s1.782-2.033,2.806-2.5,1.9-.467,1.475.01-2.275.94-2.861,1.976-1.111,1.734.589,1.478,4.461-1.5,4.582-2.923Z" transform="translate(-418.128 -1490.584)" fill="#77a4e5"/>
          <g id="Group_89237" data-name="Group 89237" transform="translate(1.729 3.468)">
            <path id="Path_100324" data-name="Path 100324" d="M436.124,1526.7s9.52-.367,11.622,2.355a6.889,6.889,0,0,0,1.9,2.661l.223.608s-1.65-1.74-2.24-1.844C447.625,1530.475,448.548,1527.1,436.124,1526.7Z" transform="translate(-436.124 -1526.679)" fill="#77a4e5"/>
            <path id="Path_100325" data-name="Path 100325" d="M571.743,1576.8a5.978,5.978,0,0,1,2.185,4.211l-.371.033s-.653-2.792-1.832-3.884S571.743,1576.8,571.743,1576.8Z" transform="translate(-558.225 -1571.765)" fill="#77a4e5"/>
          </g>
        </g>
        <g id="Group_89240" data-name="Group 89240" transform="translate(22.156 6.131)">
          <path id="Path_100326" data-name="Path 100326" d="M621.168,1420.944s-3.841-2.151-2.049-5.07a7.627,7.627,0,0,1,2.97-2.253s-.819,4.353,0,4.455.563-4.916.563-4.916,4.046-1.946,5.633-1.792c0,0-2.509,4.251-1.69,4.148s3.636-4.4,3.636-4.4,4.148-.768,4.814,1.69-.41,3.687-1.024,3.841-4.558.1-4.455.614,2.765,1.332,4.25.871c0,0-1.639,4.046-3.79,3.841s-2.817-1.536-4.148-1.639-2.3.256-1.639.615,2.919.118,3.994,1.032,1.946,1.478-.051,1.887-5.582.154-6.3-1.383Z" transform="translate(-618.643 -1410.998)" fill="#9fc1f4"/>
          <g id="Group_89239" data-name="Group 89239" transform="translate(0.496 2.572)">
            <path id="Path_100327" data-name="Path 100327" d="M647.268,1437.761s-10.754,3.482-11.983,7.374a8.17,8.17,0,0,1-1.024,3.739v.768s1.127-2.612,1.741-2.97C636,1446.671,633.6,1443.292,647.268,1437.761Z" transform="translate(-633.257 -1437.761)" fill="#77a4e5"/>
            <path id="Path_100328" data-name="Path 100328" d="M624.814,1548.907a7.093,7.093,0,0,0-.713,5.584l.427-.115s-.414-3.377.454-5.075S624.814,1548.907,624.814,1548.907Z" transform="translate(-623.81 -1537.795)" fill="#77a4e5"/>
          </g>
        </g>
        <g id="Group_89242" data-name="Group 89242" transform="translate(0 0)">
          <path id="Path_100329" data-name="Path 100329" d="M406.648,1360.306s5.063-2.835,2.7-6.683a10.05,10.05,0,0,0-3.915-2.97s1.08,5.738,0,5.873-.743-6.481-.743-6.481-5.333-2.565-7.425-2.363c0,0,3.308,5.6,2.228,5.468s-4.793-5.805-4.793-5.805-5.468-1.012-6.345,2.228.54,4.86,1.35,5.063,6.008.135,5.873.81-3.645,1.755-5.6,1.147c0,0,2.16,5.333,5,5.063s3.713-2.025,5.468-2.16,3.038.338,2.16.81-3.848.155-5.265,1.36-2.565,1.948.067,2.488,7.358.2,8.3-1.823Z" transform="translate(-388.077 -1347.196)" fill="#9fc1f4"/>
          <g id="Group_89241" data-name="Group 89241" transform="translate(2.774 3.39)">
            <path id="Path_100330" data-name="Path 100330" d="M416.95,1382.473s14.176,4.59,15.8,9.721a10.769,10.769,0,0,0,1.35,4.928v1.013s-1.485-3.443-2.3-3.915C431.8,1394.219,434.974,1389.764,416.95,1382.473Z" transform="translate(-416.95 -1382.473)" fill="#77a4e5"/>
            <path id="Path_100331" data-name="Path 100331" d="M589.011,1528.985a9.349,9.349,0,0,1,.94,7.361l-.562-.151s.546-4.451-.6-6.69S589.011,1528.985,589.011,1528.985Z" transform="translate(-571.864 -1514.337)" fill="#77a4e5"/>
          </g>
        </g>
        <path id="Path_100332" data-name="Path 100332" d="M479.9,1599.5a9.918,9.918,0,1,0,19.835,0Z" transform="translate(-471.076 -1575.257)" fill="#77a4e5"/>
        <path id="Path_100333" data-name="Path 100333" d="M520.608,1599.5a8.121,8.121,0,1,0,15.923,0Z" transform="translate(-507.873 -1575.257)" fill="#9fc1f4"/>
        <path id="Path_100334" data-name="Path 100334" d="M478.482,1696.454h20.109a1.224,1.224,0,0,0-1.224-1.224H479.706A1.223,1.223,0,0,0,478.482,1696.454Z" transform="translate(-469.795 -1661.786)" fill="#77a4e5"/>
        <path id="Path_100335" data-name="Path 100335" d="M596.514,1608.7h-1.345c3.638-1.469,5.086-5.034,5.086-9.2h2.464A9.92,9.92,0,0,1,596.514,1608.7Z" transform="translate(-575.269 -1575.26)" fill="#bdd8ff"/>
        <path id="Path_100336" data-name="Path 100336" d="M576.487,1696.454h10.691a1.224,1.224,0,0,0-1.224-1.224h-8.632C576.647,1695.23,576.487,1695.778,576.487,1696.454Z" transform="translate(-558.382 -1661.786)" fill="#9fc1f4"/>
        <path id="Path_100337" data-name="Path 100337" d="M477.708,1599.809H497.79a.152.152,0,0,0,.152-.152h0a.152.152,0,0,0-.152-.152H477.708a.152.152,0,0,0-.152.152h0A.152.152,0,0,0,477.708,1599.809Z" transform="translate(-468.958 -1575.26)" fill="#77a4e5"/>
      </g>
      <rect id="Rectangle_7094" data-name="Rectangle 7094" width="8.328" height="13.494" rx="4.164" transform="translate(428.454 675.236) rotate(180)" fill="#77a4e5"/>
      <path id="Path_100338" data-name="Path 100338" d="M753.026,1237.667l1.72,2.979h-3.44Z" transform="translate(-328.752 -572.979)" fill="#2b478b"/>
      <path id="Path_100339" data-name="Path 100339" d="M753.026,1290.646l1.72-2.979h-3.44Z" transform="translate(-328.752 -618.203)" fill="#2b478b"/>
      <path id="Path_100340" data-name="Path 100340" d="M1023.647,915.973a.192.192,0,0,0,.145-.318l-10.878-12.588a.192.192,0,0,0-.284-.007l-9.206,9.609a.192.192,0,0,0,.278.266l9.06-9.457,10.74,12.429A.192.192,0,0,0,1023.647,915.973Z" transform="translate(-556.994 -270.299)" fill="#2b478b"/>
      <circle id="Ellipse_1008" data-name="Ellipse 1008" cx="0.673" cy="0.673" r="0.673" transform="translate(455.077 632.626)" fill="#b64c41"/>
      <rect id="Rectangle_7095" data-name="Rectangle 7095" width="21.909" height="8.055" transform="matrix(-0.984, -0.179, 0.179, -0.984, 465.844, 653.16)" fill="#b64c41"/>
      <g id="Group_89244" data-name="Group 89244" transform="translate(446.069 644.221)">
        <path id="Path_100341" data-name="Path 100341" d="M1002.34,1026.121a.15.15,0,0,1,.068-.091.153.153,0,0,1,.1-.019l.412.063a.146.146,0,0,1,.093.053.114.114,0,0,1,.023.1,1.318,1.318,0,0,1-.167.4,1.1,1.1,0,0,1-.329.335,1.445,1.445,0,0,1-.5.2,1.922,1.922,0,0,1-.692,0,1.733,1.733,0,0,1-.6-.2,1.332,1.332,0,0,1-.413-.359,1.305,1.305,0,0,1-.225-.489,1.842,1.842,0,0,1-.039-.583q.019-.194.054-.428t.075-.425a1.845,1.845,0,0,1,.21-.545,1.305,1.305,0,0,1,.36-.4,1.331,1.331,0,0,1,.5-.22,1.729,1.729,0,0,1,.636-.01,1.921,1.921,0,0,1,.66.209,1.45,1.45,0,0,1,.421.345,1.1,1.1,0,0,1,.214.42,1.338,1.338,0,0,1,.041.43.113.113,0,0,1-.053.093.148.148,0,0,1-.1.023l-.412-.063a.164.164,0,0,1-.091-.047.144.144,0,0,1-.041-.108.567.567,0,0,0-.041-.209.623.623,0,0,0-.126-.2.8.8,0,0,0-.225-.161,1.13,1.13,0,0,0-.338-.1.91.91,0,0,0-.355.009.738.738,0,0,0-.271.125.7.7,0,0,0-.189.221,1.04,1.04,0,0,0-.109.29q-.04.192-.075.425t-.055.428a1.029,1.029,0,0,0,.018.312.7.7,0,0,0,.116.264.729.729,0,0,0,.222.2.906.906,0,0,0,.336.113,1.129,1.129,0,0,0,.353,0,.842.842,0,0,0,.265-.087.6.6,0,0,0,.181-.149A.564.564,0,0,0,1002.34,1026.121Z" transform="translate(-1000.055 -1023.493)" fill="#fff"/>
        <path id="Path_100342" data-name="Path 100342" d="M1038.133,1031.062a.141.141,0,0,1,.116.158l-.055.361a.14.14,0,0,1-.158.116l-2.226-.338a.141.141,0,0,1-.116-.158l.5-3.283a.141.141,0,0,1,.158-.116l.412.063a.133.133,0,0,1,.09.056.134.134,0,0,1,.026.1l-.422,2.785Z" transform="translate(-1032.268 -1027.387)" fill="#fff"/>
        <path id="Path_100343" data-name="Path 100343" d="M1066.875,1034.772a1.819,1.819,0,0,1,.191-.52,1.242,1.242,0,0,1,.343-.4,1.337,1.337,0,0,1,.5-.225,1.778,1.778,0,0,1,.667-.01,1.8,1.8,0,0,1,.629.2,1.262,1.262,0,0,1,.626.844,1.755,1.755,0,0,1,.03.556c-.006.065-.015.136-.025.214s-.021.159-.034.242-.026.163-.039.241-.027.149-.04.212a1.824,1.824,0,0,1-.19.52,1.246,1.246,0,0,1-.343.4,1.3,1.3,0,0,1-.5.222,2.049,2.049,0,0,1-1.3-.2,1.3,1.3,0,0,1-.413-.362,1.24,1.24,0,0,1-.211-.479,1.821,1.821,0,0,1-.028-.553q.01-.1.025-.215c.01-.078.021-.159.034-.242s.026-.164.039-.241S1066.862,1034.836,1066.875,1034.772Zm2.3.374a.808.808,0,0,0-.15-.6.817.817,0,0,0-1.466.357c-.026.128-.052.27-.075.428s-.042.3-.055.431a.8.8,0,0,0,.151.6.935.935,0,0,0,1.144.174.8.8,0,0,0,.322-.528q.04-.191.075-.428T1069.174,1035.147Z" transform="translate(-1060.321 -1032.625)" fill="#fff"/>
        <path id="Path_100344" data-name="Path 100344" d="M1103.833,1039a1.825,1.825,0,0,1,.586.182,1.507,1.507,0,0,1,.4.294,1,1,0,0,1,.216.342.79.79,0,0,1,.052.328.113.113,0,0,1-.053.093.147.147,0,0,1-.1.023l-.356-.054a.214.214,0,0,1-.125-.053.341.341,0,0,1-.065-.1.67.67,0,0,0-.2-.256.912.912,0,0,0-.446-.164,1.109,1.109,0,0,0-.256-.01.724.724,0,0,0-.221.05.433.433,0,0,0-.16.108.308.308,0,0,0-.077.165.347.347,0,0,0,.019.2.383.383,0,0,0,.143.152,1.69,1.69,0,0,0,.287.145q.181.074.451.177a3.362,3.362,0,0,1,.5.235,1.241,1.241,0,0,1,.328.268.808.808,0,0,1,.165.319.959.959,0,0,1,.012.381.918.918,0,0,1-.166.409.988.988,0,0,1-.34.292,1.489,1.489,0,0,1-.5.151,2.186,2.186,0,0,1-.627-.012,2.136,2.136,0,0,1-.533-.151,1.611,1.611,0,0,1-.43-.268,1.1,1.1,0,0,1-.275-.361.829.829,0,0,1-.075-.427.113.113,0,0,1,.053-.093.146.146,0,0,1,.1-.023l.356.054a.24.24,0,0,1,.127.051.233.233,0,0,1,.062.1.988.988,0,0,0,.066.145.476.476,0,0,0,.12.138.836.836,0,0,0,.2.117,1.338,1.338,0,0,0,.318.082,1.884,1.884,0,0,0,.3.022.866.866,0,0,0,.26-.039.536.536,0,0,0,.193-.106.3.3,0,0,0,.1-.186.253.253,0,0,0-.043-.194.577.577,0,0,0-.2-.155,3.072,3.072,0,0,0-.344-.151q-.206-.078-.475-.192a2.644,2.644,0,0,1-.423-.212,1.06,1.06,0,0,1-.284-.253.783.783,0,0,1-.142-.315,1.148,1.148,0,0,1,0-.4.9.9,0,0,1,.166-.406,1.015,1.015,0,0,1,.33-.288,1.388,1.388,0,0,1,.457-.152A1.758,1.758,0,0,1,1103.833,1039Z" transform="translate(-1092.192 -1037.491)" fill="#fff"/>
        <path id="Path_100345" data-name="Path 100345" d="M1139.731,1046.471a.14.14,0,0,1,.116.158l-.055.361a.141.141,0,0,1-.158.116l-2.231-.338a.141.141,0,0,1-.117-.158l.5-3.283a.141.141,0,0,1,.158-.116l2.19.332a.14.14,0,0,1,.116.158l-.055.361a.14.14,0,0,1-.158.116l-1.642-.249-.122.808,1.53.232a.141.141,0,0,1,.117.158l-.055.361a.141.141,0,0,1-.158.116l-1.53-.232-.128.844Z" transform="translate(-1124.098 -1041.314)" fill="#fff"/>
        <path id="Path_100346" data-name="Path 100346" d="M1171.829,1048.349a1.96,1.96,0,0,1,.638.2,1.259,1.259,0,0,1,.42.352,1.24,1.24,0,0,1,.218.485,1.985,1.985,0,0,1,.031.6,7.069,7.069,0,0,1-.114.752,1.977,1.977,0,0,1-.207.561,1.26,1.26,0,0,1-.349.4,1.242,1.242,0,0,1-.5.213,1.883,1.883,0,0,1-.653,0l-1.281-.194a.14.14,0,0,1-.116-.158l.5-3.283a.133.133,0,0,1,.055-.09.134.134,0,0,1,.1-.026Zm.621,1.529a1.041,1.041,0,0,0-.018-.312.7.7,0,0,0-.12-.268.728.728,0,0,0-.237-.2,1.1,1.1,0,0,0-.367-.116l-.681-.1-.347,2.287.706.107a1.012,1.012,0,0,0,.37,0,.723.723,0,0,0,.278-.121.686.686,0,0,0,.192-.22,1.069,1.069,0,0,0,.11-.3A7.137,7.137,0,0,0,1172.45,1049.878Z" transform="translate(-1153.594 -1045.787)" fill="#fff"/>
      </g>
      <path id="Path_100347" data-name="Path 100347" d="M1461.2,951.86c.9,2.767,5.831,7.55,6.512,8.027,0,0,1.146-1.911.917-2.98-.187-.874-2.105-3.987-2.762-5.517-.034-.079-.065-.155-.092-.225a1.382,1.382,0,0,1-.127-.524c.033-.332,1.078-1.141,2.292-2.1,1.578-1.247,3.443-2.747,3.745-3.784.535-1.834,2.388-9.1,2.388-9.1s-4.6,1-5.9,2.142-6.878,10.7-7.107,13.144A2.294,2.294,0,0,0,1461.2,951.86Z" transform="translate(-971.227 -299.869)" fill="#ffa412"/>
      <path id="Path_100348" data-name="Path 100348" d="M1462.546,1096.68c.9,2.767,5.831,7.55,6.511,8.027,0,0,1.146-1.91.917-2.98-.2-.919-2.31-4.316-2.854-5.742A7.16,7.16,0,0,0,1462.546,1096.68Z" transform="translate(-972.519 -444.749)" fill="#d9935c"/>
      <path id="Path_100349" data-name="Path 100349" d="M1596.351,898.417l0,.564s-.154,1.764,2.018,2.021,3.807-.53,3.749-1.891a17.556,17.556,0,0,1-.415-2.808,6.14,6.14,0,0,1,.077-1.056l-4.208-.084-1.24.2Z" transform="translate(-1093.816 -263.199)" fill="#d9935c"/>
      <path id="Path_100350" data-name="Path 100350" d="M1596.335,895.367l.016,3.051a3.559,3.559,0,0,0,1.517.335,4.7,4.7,0,0,0,3.838-2.448,6.14,6.14,0,0,1,.077-1.056l-4.208-.084Z" transform="translate(-1093.816 -263.195)" fill="#cb7d45"/>
      <path id="Path_100351" data-name="Path 100351" d="M1635.1,1678.416a9.887,9.887,0,0,1,.677-2.537c.473-.947,3.484-.344,3.484-.344s.6.516.731,2.924Z" transform="translate(-1128.86 -968.854)" fill="#233862"/>
      <path id="Path_100352" data-name="Path 100352" d="M1438.261,1659.912c.707-.432.3-3.653.3-3.653s-3.3-2.637-3.749-.618-3.131,2.925-4.656,3.173a1.636,1.636,0,0,0-1.479,1.1Z" transform="translate(-941.893 -950.355)" fill="#233862"/>
      <path id="Path_100353" data-name="Path 100353" d="M1493.789,1228.471a15.265,15.265,0,0,0-.242,1.78c-.306,5.977-5.031,19.642-5.876,19.961-1.308.494-3.367-.555-4.444-.9a.924.924,0,0,1-.648-1.05c2.34-10.756,3.2-26.245,3.512-35.415a41.607,41.607,0,0,1,.951-7.809s11.523-3.075,11.592.814C1498.694,1209.3,1494.816,1222.793,1493.789,1228.471Z" transform="translate(-990.974 -542.699)" fill="#4f7ce8"/>
      <path id="Path_100354" data-name="Path 100354" d="M1527.008,1228.471l-7.7-15.622a41.6,41.6,0,0,1,.951-7.809s11.523-3.075,11.592.814C1531.913,1209.3,1528.035,1222.793,1527.008,1228.471Z" transform="translate(-1024.193 -542.516)" fill="#4a76e2"/>
      <path id="Path_100355" data-name="Path 100355" d="M1572.736,1211.3l6.092,44.151a11.917,11.917,0,0,0,4.756.129s1.783-23.481,1.635-29.158,1.28-15.19-.326-16.239S1572.736,1211.3,1572.736,1211.3Z" transform="translate(-1072.485 -548.13)" fill="#5780e3"/>
      <path id="Path_100356" data-name="Path 100356" d="M1525.147,963.5a16.038,16.038,0,0,0,7.621,1.764,33.759,33.759,0,0,0,4.584-.345c3.616-.536,5.441-1.546,5.427-1.922-.012-.339-.036-1.467-.079-1.951-.575-6.627-.278-11.782-1.051-15.654-.009-.047-.018-.093-.028-.139a24.822,24.822,0,0,0-3.424-8.911,4.386,4.386,0,0,0-.483-.624c0,.919-1.956,1.712-3.551,1.608a2.31,2.31,0,0,1-2.215-1.663v0c-.11,0-6.06,4.136-6.066,7.979C1525.861,960.862,1524.817,963.5,1525.147,963.5Z" transform="translate(-1029.411 -299.886)" fill="#ffa412"/>
      <path id="Path_100357" data-name="Path 100357" d="M1649.549,1056.393c0,.019.005.038.008.056,3.616-.536,5.441-1.546,5.427-1.922-.013-.339-.036-1.467-.079-1.951-.575-6.627-.278-11.782-1.051-15.654C1651.956,1039.459,1648.054,1046.1,1649.549,1056.393Z" transform="translate(-1141.615 -391.457)" fill="#e27f2b"/>
      <path id="Path_100358" data-name="Path 100358" d="M.865,21.113C11.8,22.089,9.334,1.741,8.485.136c-.917-1.732-1.2,13.679-1.35,14.24a3.5,3.5,0,0,1-1.145.812C4.043,16.3.282,18.083.053,18.5-.253,19.063.865,21.113.865,21.113Z" transform="matrix(0.875, -0.485, 0.485, 0.875, 502.102, 641.083)" fill="#d9935c"/>
      <path id="Path_100359" data-name="Path 100359" d="M1657.788,942.307c.349,3.429,4.076,10.113,3.923,10.674a3.5,3.5,0,0,1-1.145.812,1.137,1.137,0,0,0,.126,0,5.033,5.033,0,0,1,4.6,1.995c.465-.347.7-.568.7-.568,2.57-5.4-2.08-14.876-2.929-16.482-.917-1.732-6.516-2.493-6.516-2.493A30.482,30.482,0,0,1,1657.788,942.307Z" transform="translate(-1148.241 -300.389)" fill="#ffa412"/>
      <path id="Path_100360" data-name="Path 100360" d="M1585.017,829.012s5.39,1.089,6.016-2.278,1.744-5.494-1.671-6.332-4.325.322-4.807,1.4S1583.631,828.642,1585.017,829.012Z" transform="translate(-1082.744 -195.322)" fill="#d9935c"/>
      <path id="Path_100361" data-name="Path 100361" d="M1590.668,815.889a18.007,18.007,0,0,1,1.911-3.211c.869-.988.395-5.925-3.358-5.767s-5.1,2.844-5.135,3.358,3,.118,3.832.79.04,1.778.987,2.172,1.7.4,1.541,1.264A1.906,1.906,0,0,0,1590.668,815.889Z" transform="translate(-1082.745 -183.374)" fill="#233862"/>
      <path id="Path_100362" data-name="Path 100362" d="M1497.675,1036.33a1.555,1.555,0,0,0,1.288,1.361c-.034-.079-.065-.155-.092-.225a1.381,1.381,0,0,1-.127-.524c.033-.332,1.078-1.141,2.292-2.1a8.745,8.745,0,0,0,0-3.44C1500.618,1029.834,1497.561,1035.68,1497.675,1036.33Z" transform="translate(-1004.274 -386.185)" fill="#e27f2b"/>
      <path id="Path_100363" data-name="Path 100363" d="M1698.978,1114.68s3.745-.306,4.967,1.872a.608.608,0,0,1-.829.51s-3.167-2.165-4.722-2A.845.845,0,0,1,1698.978,1114.68Z" transform="translate(-1186.067 -461.726)" fill="#e27f2b"/>
      <path id="Path_100364" data-name="Path 100364" d="M1466.285,1094.875a13.585,13.585,0,0,0-4.388,1.146s-.543-.533-.326-.839S1465.23,1093.778,1466.285,1094.875Z" transform="translate(-971.564 -443.393)" fill="#e27f2b"/>
      <path id="Path_100367" data-name="Path 100367" d="M5.324.773S2.522-.348,1.834.11,0,1.256,0,1.715,1.452,3.4,2.675,3.778s3.617-.408,3.617-.408Z" transform="matrix(0.875, -0.485, 0.485, 0.875, 506.087, 659.066)" fill="#d9935c"/>
    </g>
  </g>
</svg>
