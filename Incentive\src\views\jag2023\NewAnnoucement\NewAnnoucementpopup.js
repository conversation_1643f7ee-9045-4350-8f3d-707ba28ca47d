import React from 'react';
import { withStyles } from '@material-ui/core/styles';
import Button from '@material-ui/core/Button';
import Dialog from '@material-ui/core/Dialog';
import MuiDialogTitle from '@material-ui/core/DialogTitle';
import MuiDialogContent from '@material-ui/core/DialogContent';
import MuiDialogActions from '@material-ui/core/DialogActions';
import '../../jag2023/scss/NewAnnoucement.scss'



const styles = (theme) => ({
    root: {
        margin: 0,
        padding: theme.spacing(2),
    },
    closeButton: {
        position: 'absolute',
        right: theme.spacing(1),
        top: theme.spacing(1),
        color: theme.palette.grey[500],
    },
});



const DialogContent = withStyles((theme) => ({
    root: {
        padding: theme.spacing(2),
    },
}))(MuiDialogContent);



export default function NewAnnoucementpopup() {
    const [open, setOpen] = React.useState(false);

    const handleClickOpen = () => {
        setOpen(true);
    };
    const handleClose = () => {
        setOpen(false);
    };

    return (
        <div>
            <Button variant="outlined" color="primary" onClick={handleClickOpen}>
                NewAnnoucement
            </Button>
            <Dialog onClose={handleClose}  className="NewAnnoucementPopup" open={open}>
                <DialogContent >
                <img src="/images/jag2023/AnnoucementIcon.svg" />
                    <h3>New Announcement</h3>
                    <ul>
                        <li>Growth Based on target</li>
                        <li>For any feedback or query related to the contest please write back to <NAME_EMAIL>
                            APE will be projected for full month for calculation of cash rewards
                            10% of APE will be considered for Single Pay</li>
                    </ul>
                    <Button autoFocus onClick={handleClose} color="primary">
                    Read More
                    </Button>
                </DialogContent>
               
            </Dialog>
        </div>
    );
}
