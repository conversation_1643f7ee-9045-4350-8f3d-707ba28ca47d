import React, { useEffect, useState } from "react";
import { makeStyles } from "@material-ui/styles";

import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
} from "@material-ui/core";

const ProjectedGrowth = (props) => {
  const growthCriteria = props.growthCriteria || 0;
  const productId = props.productId;

  return (
    <div className='projected-growth'>
      <ul>
        <li>
          {
            productId && [2, 7, 115, 131].includes(productId) &&
            <label>Required issued APE</label>
          }
          {
            productId && [117].includes(productId) && 
            <label>Required Issued Bookings</label>
          }
          {
            growthCriteria === 0 && productId && [2, 7, 115, 131].includes(productId) &&
            <h4>₹{props.reward && parseInt(props.reward.Target).toLocaleString('en-IN') || 0}</h4>
          }
          {
            growthCriteria === 1 && productId && [2, 7, 115, 131].includes(productId) &&
            <h4>₹{parseInt(props.projectedIssuedAPE).toLocaleString('en-IN') || 0}</h4>
          }
          {
            growthCriteria === 0 && productId && [117].includes(productId) &&
            <h4>{props.reward && parseInt(props.reward.Target).toLocaleString('en-IN') || 0}</h4>
          }
          {
            growthCriteria === 1 && productId && [117].includes(productId) &&
            <h4>{parseInt(props.projectedIssuedAPE).toLocaleString('en-IN') || 0}</h4>
          }
        </li>
        <li>
          <label>Growth</label>
          <h4>{props.growth && parseFloat(props.growth).toFixed(2) || 0}%</h4>
        </li>
        <li>
          <label>Cash reward</label>
          {
            growthCriteria === 0 &&
            <h4>₹{props.reward && parseInt(props.reward.CashReward).toLocaleString('en-IN') || 0} / {props.reward && props.reward.TicketCount || 0} Tickets</h4>
          }
          {
            growthCriteria === 1 &&
            <h4>₹{parseInt(props.cashReward).toLocaleString('en-IN') || 0} / {props.ticketCount} Tickets</h4>
          }

        </li>
      </ul>
    </div>
  );
};

export default ProjectedGrowth;