import React, { useEffect, useState } from "react";
import clsx from "clsx";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import PropTypes from "prop-types";
import Drawer from "@material-ui/core/Drawer";
import AppBar from "@material-ui/core/AppBar";
import Toolbar from "@material-ui/core/Toolbar";
import CssBaseline from "@material-ui/core/CssBaseline";
import DashboardIcon from "@material-ui/icons/Dashboard";
import AccountBalanceIcon from '@material-ui/icons/AccountBalance';
import EmojiEventsIcon from '@material-ui/icons/EmojiEvents';
import HelpIcon from "@material-ui/icons/Help";
import SettingsIcon from "@material-ui/icons/Settings";
import { Profile, SidebarNav } from "./components";
import CallEndIcon from "@material-ui/icons/CallEnd";
import LastLogin from "./LastLogin";
import DescriptionOutlinedIcon from "@material-ui/icons/DescriptionOutlined";
import { useParams } from "react-router";

const drawerWidth = 240;
const drawerWidthMinimized = 56;

const useStyles = makeStyles((theme) => ({
  root: {
    display: "flex",
  },
  appBar: {
    zIndex: 1201,
    width: `calc(100% - ${drawerWidthMinimized}px)`,
    backgroundColor: "transparent",
    boxShadow: "none",
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
  },
  appBarShift: {
    marginLeft: drawerWidth,
    width: `calc(100% - ${drawerWidth}px)`,
    transition: theme.transitions.create(["width", "margin"], {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
  menuButton: {
    marginRight: 36,
    color: theme.palette.primary.main,
    "& svg": {
      backgroundColor: "red",
    },
  },
  logo: {
    maxWidth: "122px",
  },
  hide: {
    display: "none",
  },
  drawer: {
    width: drawerWidth,
    flexShrink: 0,
    whiteSpace: "nowrap",
  },
  drawerOpen: {
    width: drawerWidth,
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.enteringScreen,
    }),
    "& $logo": {
      backgroundImage: "url('/images/logo.png')",
      backgroundPosition: "center",
      backgroundSize: "contain",
      backgroundRepeat: "no-repeat",
      maxWidth: "100%",
      height: 60,
      width: 170,
      margin: "5px auto",
    },
  },
  drawerClose: {
    transition: theme.transitions.create("width", {
      easing: theme.transitions.easing.sharp,
      duration: theme.transitions.duration.leavingScreen,
    }),
    overflowX: "hidden",
    width: 0,
    [theme.breakpoints.up("sm")]: {
      width: theme.spacing(7) + 0,
    },
    "& $logo": {
      backgroundImage: "url('/images/logo_mob.png')",
      backgroundPosition: "center",
      backgroundSize: "contain",
      backgroundRepeat: "no-repeat",
      maxWidth: "100%",
      position: "unset",
      height: 30,
      width: 30,
      margin: "5px auto",
    },
  },
  toolbar: {
    display: "flex",
    alignItems: "center",
    justifyContent: "flex-end",
    padding: theme.spacing(0, 1),
    // necessary for content to be below app bar
    ...theme.mixins.toolbar,
  },
  content: {
    flexGrow: 1,
    padding: theme.spacing(3),
  },
  phoneBg: {
    color: "#de350b",
    fontSize: "19px",
    margin: "5px",
  },
  svg: {
    backgroundColor: "#ffcccc",
  },
}));

const Sidebar = (props) => {
  const { variant, /*onClose,*/ className, user, open, ...rest } = props;
  const classes = useStyles();
  const theme = useTheme();
  const urlParams = useParams();

  console.log("The user is ", user);
  console.log("the url is ", user && [7,1000].includes(parseInt(user.ProductId))?"/TermDashboard":"/dashboard/" + (urlParams.Token || "") + (urlParams.Source ? "/" + urlParams.Source : ""));
  /*const [open, setOpen] = React.useState(false);

  const handleDrawerOpen = () => {
    setOpen(true);
  };

  const handleDrawerClose = () => {
    setOpen(false);
  };*/
  // const [pages, setPages] = useState([{
  //   title: "Incentive",
  //   href: "/dashboard/" + (urlParams.Token || ""),
  //   icon: <DashboardIcon />,
  //   show: true,
  // }]);
  let [pages, setPages] = useState([
    {
      title: "Incentive",
      href: user && [7,1000].includes(parseInt(user.ProductId))?"/TermDashboard":"/dashboard/" + (user.AgentId || "") + (urlParams.Source ? "/" + urlParams.Source : ""),
      // href: "/dashboard/" + (urlParams.Token || "") + (urlParams.Source ? "/" + urlParams.Source : ""),
      icon: <DashboardIcon />,
      show: true,
    },
    {
      title: "JAG",
      href: "/jag/" + (user.AgentId || "") + (urlParams.Source ? "/" + urlParams.Source : ""),
      icon: <AccountBalanceIcon />,
      show: false,
    },
    {
      title: "FAQ",
      href: "/FAQ/" + (user.AgentId || "") + (urlParams.Source ? "/" + urlParams.Source : ""),
      icon: <HelpIcon />,
      show: true,
    },
    {
      title: "About Us",
      href: "/home/" + (user.AgentId || "") + (urlParams.Source ? "/" + urlParams.Source : ""),
      icon: <EmojiEventsIcon />,
      show: false,
    }
  ]);


  const checkforpagetitle = (title) => {

    var hasMatch = false;
    let menu = {
      Incentive: true,
      JAG: true,
    }
    if (localStorage.getItem("menu") !== null) {
      menu = JSON.parse(localStorage.getItem("menu"));
    }
    for (var index = 0; index < pages.length; ++index) {
      var page = pages[index];
      if (page.title === title) {
        page.show = menu[title]
        hasMatch = true;
        setPages(pages);
        break;
      }
    }
    return hasMatch;
  }

  useEffect(() => {
    console.log(user);
    checkforpagetitle("Incentive");
    if (user && user.JAGFlag) {
      checkforpagetitle("JAG");

      //if (!checkforpagetitle("JAG")) {
      // pages.push(
      //   {
      //     title: "JAG",
      //     href: "/jag/" + (urlParams.Token || ""),
      //     icon: <AccountBalanceIcon />,
      //     show: true,
      //   }
      // )
      //}
      //setPages(pages);
    }
  }, [props]
  );

  return (
    <div className={classes.root}>
      <CssBaseline />
      {/* <div id="log-slide">
        <div id="toggle">
          <i className="fa fa-angle-right"></i>
        </div>
        <div className="box">
          <ul>
            <li className="call-log-time">
              <label>2:40:36</label>
              <select>
                <option value="0">xxxxx99999</option>
              </select>
            </li>
            <li>
              <a href="#">
                <img src="/images/call_buzzer.png" />
              </a>
            </li>
            <li>
              <a href="#">
                <img src="/images/microphone.png" />
              </a>
            </li>
            <li>
              <a href="#">
                <img src="/images/filter.png" />
              </a>
            </li>
            <li>
              <span className="end-call">
                <a href="#">
                  <CallEndIcon className={classes.phoneBg} />
                </a>
              </span>
            </li>
          </ul>
        </div>
      </div> */}
      <AppBar
        position="fixed"
        className={clsx(classes.appBar, {
          [classes.appBarShift]: open,
        })}
      >
        {/* <Toolbar>
          <IconButton
            aria-label="open drawer"
            onClick={handleDrawerOpen}
            edge="start"
            className={clsx(classes.menuButton, {
              [classes.hide]: open,
            })}
          >
            <MenuIcon />
          </IconButton>
          <IconButton 
              color="inherit"
              aria-label="open drawer"
              onClick={handleDrawerClose} 
              className={clsx(classes.menuButton, {
                [classes.hide]: !open,
              })}>
              {theme.direction === 'rtl' ? <ChevronRightIcon /> : <ChevronLeftIcon />}
            </IconButton>
        </Toolbar> */}
      </AppBar>
      <Drawer
        variant="permanent"
        className={clsx(classes.drawer, {
          [classes.drawerOpen]: open,
          [classes.drawerClose]: !open,
        })}
        classes={{
          paper: clsx({
            [classes.drawerOpen]: open,
            [classes.drawerClose]: !open,
          }),
        }}
      >
        <div className={classes.logo}></div>
        <SidebarNav className={classes.nav} pages={pages} />
        <LastLogin />
      </Drawer>
    </div>
  );
};

Sidebar.propTypes = {
  className: PropTypes.string,
  // onClose: PropTypes.func,
  open: PropTypes.bool.isRequired,
  variant: PropTypes.string.isRequired,
  user: PropTypes.object,
};

export default Sidebar;
