import axios from 'axios';

import React, { useEffect, useState } from "react";

import { Document, Page } from 'react-pdf';

import { pdfjs } from 'react-pdf';

pdfjs.GlobalWorkerOptions.workerSrc = `https://unpkg.com/pdfjs-dist@2.16.105/legacy/build/pdf.worker.min.js`;

const PDFViewer = ({ pdfUrl }) => {

    const [numPages, setNumPages] = useState(0);
    const [pageNumber, setPageNumber] = useState(1);

    const [pdfData, setPdfData] = useState(null)


    useEffect(()=>{
      // console.log("the pdf url in PDFViewer is  ", pdfUrl);
      if(pdfUrl)
      {
      const fetchPDF = async () => {
        try {

          // const url = 'http://localhost/api/v1/Common/DownloadPdf';
          // const headers = {
          //     'Content-Type': 'application/json',
          //     Cookie: 'MatrixToken=eyJVc2VySWQiOiI0NTMyMCIsIkVtcGxveWVlSWQiOiJQVzAxODkwMCIsIkFzdGVyaXNrVG9rZW4iOiJlZDQ1YThjZS01MzY2LTQwZGMtYjkxMC1kYWE3NTY3ZDdkMWQtMjIxOTE0IiwiR3JvdXBJZCI6MTU1NSwiUm9sZUlkIjoiMiJ9',
          // };

          let urlData = pdfUrl.split('/');

          let modifiedUrl="";

          for(let i=0;i<urlData.length-1;i++)
          {
            if(i>=3)
            {
              modifiedUrl= modifiedUrl+ urlData[i]+'/';
            }
          }
          modifiedUrl= modifiedUrl+ urlData[urlData.length-1];

          // let modifiedUrl = urlData[urlData.length-3]+'/'+urlData[urlData.length-2]+'/'+urlData[urlData.length-1];

          const data = {
              Url: modifiedUrl,
          };

          // const response = await axios.post(url, data, { headers });


          const headers = {
              'Content-Type': 'application/json',
              // "token":"4c26c9e9-64e9-4aac-b981-c167b2cb983a",
          };

         
      // const response = await axios.post('http://localhost:80/api/v1/Download/DownloadPdf',body);

            const response = await axios.post('https://incentiveapi.policybazaar.com/api/Incentive/DownloadPdf',data,{headers: headers,  responseType: 'blob'});

            const blobPdfUrl = URL.createObjectURL(new Blob([response.data], { type: 'application/pdf' }));

            setPdfData(blobPdfUrl);

        } catch (error) {
            console.error('Error fetching PDF:', error.message);
        }
    };

    fetchPDF();
  }
    },[pdfUrl])
  
    function onDocumentLoadSuccess(numPages){
        
      setNumPages(numPages);
    
    }

  return (
    <div style={{ height: "100vh", width: "100%" }}>
     
         {/* <Document file={pdfData} onLoadSuccess={onDocumentLoadSuccess}> */}
        {/* <Document file={"https://cdn.filestackcontent.com/wcrjf9qPTCKXV3hMXDwK"} onLoadSuccess={onDocumentLoadSuccess}> */}
        {/* <Page pageNumber={pageNumber} />
      </Document> */}
      {/* <iframe
        id="pdf-iframe"
        src={googleDocsViewerUrl}
        width="100%"
        height="100%"
        style={{ border: "none" }}
        title="PDF Viewer"
      />
      <style>{`
                                    .ndfHFb-c4YZDc-Wrql6b{
                                    display: none !important
                                    }
                                  `}</style> */}               

            {pdfData ? (
              <div>
                <Document file={pdfData}  onLoadSuccess={onDocumentLoadSuccess} className="Pdfwidth">
                    <Page pageNumber={1} scale={0.75}/>
                </Document>
                {/* <div>
                  <button 
                      onClick={() => setPageNumber((prev) => Math.max(prev - 1, 1))} 
                      disabled={pageNumber === 1}
                  >
                  Previous
                  </button>
                  <button 
                      onClick={() => setPageNumber((prev) => Math.min(prev + 1, numPages))} 
                      disabled={pageNumber === numPages}
                  >
                  Next
                  </button>
                </div>
      
                <p>
                  Page {pageNumber} of {numPages}
                </p> */}
              </div> 
            ) : (
                <p>Loading PDF...</p>
            )}
    </div>
  );
};

export default PDFViewer;
