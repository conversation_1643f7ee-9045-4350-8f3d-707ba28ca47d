import React, { useEffect } from "react";
import { makeStyles } from "@material-ui/styles";
import {
  Grid,
} from "@material-ui/core";

import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';

//import DisclaimerBannerTERM from '/images/DisclaimerBannerTERM.png'

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const FinaleUpdate = (props) => {
  const { userId, productId } = props;
  const [open, setOpen] = React.useState(true);
  const [url, setUrl] = React.useState(null);

  const bindImage = () => {
    if (productId == 7) {
      setUrl("/images/DisclaimerBannerTERM.png");
    }
    else if (productId == 2) {
      setUrl("/images/DisclaimerBannerHEALTH.png");
    }
    else if (productId == 117) {
      setUrl("/images/DisclaimerBannerMOTOR.png");
    }
    else if (productId == 115) {
      setUrl("/images/DisclaimerBannerINVESTMENT.png");
    }

  }

  useEffect(() => {
    if (userId, productId) {
      bindImage();
    }

  }, [userId, productId]
  );


  const handleClose = () => {
    //debugger
    setOpen(false);

  }

  return (
    <>{url &&
      <Dialog
        open={open}
        onClose={() => handleClose()}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        className="FinaleUpdate"
      >
        {/* <DialogTitle id="alert-dialog-title">wall of fame</DialogTitle> */}
        <DialogContent>
          <img src={url} width="100%"></img>
        </DialogContent>

      </Dialog>
    }
    </>
  );
};

export default FinaleUpdate;
