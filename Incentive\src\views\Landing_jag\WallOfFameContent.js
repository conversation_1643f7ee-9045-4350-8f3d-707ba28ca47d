import React, { useEffect } from "react";
import { makeStyles } from "@material-ui/styles";
import {
    Grid,
} from "@material-ui/core";

import * as services from "../../services";
import { getCookie, setCookie } from "../../utils/utility";

const useStyles = makeStyles((theme) => ({
    root: {
        padding: theme.spacing(0),
    },
    margin: {
        margin: theme.spacing(1),
    },
    extendedIcon: {
        marginRight: theme.spacing(1),
    },
    forBox: {
        width: "100%",
    },
    card: {
        background: "#ffffff",
        boxShadow: "0px 0px 26px #0000001a",
        borderRadius: "20px",
    },

    cardBody: {
        padding: "15px 15px 0 !important",
    },
}));

export default function WallOfFameContent(props) {
    const { userId, productId } = props;
    const [open, setOpen] = React.useState(false);
    const [wallOfFame, setWallOfFame] = React.useState([]);
    const [count, setCount] = React.useState(0);

    const getDetail = () => {

        if (!userId && !productId) {
            return;
        }

        services
            .API_GET(`Jag/GetWallOfFame/${productId}`)
            .then(response => {
                if (response && response != "[]") {

                    if (response.Status) {
                        let data = JSON.parse(response.Response);
                        if (data.length > 0 && parseInt(data[0]) > 5) {
                            setCount(parseInt(data[0]));
                            setWallOfFame(JSON.parse(data[1]));
                            setOpen(true)
                        }
                    }
                }
            })
            .catch((err) => {
                console.log("Error", err);
            });

    }



    useEffect(() => {
        if (userId, productId) {
            getDetail();
        }

    }, [userId, productId]);



    return (
        <div className="wallofFamePopup">
            <div className="wallofFamecontent">
                <Grid container spacing={2}>
                    <Grid item xs={4} sm={4} md={4}>
                        <img src="/images/JAGLogo.png" className="jaglogo" />
                    </Grid>
                    <Grid item xs={4} sm={4} md={4}>
                        <div className="wofLogo">
                            <img src="/images/woflogo.svg" />
                            <span> {count} agents are going to make lottery tickets in your BU,
                  stay tuned for updates...</span>
                        </div>
                    </Grid>
                    <Grid item xs={4} sm={4} md={4} className="text-right">
                        <img src="/images/pblogo.svg" className="pblogo" />
                    </Grid>
                </Grid>
                <Grid container spacing={2}>
                    <Grid item xs={2} sm={2} md={2} className="text-center"><img src="/images/Pole.svg" style={{ height: "280px" }} /></Grid>
                    <Grid item xs={8} sm={8} md={8} >
                        <div id="scrollbar" className="scrollWOF">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Employee ID</th>
                                        <th>Lottery Ticket</th>
                                        <th>Gold/Silver</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {
                                        wallOfFame && wallOfFame.length > 0 && wallOfFame.map((item, index) =>
                                            <tr className={item.MemberType.toLowerCase()}>
                                                <td>{item.UserName}</td>
                                                <td>{item.EmployeeId}</td>
                                                <td>{item.LotteryTicket}</td>
                                                <td>

                                                    {item.MemberType.toLowerCase() == 'silver' ? <img src="/images/btn_silver.svg" /> : <img src="/images/btn_gold.svg" />}

                                                </td>
                                            </tr>
                                        )}
                                </tbody>
                            </table>
                        </div>
                    </Grid>
                    <Grid item xs={2} sm={2} md={2} className="text-center"><img src="/images/Pole.svg" style={{ height: "280px" }} /></Grid>
                    <Grid item xs={12} sm={12} md={12}><div className="text-center radbg">
                        <img src="/images/bg.svg" />
                    </div></Grid>
                </Grid>
            </div>
        </div>
    );
};


