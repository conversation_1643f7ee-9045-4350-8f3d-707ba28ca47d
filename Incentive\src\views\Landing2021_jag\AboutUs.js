import React, { } from "react";
import {
  Grid,
} from "@material-ui/core";
import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogTitle from '@material-ui/core/DialogTitle';
import Slide from '@material-ui/core/Slide';


import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';


export default function AboutUs(props) {


  var slider_leaderboard = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 1000,
    slidesToShow: 1,
    slidesToScroll: 1,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1024,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          slidesPerRow: 4,
          dots: true

        }
      },
      {
        breakpoint: 600,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          slidesPerRow: 4,
          initialSlide: 0
        }
      },
      {
        breakpoint: 480,
        settings: {
          slidesToShow: 1,
          slidesPerRow: 4,
          slidesToScroll: 1
        }
      }
    ]
  };

  const Transition = React.forwardRef(function Transition(props, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
  });

  const [open, setOpen] = React.useState(false);
  const [url, setUrl] = React.useState(false);

  const handleClickOpen = (url) => {
    setOpen(true);
    setUrl(url)
  };

  const handleClose = () => {
    setOpen(false);
    setUrl(null)
  };

  // const bindVideo = () => {
  //   const { url } = this.state;
  //   return <Dialog
  //     open={open}
  //     TransitionComponent={Transition}
  //     keepMounted
  //     onClose={handleClose}
  //     aria-labelledby="alert-dialog-slide-title"
  //     aria-describedby="alert-dialog-slide-description"
  //   >
  //     {/* <DialogTitle id="alert-dialog-slide-title"></DialogTitle> */}
  //     <DialogContent>
  //       <iframe width="540" height="315" src={url} title="JAG Videos" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen></iframe>
  //     </DialogContent>
  //   </Dialog>
  // }


  return (


    <Grid item md={12} xs={12} className="leaderboard" id="Winners">
      <h2>
        Hola! 2021 <b>Winners</b>
      </h2>

      <p className="textMsg">Lorem ipsum dolor sit amet, consetetur sadipscing elitr, sed diam nonumy eirmod tempor invidunt ut labore et dolore</p>
      <Grid container>
        <Grid item md={9} xs={12} >
          <Slider {...slider_leaderboard}>
            <div>
              <Card className="card">
                <CardContent>
                <div className="slide">
                    <img src="/images/winners/JAG_YT_Umang-thakur_02.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/lDR11vrVgLM")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/lDR11vrVgLM")} */}
                  </div>
                  <div className="slide">
                    <img src="/images/winners/JAG_YT_Chirag-Kumar.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/Nszp1movIW4")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/Nszp1movIW4")} */}

                  </div>
                  <div className="slide">
                    <img src="/images/winners/JAG_YT_Lakshay.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/VuB02uPOnlg")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/VuB02uPOnlg")} */}
                  </div>
                  <div className="slide">
                    <img src="/images/winners/JAG_YT_Shubham.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/CsfzCyikkh0")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/CsfzCyikkh0")} */}
                  </div>       
         

                </CardContent>
              </Card>
            </div>


            <div>
              <Card className="card">
                <CardContent>
                <div className="slide">
                    <img src="/images/winners/Neha_YT-cover.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/oUdPya05iUU")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/oUdPya05iUU")} */}
                  </div>
                  <div className="slide">
                    <img src="/images/winners/Kautuk-Jha_YT-Cover.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/Bfwtgzwu57A")}>Play Video</a>
                  </div>
              
                  <div className="slide">
                    <img src="/images/winners/JAG_Sunita-Rani.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/qkvZbzxKkiY")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/qkvZbzxKkiY")} */}

                  </div>
                 
                  <div className="slide">
                    <img src="/images/winners/Rishab-Jain_YT-cover.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/im6CKx-CFzg")}>Play Video</a>
                    {/* {bindVideo("https://www.youtube.com/embed/im6CKx-CFzg")} */}
                  </div>
               
                

                </CardContent>
              </Card>
            </div>
            <div>
              <Card className="card">
                <CardContent>
              
                <div className="slide">
                    <img src="/images/winners/JAG_ROhit-kumar.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/t2B5rayXJJE")}>Play Video</a>

                  </div>
                <div className="slide  mt-200">
                    <img src="/images/winners/Gaurav-Rawat_YT-Cover.jpg" alt="" className="circle-fluid" />
                    <a onClick={() => handleClickOpen("https://www.youtube.com/embed/I-zD67iZh4I")}>Play Video</a>

                  </div>
                

                </CardContent>
              </Card>
            </div>

          </Slider>
          <Dialog
            open={open}
            
            onClose={handleClose}
            
          >
            <DialogContent>
              <iframe width="540" height="315" src={url} title="JAG Videos" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowFullScreen></iframe>
            </DialogContent>
          </Dialog>
        </Grid>
        <div className="winnerbanner">
          <img src="/images/winnerbanner.png" />
        </div>
      </Grid>



    </Grid>

  );
}
