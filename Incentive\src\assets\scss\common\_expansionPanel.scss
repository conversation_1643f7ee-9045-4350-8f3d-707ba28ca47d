@import '../variables/variables';

.expansion-panel{
    background: #FAFAFA !important;
    margin: 0 !important;   
    >div{
        &[aria-expanded="true"]{
            background: $white;
            border-radius: 4px;
            span:first-child{
                svg{
                    display: none;
                }
                &:after{
                    content: '\f068';
                    font-family: 'FontAwesome';
                    width: 24px;
                    height: 24px;
                    display: inline-block;
                    font-size: 14px;
                    transition: fill 200ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
                    line-height: 24px;
                }
            }
            .heading{
                color: $primary-color
            }
        }
        &:nth-child(2){
            background: $white;
            border-radius: 4px;
        }
        span:first-child{
            border: 1px solid $secondary-color;
            border-radius: 50%;
            color: $secondary-color
        }
        .heading{
            color: $secondary-color;
            font: 500 16px/20px Lato;

        }
    }
    
}