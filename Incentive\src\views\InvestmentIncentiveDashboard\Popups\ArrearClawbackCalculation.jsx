import React,{useEffect, useState, useContext, useRef} from "react";
import DataTable from "../Components/DataTable2";
import { GetCommonData } from "../../../common/CommonAction";
import { rupeeConverter } from  "../../Dashboard2024/Utility/Utility";
import { DashboardContext } from "../Context/Context";
import { handleDownload } from  "../../Dashboard2024/Utility/Utility";
import { getUserDetails } from  "../../Dashboard2024/Utility/Utility";
import {Tooltip } from "@mui/material";
import Loader from "../Components/Loader";


const ArrearClawbackCalculation = () => {
    const [rows, setRows] = useState([]);
    const [columns, setColumns]= useState([]);
    const [overallCalcData, setOverallCalcData]= useState([]);
    const [overallCalcCol, setOverallCalcCol] = useState([]);
    const [loader, setLoader]= useState(true);


    const dataToExport1  = useRef([]);
    const dataToExport2 = useRef([]);
    const dashboardData= useContext(DashboardContext);

    let rupee = rupeeConverter();
    
    let sortColumns=['originalWtAPE'];


    let arrearClawbackDataProcess=(data)=>{

        let previousCalcListCols= (data.hasOwnProperty('previousCalcLabel') && data['previousCalcLabel']) || {};
        let previousCalcData= ( Array.isArray(data['previousCalcList']) && data['previousCalcList']) || [];
        let bookingStatusLabel = (data.hasOwnProperty('bookingStatusLabel') && data['bookingStatusLabel']) || {};
        let bookingStatusData = (Array.isArray(data['bookingsStatusUpdate']) && data['bookingsStatusUpdate'])|| [];
        let columns=[];
    

        if(previousCalcData.length==0 && bookingStatusData.length==0)
        {
            dashboardData.closeFunction('arrearClawBack');
            setLoader(false);
            return;
        }

        
    
        if(Object.keys(previousCalcListCols).length> 0)
        {
            // columns.push({
            //     id: 'bookingMonth',
            //     type: typeof('a'),
            //     sort: false,
            //     label: 'Booking Month',
            //     disablePadding:false,
            // })
            let tempSort = (Array.isArray(sortColumns) && sortColumns.length > 0 && sortColumns) || [];
            
            Object.keys(previousCalcListCols).map((key)=>{
                let column = {
                    id: key,
                    type: typeof ('a'),
                    sort: tempSort.includes(key) ? true : false,
                    label: previousCalcListCols[key],
                    disablePadding: false,
                    color: key=='finalIncentive' ? true : false
                };
                columns.push(column);
            })

            columns.push({
                id:'button',
                type:typeof('a'),
                sort: false,
                label:null,
                disablePadding:false
            })
        }
        
        if( previousCalcListCols.hasOwnProperty('bookingMonth') && previousCalcData.length>0)
        {
            
                  dataToExport1.current = JSON.parse(JSON.stringify(previousCalcData));
                  let bookingMonthStartIndex= new Map();
                  let bookingMonthCount= new Map();


                    previousCalcData.map((bookings, index)=>{

                        if(!bookings){return null}

                        if(bookingMonthStartIndex.has(bookings['bookingMonth']))
                        {
                             bookingMonthCount.set(bookingMonthStartIndex.get(bookings['bookingMonth']), bookingMonthCount.get(bookingMonthStartIndex.get(bookings['bookingMonth']))+1);
                        }
                        else{
                            bookingMonthStartIndex.set(bookings['bookingMonth'],index);
                            bookingMonthCount.set(bookingMonthStartIndex.get(bookings['bookingMonth']),1);
                        }
                      
                      
                        
                        bookings['bookingMonth'] = bookings['bookingMonth'];
                        bookings['ClassName'] = (bookings['finalIncentive'] || 0) >= 0?'statusYes' : 'statusNo';
                        bookings['sourcedAPE'] = rupee.format(bookings['sourcedAPE']) ;
                        bookings['issuedAPE'] = rupee.format(bookings['issuedAPE'] );
                        bookings['weightedAPE']= rupee.format(bookings['weightedAPE']);
                        bookings['cjIncentive'] = rupee.format(bookings['cjIncentive']);
                        bookings['netDiff'] = rupee.format(bookings['netDiff']);
                        bookings['finalIncentive']= rupee.format(bookings['finalIncentive']);
                        bookings['incentiveSlabcd']= bookings['incentiveSlab']+'%' || '-';

                        if(Array.isArray(bookings['breakup']) &&  bookings['breakup'].length>0)
                        {
                            bookings['breakup'].forEach((booking)=>{
                                booking['bookingMonth']=bookings['bookingMonth']
                                booking['weightedAPE'] = rupee.format(booking['weightedAPE']);
                                booking['incentiveSlab']= booking['incentiveSlab']+'%' || '-';
                                booking['cjIncentive']= rupee.format(booking['cjIncentive']);
                                booking['ClassName'] =bookings['ClassName'];
                            })
                        }
                       
                    }) ;

                    bookingMonthCount.forEach((value,key)=>{
                        previousCalcData[key]['span']=value;
                    })

              

        }
     

        setOverallCalcCol(columns);
        setOverallCalcData(previousCalcData);

        let cols2 = [];
        
        if(Object.keys(bookingStatusLabel).length> 0)
        {
            cols2.push(
                {
                    id: 'ListNo',
                    type: typeof (1),
                    sort: true,
                    label: 'Sr. No',
                    disablePadding: true
                }
            );
       
            let tempSort =  (Array.isArray(sortColumns) && sortColumns.length > 0 && sortColumns) || [];
           
            Object.keys(bookingStatusLabel).map((key) => {
    
                let column = {
                    id: key,
                    type: typeof ('a'),
                    sort: tempSort.includes(key) ? true : false,
                    label: bookingStatusLabel[key],
                    disablePadding: false,
                    sum:0,
                    extraRow: null
                }
               
                cols2.push(column);
            });
        }
        let sumOriginalWtAPE = 0 ;
        let sumRevisedWtAPE = 0;
        
        if (bookingStatusData.length>0) {
            
        
           dataToExport2.current= JSON.parse(JSON.stringify(bookingStatusData));
            bookingStatusData.map((bookings, index) => {
                if (!bookings) { return null }
                sumOriginalWtAPE = sumOriginalWtAPE + bookings['originalWtAPE'];
                sumRevisedWtAPE = sumRevisedWtAPE + bookings['revisedWtAPE'];
                bookings['ListNo'] = index + 1;
                bookings['ClassName'] = (bookings['revisedWtAPE'] || 0) >= (bookings['originalWtAPE'] || 0) ? 'statusYes' : 'statusNo';
                bookings['sourcedAPE'] = rupee.format(bookings['sourcedAPE']);
                bookings['originalWtAPE'] = rupee.format(bookings['originalWtAPE']);
                bookings['revisedWtAPE'] = rupee.format(bookings['revisedWtAPE']);
            })
        }
        
        let difference = sumRevisedWtAPE - sumOriginalWtAPE;
        
        cols2.map((col)=>{
            if(col.id=='originalWtAPE')
            {
                let arr =[];
                arr.push("Sum = "+rupee.format(sumOriginalWtAPE));
                arr.push(<>Change in Weighted APE  <Tooltip title="Sum of Revised Weighted APE - Sum of Original Weighted APE" arrow
                placement="bottom"
            >
                <i className="fa fa-info-circle"></i>
            </Tooltip></>);
                col.extraRow = arr;
            }
            else if(col.id== 'revisedWtAPE')
            {
               let arr= [];
               arr.push("Sum = "+rupee.format(sumRevisedWtAPE));
               arr.push(rupee.format(difference));
               col.extraRow = arr;
            }
        })

        setColumns(cols2);
        setRows(bookingStatusData);
        setLoader(false);
 
    }


    const handleDownloadButton=()=>{
        handleDownload(dataToExport1.current,'ac1');
        handleDownload(dataToExport2.current,'ac2');
    }


    useEffect(()=>{

        if(dashboardData.dashboardData && dashboardData.dashboardData.ArrearClawback)
        {
           
            arrearClawbackDataProcess(JSON.parse(JSON.stringify(dashboardData.dashboardData.ArrearClawback)));   
        }
        else{
        // let body = {
        //     eCode: "PW00000" || dashboardData.agentDetails.EmpId,
        //     monthYear: "NOV2023" || dashboardData.monthChosen,
        //     userid: 91072,
        //     bu: "investment"
        // }
        let body={
            eCode: dashboardData.agentDetails && dashboardData.agentDetails.EmpId,
            monthYear:  dashboardData.monthChosen,
            userid: getUserDetails('UserId'),
            bu: "investment"
        }
       
        let data = {EndPoint:"arrearClawbackInvestment", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo:"investment", body: JSON.stringify(body)};
    
        GetCommonData(
            "POST", data
            ,(errorStatus,data)=>{
            if(!errorStatus && data)
            {
             
                dashboardData.setDashboardData(data,'ArrearClawback');
                // arrearClawbackDataProcess([]);
                arrearClawbackDataProcess(JSON.parse(JSON.stringify(data)));
            }
            else{
                setRows([]);
                setColumns([]);
                setOverallCalcCol([]);
                setOverallCalcData([]);
                dashboardData.setDashboardData(null, 'ArrearClawback');
            }
           
        });
    }
    
    },[])

    return (
       <>

{  loader ? <Loader/>:
       
        <div className="BookingBreakdown">
            <div className="Heading">
                <h3>Net Arrear/Clawback</h3>   
                { rows.length> 0 && overallCalcData.length>0 &&
                <button onClick={handleDownloadButton}><img src="/images/TermDashboard/download.svg" /> Download</button>
                }
            </div>
            {overallCalcCol && overallCalcData && overallCalcData.length>0 &&
             <>
            <h5>Overall Calculations</h5>
            <DataTable data={overallCalcData} columns={overallCalcCol} cellSpan={'bookingMonth'} breakup={true} />
            </>
            }
            <br/>
            {columns && rows && rows.length>0 &&
            <>
            <h5>Bookings where Status Updated</h5>

            <DataTable data={rows} columns={columns} headerSpan={'ListNo'} extraRow={2} headerColor={'#0065FF1A'}/>
            </>
            }
        </div>

}
        
       </> 
    )
}
export default ArrearClawbackCalculation;