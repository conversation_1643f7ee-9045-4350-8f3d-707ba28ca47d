import * as actionTypes from '../ActionsTypes';

const initialState = {
    token: null,
    isAuth:null,
    errors:[]
};


const authSuccess = (state, action) => {
    console.log(action);
    
    return {
        ...state, 
        token: action.token || state.token,
        user: action.user,
        isAuth:true,
        errors: []
    }
};


const reducer = (state = initialState, action) => {
    switch (action.type) {
        case actionTypes.AUTH_SUCCESS: return authSuccess(state, action);
        default:
            return state;
    }
};

export default reducer;