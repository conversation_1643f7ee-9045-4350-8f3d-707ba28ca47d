import React, { useEffect, useState } from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../Loader";
import Table from '@material-ui/core/Table';
import TableBody from '@material-ui/core/TableBody';
import TableCell from '@material-ui/core/TableCell';
import TableContainer from '@material-ui/core/TableContainer';
import TableHead from '@material-ui/core/TableHead';
import TableRow from '@material-ui/core/TableRow';
import Tooltip from '@material-ui/core/Tooltip';
import Zoom from '@material-ui/core/Zoom';
import moment from "moment";

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    color: "#0164ff",
    padding: "10px",
    boxShadow: "0px 0px 26px #0000001a",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);


const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },

  table: {
    border: "2px solid #4e93ff",
    margin: "10px auto",
  },
  tblCell: {
    "&.MuiTableCell-body": {
      color: "#0164ff"
    },
  },
  bold: {
    fontWeight: "700",
    color: "#000000 !important"
  },
  underline: {
    fontDecoration: "underline"
  },
  blueRow: {
    background: "#e5efff"
  },
  memberType: {
    textTransform: "capitalize"
  },
  redText: {
    color: "red"
  }
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

const ProjectedRewardCalculationCriteria = withStyles(styles)((props) => {
  const { show, handleClose, superGroupId, productId, date, classes, AvailableData } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);


  useEffect(() => {
    setIsLoading(false);
  }, [props]);


  const showReason = (AvailableData) => {
    let result = null;
    //debugger;
    if (AvailableData.CashRewards == 0) {
      if (AvailableData.Reason && AvailableData.Reason == "NOGROWTH") {

        if (AvailableData.ActualG2 > AvailableData.MinValue && AvailableData.TotalPoints < 500) {
          result = <span className={classes.redText}>You need minimum 500 points to make rewards</span>;
        }
        else {
          result = <span className={classes.redText}>According to the current projections, your {['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(AvailableData.BU) == -1 ? "APE" : "BKG"} growth criteria has not met</span>;
        }

      }
    }

    return result;
  }


  const showMinPoints = (AvailableData) => {
    let minBookings = 0;
    if (AvailableData.CashRewards > 0) {
      return 0;
    }
    if (AvailableData.ActualG2 < AvailableData.MinValue) {
      minBookings = (AvailableData.TotalBKG_PrevYear * (1 + (AvailableData.OverAllBUBKGSGrowth / 200)) * ((100 + AvailableData.MinValue) / 100)).toFixed(0)
    }
    else if (AvailableData.ActualG2 > AvailableData.MinValue) {
      minBookings = (AvailableData.TotalBKG_PrevYear * (1 + (AvailableData.OverAllBUBKGSGrowth / 200)) + (500 / AvailableData.SlabPoints) * 10).toFixed(0);
    }
    return minBookings;
  }


  return (
    <div>
      {/* <Button variant="outlined" color="primary" onClick={handleClickOpen}>
        Open dialog
      </Button> */}

      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"md"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
          <strong>Rewards Calculation Details</strong>
          {<span> Last Updated On: {moment().subtract(1,'days').format("DD-MMMM-YYYY")}</span>}
        </DialogTitle>
        <DialogContent className="critiria-popup">
          <div className="popup-inner-box">
            <div className="criteriaHtml">
              <TableContainer >
                <Table className="" size="large">
                  <TableBody>
                    <TableRow>
                      <TableContainer >
                        <Table className={classes.table} size="large">
                          <TableBody>

                          {AvailableData.BU && ["Health Renewal","Motor","Motor Renewal"].indexOf(AvailableData.BU) != -1 &&<TableRow>
                          <TableCell className={classes.tblCell}>Total Sourced Bookings FY 21-22</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{
                                !AvailableData.TotalBKG_CurrYear ? 0 : (AvailableData.TotalBKG_CurrYear)
                              }</TableCell>
                            </TableRow>}

                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Issued bookings FY 21-22 <span>  </span>
                              <WrapperTooltip
                              disableFocusListener
                              TransitionComponent={Zoom}
                              placement="top"
                              arrow
                              title= {"Issued Bookings will keep updating on a daily basis till May'2022"}
                            classes={{ tooltip: classes.customWidth }}>
                  <i className="fa fa-info-circle"></i>
              </WrapperTooltip>
              
                              </TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">
                                {
                                  !AvailableData.IssuedBKG_CurrYear ? 0 : (AvailableData.IssuedBKG_CurrYear)
                                }
                              </TableCell>
                            </TableRow>
                            {/* <TableRow>
                              <TableCell className={classes.tblCell}>Total Projected Issued bookings FY 21-22 (This Year)
                              <span>  </span>
                              <WrapperTooltip
                              disableFocusListener
                              TransitionComponent={Zoom}
                              placement="top"
                              arrow
                              title= {"Data will keep updating on a daily basis."}
                            classes={{ tooltip: classes.customWidth }}>
                  <i className="fa fa-info-circle"></i>
              </WrapperTooltip></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"> {
                                !AvailableData.ProjectedBKG_CurrYear ? 0 : AvailableData.ProjectedBKG_CurrYear
                              }</TableCell>
                            </TableRow> */}
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            <TableRow>
                              <TableCell className={classes.tblCell + ' ' + classes.underline}><em>Overall Bookings growth of <span className={classes.memberType}>{AvailableData.MemberType}</span> Category agents is {AvailableData.OverAllBUBKGSGrowth || 0}% this year, inflation factor becomes</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{
                                !AvailableData.OverAllBUBKGSGrowth ? 0 : (AvailableData.OverAllBUBKGSGrowth / 2).toFixed(1)
                              }%</TableCell>
                            </TableRow>
                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Issued Bookings FY 20-21 (Last Year)</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"> {
                                !AvailableData.TotalBKG_PrevYear ? 0 : AvailableData.TotalBKG_PrevYear
                              }</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>Inflated Issued Bookings FY 20-21 (Last Year)</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"> {
                                !AvailableData.TotalBKG_PrevYear ? 0 : (AvailableData.TotalBKG_PrevYear * (1 + (AvailableData.OverAllBUBKGSGrowth / 200))).toFixed(0)
                              }</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            {/* <TableRow>
                              <TableCell className={classes.tblCell}><em>Annual Projected Growth (POST INFLATION)</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.ActualG1 || 0}%</TableCell>
                            </TableRow> */}
                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}>Annual Bookings Growth</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.ActualG2 || 0}%</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>Your points per 10 additional bookings</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.SlabPoints || 0}</TableCell>
                            </TableRow>
                          </TableBody>
                        </Table>
                        <Table className={classes.table} size="large">
                          <TableBody>
                            <TableRow>
                              <TableCell className={classes.tblCell}><em> Reward Points</em></TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.TotalPoints || 0}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}>{AvailableData.Reason && AvailableData.Reason != "" ? "Reason" : ""}</TableCell>
                              <TableCell className={classes.tblCell} align="right">
                                {
                                  showReason(AvailableData)
                                }

                              </TableCell>
                            </TableRow>



                            <TableRow className={classes.blueRow}>
                              <TableCell className={classes.tblCell}> Tickets <span> </span>
                             
                             <WrapperTooltip
                               disableFocusListener
                               TransitionComponent={Zoom}
                               placement="top"
                               arrow
                               title= {"Your sourcing for FY 21-22 is closed. Your rewards and ticket will keep on updating based on your issuance till May 31, 2022."}
                               classes={{ tooltip: classes.customWidth }}>
                               <i className="fa fa-info-circle"></i>
                             </WrapperTooltip>
                              </TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">{AvailableData.LotteryTicket || 0}</TableCell>
                            </TableRow>
                            <TableRow>
                              <TableCell className={classes.tblCell}> Cash Rewards</TableCell>
                              <TableCell className={classes.tblCell + ' ' + classes.bold} align="right"><i className="fa fa-inr"></i> {AvailableData.CashRewards || 0}</TableCell>
                            </TableRow>

                            {
                              showMinPoints(AvailableData) > 0 &&
                              <TableRow>
                                <TableCell className={classes.tblCell}>
                                  <span className={classes.redText}>Minimum booking required for complete FY 21-22 to make incentive</span></TableCell>
                                <TableCell className={classes.tblCell + ' ' + classes.bold} align="right">
                                  <span className={classes.redText}>{showMinPoints(AvailableData)}</span>
                                </TableCell>
                              </TableRow>
                            }

                          </TableBody>
                        </Table>
                      </TableContainer>
                    </TableRow>
                  </TableBody>
                </Table>
              </TableContainer>
            </div>

            {/* 
          criteriaList && criteriaList.length > 0 && criteriaList.map((item, index) =>
          <p key={index}>
            {item.Description}
          </p>
          )
          */}

          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
});
export default ProjectedRewardCalculationCriteria;