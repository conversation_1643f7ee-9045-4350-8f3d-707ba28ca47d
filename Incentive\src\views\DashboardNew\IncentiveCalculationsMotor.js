import React, { useEffect, useState, Fragment } from "react";
import { withStyles, makeStyles } from "@material-ui/styles";
import * as services from "../../services";
import Skeleton from "@material-ui/lab/Skeleton";
import _ from "lodash";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  Button,
} from "@material-ui/core";
import moment from "moment";
import Zoom from '@material-ui/core/Zoom';
import Tooltip from '@material-ui/core/Tooltip';


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(0),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  content: {
    paddingTop: 150,
    textAlign: "center",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },
  textRight: {
    textAlign: "right",
  },
  textCenter: {
    textAlign: "center",
  },
  cardBody: {
    padding: "15px 15px 0 !important",
  },
  image: {
    marginTop: 50,
    display: "inline-block",
    maxWidth: "100%",
    width: 560,
  },
  headTop: {
    background: "#fff",
    boxShadow: "0px 0px 16px #00000014",
    borderRadius: "18px",
    padding: "0 10px",
    alignItems: "center",
    height: "70px",
    margin: 0,
    "& h5": {
      display: "block",
      fontSize: "16px",
      color: "#303030",
      fontWeight: 500,
      margin: "5px 15px 0",
    },
    "& svg": {
      background: " #c8dcfa 0% 0% no-repeat padding-box",
      borderRadius: "50%",
      padding: "8px",
      width: "35px",
      height: "35px",
      color: "#0052cc",
    },
    "& ul": {
      display: "table",
      width: "100%",
      "& li": {
        display: "table-cell",
        width: "auto",
      },
    },
  },
  expandIcon: {
    background: "#00398e",
    color: "#fff",
    borderRadius: "50%",
  },
  warningBtn: {
    background: "#c8dcfa",
    borderRadius: "17px",
    margin: "5px 0",
    fontSize: "12px",
    color: "#0052cc",
    "&:hover": {
      background: "#c8dcfa",
    },
  },
}));

const WrapperTooltip = withStyles(theme => ({
  tooltip: {
    backgroundColor: "#fff",
    padding: "10px",
    boxShadow: "0px 6px 16px #3469CB29",
    fontSize: 11,
    borderRadius: "5px",
    maxWidth: "300px",
    "& ul": {
      "& li": {
        listStyle: "none",
        color: "#808080",
      },
    },
  },
  arrow: {
    color: "#fff",
  }
}))(Tooltip);


const IncentiveCalculationsMotor = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [SourcingDetails, setSourcingDetails] = useState([]);
  const [PlanCategory, setPlanCategory] = useState([]);
  const response = JSON.parse(localStorage.getItem('user'));


  useEffect(() => {
    setSourcingDetails([]);
    setPlanCategory([]);
    if (!userId) {
      return;
    }
    //http://localhost:53481/api/incentive/GetAgentIncMotor/33390/01-08-2021
    //let url = "http://localhost:53481/api/incentive/GetAgentIncMotor/9930/01-08-2021";
    let url =`Incentive/GetAgentIncMotor/${userId}/${date}`;

    services
      .API_GET(url).then(response => {
        //debugger;
        if (response && response.Status && response.Response != "[]") {
          response = JSON.parse(response.Response)

          let sourcingDetails = JSON.parse(response[1]);

          let newObjsourcingDetails = {};
          let totalSourcedBooking = 0;
          let totalIssuedBookings = 0;
          let totalEligibleforInc = 0;
          for (let index = 0; index < sourcingDetails.length; index++) {
            const element = sourcingDetails[index];
            totalSourcedBooking = totalSourcedBooking + element.SourcedBooking;
            totalIssuedBookings = totalIssuedBookings + element.IssuedBookings;
            totalEligibleforInc = totalEligibleforInc + element.EligibleforInc;
          }
          newObjsourcingDetails.InsurerCategory = "Grand Total";
          newObjsourcingDetails.SourcedBooking = totalSourcedBooking;
          newObjsourcingDetails.IssuedBookings = totalIssuedBookings;
          newObjsourcingDetails.EligibleforInc = totalEligibleforInc;
          sourcingDetails.push(newObjsourcingDetails);


          setSourcingDetails(sourcingDetails);


          let plancategory = JSON.parse(response[2]);

          let newObj = {};
          let totalEligibleBookings = 0;
          let totalIncentivePool = 0;
          for (let index = 0; index < plancategory.length; index++) {
            const element = plancategory[index];
            totalEligibleBookings = totalEligibleBookings + element.EligibleBookings;
            totalIncentivePool = totalIncentivePool + element.IncentivePool;
          }
          newObj.PolicyType = "Grand Total";
          newObj.EligibleBookings = totalEligibleBookings;
          newObj.IncentivePool = totalIncentivePool;
          plancategory.push(newObj);


          setPlanCategory(plancategory);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [userId,date]);

  const renderPayoutIncentiveTooltip = (data) => {

    return <div className="common-tooltip-popup">
      <span>(As per the Issuance till {moment(data.IncentiveDate).format('Do MMMM')})</span>
    </div>
  }

  return (
    <div className={classes.root} className = "incentive">
     
      
      <Card className={classes.card}>
        <ul className="incentive-box table-incentive">
          <h6>
            Incentive Calculations
          </h6>
          
          <li>
            <table className="motorIncentive">
              <thead>
                <tr>
                  <th>Category</th>
                  <th>Commercial TP</th>
                  <th>Commercial Car</th>
                  <th>TP</th>
                  <th>Bookings</th>
                  <th>Total Eligible <br/>Bookings</th>
                </tr>
              </thead>
              <tbody>
                {PlanCategory && PlanCategory.length > 0 && PlanCategory.map((data, index) => {
                  return <tr>
                    <td>{data.PolicyType=="NONPSU"? "Non PSU": data.PolicyType}</td>
                    <td>{data.CommercialTP}</td>
                    <td>{data.CommercialCar}</td>
                    <td>{data.TP}</td>
                    <td>{data.TotalBookings}</td>
                    <td>{data.EligibleBookings}</td>
                  </tr>
                })}

                <tr>
                  <td>&nbsp;</td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                  <td></td>
                </tr>

                <tr>
                  <th>Multipliers Used</th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th></th>
                  <th>Total Incentive before <br/>salary deduction</th>
                </tr>

                {PlanCategory && PlanCategory.length > 0 && PlanCategory.map((data, index) => {
                  return <tr>
                    <td>{data.PolicyType=="NONPSU"? "Non PSU": data.PolicyType}</td>
                    <td>{data.CommercialTPSlab}</td>
                    <td>{data.CommercialCarSlab}</td>
                    <td>{data.TPSlab}</td>
                    <td>{data.Slab}</td>

                    <td><i className="fa fa-inr"></i> {parseFloat(data.IncentivePool).toLocaleString('en-IN')}</td>
                  </tr>
                })}

              </tbody>
            </table>
          </li>
        </ul>
      </Card>
    </div>
    
  );
};

export default IncentiveCalculationsMotor;
