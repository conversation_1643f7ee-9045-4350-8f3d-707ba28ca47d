@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;700&display=swap");


html {
  scroll-behavior: smooth;
}

.full-width {
  width: 100%;
}

.no-padding {
  padding: 0;
}

.clear {
  clear: both;
}

.tab-view {
  background-color: #fff;
  margin: 2em 0 0;

  .MuiTabs-flexContainer {
    background-color: #fff;
  }

  .PrivateTabIndicator-colorPrimary-36 {
    background-color: #4669d6 !important;
  }
}

.month-view {
  margin: 15px 5px 10px;
  display: inline-block;
  border: none;
  padding: 8px;
  border-radius: 5px;
  background: transparent;

  select {
    border: transparent;
    color: #0065FF;
    font-size: 13px;
    width: 80px;
    background: transparent;

    &:focus {
      outline: none;
    }
  }

  .option-bg {
    background-color: #fff;
    color: #253858;
  }

  i {
    color: #0065FF;
    font-size: 14px;
  }
}

.mt-1 {
  margin-top: 1em;
}

.mt-2 {
  margin-top: 1.5em;
}

.mt-3 {
  margin-top: 2em;
}

.mb-3 {
  margin-bottom: 3em;
}

h6 {
  font-size: 14px;
  letter-spacing: 0;
  font-weight: 700;
  margin-bottom: 15px;
  line-height: 19px;
  color: #253858;
  padding: 0 0 0 5px;

  @media screen and(max-width:767px) {
    font-size: 16px;
    padding: 0;
    line-height: 21px;
    margin-bottom: 12px
  }

  span {
    font-size: 11px;
    color: #808080;
    display: block;
    font-weight: 400;

    @media screen and(max-width:767px) {
      font-size: 12px;
    }

  }
}

// ranking-box
.ranking-box {
  .MuiExpansionPanelSummary-expandIcon {
    display: none;
  }

  .MuiExpansionPanelSummary-content {
    display: block;
    padding: 0 0 15px;
  }

  h5 {
    color: #00458b;
    font-size: 20px;
    font-weight: 500;
    line-height: 12px;
    letter-spacing: 1px;

    span {
      margin: 3px 0 0;
      display: block;
      letter-spacing: 0;
    }
  }

  ul {
    width: 100%;

    li {
      width: 84%;
      display: inline-block;

      strong {
        display: block;
        color: #00458b;
        font-size: 14px;
      }

      span {
        color: #00458b;
        font-size: 10px;
      }

      &:last-child {
        text-align: right;
        width: 16%;
      }
    }
  }

  .MuiExpansionPanelDetails-root {
    background-color: #0065ff;
    box-shadow: 0 7px 5px 8px #dad4d4e8;
    padding: 12px 16px 6px;
    border-radius: 20px 20px 0 0;
  }

  .progress-bar {
    background-color: #b3d4ff;
    position: relative;
    margin: 15px 0 0;
    width: 100%;
    height: 8px;
    border-radius: 15px;

    .status-bar {
      background-color: #0065ff;
      width: 35%;
      left: 0;
      top: 0px;
      display: block;
      height: 8px;
      border-radius: 15px;
      position: absolute;

      span {
        position: absolute;
        right: 0;
        top: -16px;
        color: #2699fb;
        font-size: 10px;
      }
    }

    ul {
      padding: 8px 0 0;

      li {
        display: inline-block;
        width: 50%;

        &:last-child {
          text-align: right;
        }
      }
    }
  }
}

.ranking-data {
  box-shadow: 0px 0px 16px #00000014;
  position: relative;
  margin: 0 0 0;
  border-radius: 18px;

  .rank-box {
    position: relative;
    background: #fff url(/images/ranking-block-bg.svg) no-repeat top right;
    border-radius: 18px;
    padding: 15px 0 15px 15px;
    z-index: 99;
    left: 0;
    top: 0;
    display: flex;
    align-items: flex-start;
    justify-content: space-between;

    @media screen and (max-width: 768px) {
      flex-direction: column;
    }

    h5 {
      color: #00458b;
      font-size: 20px;
      font-weight: 500;
      line-height: 12px;
      letter-spacing: 1px;

      span {
        margin: 3px 0 0;
        display: block;
        letter-spacing: 0;
      }
    }

    .rank-box-left-section {
      width: 65%;

      @media screen and (max-width: 768px) {
        width: 100%;
      }

      .name-description {
        margin: 10px 0 20px;

        strong {
          display: block;
          color: #00458b;
          font-size: 24px;
          font-weight: 700;
          line-height: 31px;
          font-family: $heading-font-family
        }

        span {
          color: #00458b;
          font-size: 10px;
          font-weight: 600;
        }

        p {
          color: #00458B;
          font-family: $font-family;
          font-weight: 600;
          font-size: 12px;
        }


      }

      .highlights {
        .slick-slider {
          .slick-slide {
            padding: 0;

            .highlights-heading {
              color: $primary-dark;
              font-size: 14px;
              font-weight: 600;
              line-height: 24px;
            }

            .highlights-description {
              color: $primary-dark;
            }
          }

          .slick-dots {
            text-align: left;
            bottom: -13px;

            li {
              &.slick-active button::before {
                background: $secondary-dark;
              }

              button:before {
                height: 3px;
              }
            }
          }
        }
      }

      .total-ape-progress-bar {
        margin: 47px 0 24px;
        position: relative;
        padding: 20px 0 0;
        display: flex;
        align-items: center;
        justify-content: space-around;

        &::before {
          content: '';
          background: #CCDBFF;
          width: 70%;
          height: 1px;
          position: absolute;
          top: 0;
          left: -15px;
        }

        >div {
          width: 30%;
          text-align: center;

          .MuiFab-label {
            flex-direction: column;

            .ape-gained {
              color: $primary-dark;
              font-size: 14px;
            }

            .ape-left {
              color: $primary-dark;
              font-size: 10px;
            }
          }
        }

        p {
          font-size: 12px;
          line-height: 16px;
          color: $primary-dark;
          width: 80%;

          span {
            color: #1DD1A1;
            font-size: 16px;
          }
        }
      }
    }

    .rank-box-right-section {
      width: 33%;
      height: 202px;

      @media screen and (max-width: 768px) {
        width: 100%;
      }

      ul {
        margin-top: 40px;

        @media screen and (max-width: 768px) {
          margin-top: 20px;
        }

        li {
          background: url(/images/ranking-block-right-detail-bg.svg) no-repeat right;
          margin: 0 0 16px;
          list-style: none;
          padding: 15px 15px 15px 45px;
          position: relative;

          @media screen and (max-width: 768px) {
            text-align: right;
          }

          &::before {
            content: '';
            background: url(/images/icons/icon-rank.svg);
            position: absolute;
            height: 32px;
            width: 32px;
            left: 0;
            top: 50%;
            transform: translateY(-50%);

            @media screen and (max-width: 768px) {
              left: auto;
              right: 55px;
            }
          }

          // &:last-child {
          //   &::before {
          //     background: url(/images/icons/icon-slab.svg) no-repeat right;
          //   }
          // }

          .label {
            color: #6C5A42;
            font-size: 12px;
            font-weight: 600;
            line-height: 16px;
            margin-bottom: 2px;
            display: block;

            h4 {
              font-size: 10px;
              font-weight: 100;
            }
          }

          .value {
            color: $primary-dark;
            font-size: 18px;
            line-height: 24px;
            font-weight: bold;
          }
        }
      }

      .background-caricrature {
        background: url(/images/ranking-block-right-caricrature.svg) no-repeat right top;
        position: absolute;
        right: 5%;
        top: 20px;
        width: 84px;
        height: 202px;

        @media screen and (max-width: 768px) {
          display: none;
        }
      }
    }

    .progress-bar {
      background-color: #b3d4ff;
      position: relative;
      margin: 24px 0 12px;
      width: 100%;
      height: 8px;
      border-radius: 15px;

      .status-bar {
        background-color: #0065ff;
        width: 35%;
        left: 0;
        top: 0px;
        display: block;
        height: 8px;
        border-radius: 15px;
        position: absolute;

        span {
          position: absolute;
          right: 0;
          top: -16px;
          color: #2699fb;
          font-size: 10px;
          font-weight: 900;
        }
      }

      ul {
        width: 100%;

        li {
          display: inline-block;
          width: 50%;

          &:last-child {
            text-align: right;
          }
        }
      }
    }
  }

  .rank-box-inner {
    position: absolute;
    background: #0065ff;
    border-radius: 18px;
    padding: 15px;
    z-index: 98;
    top: 75px;
    left: 0;
    width: 100%;
    padding: 37px 10px 5px;
    margin: 20px 0;

    ul {
      width: 100%;
      display: table;

      li {
        position: relative;
        width: 72%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;

        span {
          color: #fff;
          font-size: 10px;

          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
          }
        }

        &:nth-child(1) {
          span {
            padding-left: 25px;

            &.numbere {
              font-size: 11px;
              padding-left: 0;
              font-weight: 700;
              line-height: 16px;
            }
          }
        }

        &:last-child {
          width: 28%;
          text-align: right;
          float: right;

          //cursor: pointer;
          span {
            font-size: 12px;

            button {
              background-color: transparent;
              border: transparent;
              color: #fff;
              cursor: pointer;
              font-weight: 700;
              line-height: 16px;

              &:focus {
                outline: none;
              }
            }

            i {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

// critiria-popup
.critiria-popup {
  position: relative;

  .close-btn {
    position: absolute;
    top: 3px;
    right: 0;

    svg {
      font-size: 0.7em;
    }
  }

  p {
    color: #253858;
    font-size: 13px;
    line-height: 18px;
    margin-bottom: 20px;
  }

  li {
    /*list-style: none;
color: #253858;
font-size: 13px;
line-height: 18px;
margin-bottom: 20px;
list-style: none;
padding: 0 8px;
text-align: left;
color: #253858;
font-size: 13px;
line-height: 18px;
margin-bottom: 20px;*/
  }

  // slabs box
  .slabs-box {
    border: 1px solid #ccc;
    width: 300px;
    max-width: 100%;
    box-shadow: 0 0rem 0.4rem rgba(0, 0, 0, 0.15);
    margin: 0 auto;

    ul {
      li {
        list-style: none;
        border-bottom: 1px solid #ccc;
        padding: 0 8px;
        text-align: center;
        background: linear-gradient(to right, #2775ff, #7202bb);

        h5 {
          position: relative;
          color: #fff;
          font-size: 14px;
          padding: 10px 0 5px;

          &:after {
            content: "";
            position: absolute;
            left: 0;
            right: 0;
            margin: auto;
            top: 100%;
            height: 2px;
            width: 50%;
            background: #275efe;
          }
        }

        p {
          font-size: 12px;
          margin: 5px 0;
          color: #fff;
        }
      }
    }
  }

  .popup-inner-box {
    width: 90%;
    max-width: 100%;
    margin: 0 auto;
  }

  .payoutClawPopup {
    width: 100% !important;

    .topPerformance {

      table {
        font-family: roboto;
        border-collapse: collapse;
        width: 100%;
      }

      th {
        background-color: #F2F7FF;
        padding: 10px 19px;
        font: normal normal 600 11px/14px Roboto;
        letter-spacing: 0px;
        color: #050505;
        opacity: 0.6;
        text-align: center;
      }

      td {
        border-top: 1px solid #F2F7FF;
        text-align: center;
        padding: 23px;
        font: normal normal normal 12px/14px Roboto;
        letter-spacing: 0px;
        color: #050505;
        opacity: 1;
        position: relative;
      }

    }
  }
}

// criteriaHtml
.criteriaHtml {
  li {
    color: #253858;
    font-size: 13px;
    line-height: 18px;
    margin-bottom: 20px;
    list-style: none;
  }
}

// highlights-card
.highlights {
  h6 {
    margin: 0;
  }

  .highlights-card {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 18px;
    background: #ffe8c4 url(/images/cureved_bg.svg) no-repeat center right;
    height: 96px;
    margin: 10px 0 0;

    ul {
      padding: 7px 0;

      li {
        display: inline-block;
        vertical-align: middle;
        padding-left: 15px;

        strong {
          color: #c47800;
          font-size: 20px;
          font-weight: 500;
          padding-right: 5px;

          span {
            font-size: 12px;
            display: inline;
            padding: 0 3px;
          }
        }

        width: 70%;

        &:last-child {
          width: 30%;
          text-align: right;
          padding: 0;
        }
      }
    }
  }
}

// leader-board
.leader-board {
  .lader-ranking {
    padding: 15px 15px 10px;
    min-height: 328px;

    @media screen and(max-width:767px) {
      min-height: auto;
    }

    li {
      display: inline-block;
      width: 50%;
      color: #253858;
      vertical-align: middle;

      strong {
        display: block;
        font-size: 11px;
        font-weight: 900;
        line-height: 13px;

        @media screen and(max-width:767px) {
          font-size: 12px;
          line-height: 16px;
        }
      }

      span {
        font-size: 12px;
        font-weight: 500;
        display: block;

        img {
          padding-left: 5px;
          vertical-align: text-top;
          padding-bottom: 2px;
        }

      }

      &:nth-child(even) {
        text-align: right;
      }

      &:nth-child(odd) {
        strong {
          color: #253858;
        }
      }
    }

    div {
      margin: 0;
      padding: 10px;

      &.active {
        background: #0065ff;
        border-radius: 8px;
        margin: 0 0 0.5em;

        @media screen and(max-width:767px) {
          background: transparent linear-gradient(109deg, #0052C1 0%, #F45E9D 100%) 0% 0% no-repeat padding-box;
          padding: 10px 13px
        }

        li {
          color: #fff;

          &:nth-child(odd) {
            strong {
              color: #fff;
            }
          }
        }
      }
    }
  }

}

// Incentive
.incentive {
  .incentive-box {
    padding: 15px 15px;
    min-height: 250px;

    @media screen and(max-width:767px) {
      min-height: auto;

      h6 {
        margin-bottom: 5px;
      }
    }

    li {
      display: inline-block;
      width: 100%;
      color: #050505;
      margin: 6px 0;
      letter-spacing: 0px;

      @media screen and(max-width:767px) {
        margin: 15px 0px 0px;
      }

      strong {
        display: block;
        font-size: 18px;
        font-weight: 500;
        color: #273a59;
        float: right;

        @media screen and(max-width:767px) {
          font-size: 14px;
          color: #253858;
        }

        i {
          font-size: 16px;
        }
      }

      span {
        color: #808080;
        font-size: 11px;
        line-height: 26px;
        font-weight: 500;
        float: left;

        @media screen and(max-width:767px) {
          font-size: 12px;
          color: #253858;
          line-height: 16px
        }

      }

      &:nth-child(even) {
        text-align: right;
      }
    }
  }

}

// booking-table
.booking-table {
  margin: 1em 0;

  @media screen and(max-width:767px) {
    button {
      margin-top: -40px !important;
      border: 1px solid #2595F5;
      border-radius: 25px;
      height: 32px;
      line-height: normal;
      padding: 0;
      min-width: 92px;

      span {
        color: #2595F5 !important;
        margin-top: 1px !important;
      }
    }
  }

  h6 {
    display: block;

    @media screen and(max-width:767px) {
      padding-left: 5px !important;
    }
  }

  ul {
    display: table;
    padding: 10px 15px;
    width: 100%;

    li {
      font-size: 12px;
      font-weight: 600;
      display: table-cell;
      text-align: left;
      width: 10%;
      color: #808080;

      span {
        display: none;
      }

      strong {
        font-weight: 500;
        position: relative;
        color: #273A59;
        font-size: 12px;

        &.weighted-price {
          cursor: pointer;
        }

        svg {
          font-size: 13px;
          margin: 2px 4px 0;
          position: absolute;
          // cursor: pointer;
          color: #90bafb;
        }
      }

      &.booking-SNo {
        width: 6%;

        @media screen and(max-width:767px) {
          display: none;
        }

      }

    }

    &.booking-table-head {
      background: #ffffff;
      border-radius: 18px;
      box-shadow: 0px 0px 16px #00000014;

      li {
        font-weight: 900;
        font-size: 12px;
        line-height: 16px;
      }
    }

    &.booking-table-body {
      background: #ffffff;
      margin: 5px 0 0;

      @media screen and(max-width:767px) {
        border-radius: 8px;
        position: relative;
        padding: 15px 15px 4px !important;
        margin-bottom: 15px !important;
        box-shadow: 0px 3px 6px #3469CB29;
        -webkit-box-shadow: 0px 3px 6px #3469CB29;
      }

      li {
        font-size: 12px;
        font-weight: 500;
        vertical-align: middle;

        @media screen and(max-width:767px) {
          &:nth-child(even) {
            text-align: left;
          }

          &:nth-child(n) {
            font-weight: 600;
            margin: 8px 0px;
          }

          &:nth-child(n) span {
            margin-top: 0px;
            line-height: 1.58;
          }

          &:nth-child(n) strong {
            font-size: 12.6px;
          }

          &:nth-child(2) {
            background: #CCD2FF;
            border-radius: 0px 8px;
            width: 120px;
            height: 24px;
            line-height: 24px;
            margin: auto;
            position: absolute;
            right: 0px;
            top: 0px;
            text-align: center !important;

            span {
              display: none;
            }
          }

          &:nth-child(3) {
            margin: 0;
          }

          &:nth-child(3),
          &:nth-child(4) {
            span {
              display: none;
            }
          }

          &:nth-child(3) strong {
            font-size: 11px;
            font-weight: 500 !important;
          }

          &:nth-child(4) {
            text-align: left;
            margin: 0px 0px 5px;
            display: block;
          }

          &:nth-child(4) strong {
            font-size: 14px
          }

          &:nth-child(5) {
            width: 44%;
            margin-bottom: 2px;
          }

          &:nth-child(6),
          &:nth-child(7) {
            width: 26%;
            white-space: nowrap;
            margin-bottom: 2px;
          }

          &:nth-child(8),
          &:nth-child(9) {
            padding-left: 0 !important;
            margin-left: 0 !important;
          }

        }

        &:nth-child(8) {
          padding-left: 2px;
        }

        &:nth-child(9) {
          padding-left: 6px;
        }
      }
    }

    &.body-table {
      height: 100px;
    }
  }

  .table-scroll {
    min-height: 260px;
    max-height: 400px;
    //height: 260px;
    overflow-y: auto;
    padding: 0 0 20px;
    margin: 10px 0 0;
  }

  .tooltip-popup {
    background-color: #ffcccc;
    transition-duration: 2s;

    li {
      background-color: #ffcccc;
      ;
      display: block;
      list-style: none;
      position: relative;
      padding-left: 20px;
      width: 100%;

      &.yes::before {
        content: "\f00c";
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        text-decoration: inherit;
        color: #28a745;
        font-size: 12px;
        padding-right: 0.5em;
        position: absolute;
        top: 4px;
        left: 0;
      }

      &.no::before {
        content: "\f00d";
        font-family: FontAwesome;
        font-style: normal;
        font-weight: normal;
        text-decoration: inherit;
        color: #dc3545;
        font-size: 12px;
        padding-right: 0.5em;
        position: absolute;
        top: 4px;
        left: 0;
      }
    }
  }

}



.MuiAvatar-colorDefault {
  color: #414141 !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  font-size: 13px !important;
  border-radius: 0 !important;
  width: auto !important;
}

@media all and (max-width: 1080px) and (min-width:768px) {
  .incentive {
    .incentive-box {
      padding: 12px 20px;
    }
  }

}

@media all and (max-width: 768px) {

  .criteria-slabs {
    width: 70%;
    border: 1px solid #050505;
    margin: 0px 15% !important;
    text-align: center;
    line-height: 22px !important;
  }


  .booking-table {
    h6 {
      display: block;
    }

    ul {
      border-radius: 18px;

      &.booking-table-head {
        display: none;
      }

      &.booking-table-body {
        margin-bottom: 20px;

        li {
          &.booking-SNo {
            width: 48%;
          }
        }
      }

      li {
        display: inline-block;
        width: 48%;

        strong {
          color: #273a59;
          font-weight: 600;
        }

        span {
          display: block;
          font-size: 10px;
        }

        &:nth-child(even) {
          text-align: right;
          margin: 8px 5px;
        }
      }
    }

    .table-scroll {
      height: 260px;
      overflow-y: auto;
      padding: 10px 0 20px
    }
  }
}

.scrool-toll::-webkit-scrollbar-track {
  box-shadow: transparent;
  background-color: #fff;
}

.scrool-toll::-webkit-scrollbar {
  width: 6px;
  background-color: #808080;
  height: 1px;
  border: 3px;
}

.scrool-toll::-webkit-scrollbar-thumb {
  background-color: #808080;
}

.textGrey {
  color: #050505 !important;
}

.criteria-slabs {
  width: 60%;
  border: 1px solid #050505;
  margin: 0px 20% !important;
  text-align: center;
  line-height: 22px !important;
}


/* Criteria HTML Style */
.block_container {
  width: 100%;
  margin: 0 0 0 0;
  padding: 0 0 20px 0;
}

.table_hd {
  background: #0065ff;
  border-radius: 8px 8px 0 0;
  color: #fff;
  display: table;
  width: 100%;
}

.table_hd>div {
  display: table-cell;
  width: 50%;
  padding: 10px 5px;
  font-size: 13px;
  box-sizing: border-box;
}

.table_data {
  border-bottom: 1px solid #0662f6;
}

.table_data>div.loop_box {
  display: table;
  padding: 0;
  margin: 0;
  width: 100%;
  border: 1px solid #0662f6;
  border-bottom: 0;
}

.table_data>div.loop_box>div {
  display: table-cell;
  width: 50%;
  padding: 5px;
  font-size: 13px;
  border-right: 1px solid #0662f6;
  box-sizing: border-box;
}

.table_data>div.loop_box>div:last-child {
  border-right: 0;
}

.critiria-popup>ul {
  display: block;
  width: 100%;
  padding: 0;
  margin: 0;
}

.critiria-popup>ul li {
  margin: 0 0 0 20px;
  padding: 0 0 2px 0;
  list-style: decimal;
  font-size: 13px;
  color: #253858;
  line-height: 18px;
}

// Incentive
.cursorPointer {
  cursor: pointer;
}

.ancor {
  color: #0065ff !important;
}

.payout {
  .payout-box {
    padding: 15px 15px;
    background: #ffffff;
    border-radius: 18px;
    box-shadow: 0px 0px 16px #00000014;
    min-height: 328px;

    @media screen and(max-width:767px) {
      border-radius: 8px;
      margin: 0px 12px;
      padding: 15px 15px 7px
    }

    li.firstbox {
      width: 100%;
      background-color: #cbffed;
      color: #55b795;
      display: inline-block;

      strong {
        color: #0F8D61;
        font-size: 12px;
        font-weight: 700;
      }

      span {
        color: #0F8D61;
        font-size: 18px;
        text-align: center;
        padding-left: 0;
        font-weight: 600;
        width: 100%;
      }

    }

    li {
      // display: inline-block;
      display: flex;
      width: 100%;
      color: #050505;
      padding: 6px;
      border-radius: 8px;
      text-align: center;
      letter-spacing: 0px;
      justify-content: space-between;

      strong {
        display: block;
        font-size: 18px;
        font-weight: 500;
        color: #273a59;

        i {
          font-size: 16px;
        }
      }

      span {
        color: #808080;
        font-size: 12px;
        line-height: 26px;
        font-weight: 500;
        display: inline-block;
        text-align: left;
        width: 55%;
        float: left;
        padding-left: 0px;

        @media screen and(max-width:767px) {
          line-height: 1.5;
          white-space: nowrap;
          width: auto
        }
      }

      p {
        color: #273A59;
        font-size: 14px;
        line-height: 26px;
        font-weight: 500;
        text-align: right;
        padding-left: 20px;
        width: 45%;
        float: left;

        @media screen and(max-width:767px) {
          width: 100%;
        }
      }

      i {
        font-size: 15px;
        margin-left: 6px;
      }

      i.fa.fa-info-circle {
        color: #808080;
      }

      em {
        background-color: #0052cc;
        color: #ffffff;
        padding: 2px 4px;
        font-weight: 700;
        font-style: normal;
        font-size: 9px;
      }
    }
  }

}

.common-tooltip-popup {
  transition: 2s linear;
  -webkit-transition: 2s linear;
  color: #273A59;
  padding: 3px;
  text-align: center;
}

@media only screen and (min-width: 360px) and (max-width:767px) {
  .firstbox {
    width: 100% !important;
    padding: 7px !important;
    margin-bottom: 16px;
    display: inline-block !important;
  }

  .payout {
    .payout-box {
      li {
        width: 100%;
        padding: 2px;
        color: #253858;

        span {
          width: 50%;
          float: none !important;
          padding-left: 0px;
          color: #253858;
        }

        i.fa-info-circle {
          color: #808080 !important;
          vertical-align: middle;
        }

        p {
          width: 50%;
        }

        .ancor {
          color: #253858 !important
        }

        .ancor i {
          color: #808080 !important;
        }

      }
    }
  }

  header .MuiIconButton-edgeStart {
    margin-left: 8px;
    padding: 8px;
  }

  header .MuiButtonBase-root.mobile-view {
    display: none;
  }

  .table-scroll {
    height: auto !important;
    width: 100%;
    padding: 10px 4px 0px !important;
    margin: 0px !important;
    max-height: 100% !important;
  }




}


// Start  CSS of Jeeto apna ghar //
.UserHelp {
  position: fixed;
  bottom: 40px;
  z-index: 99;
  right: 40px;
}

.UserHelpHoverText {
  position: fixed;
  bottom: 70px;
  z-index: 99;
  right: 120px;
  border: #003F9E;
  text-decoration-color: #00C417;
  font-size: 15px;
  color: white;
  background: #0065ff;
  border-radius: 0.5rem;
  padding: 5px 10px;
  font-weight: 500;
}

.wrapperBG {
  background: #FAFAFA;
  background-image: linear-gradient(to bottom, #F2F7FF, #F2F7FF 29%, #fff 10%, #fff 63%, #FAFAFA 63%);
}

.jag-ranking-data {
  box-shadow: 0px 0px 16px #00000014;
  position: relative;
  margin: 0 0 48px;
  border-radius: 18px;

  .rank-box {
    position: relative;
    background: #ffffff;
    border-radius: 18px;
    padding: 15px 15px 8px;
    z-index: 99;
    left: 0;
    top: 0;


    h5 {
      color: #00458b;
      font-size: 20px;
      font-weight: 500;
      line-height: 12px;
      letter-spacing: 1px;

      span {
        margin: 3px 0 0;
        display: block;
        letter-spacing: 0;
      }
    }

    ul {
      margin: 10px 0;

      li {
        width: 66%;
        display: inline-block;
        vertical-align: top;

        strong {
          display: block;
          color: #00458b;
          font-size: 16px;
          font-weight: 700;
          line-height: 19px;
        }

        span {
          color: #00458b;
          font-size: 12px;
          font-weight: 600;
        }

        &:last-child {
          text-align: right;
          width: 34%;
        }
      }
    }

    .gold {
      width: 100px;
      background: #FFF5E5 0% 0% no-repeat padding-box;
      border: 1px solid #EDC689;
      border-radius: 50px;
      opacity: 1;
      padding: 4px;
      text-align: center;
      text-transform: uppercase;
      color: #C47800;
      font-weight: 600;
      margin-top: 28px;
      font-size: 10px;

      img {
        vertical-align: text-top;
        margin-right: 7px;
      }

    }

    .silver {
      width: 100px;
      background: #F2F2F2 0% 0% no-repeat padding-box;
      border: 1px solid #D1D1D1;
      border-radius: 50px;
      opacity: 1;
      padding: 4px;
      text-align: center;
      text-transform: uppercase;
      color: #9FA5AA;
      font-weight: 600;
      margin-top: 28px;
      font-size: 10px;

      img {
        vertical-align: text-top;
        margin-right: 7px;
      }
    }

    .bottom-shadw {
      background: transparent linear-gradient(180deg, #FFFFFF 0%, #DDE8FD 100%) 0% 0% no-repeat padding-box;
      width: 100%;
      padding: 10px 6px 0px 15px;
      border-radius: 8px;

      h5 {
        font-size: 14px;
        font-weight: 600;
        margin-bottom: 20px;
      }

      li {
        width: 33%;

      }

      span {
        font-size: 12px;
        color: #808080;
        font-weight: normal;
      }

      strong {
        color: #253858;
        font-size: 14px;
        font-weight: 600;
        margin-top: 7px;
      }

      img {
        // position: relative;
        top: 4px;
      }
    }



  }

  .rank-box-inner {
    position: absolute;
    background: #0065ff;
    border-radius: 18px;
    z-index: 98;
    top: 205px;
    left: 0;
    width: 100%;
    padding: 40px 12px 10px;
    margin: 20px 0;

    ul {
      width: 100%;
      display: table;

      li {
        position: relative;
        width: 72%;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;

        span {
          color: #fff;
          font-size: 11px;

          img {
            position: absolute;
            left: 0;
            top: 0;
            width: 18px;
            height: 18px;
          }
        }

        &:nth-child(1) {
          span {
            padding-left: 20px;

            &.numbere {
              font-size: 12px;
              padding-left: 0;
              font-weight: 500;
              line-height: 16px;
            }
          }
        }

        &:last-child {
          width: 28%;
          text-align: right;
          float: right;

          //cursor: pointer;
          span {
            font-size: 12px;

            button {
              background-color: transparent;
              border: transparent;
              color: #fff;
              cursor: pointer;
              font-weight: 700;
              line-height: 16px;

              &:focus {
                outline: none;
              }
            }

            i {
              font-size: 14px;
            }
          }
        }
      }
    }
  }
}

.growthDetails {
  background: #FFFFFF 0% 0% no-repeat padding-box;
  border-radius: 8px;
  width: 355px;
  height: 35px;
  padding: 9px 15px;
  display: inline-block;
  float: right;
  font: normal normal normal 11px/18px Roboto;
  letter-spacing: 0px;
  color: #808080;
  opacity: 1;

  span {
    top: 3px;
    position: relative;
    float: right;
    right: 0px;
    font: normal normal normal 14px/14px Roboto;

    svg {
      position: absolute;
      left: -27px;
      top: -11px;
      font-size: 2.5em;
      //transform: rotate(30deg);
    }
  }
}

//jag leader board//
.jag-leader-board {
  h6 {
    display: inline-block;
  }

  .lader-ranking {
    padding: 15px 15px 10px;
    min-height: 250px;


    li {
      display: inline-block;
      width: 33%;
      color: #050505;

      strong {
        display: block;
        font-size: 12px;
        font-weight: 900;
        line-height: 13px;
      }

      span {
        font-size: 12px;
        font-weight: 500;
        color: #050505;
      }

      &:nth-child(odd) {
        strong {
          color: #00458b;
        }
      }

      .gold {
        text-transform: uppercase;
        color: #C47800 !important;
        font-size: 11px !important;

        img {
          vertical-align: text-top;
          top: 0px !important;
        }
      }

      .silver {
        text-transform: uppercase;
        color: #9FA5AA !important;
        font-size: 11px !important;

        img {
          vertical-align: text-top;
          top: 0px !important;
        }
      }
    }

    li:nth-child(2) {
      text-align: center;
    }

    li:nth-child(3) {
      text-align: center;
    }

    div {
      margin: 0 0 0.8em;
      padding: 10px 10px 10px;

      &.active {
        background: #D8EAFA;
        border-radius: 8px;

        li {
          color: #fff;

          strong {
            color: #00458b;
            margin-bottom: 5px;

            img {
              position: relative;
              top: 5px;
            }
          }

          &:last-child {
            strong {
              color: #00458b;
              margin-bottom: 5px;
            }
          }
        }
      }
    }
  }

  .topPerformance {
    min-height: 300px;

    table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
    }

    th {
      background-color: #F2F7FF;
      padding: 10px 19px;
      font: normal normal 600 11px/14px Roboto;
      letter-spacing: 0px;
      color: #050505;
      opacity: 0.6;
      text-align: center;
    }

    td {
      border-top: 1px solid #F2F7FF;
      text-align: center;
      padding: 26px;
      font: normal normal normal 14px/14px Roboto;
      letter-spacing: 0px;
      color: #050505;
      opacity: 1;
      position: relative;

      &:first-child {
        font: normal normal bold 16px/14px Roboto;
        letter-spacing: 0px;
        color: #FFBE05;
        opacity: 1;
      }

      .new {
        position: absolute;
        top: 0;
        right: 0;
        left: 98px;
        bottom: 0;
        margin: auto;
      }

      svg {
        position: absolute;
        top: 0;
        bottom: 0;
        right: 50px;
        margin: auto;
        left: 0;
      }

      .arrowRight {
        right: 40px;
        color: #0063FA;
      }
    }

  }
}

.mt-5 {
  margin-top: 5em;
}

.green {
  color: #00C417 !important;
}

.red {
  color: #D92626 !important;
}

.agentName {
  text-align: left;
  font: normal normal normal 28px/36px Roboto;
  letter-spacing: 0px;
  color: #253858;
  opacity: 1;
  margin-bottom: 18px;
}



//END leader board CSS//

//Highlights//
.jag-highlights {
  h3 {
    text-align: left;
    font: normal normal normal 28px/36px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
    margin-bottom: 18px;
  }

  .ranking-data {
    box-shadow: none;
    position: relative;
    margin: 0 0 10px;
    border-radius: 18px;

    h3 {
      color: #0E4E84;
      font-size: 18px;
      margin-top: 20px;
      margin-bottom: 2px;
    }

    .rank-box {
      position: relative;
      background: #ffffff;
      border-radius: 18px;
      padding: 15px;
      z-index: 99;
      left: 0;
      top: 0;
      min-height: 110px;

      span {
        color: #0E4E84;
        font-size: 11px;
      }
    }

    .active {
      background: #D8EAFA;
      border-radius: 8px;

      span {
        font-size: 11px;
        font-weight: 500;
        color: #050505;
      }

      h3 {
        color: #00458b;
        margin-bottom: 5px;
        text-align: right;
        width: 28%;
        font-weight: 600;
        line-height: 25px
      }
    }
  }

  h6 {
    margin: 0;
    margin-bottom: 15px;

    i {
      font-size: 10px;
    }
  }

  .highlights-card {
    box-shadow: 0px 0px 16px #00000014;
    border-radius: 18px;
    background: #ffe8c4;
    height: 250px;
    margin: 10px 0 0;
    padding: 8px;

    ul {
      padding: 7px 0;

      li {
        display: inline-block;
        vertical-align: top;
        padding-left: 15px;

        strong {
          color: #c47800;
          font-size: 20px;
          font-weight: 500;
          padding-right: 5px;

          span {
            font-size: 12px;
            display: inline;
            padding: 0 3px;
          }
        }

        width: 70%;

        &:last-child {
          width: 30%;
          text-align: right;
          padding: 0;
        }

        h3 {
          color: #C47800;
          font-size: 16px;
          position: relative;
          top: 46px;
        }

        p {
          color: #C47800;
          font-size: 12px;
          text-align: left;
          margin-top: 56px;
          // height: 75px;
          overflow-y: auto;
          padding-right: 5px;
        }

        p::-webkit-scrollbar {
          width: 5px;

        }

        /* scrollbar itself */
        p::-webkit-scrollbar-thumb {
          background-color: #C47800;
          border-radius: 16px;
        }

        button {
          background-color: #C47903;
          padding: 10px 16px;
          border: none;
          color: #fff;
          margin-top: 20px;
          border-radius: 24px;
          cursor: pointer;
        }
      }
    }

    img {
      mix-blend-mode: luminosity;
    }

    // slider CSS//
    .mySlides {
      display: none
    }

    /* Slideshow container */
    .slideshow-container {
      max-width: 1000px;
      position: relative;
      margin: auto;
      height: 130px;
    }

    /* Next & previous buttons */
    .prev,
    .next {
      cursor: pointer;
      position: absolute;
      top: 50%;
      width: auto;
      padding: 16px;
      margin-top: -22px;
      color: white;
      font-weight: bold;
      font-size: 18px;
      transition: 0.6s ease;
      border-radius: 0 3px 3px 0;
      user-select: none;
    }

    /* Position the "next button" to the right */
    .next {
      right: 0;
      border-radius: 3px 0 0 3px;
    }

    /* On hover, add a black background color with a little bit see-through */
    .prev:hover,
    .next:hover {
      background-color: rgba(0, 0, 0, 0.8);
    }

    /* Caption text */
    .text {
      color: #f2f2f2;
      font-size: 15px;
      padding: 8px 12px;
      position: absolute;
      bottom: 8px;
      width: 100%;
      text-align: center;
    }

    /* Number text (1/3 etc) */
    .numbertext {
      color: #f2f2f2;
      font-size: 12px;
      padding: 8px 12px;
      position: absolute;
      top: 0;
    }

    /* The dots/bullets/indicators */
    .dot {
      cursor: pointer;
      height: 4px;
      width: 34px;
      margin: 0 2px;
      background-color: #C7AB7D;
      border-radius: 5px;
      display: inline-block;
      transition: background-color 0.6s ease;
    }

    .active,
    .dot:hover {
      background-color: #C47903;
      cursor: pointer;
    }

    /* Fading animation */
    .fade {
      -webkit-animation-name: fade;
      -webkit-animation-duration: 1.5s;
      animation-name: fade;
      animation-duration: 1.5s;
    }

    @-webkit-keyframes fade {
      from {
        opacity: .4
      }

      to {
        opacity: 1
      }
    }

    @keyframes fade {
      from {
        opacity: .4
      }

      to {
        opacity: 1
      }
    }

    /* On smaller screens, decrease text size */
    @media only screen and (max-width: 300px) {

      .prev,
      .next,
      .text {
        font-size: 11px
      }
    }

    .show {
      display: block;
    }

    //End Slider CSS//
  }

  .calculator-box {
    padding: 10px 0px;
    background-color: #fff;
    width: 100%;
    border-radius: 18px;

    .formatselector-buttons {
      font-size: 14px;

      span {
        cursor: pointer;
        margin-left: 5px;
        padding: 5px;
      }

      span.active {
        color: #0063FA;
        font-weight: 700;
        border-bottom: 2px solid #0063FA;
      }
    }

    .selectoptions {
      margin-top: 25px;

      select {
        border-radius: 18px;
        padding: 10px 18px;
        margin-right: 25px;
        font-size: 11px;
        width: 150px;
      }

      button {
        padding: 10px 15px;
        background-color: #0063FA;
        border: none;
        border-radius: 18px;
        color: #fff;
        cursor: pointer;
      }

    }

    h4 {
      text-transform: uppercase;
      margin-bottom: 25px;
      width: 100%;
    }

    .box {
      border-right: 1px solid #BCBCBC;
      margin: 10px 15px;
    }

    .noborder {
      border: none;
    }

    .rewardbreakup {
      float: right;
      margin-right: 30px;
      font-size: 18px;
      cursor: pointer;
    }

  }

  .noReward {
    text-align: center;
    font-size: 20px;
    color: #ff6978;
  }
}

//END Highlight CSS//

//graph css//
.graphbox {
  background-color: #fff;
  border-radius: 16px;
  box-sizing: border-box;
  padding: 2px;
}

//END Graph css//


//BU Wise Criteria Popup CSS//
.jag-criteriaHtml {
  table {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }

  table td,
  table th {
    border: 1px solid #ddd;
    padding: 8px;
  }



  table {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 100%;
  }

  table th:first-child {
    background-color: transparent;
    border: none;
  }

  table td,
  table th {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }



  table th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #0063FA;
    color: white;
  }
}

.jag-criteriaList {

  table {
    font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
    border-collapse: collapse;
    width: 50%;
    display: inline-table;
  }


  table td,
  table th {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
  }



  table th {
    padding-top: 12px;
    padding-bottom: 12px;
    text-align: left;
    background-color: #0063FA;
    color: white;
  }
}


//END POP CSS//
// Wallof fame popup css//
.wallofFamePopup {
  .MuiDialogContent-root {
    overflow: hidden !important;
  }

  .MuiPaper-root {
    background: #0762F7 0% 0% no-repeat padding-box;
    border-radius: 20px;
    width: 1200px;
    max-width: 1200px;
    padding: 25px 7px;
  }

  .wallofFamecontent {
    background: #FAF8FB 0% 0% no-repeat padding-box;
    border-radius: 20px;
    opacity: 1;
    padding: 32px;

    .jaglogo {
      width: 214px;
      position: absolute;
      left: -3px;
      top: -21px
    }

    .wofLogo {
      position: relative;

      span {
        top: 80px;
        position: absolute;
        left: 85px;
        text-align: left;
        font: normal normal normal 13px/30px Roboto;
        letter-spacing: 0px;
        opacity: 1;
      }

      span:last-child {
        top: 80px;
        line-height: 15px;
      }

      img {
        width: 85%;
      }
    }

    .pblogo {
      margin-top: 40px;
      position: absolute;
      right: 7px;
      top: -41px;
      width: 190px;
    }

    .scrollWOF {
      overflow-y: auto;
      height: 280px;
    }

    #scrollbar::-webkit-scrollbar {
      width: 6px;
      background: rgba(100, 100, 100, 0.1);
      height: 5px;
      border-radius: 50px;
    }

    #scrollbar::-webkit-scrollbar-thumb {
      background-color: #96A0B5;
      border-radius: 50px;
    }

    table {
      font-family: roboto;
      width: 100%;
      border-collapse: separate;
      border-spacing: 0 5px;
    }

    table tbody tr {
      background: transparent linear-gradient(270deg, #FAF8FB 0%, #FAEBBF 73%, #F3CF62 100%) 0% 0% no-repeat padding-box;
      text-align: left;
      font: normal normal 500 16px/30px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
    }

    .silver {
      background: transparent linear-gradient(270deg, #FAF8FB 0%, #F1F1F1 73%, #E2E2E2 100%) 0% 0% no-repeat padding-box;
    }

    table td,
    table th {
      border: none;
      padding: 3px 8px;
      line-height: 11px;
      text-align: center;
    }

    table tbody td:first-child {
      border-top-left-radius: 4px;
      border-bottom-left-radius: 4px;
    }

    table th {
      border: none;
      font: normal normal 500 14px/30px Roboto;
      letter-spacing: 0px;
      color: #A2A2A2;
      text-transform: uppercase;
      opacity: 1;
    }

    .radbg {
      position: relative;
      width: 100%;
      height: 50px;

      img {
        // position: absolute;
        left: 0;
        top: -76px;
        right: 0;
        margin: auto;
      }
    }
  }

  .closeIcon {
    position: absolute;
    z-index: 9999999;
    right: 10px;
    top: 10px;
    background-color: #0065FF;
    color: #fff;
    border-radius: 14px;
    cursor: pointer;
  }
}

// END css//

@media only screen and (min-width: 360px) and (max-width:767px) {
  .firstbox {
    width: 100% !important;
  }

  .payout {
    .payout-box {
      li {
        width: 100%;

        span {
          width: 50%;
          float: left;
          text-align: left;
          padding-left: 0px;
        }

        p {
          width: 50%;
          float: left;
          text-align: right;
        }
      }
    }
  }

  .jag-highlights {
    .ranking-data {
      .rank-box {
        padding: 15px 11px;
      }
    }

    .calculator-box {
      .box {
        border-right: none;
        border-bottom: 2px solid #BCBCBC;
        padding-bottom: 10px;
      }

      .noborder {
        border: none;
      }

    }

  }

  .faq-section .MuiInputBase-root {
    width: 86% !important;
  }
}

@media only screen and (max-width: 320px) {

  .faq-section .MuiInputBase-root {
    width: 86% !important;
  }
}

/* FAQ CSS start */
.MuiTab-textColorInherit.Mui-selected {
  opacity: 1;
  border-bottom: 3px solid #0065FF;
  // font-size: 18px;
  font-weight: bold;
  border-radius: 3px;
}


.faq-section {
  h4 {
    color: #253858;
    font-size: 20px;
    margin: 35px 0px 30px;
  }

  .searchbar {
    box-shadow: 0px 0px 16px #00000014;
    background-color: #fff;
    border-radius: 40px;
    margin-bottom: 20px;
    padding: 0px 17px
  }

  .MuiInputBase-root {
    width: 93%;
  }

  .PrivateTabIndicator-colorSecondary-43 {
    display: none;
  }

  .MuiTab-textColorInherit.Mui-selected {
    opacity: 1;
    border-bottom: 3px solid #0065FF;
    font-size: 18px;
    font-weight: bold;
    border-radius: 3px;
  }

  .MuiAppBar-colorPrimary {
    color: #253858;
    background-color: transparent;
    box-shadow: none;
  }

  .MuiTab-textColorInherit {
    opacity: 0.7;
    padding: 5px 20px;
    min-width: auto;
    font-size: 16px;
    color: #253858;
    text-transform: capitalize;
    margin-right: 20px;
  }

  .faq-tab {
    margin-top: 10px;

    .MuiTab-textColorInherit.Mui-selected {
      opacity: 1;
      border-bottom: none;
      font-size: 12px;
      background-color: #0065FF;
      font-weight: bold;
      color: #fff;
      border-radius: 49px;
      padding: 0px 30px;
    }

    .MuiTab-textColorInherit {
      font-size: 12px;
    }

    .MuiTab-root {
      min-height: 38px;
    }

    .MuiTouchRipple-root {
      display: none;
    }

    .MuiTabs-indicator {
      display: none;
    }
  }

  .MuiBox-root {
    padding: 24px 0px !important;
  }

  .MuiAccordion-rounded:first-child {
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
  }

  .MuiAccordion-rounded:last-child {
    border-bottom-left-radius: 16px;
    border-bottom-right-radius: 16px;
  }

  .MuiAccordion-rounded {
    border-radius: 16px;
  }

  .MuiAccordion-root {
    position: relative;
    transition: margin 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;
    margin-bottom: 10px;
    box-shadow: none;
  }

  .MuiAccordion-root:before {
    display: none;
  }

  .MuiSvgIcon-root {
    color: #0065FF;
  }

  .faqtab-data {
    .MuiTypography-body1 {
      color: #495973;
      font-size: 14px;
      font-family: "Roboto";
    }

    .faq-ans {
      color: #808080;
    }
  }

  .read-more-link {
    h2 {
      padding: 6px;
      background-color: #0065FF;
      width: 100px;
      color: #fff;
      font-size: 12px;
      margin: auto;
      text-align: center;
      border-radius: 24px
    }
  }
}

.faq-rightside {

  h6 {
    color: #253858;
    font-size: 16px;
    margin: 45px 0px 0px;
  }

  p {
    font-size: 14px;
    padding: 9px 5px;
    color: #808080;
  }

  .rightside-box {
    float: left;
    background-color: #fff;
    width: 100%;
    border-radius: 16px;
    padding: 20px;

    img {
      width: 35px;
    }

    .reward {
      width: 22px;
    }

    p {
      padding-left: 0px;
      padding-top: 23px;
      font-size: 12px;
      color: #253858;
      font-weight: bold;
    }
  }

  .contact-us {
    padding: 10px;
    width: 100%;

    h6 {
      color: #253858;
      font-size: 16px;
      margin: 0px;
    }

    img {
      float: left;
    }

    a {
      float: left;
      margin-left: 10px;
      margin-top: 7px;
      color: #303030;
      font-size: 14px;
    }

    .rightside-box {
      margin-bottom: 10px;
      padding: 30px 20px 30px 20px;
    }
  }
}

.breakupheader {
  color: #0063FA;
}

.how-it-works-section {
  background: #D6E2FF;
  display: flex;
  flex-direction: COLUMN;
  align-items: center;
  height: 100%;
  padding: 32px 16px;

  h3 {
    font-size: 24px;
    font-weight: bold;
    letter-spacing: 4.08px;
    color: $primary-dark;
    margin-bottom: 18px;
  }

  p {
    letter-spacing: 0px;
    color: $primary-dark;
    font-size: 16px;
    line-height: 24px;
  }

  .slick-slider {
    .slick-arrow {
      display: block !important;
      background: transparent;
      z-index: 1000;

      &.slick-next {
        right: 10px;

        &:before {
          color: #606060;
        }
      }

      &.slick-prev {
        left: 10px;

        &:before {
          color: #606060;
        }
      }
    }

    .slick-slide {
      img {
        text-align: center;
        margin: 15px auto 0;
      }

      .steps {
        .incentive-slab {
          // background: url(/images/incentive_slabs_background.svg) no-repeat center;
          // height: 183px;
          margin-top: 30px;

          .slab {
            text-align: center;

            p {
              font-size: 12px;
              color: #303030;
              line-height: 16px;
              padding: 13px 0;
            }

            &.slab-4 {
              background: url(/images/slab-yellow-bg.svg) no-repeat center;
            }

            &.slab-3 {
              background: url(/images/slab-blue-bg.svg) no-repeat center;
            }

            &.slab-2 {
              background: url(/images/slab-green-bg.svg) no-repeat center;
            }

            &.slab-1 {
              background: url(/images/slab-red-bg.svg) no-repeat center;
            }
          }
        }

        >ul {
          padding: 10px 30px 0;

          li {
            color: $primary-dark;
            font-size: 16px;
            line-height: 24px;
            list-style: none;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            justify-content: flex-start;

            &:last-child {
              margin-bottom: 0;
            }

            p {
              width: 90%;
            }

            span {
              font-size: 12px;
              height: 48px;
              width: 48px;
              display: inline-flex;
              align-items: CENTER;
              justify-content: CENTER;
              border-radius: 50%;
              color: #fff;
              margin-right: 12px;

              &.pink {
                background: #F3A191;
              }

              &.purple {
                background: #868CF1;
              }

              &.blue {
                background: #4D5C7E;
              }
            }

          }

        }

        div {
          padding: 0 60px;

          p {
            font-size: 14px;
            margin: 5px 0 0;
          }
        }

        table {
          border-collapse: collapse;
          border-radius: 4px;
          margin-left: 90px;

          thead {
            tr {
              background: #ACC4FF;
              border-radius: 4px;
            }
          }

          tbody {
            tr {
              &:nth-child(2n+2) {
                background: #ACC4FF;
              }
            }
          }

          tr {

            th,
            td {
              padding: 4px;
              letter-spacing: 0px;
              color: #253858;
              font-size: 14px;
            }
          }
        }

        &.step-3 {
          ul li {
            margin-bottom: 25px;

            img {
              margin: 0 12px 0 0;
            }
          }
        }
      }
    }

    .slick-dots {
      li {
        &.slick-active button::before {
          background: $secondary-dark;
        }

        button:before {
          height: 3px;
        }
      }
    }
  }
}

.jagBanner {
  width: 100%;
  //width:25%;
  display: inline-block;
  background-color: #fff;
  border-radius: 16px;
  // margin-bottom: 15px;
  font-family: roboto;

  .logo {
    float: left;
  }

  h2 {
    text-align: center;
    font-size: 15px;
    padding-top: 16px;
    color: #0663F6;
    letter-spacing: 0.24px;
    line-height: 24px
  }

  .houseImg {
    width: 170px;
    margin-top: 2em;
  }

  .beginsIN {
    position: relative;
    color: #0663F6;
    font-size: 10px;
    font-weight: 600;
    text-align: center;
    line-height: 24px;
    letter-spacing: 0.16px;
    clear: both;
    top: -17px;
  }

  .calender {

    margin: 0 auto;
    text-align: center;

    div {
      height: 34px;
      width: 42px;
      border-radius: 0px 0px 8px 8px;
      font-size: 28px;
      font-weight: bold;
      color: #FFA71A;
      background: transparent linear-gradient(358deg, #a7c9ff 0%, #fff 100%);
      text-align: center;
      margin-right: 5px;
      margin-left: 5px;
      display: inline-block;
      letter-spacing: 0.45px;
      line-height: 28px;

      p {
        font-size: 8px;
        font-weight: 600;
        color: #253858;
      }
    }

    span {
      font-size: 28px;
      position: relative;
      color: #FFA71A;
      letter-spacing: 0.45px;
      font-weight: bold;
      top: -20px;
    }
  }

  .keyimg {
    vertical-align: middle;
    width: 62px;
    margin-left: 10px;
    margin-top: 0px;

  }

  .msg {
    width: auto;
    text-align: left;
    margin-bottom: 15px;
    padding: 0px 15px 0px 2px;

    .jagmsg {
      color: #FFA71A;
      font-size: 12px;
      font-family: roboto;
      font-weight: bold;
      line-height: 16px;
      display: flex;
      letter-spacing: 0.19px;
      margin-top: 10px;
      animation-name: topFadeOut;
      animation-duration: 2s;
      align-items: center;

    }
  }

  @keyframes topFadeOut {
    from {
      opacity: 0;
      -webkit-transform: scale3d(0.3, 0.3, 0.3);
      transform: scale3d(0.3, 0.3, 0.3);
    }

    50% {
      opacity: 1;
    }
  }

}

.banner-lottery {
  background: url(/images/TopBannerBG.svg) no-repeat center;
  width: 100%;
  height: 104px;
  overflow: hidden;
  background-size: cover;


  .jlogo {
    float: right;
    margin-top: 15px;
  }

  .LuckyDrawDate {
    float: left;
    margin-top: 22px;

    p {
      text-align: left;
      font: normal normal normal 14px/19px Roboto;
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      width: 229px;
      float: left;
      padding-top: 9px;
      margin-left: 30px;
    }

    .mr-top {
      margin-top: 13px;
    }
  }

  .disclaimer {
    float: right;
    margin-top: 80px;
    margin-right: 10px;

    a {
      color: #ffffff;
      font-size: 14px;
    }
  }

  button {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 13px 26px #5f5f5f29;
    border-radius: 24px;
    opacity: 1;
    font: normal normal 600 14px/19px Roboto;
    letter-spacing: 0px;
    outline: none;
    border: none;
    margin-top: 10px;
    margin-left: 40px;
    padding: 8px 26px;

    a {
      color: #003F9E;
    }
  }

  button:hover {
    background: #FFFFFF 0% 0% no-repeat padding-box;
  }

  .Jagyear {
    text-align: left;
    font: normal normal 900 40px/53px Roboto;
    letter-spacing: 0px;
    color: #FFFFFF;
    text-transform: uppercase;
    opacity: 1;
    margin-left: 20px;
    float: left;
  }
}

.ticketSearchDesign {
  width: 640px;
  background: transparent linear-gradient(180deg, #FFFFFF 0%, #DDE8FD 100%) 0% 0% no-repeat padding-box;
  border-radius: 8px 8px 0px 0px;
  opacity: 1;
  height: 100%;
  padding: 25px;

  h3 {
    text-align: left;
    font: normal normal 600 18px/24px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
  }

  span {
    text-align: left;
    font: normal normal normal 12px/16px Roboto;
    letter-spacing: 0px;
    color: #808080;
    opacity: 1;
    float: left;

  }

  h2 {
    text-align: left;
    font: normal normal 600 24px/32px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
    margin-left: 20px;
  }

  .ft-r {
    float: right;
  }

  .searchTicketNo {
    .MuiAutocomplete-inputRoot {
      background: #FFFFFF 0% 0% no-repeat padding-box;
      border-radius: 25px;
      opacity: 1;
      margin-top: 8px;
      height: 40px;
    }

    .MuiFormControl-root {
      .MuiInputLabel-outlined {
        transform: translate(20px, 22px) scale(0.9);
      }

      .MuiInputLabel-shrink {
        transform: translate(14px, 3px) scale(0.75);
      }
    }

    .MuiAutocomplete-input {
      height: 0.1876em;
    }
  }

  .searchBtn {
    background: #0065FF 0% 0% no-repeat padding-box;
    border-radius: 24px;
    opacity: 1;
    text-align: center;
    font: normal normal bold 14px/30px Roboto;
    margin-left: 10px;
    letter-spacing: 0px;
    color: #FFFFFF;
    border: none;
    padding: 4px 25px;
    outline: none;
    margin-top: 8px;
  }

  .LotteryTicket {
    text-align: center;
    margin: 1.2em 0em 1em;
    width: 100%;
    position: relative;

    img {
      position: relative;
    }

    .ticketcaption {
      position: absolute;
      left: 155px;
      top: 80px;
      color: #fff;
      font: normal normal bold 12px/16px Roboto;
    }

    .ticketNo {
      font: normal normal normal 25px/35px Rockwell;
      letter-spacing: 0px;
      color: #FFFFFF;
      opacity: 1;
      bottom: 34px;
      left: 135px;
      position: absolute;
    }

    .srNo {
      position: absolute;
      color: #fff;
      font: normal normal normal 12px/16px Roboto;
      right: 210px;
      bottom: 19px;
    }
  }

  .ticketView {
    text-align: center;
    font: normal normal normal 14px/19px Roboto;
    letter-spacing: 0px;
    color: #808080;
    opacity: 1;
    width: 100%;
    margin-bottom: 12px;
  }

  .ticketBox {
    background: #FFFFFF 0% 0% no-repeat padding-box;
    box-shadow: 0px 2px 3px #00000029;
    border-radius: 4px;
    opacity: 1;
    width: 100%;
    font: normal normal normal 12px/16px Roboto;
    letter-spacing: 0px;
    color: #253858;
    padding: 7px 15px;
    line-height: 23px;
    text-align: center;
    position: relative;

    img {
      float: left;
    }

    .srNo {
      position: absolute;
      left: 31px;
      top: 11px;
    }
  }

  .TicketActive {
    background: #F2F7FF 0% 0% no-repeat padding-box;
    box-shadow: 0px 2px 3px #00000029;
    border: 1px solid #0065FF;
    border-radius: 4px;
    opacity: 1;
  }

  .scrollBar {
    overflow-y: scroll;
    overflow-x: hidden;
    height: 280px;
  }

  .scrollBar::-webkit-scrollbar-track {
    box-shadow: transparent;
    background-color: transparent;
  }

  .scrollBar::-webkit-scrollbar {
    width: 6px;
    background-color: transparent;
    height: 1px;
    border: 3px;
  }

  .ticketbox {
    position: absolute;
    transform: matrix(0, -1, 1, 0, 0, 0);
    right: 115px;
    bottom: 68px;

    h3 {
      font: italic normal normal 14px/20px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
    }

    p {
      font: normal normal normal 12px/16px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
    }
  }

}

.incentiveCriteria {
  margin-top: 50px;

  h3 {
    text-align: left;
    font: normal normal normal 28px/36px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 1;
    margin-bottom: 30px;
  }

  .criteraPoint {

    margin-top: 25px;
    list-style-type: none;

    li {
      text-align: left;
      font: normal normal normal 14px/21px Roboto;
      letter-spacing: 0px;
      color: #26395a99;
      margin-bottom: 2.5em;
      display: flex;
      align-items: center;

      img {
        margin-right: 15px;
      }
    }
  }

  .slick-slider {
    border-radius: 10px;
    display: grid;
    width: 100%;
    background-color: #F2F7FF;
    margin-top: 10px;

    .slick-slide {
      padding: 20px;

      video {
        border-radius: 12px;
      }

      .card {
        box-shadow: none;

      }

      img {
        width: 100%;
        margin: auto;
        height: 386px;
      }
    }
  }

  .slick-dots {
    bottom: -12px;
    display: none !important;
  }
}

//jag New Landing page//
.scrollBar {
  height: 330px;
  overflow-y: auto;
  padding: 0px 12px;
}

.scrollBar::-webkit-scrollbar-track {
  box-shadow: transparent;
  background-color: #fff;
}

.scrollBar::-webkit-scrollbar {
  width: 6px;
  background-color: #808080;
  height: 1px;
  border: 3px;
}

.scrollBar::-webkit-scrollbar-thumb {
  background-color: #808080;
}

.JagLandingpage {
  width: 100%;

  header {
    width: 100%;
    height: 64px;
    position: fixed;
    background-color: #fff;
    opacity: 0.9;
    padding: 0px 90px;
  }

  .spaceHeight {
    margin-top: 60px;
    background-color: #fff;
    padding: 24px 90px;
  }

  .jagLogo {
    width: 170px;
    margin-top: 18px;
    float: left;

    img {
      width: 100%;
    }
  }

  nav {
    width: auto;
    float: right;
    margin-top: 19px;

    ul {
      display: flex;
      list-style-type: none;
      justify-content: space-between;

      li {
        a {
          text-align: left;
          font: normal normal 600 14px/19px Roboto;
          letter-spacing: 0px;
          color: #253858;
          opacity: 1;
          margin-left: 40px;
          margin-right: 5px;

        }
      }

      button {
        text-align: center;
        font: normal normal 600 14px/24px Roboto;
        letter-spacing: 0px;
        color: #0065ff;
        opacity: 1;
        border: 1px solid #0065ff;
        padding: 5px 30px;
        position: relative;
        border-radius: 8px;
        background-color: transparent;
        top: -6px;
      }
    }
  }

  h2 {
    font-size: 28px;
    color: #253858;
    font-family: "Roboto";
    line-height: 36px;
    font-weight: 100;
    margin-top: 2.4em;
  }

  .textMsg {
    text-align: justify;
    font: normal normal normal 14px/21px Roboto;
    letter-spacing: 0px;
    color: #253858;
    opacity: 0.6;
    padding: 10px 79px 20px 0px;
  }

  .homeSection {
    ul {
      list-style-type: none;
      display: flex;
      margin-left: 0px;
      margin-top: 1em;

      li {
        text-align: left;
        font: normal normal 600 12px/16px Roboto;
        letter-spacing: 0px;
        color: #008b10;
        opacity: 1;
        margin-right: 38px;

        img {
          position: relative;
          top: 5px;
          margin-right: 7px;
        }

        &:last-child {
          color: #0e047e;
        }
      }
    }

    .btnSection {
      padding: 46px 0px;

      button {
        background: #0065ff 0% 0% no-repeat padding-box;
        border-radius: 8px;
        opacity: 1;
        text-align: center;
        font: normal normal 600 14px/15px Roboto;
        letter-spacing: 0px;
        color: #ffffff;
        padding: 14px 35px;
        border: none;
        margin-right: 15px;

        &:last-child {
          border: 1px solid #0065ff;
          background-color: #fff;
          color: #0065ff;
        }
      }
    }
  }

  .ContestSection {
    h2 {
      padding-left: 0px;
    }

    .textMsg {
      padding-left: 0px;
      padding-right: 0px;
    }

    .contestBox {
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 16px #3469cb29;
      border-radius: 8px;
      opacity: 1;
      padding: 18px 12px;

      h3 {
        text-align: left;
        font: normal normal bold 17px/26px Roboto;
        letter-spacing: 0px;
        color: #2d3cd9;
        margin-bottom: 7px;

        span {
          font: normal normal bold 14px/26px Roboto;
        }
      }

      p {
        text-align: left;
        font: normal normal normal 12px/18px Roboto;
        letter-spacing: 0px;
        color: #253858;
        opacity: 0.6;
      }
    }
  }

  .textblue {
    color: #0bbdf2 !important;
  }

  .textgreen {
    color: #47d990 !important;
  }

  .textyellow {
    color: #ffc400 !important;
  }

  .leaderboard {
    position: relative;

    .card {
      height: 500px !important;

      .MuiCardContent-root {
        margin-top: 20px !important;
      }
    }

    .slick-slider ul.slick-dots li button::before {
      height: 12px;
      width: 12px;
      border-radius: 50%;
      border: 1px solid #00458b;
      background-color: transparent;
    }

    .slick-slider ul.slick-dots li.slick-active button::before {
      background-color: #00458b !important;
    }

    .card-body {
      height: 80px !important;
      font: normal normal normal 12px/18px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 0.6;
    }

    .textMsg {
      width: 45%;
    }

    .slick-slider {
      z-index: 999;
      background-color: transparent;
      padding-left: 0px;
    }

    .slide {
      width: 42%;
      display: inline-block;
      margin-right: 1.7%;
      margin-left: 0.9%;
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 16px #3469cb29;
      border-radius: 8px;
      opacity: 1;
      padding: 10px 10px 7px 10px;
      margin-bottom: 1.5em;

      .circle-fluid {
        border-radius: 8px !important;
        width: 100% !important;
        height: 160px !important;
      }

      .card-header {
        float: left;
      }

      h1 {
        text-align: left;
        font: normal normal bold 18px/26px Roboto;
        letter-spacing: 0px;
        color: #2d3cd9;
        opacity: 1;

        small {
          display: flex;
          font-weight: 500;
          text-align: left;
          font: normal normal normal 12px/18px Roboto;
          letter-spacing: 0px;
          color: #253858;
          opacity: 1;
        }
      }

      &:nth-child(2n + 2) {
        position: relative;
        top: 0px;
        margin-bottom: 0px;
      }

      a {
        margin-top: 9px;
        float: right;
        color: #1662df;
        font-weight: 600;
        cursor: pointer;
      }
    }

    .mt-200 {
      top: 200px !important;
    }

    .winnerbanner {
      position: absolute;
      right: 0px;
      z-index: 0;
      top: 60px;
    }
  }

  .FaqSection {
    h2 {
      padding-left: 12px;
    }

    .textMsg {
      padding-left: 12px;
    }

    .faqTab {
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 16px #3469cb29;
      border-radius: 8px;
      opacity: 1;
      margin-bottom: 0.8em;

      &::before {
        height: 0px;
      }

      p {
        text-align: left;
        font: normal normal normal 14px/21px Roboto;
        letter-spacing: 0.2px;
        color: #303030;
        opacity: 1;
      }

      svg {
        color: #0065ff;
      }
    }

    button {
      box-shadow: 0px 0px 16px #00000014;
      border-radius: 24px;
      opacity: 1;
      outline: none;
      border: none;
      color: #fff;
      font: normal normal normal 12px/21px Roboto;
      width: 110px;
      margin: auto;
      letter-spacing: 0.17px;
      padding: 6px;
      background-color: #0065ff;
      margin-top: 8px;
    }
  }

  footer {
    .topSection {
      background: #eaeaea 0% 0% no-repeat padding-box;
      padding: 17px;

      img {
        margin-left: 1em;
      }
    }

    .bottomSection {
      background: #00458b 0% 0% no-repeat padding-box;
      opacity: 1;
      padding: 13px;

      p {
        font: normal normal normal 14px/21px Roboto;
        letter-spacing: 0.2px;
        color: #ffffff;
        opacity: 1;
        text-align: center;
      }
    }
  }

  .GallerySection {
    header {
      position: relative;
      background-color: transparent;
      box-shadow: none;
      padding: 0;
    }

    .MuiBox-root {
      padding: 24px 0px;
    }

    img {
      width: 100%;
      height: 192px;
      border-radius: 8px;
    }

    h2 {
      text-align: center;
      margin: 0 0px 1em;
      padding-left: 0;
    }

    .MuiTabs-scrollButtons {
      height: 32px;
      width: 32px;
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 3px 6px #00000029;
      opacity: 1;
      color: #0065ff;
      border-radius: 20px;
    }

    .Mui-selected {
      background: #0065ff 0% 0% no-repeat padding-box !important;
      color: #fff !important;
    }

    .cateGoryIcon {
      display: inline-block;
      padding: 3px 11px;
      background: #54f89380 0% 0% no-repeat padding-box;
      border-radius: 8px;
      opacity: 1;
      margin-right: 10px;
      float: left;
      text-align: center;

      svg {
        width: 20px;
        margin-top: 5px;
      }
    }

    .version {
      display: contents;
      font-size: 16px;
      line-height: 26px;
      color: #253858;
      font-weight: 600;
      text-transform: uppercase;
    }

    .versiondate {
      text-align: left;
      font: normal normal normal 12px/18px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 0.6;
    }

    .imgcategories {
      background: #ffffff 0% 0% no-repeat padding-box;
      box-shadow: 0px 6px 6px #3469cb29;
      border: 1px solid #99d6e8;
      border-radius: 16px;
      opacity: 1;
      padding: 4px;
      min-height: 32px;
      text-align: center;
      font: normal normal normal 12px/21px Roboto;
      letter-spacing: 0.17px;
      text-transform: capitalize;
      color: #303030;
      margin: 0px 15px 0px 5px;
      min-width: 124px;
    }

    .Description {
      text-align: left;
      font: normal normal normal 12px/18px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 0.6;
      margin-bottom: 1em;
    }

    .date {
      text-align: left;
      font: normal normal 600 10px/14px Roboto;
      letter-spacing: 0px;
      color: #253858;
      opacity: 1;
      margin-bottom: 5px;
    }
  }
}

.howItWorks {
  border-radius: 10px !important;
  width: 100%;
  background-color: #ffffff !important;
  color: #0065ff !important;
  font-weight: 700 !important;
  border-color: #0065ff !important;
}

.IncentiveHighLight {
  color: rgb(0, 175, 81) !important;
  display: inline-block;
  font-weight: 700;
  // text-decoration: underline;
}

table.tableizer-table {
  font-size: 12px;
  border: 1px solid #CCC;
  width: 100%;
  margin-left: 0px !important;
}

table.tableizer-table tbody tr:nth-child(2n+2) {
  background-color: #fff !important;
}

.tableizer-table td {
  padding: 4px;
  margin: 3px;
  border: 1px solid #CCC;
}

.tableizer-table th {
  background-color: #104E8B;
  color: #FFF !important;
  font-weight: bold;
}

.Blink {
  font-weight: bold !important;
  animation: blinker 1s linear infinite;
  color: #258f23 !important;
}

@keyframes blinker {
  50% {
    opacity: 0.4;
  }
}


.downloadBtn {
  box-shadow: 0px 6px 16px #3469cb29 !important;
  width: auto;
  margin: 0px auto 20px !important;
  background-color: #0065ff !important;
  font: normal normal 600 15px/36px Roboto !important;
  color: #fff !important;
  padding: 0px 15px !important;
}