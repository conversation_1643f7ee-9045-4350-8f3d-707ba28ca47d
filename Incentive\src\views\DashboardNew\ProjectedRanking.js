import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import * as services from "../../services";
import moment from "moment";
import _ from "lodash";



const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
}));

const ProjectedRanking = (props) => {
  const classes = useStyles();
  const {
    projectedData,
    userId
  } = props;
  const [agentDetail, setAgentDetail] = useState([]);
  const [date, setDate] = useState(moment().subtract(3, 'months').startOf('month').format("DD-MM-YYYY"));

  const getAgentDetail = () => {
    setAgentDetail([]);

    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/GetIncentiveAgentDetails/${userId}/${date}`)
      .then(response => {
        if (response && response != "[]") {
          setAgentDetail(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  useEffect(() => {
    getAgentDetail();
  }, []);

  return (
    <>
      <div className="pading-zero" className={classes.root}>

        <div className="ranking-data">

          <div className="rank-box">
            <div className='rank-box-left-section'>
              <div className='name-description'>
                <strong>{agentDetail.UserName || "-"}</strong>
                <p> {projectedData.CurrentProcess || "-"}, {agentDetail.JoiningMonth || "-"} Months
                </p>
                <p>
                  Projected CJ Incentive: <h4 className="IncentiveHighLight">
                    <h4 className={projectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}>
                      <i className="fa fa-inr"></i> {projectedData.ActualCurrentProjectedIncentive && Math.round(projectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}
                    </h4>
                  </h4>
                </p>
              </div>
              {projectedData.ActualCurrentProjectedIncentive == 0 &&
                <div className="currentMonthSlab">
                  <h3>
                    Attention!
                    <p>You need to work hard in order to earn CJ incentive.</p>
                  </h3>
                  {projectedData.SuperGroupTypeId == 3 &&
                    <p><div><img src="/images/incentive/task_alt.svg" /></div>
                      Current Sourced Bookings/Day.
                      <a>#{projectedData.PerDayBKGS}</a>
                    </p>}
                  {projectedData.SuperGroupTypeId == 3 && projectedData.ActualCurrentProjectedIncentive > 0 &&
                    <p className='add'><div><img src="/images/incentive/task_alt.svg" /></div>
                    You are required to do Sourced Booking of &nbsp;
                      <a>#{Math.round((projectedData.MinBKGSRequired - projectedData.BKGSTillNow) / projectedData.NextWorkingDays * 10) / 10}</a>
                      &nbsp;per day for remaining {projectedData.NextWorkingDays} days to justify your cost.
                    </p>
                  }
                  {projectedData.SuperGroupTypeId == 1 &&
                    <p><div><img src="/images/incentive/task_alt.svg" /></div>
                      Current Sourced APE/Day.
                      <i className="fa fa-inr"></i> {projectedData.PerDayAPE && Math.round(projectedData.PerDayAPE).toLocaleString('en-IN')}
                    </p>
                  }
                  {projectedData.SuperGroupTypeId == 1 && projectedData.ProductId != 115 &&
                    <p className='add'><div><img src="/images/incentive/task_alt.svg" /></div>
                      You are required to do  Sourced APE of &nbsp;
                      <a><i className="fa fa-inr"></i> {Math.round((projectedData.MinAPERequired - projectedData.APETillNow) / projectedData.NextWorkingDays).toLocaleString('en-IN')}</a>
                      &nbsp;per day for remaining {projectedData.NextWorkingDays} days to justify your cost.
                    </p>
                  }
                  {/* {projectedData.SuperGroupTypeId == 1 && projectedData.ProductId == 115 &&
                    <p className='add'><div><img src="/images/incentive/task_alt.svg" /></div>
                      You are required to do  Sourced APE of &nbsp;
                      <a><i className="fa fa-inr"></i> {Math.round((projectedData.MinAPERequired - projectedData.APETillNow) / projectedData.NextWorkingDays).toLocaleString('en-IN')}</a>
                      &nbsp;per day for remaining {projectedData.NextWorkingDays} days to justify your cost.
                    </p>
                    
                  } */}

                  {/* {projectedData.SuperGroupTypeId == 3 && projectedData.ProductId == 117 &&
                    <p className='add'><div><img src="/images/incentive/task_alt.svg" /></div>
                      You are required to do Booking of &nbsp;
                      <a>{sumDRR("CurrentProjection")}</a>
                      &nbsp;per day for remaining {projectedData.NextWorkingDays} days to justify your cost.
                    </p>
                  } */}
                </div>
              }

              {projectedData.ActualCurrentProjectedIncentive > 0 &&

                <div className="currentMonthSlab">
                  <h3>
                    Congratulations!
                  </h3>
                  {[117, 115].indexOf(projectedData.ProductId) == -1 && <>
                    <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> You are qualifying for Slab {projectedData.Slab} basis current projections, and your CJ incentives will be <a>calculated @ {projectedData.CurrentSlabPercentage}%</a></p>
                    {projectedData.Slab > 1 &&
                      <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> However there is an opportunity for you to earn incremental CJ Incentive by Qualifying for <a> Slab {projectedData.NextSlab} @ {projectedData.NextSlabPercentage}% = {projectedData.IncrementalIncentive && Math.round(projectedData.IncrementalIncentive / 1000).toLocaleString('en-IN')}K </a></p>
                    }
                    {projectedData.Slab > 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Daily sourcing required for next {projectedData.NextWorkingDays} days to qualify for <a>SLAB {projectedData.NextSlab} =
                        {projectedData.SuperGroupTypeId == 1 ? projectedData.NextSlabPerDayAPE && Math.round(projectedData.NextSlabPerDayAPE / 1000).toLocaleString('en-IN') + 'K' : projectedData.NextSlabPerDayBKGS + ' Bookings'}</a></p>
                    }

                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Current Sourced APE/Day <a><i className="fa fa-inr"></i> {projectedData.PerDayAPE && Math.round(projectedData.PerDayAPE).toLocaleString('en-IN')}K </a></p>
                    }
                    
                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Keep up the good work to maximize your incentive this month.</p>
                    }</>
                  }

                  {projectedData.ProductId == 117 && <>
                    <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> You are qualifying for Slab {projectedData.Slab} basis current projections</p>
                    {projectedData.Slab > 1 &&
                      <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> However there is an opportunity for you to earn incremental CJ Incentive by Qualifying for <a> Slab {projectedData.NextSlab} = {projectedData.IncrementalIncentive && Math.round(projectedData.IncrementalIncentive / 1000).toLocaleString('en-IN')}K </a></p>
                    }
                    {projectedData.Slab > 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Daily sourcing required for next {projectedData.NextWorkingDays} days to qualify for <a>SLAB {projectedData.NextSlab} =
                        {projectedData.SuperGroupTypeId == 1 ? projectedData.NextSlabPerDayAPE && Math.round(projectedData.NextSlabPerDayAPE / 1000).toLocaleString('en-IN') + 'K' : projectedData.NextSlabPerDayBKGS + ' Bookings'}</a></p>
                    }
                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Current Sourced Booking/Day =<a> {projectedData.PerDayBKGS} </a></p>
                    }
                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Keep up the good work to maximize your incentive this month.</p>
                    }</>
                  }
                  {projectedData.ProductId == 115 && <>
                    <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> You are qualifying for Slab {projectedData.Slab} basis current projections</p>
                    {projectedData.Slab > 1 &&
                      <p className='add'> <div><img src="/images/incentive/task_alt.svg" /></div> However there is an opportunity for you to earn incremental CJ Incentive by Qualifying for <a> Slab {projectedData.NextSlab} = {projectedData.IncrementalIncentive && Math.round(projectedData.IncrementalIncentive / 1000).toLocaleString('en-IN')}K </a></p>
                    }
                    {projectedData.Slab > 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Daily sourcing required for next {projectedData.NextWorkingDays} days to qualify for <a>SLAB {projectedData.NextSlab} =
                        {projectedData.SuperGroupTypeId == 1 ? projectedData.NextSlabPerDayAPE && Math.round(projectedData.NextSlabPerDayAPE / 1000).toLocaleString('en-IN') + 'K' : projectedData.NextSlabPerDayBKGS + ' Bookings'}</a></p>
                    }
                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Current Sourced APE/Day <a><i className="fa fa-inr"></i> {projectedData.PerDayAPE && Math.round(projectedData.PerDayAPE).toLocaleString('en-IN')}K </a></p>
                    }
                    {projectedData.Slab == 1 &&
                      <p><div><img src="/images/incentive/task_alt.svg" /></div> Keep up the good work to maximize your incentive this month.</p>
                    }</>
                  }

                </div>
              }

              {/* END current month TAb */}
            </div>
            <div className='rank-box-right-section'>
              <ul>
                <li className='rank' style={{ width: '245px' }}>
                  <span className='label'>Rank 
                  {([117].indexOf(projectedData.ProductId) == -1) &&  <h4>Based on Projected Weighted APE</h4>}
                  {([117].indexOf(projectedData.ProductId) > -1) &&  <h4>Based on Projected Bookings</h4>}
                  </span><span className='value'>#{projectedData.AgentRank || "-"}/{projectedData.TotalProcessAgents || "-"}</span>
                </li>
                {(projectedData.ProductId) ?
                  <li className='slab' style={{ width: '245px' }}>
                    <span className='label'>Slab</span><span className='value'>{projectedData.Slab || "-"}
                    </span>
                  </li>
                  : ''}
              </ul>
              <div className='background-caricrature'></div>
            </div>

            {/* <ProcessBar firstLevel={agentDetail.Currentlevel} lastLevel={agentDetail.NextLevel} levelPercentage={agentDetail.LevelPercentage || "0"} /> */}
          </div>
        </div>
      </div>
    </>
  );
};

export default ProjectedRanking;
