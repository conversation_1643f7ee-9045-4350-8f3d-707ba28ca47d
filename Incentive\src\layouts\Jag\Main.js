import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import clsx from 'clsx';
import { makeStyles, useTheme } from '@material-ui/styles';
import { useMediaQuery } from '@material-ui/core';

import { Sidebar, Topbar, Bottombar } from './components';
import LinearProgressBar from './../../components/LinearProgressBar';
// import Notifier from './../../components/Notifier';
import { connect } from 'react-redux';
import * as actions from './../../store/actions';
import LoaderComponent from "../../components/Loader";


const useStyles = makeStyles(theme => ({
  root: {
    display: 'flex',
  },
  shiftContent: {
    //paddingLeft: 215
  },
  content: {
    height: '100%',
    flexGrow: 1
  }
}));

const Main = props => {
  const { children, user, onLogout, notifications } = props;
  
  const [isLoading, setIsLoading] = useState(true);
  const [userDetail, setUserDetail] = useState(null);

  if (!userDetail || userDetail == null) {
    setTimeout(() => {
      const userData = JSON.parse(localStorage.getItem('user'));
      if (userData != 'undefined' && userData != null) {
        setUserDetail(userData);      
      }
      setIsLoading(false);
    }, 2000);
    }

  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });

  const [openSidebar, setOpenSidebar] = useState(false);

  const handleSidebarOpen = () => {
    setOpenSidebar(true);
  };

  const handleSidebarClose = () => {
    setOpenSidebar(false);
  };

  const shouldOpenSidebar = openSidebar;

  return (
    <div
      className={clsx({
        [classes.root]: true,
        [classes.shiftContent]: isDesktop
      })}
    >
      {/* <LinearProgressBar /> */}
        {/* <Sidebar
          // onClose={handleSidebarClose}
          open={shouldOpenSidebar}
          variant='persistent'
          user={userDetail}
        /> */}
      {/* <Notifier /> */}
      <main className="JagLandingpage">
      {isLoading ? <LoaderComponent open={true} /> : null}
      <Topbar user={userDetail}
        history={children.props.history}
        onSidebarOpen={handleSidebarOpen}
        onSidebarClose={handleSidebarClose}
        isSideBarOpen = {shouldOpenSidebar}
        onLogout={onLogout} 
        notifications = {notifications}
        />
           
        {children}
        <Bottombar />
      
       
       
      </main>
    </div>
  );
};

Main.propTypes = {
  children: PropTypes.node
};

const mapStateToProps = state => {
  return {
    isAuth: !!state.auth.isAuth,
    user: state.auth.user,
    access: !!(state.common.access == 'web'),
    notifications: state.notifications
  };
};

const mapDispatchToProps = dispatch => {
  return {
    onLogout: (historyNav) => console.log(0)
  };
};

export default connect(mapStateToProps, mapDispatchToProps)(Main);
