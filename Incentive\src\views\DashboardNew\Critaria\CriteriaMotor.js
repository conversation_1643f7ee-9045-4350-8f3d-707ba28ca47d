import React, { useEffect, useState } from "react";
import * as services from "../../../services";
import Slider from "react-slick";

import parse from "html-react-parser";

const settings = {
  dots: true,
  infinite: true,
  arrows: true,
  speed: 500,
  slidesToShow: 1,
  slidesToScroll: 1
};

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});


const CriteriaMotor = (props) => {
  const { show, handleClose, superGroupId, productId, date } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState(null);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getCriteria = () => {
    setCriteriaList([]);
    setCriteriaListHtml(null);
    setCriteriaSlabs([]);
    services
      .API_GET(`Incentive/GetIncentiveCriteria/${superGroupId}/${productId}/${date}`)
      .then(response => {
        console.log(response && response != "[]");
        if (response && response != "[]") {
          setCriteriaList(response.Insights || null);
          setCriteriaListHtml(response.InsightsHtml || null);
          setCriteriaSlabs(response.Slabs ? _.orderBy(response.Slabs, ['IncentiveLevel'], ['desc']) : null);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  useEffect(() => {
    if (superGroupId && productId && date) {
      //getCriteria();
    }
  }, [superGroupId, productId, date]);

  const renderHtml = () => {

    let html = parse(criteriaListHtml)

    return html;
  }


  return (
    <div className='how-it-works-section'>
      <span>Incentive Criterias</span>
      <h3>HOW IT WORKS?</h3>
      <p className="text-center">Your journey will be divided into levels based on Booking</p>
      {/* <div className='steps step-1'>
        <div className="incentive-slab">
          {
            criteriaSlabs && criteriaSlabs.map((item, index) => {
              return <div className={"slab slab-" + (criteriaSlabs.length - index)}>
                <p>SLAB {index + 1}<br /> {Math.round(item.MinRangeDisplay).toLocaleString('en-IN')} - {Math.round(item.MaxRangeDisplay).toLocaleString('en-IN')} ({item.IncentivePercentage}%)</p>
              </div>
            })
          }
        </div>
      </div> */}
      {/* {criteriaListHtml && renderHtml()} */}
      {/* <Slider {...settings} > */}
      <div className='steps step-1'>

        <table class="tableizer-table">
          <thead><tr class="tableizer-firstrow"><th colSpan={5}>Non PSU</th></tr>
            <tr><th>Process</th><th>TENURE</th><th colSpan={2}>Sales</th><th>Amount</th></tr>
          </thead><tbody>
            <tr><td>Inbound</td><td>2.00</td><td>1.00</td><td>89.00</td><td>375.00</td></tr>
            <tr><td>Inbound</td><td>2.00</td><td>90.00</td><td>100.00</td><td>400.00</td></tr>
            <tr><td>Inbound</td><td>2.00</td><td>101.00</td><td>110.00</td><td>400.00</td></tr>
            <tr><td>Inbound</td><td>2.00</td><td>111.00</td><td>1000.00</td><td>400.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>1.00</td><td>89.00</td><td>475.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>90.00</td><td>100.00</td><td>500.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>101.00</td><td>110.00</td><td>525.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>111.00</td><td>120.00</td><td>550.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>121.00</td><td>500.00</td><td>600.00</td></tr>
            <tr><td>Outbound</td><td>2.00</td><td>501.00</td><td>1000.00</td><td>600.00</td></tr>
            <tr><td>Chat</td><td>2.00</td><td>1.00</td><td>174.00</td><td>350.00</td></tr>
            <tr><td>Chat</td><td>2.00</td><td>175.00</td><td>199.00</td><td>375.00</td></tr>
            <tr><td>Chat</td><td>2.00</td><td>200.00</td><td>224.00</td><td>425.00</td></tr>
            <tr><td>Chat</td><td>2.00</td><td>225.00</td><td>500.00</td><td>450.00</td></tr>
            <tr><td>Chat</td><td>2.00</td><td>501.00</td><td>1000.00</td><td>450.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>1.00</td><td>89.00</td><td>550.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>90.00</td><td>100.00</td><td>580.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>101.00</td><td>110.00</td><td>610.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>111.00</td><td>120.00</td><td>640.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>121.00</td><td>500.00</td><td>700.00</td></tr>
            <tr><td>Tamilian</td><td>2.00</td><td>501.00</td><td>1000.00</td><td>700.00</td></tr>
            <tr><td>Offline</td><td>2.00</td><td>1.00</td><td>79.00</td><td>350.00</td></tr>
            <tr><td>Offline</td><td>2.00</td><td>80.00</td><td>99.00</td><td>375.00</td></tr>
            <tr><td>Offline</td><td>2.00</td><td>100.00</td><td>119.00</td><td>575.00</td></tr>
            <tr><td>Offline</td><td>2.00</td><td>120.00</td><td>1000.00</td><td>600.00</td></tr>
            <tr><td>Retention</td><td>2.00</td><td>1.00</td><td>69.00</td><td>400.00</td></tr>
            <tr><td>Retention</td><td>2.00</td><td>70.00</td><td>79.00</td><td>415.00</td></tr>
            <tr><td>Retention</td><td>2.00</td><td>80.00</td><td>89.00</td><td>447.00</td></tr>
            <tr><td>Retention</td><td>2.00</td><td>90.00</td><td>99.00</td><td>481.00</td></tr>
            <tr><td>Retention</td><td>2.00</td><td>100.00</td><td>1000.00</td><td>519.00</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>1</td><td>149</td><td>350</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>150</td><td>174</td><td>375</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>175</td><td>199</td><td>425</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>200</td><td>500</td><td>450</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>501</td><td>1000</td><td>450</td></tr>
          </tbody></table>

      </div>
      <div className='steps step-2'>
        <table class="tableizer-table">
          <thead><tr class="tableizer-firstrow"><th colSpan={5}>PSU</th></tr>
            <tr><th>Process</th><th>TENURE</th><th colSpan={2}>Sales</th><th>Amount</th></tr>
          </thead><tbody>
            <tr><td>Inbound</td><td>2</td><td>1</td><td>89</td><td>175.00</td></tr>
            <tr><td>Inbound</td><td>2</td><td>90</td><td>100</td><td>200.00</td></tr>
            <tr><td>Inbound</td><td>2</td><td>101</td><td>110</td><td>200.00</td></tr>
            <tr><td>Inbound</td><td>2</td><td>111</td><td>1000</td><td>200.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>1</td><td>89</td><td>275.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>90</td><td>100</td><td>300.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>101</td><td>110</td><td>325.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>111</td><td>120</td><td>350.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>121</td><td>500</td><td>400.00</td></tr>
            <tr><td>Outbound</td><td>2</td><td>501</td><td>1000</td><td>400.00</td></tr>
            <tr><td>Chat</td><td>2</td><td>1</td><td>174</td><td>150.00</td></tr>
            <tr><td>Chat</td><td>2</td><td>175</td><td>199</td><td>175.00</td></tr>
            <tr><td>Chat</td><td>2</td><td>200</td><td>224</td><td>225.00</td></tr>
            <tr><td>Chat</td><td>2</td><td>225</td><td>500</td><td>250.00</td></tr>
            <tr><td>Chat</td><td>2</td><td>501</td><td>1000</td><td>250.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>1</td><td>89</td><td>350.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>90</td><td>100</td><td>380.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>101</td><td>110</td><td>410.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>111</td><td>120</td><td>440.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>121</td><td>500</td><td>500.00</td></tr>
            <tr><td>Tamilian</td><td>2</td><td>501</td><td>1000</td><td>500.00</td></tr>
            <tr><td>Offline</td><td>2</td><td>1</td><td>79</td><td>150.00</td></tr>
            <tr><td>Offline</td><td>2</td><td>80</td><td>99</td><td>175.00</td></tr>
            <tr><td>Offline</td><td>2</td><td>100</td><td>119</td><td>375.00</td></tr>
            <tr><td>Offline</td><td>2</td><td>120</td><td>1000</td><td>400.00</td></tr>
            <tr><td>Retention</td><td>2</td><td>1</td><td>69</td><td>200.00</td></tr>
            <tr><td>Retention</td><td>2</td><td>70</td><td>79</td><td>215.00</td></tr>
            <tr><td>Retention</td><td>2</td><td>80</td><td>89</td><td>247.00</td></tr>
            <tr><td>Retention</td><td>2</td><td>90</td><td>99</td><td>281.00</td></tr>
            <tr><td>Retention</td><td>2</td><td>100</td><td>1000</td><td>319.00</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>1</td><td>149</td><td>150</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>150</td><td>174</td><td>175</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>175</td><td>199</td><td>225</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>200</td><td>500</td><td>250</td></tr>
            <tr><td>Offline-Chat</td><td>2</td><td>501</td><td>1000</td><td>250</td></tr>
          </tbody></table>
      </div>
      <div class='steps'>
        <ul>
          {/* <li>
            <span class='pink'>NC</span>
            <p>New Car: 600 Rs per sale with cost justification </p>
          </li> */}
          <li>
            <span class='purple'>QD</span>
            <p>Quality Deduction </p>
          </li>
          <div>
            <p>Less than 80% = 20% Deduction</p>
          </div>
        </ul>

      </div>


      {/* </Slider> */}
    </div>
  );
}
export default CriteriaMotor;