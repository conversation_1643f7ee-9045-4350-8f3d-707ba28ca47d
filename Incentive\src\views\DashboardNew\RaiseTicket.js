import React, { useEffect, useState, Fragment } from "react";
import moment from "moment";
import * as services from "../../services";

const RaiseTicket = (props) => {
  const [feedbackurl, setfeedbackurl] = useState(null);
  const [userId, setUserId] = useState(null);
  const [hover, setHover] = useState(false);
  const onHover = () => {
      setHover(!hover);
  };
  useEffect(() => {
    const tokenPath = window.location.pathname.split("/");
    if (tokenPath.length >= 3) {
      let urlToken = tokenPath[2];
      setUserId(urlToken);
      setfeedbackurl(
        "https://matrixticket.policybazaar.com/Landing.html#/matrix/LandingPage/" +
          urlToken +
          "/incentive"
      );
    }
  }, []);

  const handlefeedbackbutton = (event) => {
    // const dt = moment()
    //   .subtract(1, "months")
    //   .startOf("month")
    //   .format("DD-MM-YYYY");
    // services
    //   .API_GET(
    //     `Incentive/InsertAgentIncentivLog/${userId}/0/7/${dt}?PageName=FAQ&EventName=Raise a Ticket`
    //   )
    //   .then((response) => {})
    //   .catch((err) => {});
  
    window.open(feedbackurl);
  };
  

  return (
      <>
          {hover && <h6 className="UserHelpHoverText">{props.onHover}</h6>}
          <a onClick={handlefeedbackbutton}>
              <img
                  src="/images/incentive/help_icon.svg"
                  className="UserHelp"
                  onMouseEnter={onHover}
                  onMouseLeave={onHover}
              />
          </a>
      </>
  );
};

export default RaiseTicket;
