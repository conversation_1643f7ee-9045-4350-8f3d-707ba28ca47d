.jag-design {
    background: #fff
}

.jag-design .top-header {
    background: #f5f5f5;
    padding: 10px 0px 0px;
}

.jag-design .top-header p {
    margin: 15px 8px 0px;
    text-align: left;
}

.jag-design .main-header {
    background: #fff;
    padding: 9px 60px;

    @media (max-width:1024px) and (min-width:320px) {
        padding: 8px 16px;
    }
}

.jag-design .main-header ul li {
    position: relative;
    border-right: 1px solid #17103c33;
    padding-right: 11px;
}

.jag-design .main-header ul li:last-child {
    background: #0065FF;
    border-radius: 8px;
    width: 39px;
    height: 39px;
    line-height: 39px;
    text-align: center;
    margin-left: 11px;
    padding: 0;
    display: block;

    @media (max-width:1024px) and (min-width:320px) {
        margin: 0;
    }
}

.jag-design .agentdetails:before {
    position: absolute;
    content: "";
    top: 0;
    left: 0;
    right: 0;
    margin: auto;
    background: url(/images/jag/purple-bg.svg);
    z-index: -1;
    background-size: cover;
    background-repeat: no-repeat;
    height: 280px;
    clip-path: polygon(0 0, 100% 0, 100% 52%, 0% 100%);
    -webkit-clip-path: polygon(0 0, 100% 0, 100% 52%, 0% 100%);
    background-position: center;

    @media (max-width:1024px) and (min-width:320px) {
        background-position: inherit !important;
        clip-path: polygon(0 0, 100% 0, 100% 90%, 0% 100%);
        -webkit-clip-path: polygon(0 0, 100% 0, 100% 90%, 0% 100%);
        height: 430px;
        // @media (max-width:767px) and (min-width:320px){
        // height: 450px
        // }    
    }
}

.jag-design .agentdetails {
    display: flex;
    padding: 38px 60px 0px;
    color: #fff;
    z-index: 1;
    position: relative;
    justify-content: space-between;

    @media (max-width:1024px) and (min-width:320px) {
        padding: 16px;
        white-space: nowrap;
    }

}

.jag-design .agentdetails h1 {
    font-family: 'Merriweather';
    font-weight: 700;
    font-size: 32px;
    line-height: 40px;

    @media (max-width:1024px) and (min-width:320px) {
        font-size: 24px;
        line-height: 30px;
    }
}

.agentdetails .leftside .inner {
    margin: 8px 0px 21px;

    @media (max-width:1024px) and (min-width:320px) {
        margin-bottom: 10px;
    }
}

.agentdetails .leftside .inner ul.d-flex {
    background: #00000033;
    border-radius: 24px;
    padding: 0px 10px;
    margin: 0;
    margin-right: 12px;
}

.agentdetails .leftside ul.d-flex li {
    padding: 5px 6px;
    font-weight: 500;
    font-size: 14px;
    line-height: 16px;
    position: relative;

    @media (max-width:1024px) and (min-width:320px) {
        font-size: 12px;
    }
}

.agentdetails .leftside ul.d-flex li:first-child:after,
.agentdetails ul.rewards-listing li:first-child:after {
    position: absolute;
    content: "";
    top: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    background: #fff;
    width: 1px;
    height: 15px;
}

.agentdetails ul.rewards-listing {
    margin-bottom: 20px;
    justify-content: space-between;

    @media (max-width:1024px) and (min-width:320px) {
        margin-bottom: 15px;
    }
}

.agentdetails ul.rewards-listing li {
    padding: 0px 15px !important;
    width: 50%;
    white-space: nowrap;

    @media (max-width:1024px) and (min-width:320px) {
        padding: 5px 45px 5px 0px !important;
        width: auto;
    }
}

.agentdetails ul.rewards-listing li:first-child {
    padding-left: 5px !important;
    padding-right: 0px !important;
}

.agentdetails ul.rewards-listing li label {
    font-weight: 700;
    font-size: 12px;
    line-height: 14px;
    display: block;
    margin-bottom: 5px;
}

.agentdetails ul.rewards-listing li span {
    font-weight: 400;
    font-size: 20px;
    line-height: 23px;

    @media (max-width:1024px) and (min-width:320px) {
        font-size: 18px;
        line-height: 21px
    }
}

.agentdetails ul.rewards-listing li:first-child:after {
    height: 45px !important;

    @media (max-width:1024px) and (min-width:320px) {
        height: 40px !important;
        right: 22px !important
    }
}

.agentdetails .leftside p {
    text-decoration: underline;
    position: relative;
    display: inline-block;
    cursor: pointer;
}

.agentdetails .leftside p img {
    padding-left: 1px;
    filter: brightness(0) invert(1);
}

.agentdetails .target-box {
    box-shadow: 0px 4px 12px #00000040;
    -webkit-box-shadow: 0px 4px 12px #00000040;
    border-radius: 8px;
    background: #fff;
    color: #17103C;
    display: block;
    padding: 7px 9px;
    margin: auto;
    width: 170px;
    position: relative;
    top: 44px;
    left: -110px;

    @media (max-width:1024px) and (min-width:320px) {
        width: 148px;
        ;
        position: absolute;
        top: auto;
        bottom: -60px;
        left: 16px;
    }
}

.agentdetails .target-box .target-icon {
    float: left;
    padding-right: 7px;

    @media (max-width:1024px) and (min-width:320px) {
        padding-right: 6px;
        padding-top: 8px;

        img {
            width: 24px;
        }

    }
}

.agentdetails .target-box label {
    font-weight: 400;
    font-size: 16px;
    line-height: 19px;
    white-space: nowrap;

    @media (max-width:1024px) and (min-width:320px) {
        font-size: 12px;
        line-height: 14px
    }
}

.agentdetails .target-box p {
    font-weight: 700;
    font-size: 24px;
    line-height: 28px;
    background: linear-gradient(90.11deg, #6FCEEF 2.36%, #918EF6 99.06%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
    white-space: nowrap;
    padding-top: 4px;

    @media (max-width:1024px) and (min-width:320px) {
        font-size: 18px;
        line-height: 21px;
        padding: 0;
    }
}

.agentdetails .house-image:after {
    position: absolute;
    content: '';
    top: auto;
    bottom: -28px;
    left: 0;
    right: 0;
    margin: auto;
    background: #fff;
    width: 350px;
    height: 40px;
    transform: rotate(174deg);
    -webkit-transform: rotate(174deg);
    z-index: 9;
}

.agentdetails .house-image {
    position: absolute;
    top: 10px;
    right: 315px;
    margin: auto;
    z-index: -1;

    @media (max-width:1024px) and (min-width:320px) {
        top: auto !important;
        right: 0;
        bottom: -72px;
        width: 350px;

        @media (max-width:767px) and (min-width:320px) {
            width: 175px;
            bottom: -75px
        }
    }
}

.agentdetails .house-image img {
    width: 400px;
}

.agentdetails .house-image img.mobile {
    display: none;

    @media (max-width:1024px) and (min-width:320px) {
        display: block;
    }

}

/*----------------Emerging-star--------------*/
.emerging-star .top-header label {
    display: none;
}

.emerging-star .main-header ul li:last-child {
    background: linear-gradient(180deg, #001A76 0%, #0A178F 100%);
}

.emerging-star .agentdetails:before {
    background: url(/images/jag/golden-bg.svg);
}

.emerging-star .agentdetails .target-box p {
    background: linear-gradient(90.11deg, #D67027 2.36%, #CA8A0D 99.06%);
    -webkit-background-clip: text;
    background-clip: text;
}

.emerging-star .agentdetails .house-image {
    top: 2px;
}


/*----------Business Health rating Popup ---------*/
.BuniessHealthRatingPOPup {
    .MuiDialog-paperWidthSm {
        width: 470px;
        padding: 0px;
        h2{      
    background-color: #0065FF;
    padding: 7px;
    text-align: center;
    color: #fff;
    font-weight: 500;
    font-size: 17px;
        }
        h3 {
            font: normal normal 600 14px/20px Roboto;
            padding: 10px 15px 20px;
            span{
                color:#d70808;
            }
        }
    }
}