import React, { useEffect } from "react";
import { makeStyles } from "@material-ui/styles";
import {
  Grid,
} from "@material-ui/core";

import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import * as services from "../../services";
import { getCookie, setCookie } from "../../utils/utility";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const Bonanza = (props) => {
  const { userId, productId } = props;
  const [open, setOpen] = React.useState(true);




  useEffect(() => {    
    // let c = getCookie('bonanza');
    // if (c) {
    //   setOpen(false)
    // }
  }, []
  );

  const handleClose = () => {
    setOpen(false);
    //setCookie("bonanza", 1, 1);
  }

  return (
    <>
      <Dialog
        open={open}
        onClose={() => handleClose()}
      >
        {/* <DialogTitle id="alert-dialog-title">wall of fame</DialogTitle> */}
        <DialogContent>
          <img src="/images/JAG_Banner_March_1.png" width="100%" />

        </DialogContent>

      </Dialog>

    </>
  );
};

export default Bonanza;
