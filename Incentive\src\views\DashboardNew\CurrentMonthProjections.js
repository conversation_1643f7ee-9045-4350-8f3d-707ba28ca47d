/* eslint-disable no-undef */
import React, { useEffect, useState, useRef } from "react";
import { makeStyles, useTheme } from "@material-ui/core/styles";
import * as services from "../../services";
import { withSnackbar } from 'notistack';
import { useMediaQuery } from '@material-ui/core';

//import { useParams } from "react-router";
import {
  TableContainer,
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  Grid,
  FormControl,

} from "@material-ui/core";
import Radio from "@material-ui/core/Radio";
import RadioGroup from "@material-ui/core/RadioGroup";
import FormControlLabel from "@material-ui/core/FormControlLabel";
import moment from "moment";
import ThumbDownIcon from '@material-ui/icons/ThumbDown';
import ThumbUpIcon from '@material-ui/icons/ThumbUp';
import FeedbackIcon from '@material-ui/icons/Feedback';
import ChevronRightIcon from '@material-ui/icons/ChevronRight';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import ProjectedRanking from "./ProjectedRanking";
import CriteriaTerm from "./Critaria/CriteriaTerm";
import Accordion from '@material-ui/core/Accordion';
import AccordionSummary from '@material-ui/core/AccordionSummary';
import AccordionDetails from '@material-ui/core/AccordionDetails';
import CriteriaMotor from "./Critaria/CriteriaMotor";
import CriteriaInvestment from "./Critaria/CriteriaInvestment";

import {
  Box,
  Tabs, Tab
} from "@material-ui/core";

const useStyles = makeStyles((theme) => ({
  rightPopup: {
    position: "relative",
    zIndex: "999",
  },
  list: {
    width: 600,
  },
  mList: {
    width: '100%',
  },
  currentProjectedIncentive: {
    textAlign: 'left',
    marginLeft: '1rem',
    marginBottom: '0.5rem',
    fontSize: '13px',
    fontWeight: 'normal'
  }
}));

const CurrentMonthProjections = (props) => {
  const { agentDetail } = props;
  const classes = useStyles();
  const theme = useTheme();
  const isDesktop = useMediaQuery(theme.breakpoints.up('lg'), {
    defaultMatches: true
  });
  //const urlParams = useParams();
  //const [userId, setUserId] = useState(null);

  //const [user, setUser] = useState(JSON.parse(localStorage.getItem('user')));
  const [date, setDate] = useState(moment().subtract(0, 'months').startOf('month').format("DD-MM-YYYY"));
  const [ProjectedData, setProjectedData] = useState([]);
  const [TakeFeedback, setTakeFeedback] = useState(true);
  const [ShowThankYou, setShowThankYou] = useState(false);
  const [NegativeFeedbackReason, setNegativeFeedbackReason] = useState(false);
  const [RightPopup, setRightPopup] = useState(false);
  const [productId, setProductId] = useState(null);
  const [ProjectionDetailsMotor, setProjectionDetailsMotor] = useState([]);

  const refCurrentTrend = useRef(null);
  const refOpportunity = useRef(null);
  const refDailySourcing = useRef(null);
  const [activeScroll, setActiveScroll] = useState(0);

  const executeCurrentTrendScroll = (e) => {
    setActiveScroll(0);
    //refCurrentTrend.current.scrollIntoView()
    window.scrollTo(0, refCurrentTrend.current.offsetTop - 60)
  }
  const executeOpportunityScroll = () => {
    setActiveScroll(1);
    window.scrollTo(0, refOpportunity.current.offsetTop - 60)
    //refOpportunity.current.scrollIntoView()
  }
  const executeDailySourcingScroll = () => {
    setActiveScroll(2);
    window.scrollTo(0, refDailySourcing.current.offsetTop - 60)
    //refDailySourcing.current.scrollIntoView()
  }

  const handleTabChange = (event, newValue) => {
    setActiveScroll(newValue);
    switch (newValue) {
      case 0:
        executeCurrentTrendScroll()
        break;
      case 1:
        executeOpportunityScroll()
        break;
      case 2:
        executeDailySourcingScroll()
        break;
      default:
        break;
    }
  }

  const toggleDrawer = (open) => (event) => {
    /*if (event && event.type === 'keydown' && (event.key === 'Tab' || event.key === 'Shift')) {
      return;
    }*/

    setRightPopup(open);
  };
  const loadCriteria = (productId) => {

    if (productId == 7) {
      return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>
    }
    if (productId == 117) {
      return <CriteriaMotor productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaMotor>
    }
    if (productId == 115) {
      return <CriteriaInvestment productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaInvestment>
    }
    return <CriteriaTerm productId={ProjectedData.ProductId} date={date} superGroupId={ProjectedData.ProcessId}></CriteriaTerm>



  }



  //const [userDetail, setUserDetail] = useState({});


  const handleFeedback = (event, value) => {
    setTakeFeedback(false);
    setShowThankYou(false);
    setNegativeFeedbackReason(false);
    if (value == "Yes") {
      setShowThankYou(true);
      submitFeedbackReason()
    } else {
      setNegativeFeedbackReason(true);
    }
  };
  console.log("Branch")
  const submitFeedbackReason = (value = "") => {
    var jsonParam = {
      "AgentId": agentDetail.AgentId,
      "Response": ShowThankYou || value === "" ? 1 : 0,
      "Reason": value,
      "Source": "ProjectedIncentive",

    }
    console.log(jsonParam);
    services
      .API_POST(`Incentive/InsertProjectedIncentiveFeedback`, jsonParam)
      .then(response => {
        ////debugger
        if (response && response.Status) {
          setShowThankYou(true);
          setNegativeFeedbackReason(false);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }
  useEffect(() => {
    services
      .API_GET(`Incentive/GetProjectedIncentive/${agentDetail.AgentId}`).then(response => {
        ////debugger
        if (response && response.Status) {
          response = JSON.parse(response.Response);
          //console.log("Dashboard================", response[0]);
          setProjectedData(response[0]);
          setProductId(response[0].ProductId);
          // let key = props.enqueueSnackbar('To make the projections more accurate, we are now showing the incentives post salary adjustment.', {
          //   variant: 'error',
          // })
          //debugger;
          try {
            const dt = moment().subtract(1, 'months').startOf('month').format("DD-MM-YYYY");
            let url = `Incentive/InsertAgentIncentivLog/${response[0].UserId}/0/${response[0].ProductId}/${dt}?PageName=ProjectedIncentive&EventName=Click`;

            services
              .API_GET(url).then(response => { })
              .catch((err) => {
              });
          }
          catch (e) {

          }
          //debugger;
          if ([117, 115].indexOf(response[0].ProductId) > -1) {
            try {
              services
                .API_GET(`Incentive/GetProjectionDetailsMotor/${agentDetail.AgentId}`).then(response => {
                  ////debugger
                  if (response && response.Status) {
                    response = JSON.parse(response.Response);
                    if (response.length > 0) {
                      response = JSON.parse(response[0]);
                      setProjectionDetailsMotor(response);
                    }
                  }
                })
                .catch((err) => {
                  console.log("Error", err);
                });
            }

            catch (e) {

            }
          }




        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }, [agentDetail.AgentId, props]);

  useEffect(() => {
    return () => {
      console.log("cleaned up");
      props.closeSnackbar();
    };
  }, [props]);

  const bindTillNowData = (CalculationType, isWeightedAPE) => {
    let result = [];
    if (ProjectionDetailsMotor.length > 0) {

      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

        const element = ProjectionDetailsMotor[index];

        if (ProjectedData.ProductId == 117) {
          if (element.CalculationType == CalculationType) {
            result.push(<tr>
              <td>{element.PlanType}</td>
              <td>{element.PlanCategory}</td>
              <td className={(element.CalculationType == "NextProjection" && element.PlanCategory == "Comprehensive") ? 'highlight' : ''}>{element.BookingCount}</td>
            </tr>)
          }
        }
        else if (ProjectedData.ProductId == 115) {
          if (element.CalculationType == CalculationType) {
            result.push(<tr>
              <td>{element.PlanType}</td>
              {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}

              <td className={(element.CalculationType == "NextProjection" && element.PlanCategory == "Comprehensive") ? 'highlight' : ''}>
                {isWeightedAPE &&
                  <><i className="fa fa-inr"></i> {element.TotalWeightedAPE && Math.round(element.TotalWeightedAPE).toLocaleString('en-IN')}</>}
                {!isWeightedAPE &&
                  <><i className="fa fa-inr"></i> {element.TotalAPE && Math.round(element.TotalAPE).toLocaleString('en-IN')}</>}


              </td>
            </tr>)
          }
        }
      }
    }
    return result;
  }

  const bindProjectiondata = (CalculationType) => {
    let result = [];
    let totalIncentiveAmount = 0;
    if (ProjectionDetailsMotor.length > 0) {

      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

        const element = ProjectionDetailsMotor[index];

        if (ProjectedData.ProductId == 117) {
          if (element.CalculationType == CalculationType) {
            totalIncentiveAmount += parseInt(element.IncentiveAmount) || 0;
            result.push(<tr>
              <td>{element.PlanType}</td>
              <td>{element.PlanCategory}</td>
              <td>{element.BookingCount} x {element.IncentiveValue}</td>
              <td>{element.IncentiveAmount}</td>
            </tr>)
          }
        }
        else if (ProjectedData.ProductId == 115) {
          if (element.CalculationType == CalculationType) {
            totalIncentiveAmount += parseInt(element.IncentiveAmount) || 0;
            result.push(<tr>
              <td>{element.PlanType}</td>
              {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}
              {ProjectedData.ProcessId != 74 && <td>-</td>}
              {/* <td>{element.PlanCategory}</td> */}
              <td>
                <i className="fa fa-inr"></i> {element.TotalWeightedAPE && Math.round(element.TotalWeightedAPE).toLocaleString('en-IN')}   {" "}   x   {" "}{element.IncentiveValue} {"%"}
              </td>
              <td>
                <i className="fa fa-inr"></i> {element.IncentiveAmount && Math.round(element.IncentiveAmount).toLocaleString('en-IN')}
              </td>


            </tr>)
          }
        }
      }
    }

    result.push(<tr>
      <td>{'-'}</td>
      <td>{' '}</td>
      <td>{' '}</td>
      <td>{' '}</td>
    </tr>);

    result.push(<tr>
      <td>Total</td>
      <td>{'-'}</td>
      <td>{'-'}</td>
      <td><i className="fa fa-inr"></i> {totalIncentiveAmount && Math.round(totalIncentiveAmount).toLocaleString('en-IN')}</td>

    </tr>);

    return result;
  }

  const bindDRR = () => {
    let result = [
      <tr>
        <th >Plan Type</th>
        <th >Plan Category</th>
        <th >Minimum Bookings Required (full month)</th>
        <th >Required DRR</th>
      </tr>
    ];


    let CalculationType = 'MinimumRequired';
    let sum_drr = 0;
    if (ProjectionDetailsMotor.length > 0) {
      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
        const element = ProjectionDetailsMotor[index];
        if (ProjectedData.ProductId == 117) {
          if (element.CalculationType == CalculationType) {

            sum_drr += Math.ceil(element.DRR);

            result.push(<tr>
              <td >{element.PlanType}</td>
              <td>{element.PlanCategory}</td>
              <td>#{element.BookingCount}</td>
              <td>
                #{Math.ceil(element.DRR).toLocaleString('en-IN')}
              </td>


            </tr>)

            // console.log(element.DRR);

            // console.log("This Works!");
          }
        }
      }
      console.log(sum_drr);
    }

    return result;

  }

  const bindSourcedBooking = () => {
    let result = [
      <tr>
        <th >Plan Type</th>
        {ProjectedData.ProcessId == 74 && <th >Plan Category</th>}
        <th >Min Sourced APE Req. (full month)</th>
        <th >Required DRR</th>
      </tr>
    ];

    let CalculationType = 'MinimumRequired';
    let sum_drr = 0;
    
    if (ProjectionDetailsMotor.length > 0) {
      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
        const element = ProjectionDetailsMotor[index];
        if (ProjectedData.ProductId == 115) {
          if (element.CalculationType == CalculationType) {

            sum_drr += Math.ceil(element.DRR);


            result.push(<tr>
              <td >{element.PlanType}</td>
              {ProjectedData.ProcessId == 74 && <td >{element.PlanCategory}</td>}
              <td><i className="fa fa-inr"></i> {Math.ceil(element.TotalAPE).toLocaleString('en-IN')}</td>
              <td><i className="fa fa-inr"></i> {Math.ceil(element.DRR).toLocaleString('en-IN')}</td>

            </tr>)

            // console.log(element.DRR);

            // console.log("This Works!");
          }
        }
      }
      console.log(sum_drr);
    }

    return result
  }



  

  const LearnMorePI = () => {
    let result = []

    if ([115].indexOf(ProjectedData.ProductId) != -1) {

      result.push(<a href="/images/incentive/pdf/SavingWebPoster.pdf" target="_blank" className="incentiveCriteraPopup alignLeft">Learn More About Projected Incentive <ChevronRightIcon /> </a>)

    }
    if ([117].indexOf(ProjectedData.ProductId) != -1) {

      result.push(<a href="/images/incentive/pdf/WebPoster.pdf" target="_blank" className="incentiveCriteraPopup alignLeft">Learn More About Projected Incentive <ChevronRightIcon /> </a>)

    }

    return result
  }

  

  const bindNextProjectionDRR = () => {
    let result = [];
    let DRR = 0;
    let CalculationType = 'NextProjection';
    if (ProjectionDetailsMotor.length > 0) {

      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {

        const element = ProjectionDetailsMotor[index];
        if (ProjectedData.ProductId == 115) {
          if (element.CalculationType == CalculationType) {
            DRR += parseInt(element.DRR) || 0;
            result.push(<tr>
              <td>{element.PlanType}</td>
              {ProjectedData.ProcessId == 74 && <td>{element.PlanCategory}</td>}
              {ProjectedData.ProcessId != 74 && <td>-</td>}

              <td>
                <i className="fa fa-inr"></i> {element.DRR && Math.round(element.DRR).toLocaleString('en-IN')}
              </td>


            </tr>)
          }
        }
      }
    }

    result.push(<tr>
      <td>{'-'}</td>
      <td>{' '}</td>

      <td>{' '}</td>
    </tr>);

    result.push(<tr>
      <td>Total</td>
      <td>{'-'}</td>

      <td><i className="fa fa-inr"></i> {DRR && Math.round(DRR).toLocaleString('en-IN')}</td>

    </tr>);

    return result;
  }
  const sumDRR = () => {
    let CalculationType = 'MinimumRequired';
    let sum_drr = 0;
    if (ProjectionDetailsMotor.length > 0) {
      for (let index = 0; index < ProjectionDetailsMotor.length; index++) {
        const element = ProjectionDetailsMotor[index];

        if (element.CalculationType == CalculationType) {

          sum_drr += Math.ceil(element.DRR);


        }
      }

    }

    return sum_drr;
  }


  return (
    <>

      <Grid container item xs={12} md={12}>
        <Grid item sm={12} md={12} xs={12}>
          <ProjectedRanking projectedData={ProjectedData} userId={agentDetail.AgentId} date={date} />
        </Grid>
        <Grid item sm={6} md={6} xs={12}>
          {LearnMorePI()}
        </Grid>
        <Grid item sm={6} md={6} xs={12} className={classes.rightPopup}>
          <a href="javascript:void(0)" onClick={toggleDrawer(true)} className="incentiveCriteraPopup">View Current Month Incentive Criteria <ChevronRightIcon /> </a>
          {
            <SwipeableDrawer
              anchor="right"
              open={RightPopup}
              onClose={toggleDrawer(false)}
              onOpen={toggleDrawer(true)}
              className="CriteraPopup"
            >
              <div
                className={isDesktop ? classes.list : classes.mList}
                role="presentation"
              //onClick={toggleDrawer(false)}
              //onKeyDown={toggleDrawer(false)}
              >
                <span className="crossButton" onClick={toggleDrawer(false)}>X</span>
                {loadCriteria(productId)}
              </div>
            </SwipeableDrawer>
          }
        </Grid>

        <Grid container spacing={2} className='mobile-view'>
        <Box className='items-list'>
            <Tabs
              value={activeScroll}
              onChange={handleTabChange}
              variant="scrollable"
              scrollButtons
              allowScrollButtonsMobile
              aria-label="scrollable force tabs example"
            >
              <Tab label="Current Trends" />
              {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
                <Tab label="Opportunity" />}
              <Tab label="Daily Sourcing Req." />
            </Tabs>
          </Box>
        </Grid>

        <Grid item sm={12} md={12} xs={12} className='common-view'>

          <div className="CurrentMonthData" ref={refCurrentTrend}>
            <Grid container>
              <Grid item sm={4} md={4} xs={12}>
                <h2>Current Trends</h2>
                <img src="/images/incentive/currentTrends.png" className="leftimage" />
              </Grid>
              <Grid item sm={8} md={8} xs={12}>
                <TableContainer>
                  <Table className='web-common' aria-label="simple table">
                    {ProjectedData.SuperGroupTypeId == 1 && ProjectedData.ProductId != 115 &&
                      <TableBody>
                        <TableRow>
                          <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                          <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell >Sourced APE (till date)</TableCell>
                          <TableCell ><i className="fa fa-inr"></i> {ProjectedData.APETillNow && Math.round(ProjectedData.APETillNow).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Projected Sourced APE (full month)</TableCell>
                          <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ProjectedAPE && Math.round(ProjectedData.ProjectedAPE).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Weighted APE/Sourced APE for your process in last payout Month</TableCell>
                          <TableCell >{ProjectedData.WeightedAPERatio && (ProjectedData.WeightedAPERatio * 100).toFixed(0)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell >Projected Weighted APE</TableCell>
                          <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.ProjectedWeightedAPE && Math.round(ProjectedData.ProjectedWeightedAPE).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(pre salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        </>
                        }
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(Post salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        }

                      </TableBody>
                    }
                    {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId != 117 &&
                      <TableBody>
                        <TableRow>
                          <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                          <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Sourced Bookings / APE (till date)</TableCell>
                          <TableCell> #{ProjectedData.BKGSTillNow} / &nbsp;<i className="fa fa-inr"></i> {ProjectedData.APETillNow && Math.round(ProjectedData.APETillNow).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Projected Sourced Bookings / APE (full month)</TableCell>
                          <TableCell >#{Math.round(ProjectedData.ProjectedBKGS)} / &nbsp; <i className="fa fa-inr"></i> {ProjectedData.ProjectedAPE && Math.round(ProjectedData.ProjectedAPE).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Issued Bookings/Sourced Bookings for your process in last payout Month</TableCell>
                          <TableCell >{ProjectedData.WeightedBKGSRatio && (ProjectedData.WeightedBKGSRatio * 100).toFixed(0)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Weighted APE/Sourced APE for your process in last payout Month</TableCell>
                          <TableCell >{ProjectedData.WeightedAPERatio && (ProjectedData.WeightedAPERatio * 100).toFixed(0)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell >Projected Issued Bookings / Weighted APE</TableCell>
                          <TableCell className="highlight">
                            #{Math.round(ProjectedData.ProjectedIssuedBookings)} / &nbsp; <i className="fa fa-inr"></i> {ProjectedData.ProjectedWeightedAPE && Math.round(ProjectedData.ProjectedWeightedAPE).toLocaleString('en-IN')}</TableCell>
                        </TableRow>
                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(pre salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        </>
                        }
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}% <i>(Post salary adjustment)</i></TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        }

                      </TableBody>
                    }
                    {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId == 117 &&
                      <TableBody>
                        <TableRow>
                          <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                          <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                      <td>Sourced Bookings (till date)</td>
                                      <td>#{ProjectedData.BKGSTillNow}</td>
                                    </tr>
                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindTillNowData("TillNow")}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                      <td>Projected Sourced Bookings (full month)</td>
                                      <td>#{ProjectedData.ProjectedBKGS}</td>
                                    </tr>
                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindTillNowData("CurrentProjection")}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>


                          </TableCell>

                        </TableRow>
                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(pre salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                          <TableRow>
                            <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        </>
                        }
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(Post salary adjustment)</i></td>
                                        <td><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindProjectiondata("CurrentProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                                <p className={classes.currentProjectedIncentive}>Your Current Projection CJ Incentive is calculated as : <b style={{ color: 'red' }}>Total - Salary</b></p>
                              </Accordion>
                            </TableCell>
                          </TableRow>
                        }

                      </TableBody>
                    }
                    {ProjectedData.SuperGroupTypeId == 1 && ProjectedData.ProductId == 115 &&
                      <TableBody>
                        <TableRow>
                          <TableCell>Your CJ Incentive Projection <span>for {ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('MMM\' YYYY')} basis sourcing till</span></TableCell>
                          <TableCell >{ProjectedData.SourcingDate && moment(ProjectedData.SourcingDate, 'YYYY-MM-DD').format('Do MMM YYYY')}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                      <td>Sourced APE (till date)</td>
                                      <td><i className="fa fa-inr"></i> {ProjectedData.APETillNow && Math.round(ProjectedData.APETillNow).toLocaleString('en-IN')}</td>

                                    </tr>
                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindTillNowData("TillNow")}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                      <td>Projected Sourced APE (full month)</td>
                                      <td><i className="fa fa-inr"></i> {ProjectedData.ProjectedAPE && Math.round(ProjectedData.ProjectedAPE).toLocaleString('en-IN')}</td>

                                    </tr>
                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindTillNowData("CurrentProjection", false)}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>


                          </TableCell>

                        </TableRow>
                        <TableRow>
                          <TableCell>Weighted APE/Sourced APE for your process in last payout Month</TableCell>
                          <TableCell >{ProjectedData.WeightedAPERatio && (ProjectedData.WeightedAPERatio * 100).toFixed(0)}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                      <td>Projected Weighted APE (full month)</td>
                                      <td><i className="fa fa-inr"></i> {ProjectedData.ProjectedWeightedAPE && Math.round(ProjectedData.ProjectedWeightedAPE).toLocaleString('en-IN')}</td>

                                    </tr>
                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindTillNowData("CurrentProjection", true)}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>


                          </TableCell>

                        </TableRow>
                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && <>
                          {/* <TableRow>
                            <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(pre salary adjustment)</i></TableCell>
                            <TableCell className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow> */}
                          <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(pre salary adjustment)</i></td>
                                        <td className={ProjectedData.ActualCurrentProjectedIncentive == 0 ? "errorfont" : ""}><i className="fa fa-inr"></i> {ProjectedData.CurrentProjectedIncentive && Math.round(ProjectedData.CurrentProjectedIncentive).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindProjectiondata("CurrentProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                                {/* <p className={classes.currentProjectedIncentive}>Your Current Projection CJ Incentive is calculated as : <b style={{ color: 'red' }}>Total - Salary</b></p> */}
                              </Accordion>
                            </TableCell>
                          </TableRow>

                          <TableRow>
                            <TableCell >Current Projected CJ Incentive <i>(Post salary adjustment)</i></TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>
                        </>
                        }
                        {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} <i>(Post salary adjustment)</i></td>
                                        <td><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindProjectiondata("CurrentProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                                <p className={classes.currentProjectedIncentive}>Your Current Projection CJ Incentive is calculated as : <b style={{ color: 'red' }}>Total - Salary</b></p>
                              </Accordion>
                            </TableCell>
                          </TableRow>
                        }

                      </TableBody>
                    }
                  </Table>
                </TableContainer>
                {/* <li>You are qualifying for Slab {ProjectedData.Slab || "-"} basis current projections - {ProjectedData.CurrentSlabPercentage}%</li> */}
                {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                  <div className="caption error"><img src="/images/incentive/announceIcon.svg" /><b>Basis current projections, you are not able to justify your cost. You need to work harder to make CJ incentives.</b></div>}
                {/* Issued Bookings/Sourced Bookings for your process in last payout Month */}
                {ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                  <div className="caption"><img src="/images/incentive/announceIcon.svg" /><b>Congratulations !!! Basis current projections you are able to justify your cost this month.</b></div>}


              </Grid>
            </Grid>

          </div>
          {ProjectedData.ActualCurrentProjectedIncentive > 0 && parseInt(ProjectedData.Slab) > 1 &&
            <div className="CurrentMonthData" ref={refOpportunity}>
              <Grid container>
                <Grid item sm={4} md={4} xs={12} className='common-view'>
                  <h2>Opportunity</h2>
                  <img src="/images/incentive/Opportunity.png" className="leftimage" />
                </Grid>
                <Grid item sm={8} md={8} xs={12}>
                  <TableContainer>
                    {ProjectedData.SuperGroupTypeId == 1 && ProjectedData.ProductId != 115 &&
                      <Table className='web-common opportunity' aria-label="simple table">
                        <TableBody>
                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell>For Slab {ProjectedData.NextSlab || "-"} Minimum Weighted APE Required</TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.NextSlabAPE && Math.round(ProjectedData.NextSlabAPE).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}


                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"} i.e. @{ProjectedData.NextSlabPercentage || 0}%</TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                            <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                        </TableBody>
                      </Table>
                    }
                    {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId != 117 &&
                      <Table className='web-common opportunity' aria-label="simple table">
                        <TableBody>
                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell>For Slab {ProjectedData.NextSlab || "-"} Minimum Issued bookings Required</TableCell>
                            <TableCell >#{ProjectedData.NextSlabBKGS}</TableCell>
                          </TableRow>}

                          {/* <TableRow>
                          <TableCell >Current Projected CJ Incentive in Slab {ProjectedData.Slab || "-"} calculated @{ProjectedData.CurrentSlabPercentage || 0}%</TableCell>
                          <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualCurrentProjectedIncentive && Math.round(ProjectedData.ActualCurrentProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                        </TableRow> */}

                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"} i.e. @{ProjectedData.NextSlabPercentage || 0}%</TableCell>
                            <TableCell ><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                            <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                        </TableBody>
                      </Table>
                    }
                    {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId == 117 &&
                      <Table className='web-common opportunity' aria-label="simple table">
                        <TableBody>
                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>For Slab {ProjectedData.NextSlab || "-"} Minimum Issued bookings Required</td>
                                        <td>#{ProjectedData.NextSlabBKGS}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindTillNowData("NextProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>
                            </TableCell>

                          </TableRow>}


                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"}</td>
                                        <td><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindProjectiondata("NextProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>
                            </TableCell>


                          </TableRow>}

                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                            <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                        </TableBody>
                      </Table>
                    }
                    {ProjectedData.SuperGroupTypeId == 1 && ProjectedData.ProductId == 115 &&
                      <Table className='web-common opportunity' aria-label="simple table">
                        <TableBody>
                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>For Slab {ProjectedData.NextSlab || "-"} Minimum Weighted APE Required</td>

                                        <td><i className="fa fa-inr"></i> {ProjectedData.NextSlabAPE && Math.round(ProjectedData.NextSlabAPE).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindTillNowData("NextProjection", true)}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>
                            </TableCell>

                          </TableRow>}


                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Projected CJ Incentive if you qualify for Slab {ProjectedData.NextSlab || "-"}</td>
                                        <td><i className="fa fa-inr"></i> {ProjectedData.ActualNextProjectedIncentive && Math.round(ProjectedData.ActualNextProjectedIncentive).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindProjectiondata("NextProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>
                            </TableCell>


                          </TableRow>}

                          {ProjectedData.Slab > 1 && <TableRow>
                            <TableCell className='last-text'>Incremental CJ Incentive</TableCell>
                            <TableCell className="highlight"><i className="fa fa-inr"></i> {ProjectedData.IncrementalIncentive && Math.round(ProjectedData.IncrementalIncentive).toLocaleString('en-IN')}</TableCell>
                          </TableRow>}

                        </TableBody>
                      </Table>
                    }
                  </TableContainer>
                  <div className="caption OpportunityNote"><img src="/images/incentive/note.svg" />

                    <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                    <ul>
                      <li>The above Incentive calculations include only Cost Justification (CJ) Incentive.</li>
                      {/* <li>Above Calculated CJ Incentive is before net of Salary for e.g. if Projected CJ Incentive: 47K and your salary is 25K then Net In-hand CJ Incentive: (47K-25K) = 22K</li> */}
                      {/* <li>Actual incentive may differ from this projections.</li> */}
                      {/* <li>In Case Projected CJ Incentive is less than salary, then Projected CJ Incentive earned is 0 and take home will be Salary.</li> */}
                    </ul>


                  </div>
                </Grid>
              </Grid>

            </div>}

          <div className="CurrentMonthData" ref={refDailySourcing}>
            <Grid container>
              <Grid item sm={4} md={4} xs={12} className='common-view'>
                <h2>Daily Targets</h2>
                <img src="/images/incentive/DailySourcingRequired.png" className="leftimage" />
              </Grid>
              <Grid item sm={8} md={8} xs={12}>

                <TableContainer>
                  {ProjectedData.SuperGroupTypeId == 1 &&
                    <Table className='web-common' aria-label="simple table">
                      <TableBody>
                        <TableRow>
                          <TableCell >Current Sourced APE/Day</TableCell>
                          <TableCell ><i className="fa fa-inr"></i> {ProjectedData.PerDayAPE && Math.round(ProjectedData.PerDayAPE).toLocaleString('en-IN')}</TableCell>
                        </TableRow>

                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && ProjectedData.productId == 117 && <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                    
                                      <td>Required Sourced Bookings/day to justify Cost and make CJ Incentive</td>
                                      <td><i className="fa fa-inr"></i> {sumDRR("CurrentProjection").toLocaleString('en-IN')}</td>
                                    </tr>


                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindSourcedBooking("CurrentProjection")}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>


                          </TableCell>

                        </TableRow>}
                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && ProjectedData.ProductId == 115 && <TableRow>
                          <TableCell colSpan={2}>
                            <Accordion>
                              <AccordionSummary
                                expandIcon={<ExpandMoreIcon />}
                                aria-controls="panel1a-content"
                                id="panel1a-header"
                              >
                                <table className="motorIncentive-header">
                                  <tbody>
                                    <tr>
                                    
                                      <td>Required Sourced APE/day to justify Cost and make CJ Incentive</td>
                                      <td><i className="fa fa-inr"></i> {sumDRR("CurrentProjection").toLocaleString('en-IN')}</td>
                                    </tr>


                                  </tbody>
                                </table>

                              </AccordionSummary>
                              <AccordionDetails>
                                <table className="motorIncentive-content">
                                  <tbody>
                                    {bindSourcedBooking("CurrentProjection")}
                                  </tbody>
                                </table>
                              </AccordionDetails>
                            </Accordion>


                          </TableCell>

                        </TableRow>}

                        


                        {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>

                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Required Sourced APE/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></td>
                                        <td><i className="fa fa-inr"></i> {ProjectedData.NextSlabPerDayAPE && Math.round(ProjectedData.NextSlabPerDayAPE).toLocaleString('en-IN')}</td>
                                      </tr>
                                    </tbody>
                                  </table>

                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindNextProjectionDRR()}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>
                            </TableCell>


                          </TableRow>}

                        {ProjectedData.ActualCurrentProjectedIncentive == 0 && ProjectedData.ProductId != 115 &&
                          <TableRow>
                            <TableCell>Required Sourced APE/day to justify Cost and make CJ incentive</TableCell>
                            <TableCell><i className="fa fa-inr"></i> {Math.round((ProjectedData.MinAPERequired - ProjectedData.APETillNow) / ProjectedData.NextWorkingDays).toLocaleString('en-IN')}</TableCell>

                          </TableRow>}

                      </TableBody>
                    </Table>}
                  {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId != 117 &&
                    <Table className='web-common' aria-label="simple table">
                      <TableBody>
                        <TableRow>
                          <TableCell >Current Sourced Bookings/Day</TableCell>
                          <TableCell >#{ProjectedData.PerDayBKGS}</TableCell>
                        </TableRow>
                        {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell>Required Sourced Bookings/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></TableCell>
                            <TableCell >#{ProjectedData.NextSlabPerDayBKGS}</TableCell>
                          </TableRow>}

                        {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                          <TableRow>
                            <TableCell>Required Sourced Bookings/day to justify Cost and make CJ incentive </TableCell>
                            <TableCell >#{Math.round((ProjectedData.MinBKGSRequired - ProjectedData.BKGSTillNow) / ProjectedData.NextWorkingDays * 10) / 10}</TableCell>
                          </TableRow>}

                      </TableBody>
                    </Table>}


                  {ProjectedData.SuperGroupTypeId == 3 && ProjectedData.ProductId == 117 &&
                    <Table className='web-common' aria-label="simple table">
                      <TableBody>
                        <TableRow>
                          <TableCell >Current Sourced Bookings/Day</TableCell>
                          <TableCell >#{ProjectedData.PerDayBKGS}</TableCell>
                        </TableRow>
                        {/* MAKE CHANGES HERE */}
                        {sumDRR() > 0 &&
                          <TableRow>
                            <TableCell colSpan={2}>
                              <Accordion>
                                <AccordionSummary
                                  expandIcon={<ExpandMoreIcon />}
                                  aria-controls="panel1a-content"
                                  id="panel1a-header"
                                >
                                  <table className="motorIncentive-header">
                                    <tbody>
                                      <tr>
                                        <td>Required Sourced Bookings/day to justify Cost and make CJ Incentive</td>
                                        <td>#{sumDRR().toLocaleString('en-IN')}</td>
                                      </tr>

                                    </tbody>
                                  </table>
                                </AccordionSummary>
                                <AccordionDetails>
                                  <table className="motorIncentive-content">
                                    <tbody>
                                      {bindDRR("CurrentProjection")}
                                    </tbody>
                                  </table>
                                </AccordionDetails>
                              </Accordion>


                            </TableCell>

                          </TableRow>
                        }

                        {ProjectedData.Slab > 1 && ProjectedData.ActualCurrentProjectedIncentive > 0 &&
                          <TableRow>
                            <TableCell>Required Sourced Bookings/day to <span>Qualify for Slab {ProjectedData.NextSlab || "-"}</span></TableCell>
                            <TableCell >#{Math.ceil(ProjectedData.NextSlabPerDayBKGS)}</TableCell>
                          </TableRow>}

                        {/* {ProjectedData.ActualCurrentProjectedIncentive == 0 &&
                          <TableRow>
                            <TableCell>Required Sourced Bookings/day to justify Cost and make CJ incentive </TableCell>
                            <TableCell >#{Math.round((ProjectedData.MinBKGSRequired - ProjectedData.BKGSTillNow) / ProjectedData.NextWorkingDays * 10) / 10}</TableCell>
                          </TableRow>} */}

                      </TableBody>
                    </Table>}
                </TableContainer>
                <div className="caption note"><img src="/images/incentive/note.svg" />
                  <span style={{ width: "140px", margin: "auto" }}>Please note: </span>
                  <ul>
                    <li>Projections does not include booking incentives, quality dampners and multiplier etc.</li>
                    <li>Actual calculations may differ from projections.</li>
                    {ProjectedData.ProductId == 115 &&
                      <li>Required Sourced APE/day  does not include  Annuity, SinglePayUlip & SinglePayCG.</li>
                    }
                  </ul>
                </div>
              </Grid>
            </Grid>

          </div>

          {TakeFeedback && (
            <div className="feedbackBox">
              <FeedbackIcon className="feedbackIcon" />
              <p>Did you find this information useful?</p>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
            </div>
          )
          }

          {NegativeFeedbackReason && (
            <div className="dislike">
              <FeedbackIcon className="feedbackIcon" />
              <p>Did you find this information useful?</p>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "Yes")}><ThumbUpIcon />Yes</span>
              <span className="thumbIcon" onClick={(event) => handleFeedback(event, "No")}><ThumbDownIcon /> No</span>
              <div>
                <p>Tell us why</p>
                <FormControl component="fieldset">
                  <RadioGroup
                    aria-label="status"
                    name="status"
                    className="radioBtn"
                  >
                    {" "}

                    <FormControlLabel
                      value="Too much information"
                      control={<Radio onClick={() => submitFeedbackReason("Too much information")} />}
                      label="Too much information"
                      small
                    />
                    <FormControlLabel
                      value="Not able to understand"
                      control={<Radio onClick={() => submitFeedbackReason("Not able to understand")} />}
                      label="Not able to understand"
                      small
                    />
                    <FormControlLabel
                      value="Data inaccuracy"
                      control={<Radio onClick={() => submitFeedbackReason("Data inaccuracy")} />}
                      label="Data Inaccuracy"
                      small
                    />
                  </RadioGroup>
                </FormControl>
              </div>
            </div>
          )}

          {ShowThankYou && (
            <div className="verifiedBox">
              <img src="/images/incentive/verified.svg" />
              <p>Thank you for your response</p>
            </div>
          )}


        </Grid>
      </Grid>

    </>
  )
};




export default withSnackbar(CurrentMonthProjections);
