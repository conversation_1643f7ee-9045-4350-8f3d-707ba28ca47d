import React, { useEffect, useState, Fragment } from "react";
import * as services from "../.././../services";
import _ from "lodash";
import { withStyles, makeStyles } from "@material-ui/styles";
import moment from "moment";


const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  // bookingSNo: {
  //   width: "6% !important",
  // },
  customWidth: {
    maxWidth: 500,
  },
  tollBox: {
    background: "red",
  },
  customWidth: {
    width: 300,
    maxWidth: "100%",
  },
  // noMaxWidth: {
  //   maxWidth: 'none',
  // },
}));

//   const WrapperTooltip = withStyles(theme => ({
//     tooltip: {
//       backgroundColor: "#fff",
//       padding: "10px",
//       boxShadow: " 0px 6px 16px #3469CB29",
//       fontSize: 11,
//       borderRadius: "5px",
//       maxWidth: "300px",
//       "& ul": {
//         "& li": {
//           listStyle: "none",
//           color: "#808080",
//         },
//       },
//     },
//     arrow: {
//       color: "#fff",
//     }
//   }))(Tooltip);

const QualifierHealthRen = (props) => {
  const classes = useStyles();
  const { userId, date, qualiUpsell } = props;
  // const [qualidata, setQualiData] = useState();

  let qualifierdata = JSON.parse(qualiUpsell)


  const renderQualifierData = (qualifierdata) =>{
    let t = []
    for(let i = 0; i < qualifierdata.length; i++){
      const element = qualifierdata[i]
      if( element.IncentiveType == "Qualifier"){
        t.push(element)
      }
    }

    return t
    
  }

  
  let qualidata  = renderQualifierData(qualifierdata)

  console.log(qualidata[0].TargetAchieved)

  

  

  return (

    
    <div className={classes.root}>     
        <div className="HealthRenewalCommanView height230">
          <h6>
          Qualifier++ Bookings Incentive
          </h6>
          <table>
            <thead>
              <tr>
                <th></th>
                <th>Incentive</th>
              </tr>
            </thead>
            <tbody>
              <tr>
              <td>Over Target Achieved Bookings</td>
                <td>{qualidata[0].TargetAchieved}</td>
              </tr>
              <tr>
              <td>Incentive Per Booking</td>
                <td><i class="fa fa-rupee"></i>{qualidata[0].Slab.toLocaleString('en-IN')}</td>
              </tr>
              <tr>
              <td>Total Booking Incentive</td>
              <td className="green"><i class="fa fa-rupee"></i>{qualidata[0].TotalIncentive.toLocaleString('en-IN')}</td>
              </tr>
             
            </tbody>

          </table>




        </div>
      </div>
 

  );

}

export default QualifierHealthRen