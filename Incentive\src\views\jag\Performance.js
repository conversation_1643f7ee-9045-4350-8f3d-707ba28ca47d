import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/Criteria";
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
} from "@material-ui/core";

import ProcessBar from "./ProcessBar";

const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 0px 26px #0000001a",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
}));

const Performance = (props) => {
  const classes = useStyles();
  const { userId, date } = props;
  const [agentDetail, setAgentDetail] = useState({});
  //const [highlights, setHighlights] = useState([]);
  //const [productId, setProductId] = useState([]);
  //const [showCriteria, setShowCriteria] = React.useState(false);

  // const handleClickOpen = (value) => {
  //   setShowCriteria(value);
  // };

  const getAgentDetail = () => {
    setAgentDetail({});
    //setHighlights([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Jag/GetAgentDetails/${userId}/${date}`)
      .then(response => {
        if (response && response != "[]") {
          setAgentDetail(response);
          //getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const showTotalPrevAPE = (agentDetail) => {
    if (agentDetail == null) {
      return '0';
    }
    else {
      //let TotalAPE_PrevYear = (agentDetail.TotalAPE_PrevYear ? agentDetail.TotalAPE_PrevYear : 0) + (agentDetail.ProratedAPE ? agentDetail.ProratedAPE : 0)
      let TotalAPE_PrevYear = (agentDetail.TotalAPE_PrevYear ? agentDetail.TotalAPE_PrevYear : 0) ;//+ (agentDetail.ProratedAPE ? agentDetail.ProratedAPE : 0)
      return (TotalAPE_PrevYear / 100000).toFixed(2) + " Lakhs"
    }
  }
  const showProjectedAPE = (agentDetail) => {

    if (agentDetail == null) {
      return '0';
    }
    else {
      if (agentDetail && agentDetail.IssuedAPE_CurrYear)
        return (agentDetail.IssuedAPE_CurrYear / 100000).toFixed(2) + " Lakhs"
      else
        return "0"
    }
  }

  useEffect(() => {
    getAgentDetail();
  }, [props]
  );

  let label = 'Issued APE'
  if ([131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1) {
    label = "Base Target";
  }
  else if (agentDetail.ProratedAPE > 0) {
    label = "Prorated APE";
  }

  return (
    <>
      <div className="pading-zero" className={classes.root}>



        <div className="jag-highlights">
          <h6>
            Performance  <i className="fa fa-chevron-right" aria-hidden="true"></i>
            <span>Shows performance for a financial year</span>
          </h6>
          <Grid container spacing={3}>
            {
              ['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(agentDetail.BU) == -1 ?
                <Grid item sm={3} md={3} xs={6}>
                  <div className="ranking-data">
                    <div className="rank-box">
                      <span>{label} <br />
                        {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? "for FY 20-21" : "for FY 19-20"}</span>
                      <h3><i className="fa fa-inr"></i> {showTotalPrevAPE(agentDetail)}</h3>
                    </div>
                  </div>
                </Grid> : ""  
            }

            {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null : <Grid item sm={3} md={3} xs={6}>
              <div className="ranking-data">
                <div className="rank-box">
                  <span>{agentDetail.ProratedAPE > 0 ? "Prorated bookings" : "Issued bookings"}<br /> for FY 19-20</span>
                  {/* <h3>{(agentDetail.TotalBKG_PrevYear ? agentDetail.TotalBKG_PrevYear : 0) + (agentDetail.ProratedBKGS ? agentDetail.ProratedBKGS : 0)}</h3> */}
                  <h3>{(agentDetail.TotalBKG_PrevYear ? agentDetail.TotalBKG_PrevYear : 0)}</h3>
                </div>
              </div>
            </Grid>}
            {['Motor', 'Motor Renewal', 'Health Renewal'].indexOf(agentDetail.BU) == -1 ?
              <Grid item sm={3} md={3} xs={6}>
                <div className="ranking-data">
                  <div className="rank-box">
                    {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ?
                      <span>Issued APE for FY 20-21 <br />{agentDetail.ActualG1}% growth vs Base Target</span>
                      :
                      <span>Issued APE for FY 20-21 <br />{agentDetail.ActualG1}% growth vs FY 19-20</span>
                    }

                    <h3><i className="fa fa-inr"></i> {showProjectedAPE(agentDetail)}</h3>
                  </div>
                </div>
              </Grid> : ""
            }
            {[131, 101].indexOf(agentDetail && agentDetail.ProductId) > -1 ? null :
              <Grid item sm={3} md={3} xs={6}>
                <div className="ranking-data">
                  <div className="rank-box">
                    <span>Issued bookings for FY 20-21<br />{agentDetail.ActualG2}% growth vs FY 19-20</span>
                    <h3>{agentDetail.IssuedBKG_CurrYear}</h3>
                  </div>
                </div>
              </Grid>
            }
          </Grid>



        </div>
      </div>

    </>
  );
};

export default Performance;
