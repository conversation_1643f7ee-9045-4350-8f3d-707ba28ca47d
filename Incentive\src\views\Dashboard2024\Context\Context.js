import React from 'react';

export const DashboardContext = React.createContext({open:false, popup: null, handlePopupClick:(e)=>{}, closeFunction:()=>{}, setRank:(rank)=>{}, dashboardData:{
    IncentiveCalculationPopup:null ,
    MissellDeduction: null,
    FosAllowance: null,
    ArrearClawback: null
}, setDashboardData: (data, key)=>{},monthChosen:null, setMonthChosen : (month)=>{},
agentDetails: null, calculationEnabler:null,
setprocessdetails: (processname, tenure)=>{}
})
export const DataTableContext = React.createContext({handleSort:(type, key, order)=>{}})