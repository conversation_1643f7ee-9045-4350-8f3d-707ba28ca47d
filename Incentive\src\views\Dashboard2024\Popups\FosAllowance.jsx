import React, { useEffect, useState, useContext } from "react";
import { Table, TableBody, TableCell, TableContainer, TableHead, TableRow } from "@mui/material";
import { GetCommonData } from "../../../common/CommonAction";
import { handleDownload, rupeeConverter, getUserDetails } from '../Utility/Utility';
import { DashboardContext } from "../Context/Context";


const FosAllowance = () => {


  const dashboardData = useContext(DashboardContext);
  const [fosAllowance, setFosAllowance] = useState(dashboardData.dashboardData.FosAllowance);


  let rupee = rupeeConverter();

  useEffect(() => {
  
    if(!fosAllowance)
    {
    // let body = {
    //   eCode: "PW00000",
    //   monthYear: "NOV2023" || dashboardData.monthChosen,
    //   userid: 8223
    // };

    let body={
      eCode: dashboardData.agentDetails && dashboardData.agentDetails.EmpId,
      monthYear:  dashboardData.monthChosen,
      userid: getUserDetails('UserId')
  }
    let data = { EndPoint: "fosAllowance", Method: "POST", AgentId: getUserDetails('UserId'), RequestTo: "term", body: JSON.stringify(body) };
    GetCommonData(
      "POST", data
      , (errorStatus, data) => {
        if (!errorStatus && data) {
        
          
          for (let key in data) {
            if (key == 'conversionPercentage') {
              data[key]['value'] = (Math.floor(data[key]['value'] * 100) + '%') || '-';
            }
            else if (key == "perVisitPayout") {
              data[key]['value'] = rupee.format(data[key]['value']) || '-' ;
            }
            else if (key == "totalAllowance") {
              data[key]['value'] = rupee.format(data[key]['value']) || '-' ;
            }
          }

          setFosAllowance(data);
          dashboardData.setDashboardData(data, 'FosAllowance');
        }
        else{
          setFosAllowance(null);
          dashboardData.setDashboardData(null, "FosAllowance");
        }
      });
    }

}, [])

  return (
    <>
      <h4>FOS Visit Allowance</h4>
      <TableContainer>
        <Table aria-label="customized table">
          <TableHead>
            <TableRow>
              {
                fosAllowance &&

                Object.keys(fosAllowance).map((key) => {

                  if (fosAllowance[key]['title']) {
                    return <TableCell align="right">{fosAllowance[key]['title']}</TableCell>
                  }
                  else {
                    return null;
                  }
                })
              }
              {!fosAllowance &&
                <>
                  <TableCell align="right">Visit</TableCell>
                  <TableCell align="right">Booking</TableCell>
                  <TableCell align="right">%Conv on Visit</TableCell>
                  <TableCell align="right">Per Visit Payout</TableCell>
                  <TableCell align="right">Overall Visit Allowance</TableCell>
                </>
              }
            </TableRow>
          </TableHead>
          <TableBody>
            <TableRow>
              {
                fosAllowance &&
                Object.keys(fosAllowance).map((key, index) => {
                  if (fosAllowance[key]['title']) {
                    return (
                      <TableCell align={index != 0 ? "center" : "right"}>{
                        fosAllowance[key]['value'] || '-'
                      }
                      </TableCell>
                    )
                  }

                  else {
                    return null
                  }
                })
              }
              {!fosAllowance &&
                <>
                  <TableCell align="right">-</TableCell>
                  <TableCell align="center">-</TableCell>
                  <TableCell align="center">-</TableCell>
                  <TableCell align="center">-</TableCell>
                  <TableCell align="center">-</TableCell>
                </>
              }
            </TableRow>

          </TableBody>
        </Table>
      </TableContainer>
      {/* <button className="downloadbutton">
        <img src="/images/TermDashboard/download.svg" />  */}
      {/* <CSVLink className="downloadbtn" filename="my-file.csv" > */}
        {/* Download */}
      {/* </CSVLink> */}
      {/* </button> */}
    </>
  )
}
export default FosAllowance;

