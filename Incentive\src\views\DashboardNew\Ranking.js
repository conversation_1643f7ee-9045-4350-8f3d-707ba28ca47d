import React, { useEffect, useState, Fragment } from "react";
import { makeStyles } from "@material-ui/styles";
import ExpansionPanel from "@material-ui/core/ExpansionPanel";
import ExpansionPanelSummary from "@material-ui/core/ExpansionPanelSummary";
import ExpansionPanelDetails from "@material-ui/core/ExpansionPanelDetails";
import ExpandMoreIcon from "@material-ui/icons/ExpandMore";
import { red } from "@material-ui/core/colors";
import * as services from "../../services";
import Criteria from "../../components/Dialogs/Criteria";
import moment from "moment";
import Slider from "react-slick";
import { green } from '@material-ui/core/colors';
import _ from "lodash";
let Activemonths = require('../../../ActiveMonths.json');
import {
  Grid,
  Typography,
  Card,
  CardContent,
  List,
  ListItem,
  MenuItem,
  CircularProgress,
  Fab
} from "@material-ui/core";

import ProcessBar from "./ProcessBar";



const useStyles = makeStyles((theme) => ({
  root: {
    padding: theme.spacing(0),
  },
  margin: {
    margin: theme.spacing(1),
  },
  extendedIcon: {
    marginRight: theme.spacing(1),
  },
  forBox: {
    width: "100%",
  },
  card: {
    background: "#ffffff",
    boxShadow: "0px 6px 16px #3469CB29",
    borderRadius: "20px",
  },

  cardBody: {
    padding: "15px 15px 0 !important",
  },
  fab: {
    width: "110px",
    height: "110px",
    flexDirection: 'column'
  },
  fabProgress: {
    color: '#1DD1A1',
    position: 'absolute',
    top: 14,
    left: 10,
    zIndex: 1,
  },
}));

// const getLastMonths = (BUId) => {
//   // let monthNames = moment.monthsShort();
//   // let today = new Date();
//   // let startMonth = 6;
//   // let year = 2021;

//   // let lastPayoutMonth = today.getMonth() - 2;
//   // let currentYear = today.getFullYear()-1;
//   // let d; let mth;
//   let monthsList = [];

//   // for(let i = startMonth; i<=11 && i<=lastPayoutMonth && year <= currentYear ; i++) {
//   //     d = new Date(year, i, 1);
//   //     mth = i + 1;
//   //     mth = mth < 10 ? "0" + mth : mth;
//   //     monthsList.push({
//   //       value: "01-" + mth + "-" + year,
//   //       name: monthNames[i] + " " + year
//   //     });

//   //     if(i == 11) {
//   //       i = 0;
//   //       year++;
//   //     }
//   // }


//   //     if(i == 11) {
//   //       i = 0;
//   //       year++;
//   //     }
//   // }

//   // let d; let mth;
//   // let monthsList = [];
//   // for (let i = months; i >= 3; i -= 1) {
//   //   d = new Date(today.getFullYear(), today.getMonth() - i, 1);
//   //   mth = d.getMonth() + 1
//   //   mth = mth < 10 ? "0" + mth : mth;
//   //   monthsList.push({
//   //     value: "01-" + mth + "-" + d.getFullYear(),
//   //     name: monthNames[d.getMonth()] + " " + d.getFullYear()
//   //   });
//   // }

//   //monthsList.push('01-06-2021');
  
//   monthsList.push({
//     value: "01-07-2022",
//     name: "July 2022"
//   });

//    monthsList.push({
//     value: "01-06-2022",
//     name: "Jun 2022"
//   });

//   monthsList.push({
//     value: "01-05-2022",
//     name: "May 2022"
//   });

//   monthsList.push({
//     value: "01-04-2022",
//     name: "April 2022"
//   });

//   monthsList.push({
//     value: "01-03-2022",
//     name: "March 2022"
//   });

//   monthsList.push({
//     value: "01-02-2022",
//     name: "Feb 2022"
//   });
  
//   monthsList.push({
//     value: "01-01-2022",
//     name: "Jan 2022"
//   });

//   monthsList.push({
//     value: "01-12-2021",
//     name: "Dec 2021"
//   });

//   console.log(monthsList)
//   setMonths(Activemonths);

//   return monthsList;
// };


const Ranking = (props) => {
  const classes = useStyles();
  const {
    userId,
    date,
    handleChange,
    TotalPayout
  } = props;
  const [agentDetail, setAgentDetail] = useState([]);
  const [highlights, setHighlights] = useState([]);
  // const [productId, setProductId] = useState("");
  const [showCriteria, setShowCriteria] = React.useState(false);
  const [currentMileStone, setCurrentMileStone] = React.useState(null);
  const [agentInfo, setAgentInfo] = useState({});
  const [showProgressBar, setShowProgressBar] = useState(false);
  const [IsCurrentMonth, setIsCurrentMonth] = useState(false);
  const [BUId, setBUid] = useState();
  //const [date, setDate] = useState(moment().subtract(3, 'months').startOf('month').format("DD-MM-YYYY"));
  const [months, setMonths] = useState([]);
  const [activeMonth] = useState(Activemonths[Object.keys(Activemonths).find(k => Activemonths[k].active === "true" )].value)
  const leaderBoardData = props.MotorRenewal && props.MotorRenewal[2] && 
    props.MotorRenewal[2].Status && props.MotorRenewal[2].Response != "[]" && JSON.parse(props.MotorRenewal[2].Response);
  let AgentRank = 0;
  let FloorProcess = '';
  const handleClickOpen = (value) => {
    setShowCriteria(value);
    const { userId, date } = props;
    let response = JSON.parse(localStorage.getItem('user'));
    let url = `Incentive/InsertAgentIncentivLog/${userId}/0/${response.ProductId}/${date}?PageName=Incentive&EventName=SeeCriteria`;
    services
      .API_GET(url).then(response => { })
      .catch((err) => {
      });

  };

  const getAgentDetail = () => {
    setMonths(Activemonths);
    setAgentDetail([]);
    setHighlights([]);
    if (!userId) {
      return;
    }

    services
      .API_GET(`Incentive/GetIncentiveAgentDetails/${userId}/${date}`)
      .then(response => {
        //debugger;
        if (response && response != "[]") {

          setBUid(response.BUId)

          setAgentDetail(response);
          getHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  const getHighlights = (agentDetail) => {
    if (!agentDetail.SuperGroupID) {
      return;
    }

    services
      .API_GET(`Incentive/GetIncentiveHighlights/${agentDetail.SuperGroupID}/${date}/${agentDetail.ProductId}/${agentDetail.AgentId}`)
      .then(response => {
        if (response && response != "[]") {
          //debugger;
          setHighlights(response);
        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  useEffect(() => {
    getAgentDetail();
  }, [userId, date]
  );


  useEffect(() => {
    if (userId, date) {
      //if (date == '01-04-2021') {
      //setShowProgressBar(true);
      //GetMileStonesData();
      // }
      // else {
      //   setShowProgressBar(false);
      // }
    }
  }, [userId, date]);

  if(leaderBoardData && leaderBoardData.length>0){
    let item = leaderBoardData;
    item.sort(function (a, b) {
        return b.TotalBookings - a.TotalBookings;
    });

    for (let i = 0; i < item.length; i++) {
        item[i] = { ...item[i], idx: i }
    }
    console.log(item)
    
    for (let j = 0; j < item.length; j++) {
      if(agentDetail && item[j].Ecode == agentDetail.EmpId) {
        AgentRank = item[j].idx+1;
        FloorProcess = item[j].Process

      }
  }
}

  const GetMileStonesData = () => {

    if (!userId) {
      return;
    }

    let dt = moment().startOf('month').format("DD-MM-YYYY");

    if (date == dt) {
      setIsCurrentMonth(true)
    }
    else {
      setIsCurrentMonth(false);
    }


    services
      .API_GET(`Incentive/GetMileStonesData/${userId}/${date}`)
      .then(response => {
        console.log(response)
        if (response && response !== "[]" && response.Status === true) {

          let list = JSON.parse(response.Response)

          let agentInfo = list.agentInfo && list.agentInfo[0];
          let milestone = _.orderBy(list.mileStones, ['TargetAmount'], ['asc'])
          //debugger;
          let CurrentMilestone = _.find(milestone, function (o) { return o.TargetAmount >= agentInfo.ProjectedIncentive; });


          //milestone = _.find(milestone, function (o) { return o.TargetAmount >= agentInfo.ProjectedIncentive; });
          //debugger;
          setCurrentMileStone(CurrentMilestone);
          setAgentInfo(agentInfo);

        }
      })
      .catch((err) => {
        console.log("Error", err);
      });
  }

  var getRemainingDays = function () {
    var date = new Date();
    var time = new Date(date.getTime());
    time.setMonth(date.getMonth() + 1);
    time.setDate(0);
    return (time.getDate() > date.getDate() ? time.getDate() - (date.getDate() - 1) : 0);
  }


  // const handleChange = (event, newValue) => {
  //   setDate(event.target.value || moment().startOf('month').format("DD-MM-YYYY"));

  //   const dt = event.target.value;
  //   let urlToken = urlParams.Token;
  //   let response = JSON.parse(localStorage.getItem('user'));
  //   let url = `Incentive/InsertAgentIncentivLog/${urlToken}/0/${response.ProductId}/${dt}?PageName=Incentive&EventName=MonthChange`;

  //   services
  //     .API_GET(url).then(response => { })
  //     .catch((err) => {
  //     });
  // };

  const settings = {
    dots: true,
    infinite: true,
    autoplay: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1
  };
  return (
    <>
      <div className="pading-zero" className={classes.root}>

        <div className="ranking-data">

          <div className="rank-box">
            <div className='rank-box-left-section'>
              <div className='name-description'>
                <strong>{agentDetail.UserName || "-"}</strong>
                {agentDetail.ProductId== 217 ? <p> {FloorProcess || "-"}, {agentDetail.JoiningMonth || "-"} Months</p> 
                : <p> {agentDetail.SuperGroupName || "-"}, {agentDetail.JoiningMonth || "-"} Months</p>}

                <span className="month-view">
                  <select onChange={handleChange} id="month" defaultValue={date} >
                    <option disabled="disabled" value="">Select Month</option>
                    {months.map((item, index) =>
                      <option
                        key={index}
                        value={item.value}
                        selected = {item.value == activeMonth}
                      >
                        {item.name}
                      </option>

                    )}
                  </select>
                </span>

              </div>
              {TotalPayout == 0 && <div className='highlights'>
                <h4>You have to work harder to earn Incentives !!!</h4>
              </div>}
              {/* {TotalPayout > 0 &&
                <div className='highlights'>
                  <h4>Congratulations !</h4>
                  <Slider {...settings}>

                    {highlights && highlights.length > 0 && highlights.map((item, index) => {
                      return <div>
                        <p className='highlights-description'><strong>{item.figure}</strong> {item.Description}</p>
                      </div>
                    })}
                  </Slider>
                </div>} */}

              {showProgressBar && currentMileStone && IsCurrentMonth && <div className='total-ape-progress-bar'>
                <img src="/images/target_incentive.svg" />

                <p>You need to make an APE of <span>₹ {currentMileStone && currentMileStone.RequiredRunRate && Math.round(currentMileStone.RequiredRunRate / 1000).toLocaleString('en-IN')}K</span> for next {getRemainingDays()} days To make an incentive of ₹ {currentMileStone && currentMileStone.TargetAmount && Math.round(currentMileStone.TargetAmount).toLocaleString('en-IN')} this month</p>
              </div>}

            </div>
            <div className='rank-box-right-section'>
              <ul>
                <li className="rank">
                  <span className='label'>{BUId == 2 ? "Rank" : "Rank (Within Process)"}
                    {([117, 217].indexOf(agentDetail.ProductId) > -1) && <h4>Based on eligible bookings</h4>}
                    {([117, 217].indexOf(agentDetail.ProductId) == -1) && BUId != 2 && <h4>Based on weighted APE</h4>}
                    {BUId == 2 && <h4>Based on Issued Bookings</h4>}

                  </span>
                  
                  {agentDetail.ProductId == 217 ? <span className='value'>#{AgentRank || "-"} </span> 
                  : <span className='value'>#{agentDetail.AgentRank || "-"} </span>}
                </li>

                {([115, 117, 217].indexOf(agentDetail.ProductId) > -1) || BUId == 2 ?
                  '' :
                  <li className="slab">
                    <span className='label'>Slab</span><span className='value'>{agentDetail.Currentlevel || "-"}</span>
                  </li>
                }
              </ul>
              <div className='background-caricrature'></div>
            </div>

            {/* <ProcessBar firstLevel={agentDetail.Currentlevel} lastLevel={agentDetail.NextLevel} levelPercentage={agentDetail.LevelPercentage || "0"} /> */}
          </div>

          {/* <div className="rank-box-inner">
            <ul>
              {(agentDetail.Incentivetext) ? (
                <li>
                  <span>
                    <img src="/images/information-circle.svg" />
                    <span className="numbere">{agentDetail.Incentivetext}</span>
                  </span>
                </li>
              ) : ""}
              <li>
                <span>
                  <button onClick={(e) => handleClickOpen(true)}>
                    See Criteria <i className="fa fa-angle-right"></i>
                  </button>
                </span>
              </li>
            </ul>
          </div> */}
        </div>

        {/* <div className="highlights">
          <h6>
            Highlights
          </h6>
          {(highlights && highlights.length > 0) ? (highlights.map((item, index) =>
            <div key={index} className="highlights-card">
              <ul>
                <li>
                  <strong>
                    {item.figure}
                    <span>
                      {item.Description}
                    </span>
                  </strong>
                </li>
                <li>
                  <img src="/images/winner.png" />
                </li>
              </ul>
            </div>
          )) : (
              <div className="highlights-card">
                <ul>
                  <li>
                    <strong>
                      <span>
                        &nbsp;
                  </span>
                    </strong>
                  </li>
                  <li>
                    <img src="/images/winner.png" />
                  </li>
                </ul>
              </div>
            )}
        </div> */}
      </div>
      {showCriteria ? <Criteria superGroupId={agentDetail.SuperGroupID} productId={agentDetail.ProductId || "7"} date={date} show={showCriteria} handleClose={() => handleClickOpen(false)} /> : null}
    </>
  );
};

export default Ranking;
