import React, {useEffect, useState} from "react";
import { withStyles } from "@material-ui/core/styles";
import Button from "@material-ui/core/Button";
import Dialog from "@material-ui/core/Dialog";
import MuiDialogTitle from "@material-ui/core/DialogTitle";
import MuiDialogContent from "@material-ui/core/DialogContent";
import MuiDialogActions from "@material-ui/core/DialogActions";
import IconButton from "@material-ui/core/IconButton";
import CloseIcon from "@material-ui/icons/Close";
import Typography from "@material-ui/core/Typography";
import * as services from "../../services";
import LoaderComponent from "../../components/Loader";

const styles = (theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(2),
  },
  closeButton: {
    position: "absolute",
    right: theme.spacing(1),
    top: theme.spacing(1),
    color: theme.palette.grey[500],
  },
});

const DialogTitle = withStyles(styles)((props) => {
  const { children, classes, onClose, ...other } = props;
  return (
    <MuiDialogTitle
      disableTypography
      className={classes.root}
      {...other}
      className="critiria-popup"
    >
      {onClose ? (
        <IconButton
          aria-label="close"
          className={classes.closeButton}
          onClick={onClose}
          className="close-btn"
        >
          <CloseIcon />
        </IconButton>
      ) : null}
      <Typography variant="h6" className="text-center">
        {children}
      </Typography>
    </MuiDialogTitle>
  );
});

const DialogContent = withStyles((theme) => ({
  root: {
    padding: theme.spacing(2),
  },
}))(MuiDialogContent);

const DialogActions = withStyles((theme) => ({
  root: {
    margin: 0,
    padding: theme.spacing(1),
  },
}))(MuiDialogActions);

const Criteria = (props) => {
  const { show, handleClose, superGroupId, productId, date } = props;
  const [criteriaList, setCriteriaList] = useState([]);
  const [criteriaListHtml, setCriteriaListHtml] = useState([]);
  const [criteriaSlabs, setCriteriaSlabs] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  const getCriteria = () => {
    setCriteriaList([]);
    setCriteriaListHtml([]);
    setCriteriaSlabs([]);
    services
    .API_GET(`Incentive/GetIncentiveCriteria/${superGroupId}/${productId}/${date}`)
    .then(response => {
      console.log(response && response != "[]");
        if (response && response != "[]") {
          setCriteriaList(response.Insights || null);
          setCriteriaListHtml(response.InsightsHtml || null);
          setCriteriaSlabs(response.Slabs ? _.orderBy(response.Slabs, ['IncentiveLevel'], ['desc']) : null);
        }
        setIsLoading(false);
      })
      .catch((err) => {
        setIsLoading(false);
        console.log("Error", err);
      });
  }

  useEffect(() => {
    getCriteria();
  }, [props]);

  return (
    <div>
      {/* <Button variant="outlined" color="primary" onClick={handleClickOpen}>
        Open dialog
      </Button> */}

      <Dialog
        onClose={handleClose}
        aria-labelledby="customized-dialog-title"
        open={show}
        fullWidth={true}
        maxWidth={"sm"}
      >
        {isLoading ? <LoaderComponent open={true} /> : null}
        <DialogTitle
          id="customized-dialog-title"
          className="text-center"
          onClose={handleClose}
        >
        <strong>CRITERIA</strong>
        </DialogTitle>
        <DialogContent className="critiria-popup">
        <div className="popup-inner-box">
        <div className="criteriaHtml" dangerouslySetInnerHTML={{ __html: criteriaListHtml }}></div>

{/* 
criteriaList && criteriaList.length > 0 && criteriaList.map((item, index) =>
<p key={index}>
  {item.Description}
</p>
)
*/}

<p>SLABS</p>

<div className="slabs-box">

  <ul>

    { criteriaSlabs && criteriaSlabs.length > 0 && criteriaSlabs.map((item, index) =>
    <li key={index}>
      <h5>Level {item.IncentiveLevel}</h5>
      <p>
      { 
        index==0 ? (`>${parseFloat(item.MinRangeDisplay).toLocaleString('en-IN')}`) : (`${parseFloat(item.MinRangeDisplay).toLocaleString('en-IN')} - ${parseFloat(item.MaxRangeDisplay).toLocaleString('en-IN')}`)
      }
      &nbsp;({item.IncentivePercentage}%)
      </p>
    </li>
    )}
  </ul>
</div>
        </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
export default Criteria;