import { Grid } from "@material-ui/core";
import React, { useState, useEffect } from "react";
import Slider from "react-slick";
import Card from '@material-ui/core/Card';
import CardContent from '@material-ui/core/CardContent';
// import ReactPlayer from 'react-player'
import { JAG_VIDEO_URL } from "../JagConstants2023";

const mainSlider = {
  dots: true,
  infinite: true,
  autoplay: true,
  speed: 800,
  slidesToShow: 1,
  slidesToScroll: 1,
  initialSlide: 0,
  responsive: [
    {
      breakpoint: 1024,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        infinite: true,
        dots: true
      }
    },
    {
      breakpoint: 600,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1,
        initialSlide: 0
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: 1,
        slidesToScroll: 1
      }
    }
  ]
};

const MessageAndCriteria = ({ agentData }) => {

  const productId = agentData.ProductId || 0;
  const BUId= agentData.BUId || 0;
  const MemberType = agentData.MemberType || "";
  const bu = agentData.BU || '';
  const videoLink = JAG_VIDEO_URL

  return (
    <Grid item md={4} xs={12}>
      <div className="MessageCriteria">
        {/* {
          videoLink &&
           
            <video width="100%" height="200" controls>
            <source src={videoLink} type="video/mp4" />
           
          </video>
    
           
        } */}

        {
          !videoLink &&
          <img src='/images/jag/coming-soon.jpeg' alt='Video' />
        }
        <div className="caption"> video <p> &nbsp; </p> information</div>
        <h4>Message and Criteria for JAG 5.0</h4>
        <hr />
        <Slider {...mainSlider}>

          <div className="banner slide1">
            <Card className="card">
              <CardContent>
                <div className="carousel-caption d-none d-md-block">
                  <h3>Message and Criteria (1)</h3>
                  {productId === 2 && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li>Cash Reward and Lottery Calculation based on FY-25 Issued APE.</li>
                    <li>To win the cash rewards  you need to grow over the target shown at the top of the page.</li>
                    <li> 100% (FOS) Secondary APE credit will be considered in the JAG & Emerging Star contest.</li>
                    <li>Issued ape will be considered after excluding FLC and First premium paid only bookings.</li>
                    </ul>
                  }

                  {productId === 7 && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li>Cash Reward and Lottery Calculation based on FY-25 Issued APE.</li>
                    <li>To win the cash rewards  you need to grow over the target shown at the top of the page.</li>
                    <li>50% (FOS) Secondary APE credit will be considered in the JAG & Emerging Star contest.</li>
                    <li>Issued ape will be considered after excluding FLC and First premium paid only bookings.</li>
                  </ul>
                  }

                  {productId === 115 && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li style={{color: 'red'}}>Business Health rating should be greater than 70% to qualify for cash rewards and lottery tickets.</li>
                    <li>Cash Reward and Lottery Calculation based on FY-25 Issued APE.</li>
                    <li>To win the cash rewards  you need to grow over the target shown at the top of the page.</li>
                    <li>50% (FOS) Secondary APE credit will be considered in the JAG & Emerging Star contest.</li>
                    <li>Issued ape will be considered after excluding FLC and First premium paid only bookings.</li>
                  </ul>
                  }
                  

                  {([65,67].includes(BUId)) && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li>Cash Reward and Lottery Calculation based on FY 24-25 Issued Bookings.</li>
                    <li> To win the cash rewards  you need to grow over the target shown at the top of the page.</li>
                    <li style={{color: 'red'}}>100% (FOS) Secondary Bookings will be considered in the JAG & Emerging Star contest.</li>
                    <li>Issued Bookings will be considered after excluding FLC and First premium paid only bookings.</li>
                    <li>Click on See how calculation works to check credit and total Source Bookings.</li>
                  </ul>
                  }

                  {([66,68].includes(BUId)) && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li>Cash Reward and Lottery Calculation based on FY 24-25 Issued APE.</li>
                    <li> To win the cash rewards  you need to grow over the target shown at the top of the page.</li>
                    <li style={{color: 'red'}}>100% (FOS) Secondary APE credit will be considered in the JAG & Emerging Star contest.</li>
                    <li>Issued APE will be considered after excluding FLC and First premium paid only APE.</li>
                    <li>Click on See how calculation works to check credit and total Source APE.</li>
                  </ul>
                  }

                  {productId === 131 && <ul>
                    <li style={{color: 'red'}}>Sourcing is closed. Maximize your Issuance.</li>
                    <li>Cash Reward and Lottery Calculation based on FY-25 Issued APE.</li>
                    <li>To win the cash rewards  you need to grow over the target shown at the top of the page. </li>
                    <li>Issued ape will be considered after excluding FLC and First premium paid only bookings.</li>
                    <li>For Renewal Groups , 50% of FY-25 Issued APE will be consider for Cash Rewards and Tickets.</li>
                  </ul>
                  }

                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="banner slide2">
            <Card className="card">
              <CardContent>
                <div className="carousel-caption d-none d-md-block">
                  <h3>Message and Criteria (2)</h3>
                  {productId === 2 && <ul>
                    <li>Click on See how calculation works  to check credit and total Source APE.</li>
                    <li>For any feedback or query related to the contest, please write back to <NAME_EMAIL></li>
                  </ul>
                  }

                  {productId === 7 && <ul>
                     <li>Click on See how calculation works  to check credit and total Source APE.</li>
                    <li>For any feedback or query related to the contest, please write back to <NAME_EMAIL></li>
                  </ul>
                  }

                  {productId === 115 && <ul>
                     <li>Single Pay CG, Ulip would be consider 10% of APE.</li>
                    <li>For LIC Team ,  APE would be Multiplied by 1.5 Factor to calculate FY 25 Issued APE.</li>
                    <li>Click on See how calculation works  to check credit and total Source APE.</li>
                    <li>For any feedback or query related to the contest, please write back to <NAME_EMAIL></li>
                  </ul>
                  }

                  {/* {([117, 217, 101].includes(productId))&&  */}
                  <ul>
                    <li>For any feedback or query related to the contest, please write back to <NAME_EMAIL></li>
                  </ul>
                  {/* } */}

                  {productId === 131 && <ul>
                   <li>Click on See how calculation works  to check credit and total Source APE.</li>
                    <li>For any feedback or query related to the contest, please write back to <NAME_EMAIL></li>
                  </ul>
                  }
                </div>

              </CardContent>
            </Card>
          </div>
          
          {/* <div className="banner slide3">
            <Card className="card">
              <CardContent>
                <div className="carousel-caption d-none d-md-block">
                  <h3>Message and Criteria</h3>
                  <ul>
                    <li>Growth Based on target</li>
                    <li>For any feedback or query related to the contest please write back to <NAME_EMAIL></li>
                    <li>APE will be projected for full month for calculation of cash rewards</li>
                    <li>10% of APE will be considered for Single Pay</li>
                  </ul>
                </div>

              </CardContent>
            </Card>
          </div> */}

        </Slider>
      </div>
    </Grid>
  );
};

export default MessageAndCriteria;